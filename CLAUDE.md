# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Core Principles

This document outlines the core principles and guidelines for working within this repository. Adhering to these standards ensures consistency, quality, and maintainability.

## Protected Files Policy

**CRITICAL**: The following files and patterns are PROTECTED and must NOT be modified without explicit user permission:

- `/docs/spec/**` - Specification documents
- `.cursor/rules/*.mdc` - AI rule definitions
- `docs/incident-reports/*.md` - Incident documentation
- `.env.example` - Environment template
- `lefthook.yml` - Git hooks configuration
- `.github/workflows/*.yml` - CI/CD configurations

If you need to modify any protected files:
1. Ask the user for explicit permission first
2. Explain why the change is necessary
3. Wait for confirmation before proceeding

Protected files are listed in `.protected-files`.

## Language Instructions

**IMPORTANT**: When working in this repository:
- Think and reason in English internally
- Communicate with users in Japanese (日本語でユーザーと会話してください)
- Code comments and documentation should follow the existing language patterns in the codebase
- Git commit messages should be written in Japanese (コミットメッセージは日本語で記述してください)

## Monorepo Structure

This is a **monorepo** using pnpm workspaces with the following structure:
- `/apps/main` - Main Next.js application with Convex backend
- `/apps/fumadocs` - Documentation site (if present)
- Root package.json manages cross-workspace commands

## Package Management and Version Control

**Package Manager**: This project uses **pnpm** (v10.13.1) for dependency management
- Efficient disk usage through symlinks (node_modules: ~376KB vs npm's 1.3GB)
- Stricter dependency resolution prevents phantom dependencies
- Faster installation times with intelligent caching
- Workspace support with filtering: `pnpm --filter <package-name>`

**Version Management**: Uses **mise** for consistent tool versions
- Automatically manages Node.js (LTS) and pnpm versions
- Configuration in `mise.toml`
- Run `mise install` to set up the correct versions

**Key Differences from npm**:
- Workspace commands: `npm -w` → `pnpm --filter`
- Script execution: `npm run` → `pnpm` (run is optional)
- Global packages: `npm install -g` → `pnpm add -g`
- Execute packages: `npx` → `pnpm dlx`

## Monorepo Command Execution

**CRITICAL**: Always execute commands from the repository root. Never use `cd` to navigate directories.

### Command Patterns
- Use filter flags: `pnpm --filter @kadou-delta-next/main dev`
- Run workspace scripts: `pnpm run build:main` (from root) or `pnpm --filter @kadou-delta-next/main build`
- Specify full paths in commands: `vitest apps/main/__tests__/`

### File Operations
- Always use absolute paths from repository root
- Edit files using full path: `/apps/main/src/components/Button.tsx`
- Never rely on relative paths after directory changes

### Package.json Script Organization
- **Root package.json**: Contains orchestration scripts that call workspace scripts
  - Example: `"dev:main": "pnpm --filter @kadou-delta-next/main dev"`
- **Workspace package.json** (e.g., `/apps/main/package.json`): Contains actual implementation
  - Example: `"dev": "concurrently \"pnpm:dev:*\""`
- Always check both root and workspace package.json files to understand the full command flow

**Remember**: Humans work from the repository root in monorepos. You should too.

## Development Commands Quick Reference

This section provides a quick reference for the most common commands. All commands should be run from the repository root.

### Core Workflow
```bash
# Start development server for a specific app (e.g., main)
# This is the most common command for daily development.
pnpm dev:main

# Run all verification checks across the monorepo (lint, types, tests)
# This is run automatically as a pre-commit hook.
pnpm verify:all

# Build a specific app for production
pnpm build:main
```

### All Monorepo Commands
```bash
# Development (run apps in parallel)
pnpm dev:all        # Main (:3232) and fumadocs (:3838)
pnpm dev:main       # Main app only (:3232, network accessible)
pnpm dev:fumadocs   # Fumadocs only (:3838)

# Build
pnpm build:all      # Build all workspaces
pnpm build:main     # Build main app only
pnpm build:fumadocs # Build fumadocs only

# Verification (run checks across all workspaces)
pnpm verify:all     # Run all checks (lint, types, tests)
pnpm test:all       # Run tests only
pnpm lint:all       # Run linting only
pnpm typecheck:all  # Run type checking only

# Formatting (root level)
pnpm format         # Format all files with Biome
pnpm lint           # Lint and fix all files with Biome
pnpm check:all      # Check all files without fixing

# Other
pnpm storybook      # Start Storybook dev server on port 6006
pnpm knip           # Detect unused code (see Code Quality Tools)
```

## Architecture Overview

This is a full-stack serverless application using:
- **Next.js 15** (App Router) for frontend with React 19
  - Turbopack enabled for development builds (stable in Next.js 15) for faster HMR and improved performance
  - Optimized imports for Chakra UI v3 via `optimizePackageImports` in next.config.ts
- **Convex** for backend (database, server functions, real-time sync)
  - Development dashboard opens automatically on `pnpm dev` via `predev` script
  - Uses `concurrently` to run frontend and backend servers in parallel
- **Clerk** for authentication (configured for Japanese locale)
- **Chakra UI v3** for component library and styling system
- **Biome** for linting and formatting (replaces ESLint/Prettier)
- **Vitest** for testing framework with jsdom environment
- **Sentry** for error tracking and performance monitoring (integrated at build time)
- **Vertical Slice Architecture (VSA)** for feature organization

### Key Architectural Patterns

1. **Vertical Slice Architecture (VSA)**
   - Features organized in `/src/features/` directory
   - Each feature contains its own components, hooks, types, and utilities
   - Promotes high cohesion and low coupling between features
   - Migration from traditional layered architecture completed

2. **Authentication Flow**
   - Route protection via `middleware.ts` using `clerkMiddleware`
   - Clerk-Convex integration through `ConvexProviderWithClerk` in `components/ConvexClientProvider.tsx`
   - User context available in Convex functions via `ctx.auth`
   - Protected routes pattern: routes under `/app` require authentication

3. **Data Layer**
   - Schema defined in `/convex/schema.ts`
   - Backend functions in `/convex/*.ts` files:
     - `query`: Read operations with real-time updates
     - `mutation`: Write operations
     - `action`: External API calls or side effects
   - Types auto-generated in `/convex/_generated/`

4. **Component Structure**
   - Server components by default in `/app` directory
   - Client components marked with `"use client"`
   - Convex hooks (`useQuery`, `useMutation`) only work in client components
   - Feature-specific components organized within each feature module

5. **Testing Structure**
   - Tests located in `__tests__/` directory
   - Uses Vitest with jsdom environment for React component testing
   - Testing utilities from `@testing-library/react` and `@testing-library/jest-dom`
   - Path aliases configured (`@/` maps to project root)

6. **Error Monitoring**
   - Sentry integrated via `next.config.ts` with `withSentryConfig`
   - Instrumentation files for client/server/edge runtimes
   - Configured for tunneling through `/monitoring` route to avoid ad blockers

## Code Development Principles

### YAGNI (You Aren't Gonna Need It) 原則

**重要**: 以下のYAGNI原則を厳守してください：

1. **必要になるまで実装しない**
   - ユーザーが明示的に要求した機能のみを実装する
   - "将来必要になるかもしれない"機能は作らない
   - 現在の要件を満たす最小限の実装を心がける

2. **過度な抽象化を避ける**
   - ❌ 避けるべき例：1つのコンポーネントのための汎用的なファクトリーパターン
   - ✅ 良い例：実際に使用されるコンポーネントの直接実装

3. **実装前に確認する**
   - 新機能を実装する前に、それが本当に必要か確認
   - 既存のコードで要件を満たせないか検討
   - 類似機能が既に存在しないか確認

4. **段階的な実装**
   - 最初は最も単純な実装から始める
   - 実際の使用状況に基づいて、必要に応じて拡張
   - 複雑な設計パターンは、実際に複雑性が必要になってから導入

5. **削除を恐れない**
   - 使用されていないコード、機能、ファイルは削除する
   - Knipツールを活用して未使用コードを定期的に確認

### 具体的な指示

- **新しいファイルを作成する前に**：既存のファイルで対応できないか確認
- **新しい依存関係を追加する前に**：既存のライブラリで対応できないか確認
- **新しいユーティリティ関数を作成する前に**：その関数が複数箇所で使用されるか確認
- **新しい設定オプションを追加する前に**：そのオプションが実際に必要か確認

### アンチパターンの例

```typescript
// ❌ 悪い例：過度に汎用的（1つのユースケースしかない）
interface ConfigurableButtonFactoryOptions<T> {
  variant?: T;
  size?: 'sm' | 'md' | 'lg';
  colorScheme?: string;
  // ... 20個以上のオプション
}

class ButtonFactory<T> {
  create(options: ConfigurableButtonFactoryOptions<T>) {
    // 複雑な実装
  }
}

// ✅ 良い例：必要最小限
function SubmitButton({ onClick }: { onClick: () => void }) {
  return <Button colorScheme="blue" onClick={onClick}>Submit</Button>
}
```

### TypeScript Class 使用方針

**重要**: このプロジェクトでは、TypeScriptのクラスを使用しません。

1. **関数ベースのアプローチを採用**
   - クラスの代わりに、純粋な関数とオブジェクトを使用する
   - ユーティリティは関数として実装する


## Code Style and Formatting

This project uses **Biome** for code quality:
- **Indentation**: Tabs (width 2)
- **Quotes**: Double quotes for strings
- **Semicolons**: As needed (ASI)
- **Import organization**: Automatic via Biome with `assist.enabled: true`
  - Automatically organizes imports on format
  - Groups imports by type (external, internal, relative)
  - Removes unused imports automatically
- **VCS Integration**: `vcs.enabled: true` for Git integration
- Files covered: `*.{js,jsx,ts,tsx,json,css}`
- Ignored paths: `node_modules`, `.next`, `dist`, `build`, `convex/_generated`, `storybook-static`, `.vercel`, `*.tsbuildinfo`

**Disabled Biome Rules**:
- `noForEach`: Allowed (complexity rule)
- `noNonNullAssertion`: Allowed (style rule)
- `useExhaustiveDependencies`: Disabled (correctness rule)
- `noExplicitAny`: Allowed (suspicious rule)
- `useButtonType`: Disabled (a11y rule)
- `noNestedComponentDefinitions`: Disabled in test files

## Git Hooks

This project uses **Lefthook** for Git hooks:
- **Installation**: Automatically set up via `pnpm run prepare` (runs `lefthook install`)
- **Pre-commit**: Runs `pnpm verify` automatically before commits
- The `verify` command runs in parallel:
  - `check`: Biome linting and formatting checks
  - `ls-lint`: File/directory naming convention checks
  - `typecheck`: TypeScript type checking for both Next.js and Convex
  - `test:run`: Vitest tests in CI mode
- **Configuration**:
  - `no_tty: false` - Terminal output enabled for better debugging
  - `parallel: false` - Commands run sequentially to ensure proper error reporting
  - Cannot be skipped with `--no-verify`

## Testing Framework

This project uses **Vitest** as the primary testing framework:

**Configuration**:
- Environment: jsdom (for React component testing)
- Setup file: `vitest.setup.ts` (includes @testing-library/jest-dom)
- Global test utilities enabled
- Path aliases configured (`@/` maps to project root)
- `passWithNoTests: true` - Tests pass even if no test files are found
- Excludes `app/**` directory from tests (App Router pages aren't unit tested)
- Testing libraries available:
  - `@testing-library/react` for component testing
  - `@testing-library/user-event` for simulating user interactions
  - `convex-test` for testing Convex functions

**Advanced Test Commands**:
```bash
# Run tests in watch mode (development)
pnpm test

# Run tests once (CI/production)
pnpm test:run

# Run tests with browser UI
pnpm test:ui

# Generate coverage report
pnpm test:coverage

# Run a specific test file
pnpm test -- __tests__/components/ResourceCard.test.tsx

# Run tests matching a pattern
pnpm test -- --grep "ResourceCard"

# Run tests in a specific directory
pnpm test -- __tests__/features/auth/

# Run tests with pattern matching in test name
pnpm test -- --testNamePattern="should render properly"

# Debug a specific test with verbose output
pnpm test -- --reporter=verbose __tests__/components/ResourceCard.test.tsx

# Run only failed tests from last run
pnpm test -- --changed

# Run tests for specific file patterns
pnpm test -- --include="**/*.test.tsx"
```

**Test Structure**:
- Unit tests: `__tests__/components/` - Component unit tests
- Feature tests: `__tests__/features/` - Feature-specific component tests
- Integration tests: `__tests__/integration/` - Feature integration tests
- Shared tests: `__tests__/shared/` - Shared utility and component tests
  - Component tests: `__tests__/shared/components/` - Shared component tests
  - Hook tests: `__tests__/shared/hooks/` - Custom hook tests
  - Layout tests: `__tests__/shared/components/layout/` - Layout component tests (e.g., Sidenav)
- Use React Testing Library for component testing
- Use jsdom for DOM manipulation testing
- Mock Convex hooks in test files when needed

## Storybook Integration

This project includes **Storybook** for component development and testing:

**Features**:
- Component isolation and documentation
- Accessibility testing with a11y addon
- Integration with Vitest for testing stories
- Browser-based testing using Playwright
- Vite-based build for faster development (`@storybook/nextjs-vite`)
- Network access enabled (`-h 0.0.0.0`)

**Commands**:
```bash
# Start Storybook development server on port 6006 (network accessible)
pnpm storybook

# Build Storybook for production
pnpm build-storybook
```

**Testing Integration**:
- Storybook tests run as part of Vitest configuration
- Uses Playwright with Chromium for browser testing
- Stories can be tested alongside unit tests
- Setup file: `.storybook/vitest.setup.ts`

## Code Quality Tools

### Knip - 未使用コード検出ツール

Knipは未使用のファイル、依存関係、エクスポートを検出する強力なツールです。

**使用方法**:
```bash
# 未使用コードの検出（ルートレベルで実行可能）
pnpm knip

# 各ワークスペースで個別に実行
pnpm --filter @kadou-delta-next/main knip

# 詳細レポート付き実行
npx knip
```

**分析レポート**: 
- `/apps/main/knip-unused-files-analysis.md`: メインアプリの詳細な分析結果
- ルートレベルでも設定済み（`knip.json`）

### similarity-ts - 類似コード検出ツール

開発環境にcargoでインストール済みの`similarity-ts`は、コードの重複や類似パターンを検出するツールです。

**使用方法**:
```bash
similarity-ts . --experimental-types
```

### ls-lint - ファイル・ディレクトリ命名規則チェッカー

`ls-lint`は、ファイルやディレクトリの命名規則を強制するツールです。

**使用方法**:
```bash
# 命名規則のチェックを実行
pnpm ls-lint
```

**ディレクトリ別命名規則**:
- `app/`: ディレクトリは`kebab-case`、ファイル(`.tsx`, `.ts`)は`kebab-case`または`PascalCase`、`.css`は`kebab-case`
- `components/`: ディレクトリは`kebab-case`または`PascalCase`、コンポーネント(`.tsx`, `.ts`)は`PascalCase`
- `convex/`: ディレクトリは`camelCase`、関数(`.ts`, `.js`)は`camelCase`または`kebab-case`
- `lib/`: ディレクトリは`kebab-case`、ファイル(`.ts`, `.tsx`)は`camelCase`または`kebab-case`
- `public/`: ディレクトリは`kebab-case`、アセット(`.svg`, `.png`, `.jpg`, `.jpeg`, `.ico`)は`kebab-case`

### dotenv-linter - 環境変数バリデーター

環境変数の整合性をチェックするツールです。`.env.local`が`.env.example`に定義されたすべての変数を含んでいることを確認します。

**インストール**:
```bash
# 自動インストール (pnpm install時)
pnpm install

# 手動インストール
cd apps/main && ./scripts/install-dotenv-linter.sh
```

**使用方法**:
```bash
# .env.localと.env.exampleを比較
pnpm envcheck
```

**動作例**:
- キーが不足している場合、終了コード1で警告を表示
- すべてのキーが一致している場合、終了コード0で正常終了
- 余分なキーがある場合も警告を表示

## Type Checking

Note: For continuous type checking during development, rely on your IDE's TypeScript integration (VSCode, WebStorm, etc.) rather than running a separate watch process. The `pnpm typecheck:all` command is available for manual verification.

## Date Handling

**CRITICAL**: Always use the date from the environment information:
- Check `Today's date` in the `<env>` tag
- Never guess or assume dates - always reference the environment
- Use ISO format (YYYY-MM-DD) for consistency
- When creating incident reports, documentation, or commit messages, always use the actual date from environment

## Important Conventions

1. **Japanese Localization**: App configured for Japanese users (HTML lang="ja", Clerk jaJP locale)

2. **Protected Routes**: Use middleware pattern - routes under `/app` are protected by default

3. **Convex Functions**: Always import from generated API:
   ```typescript
   import { api } from "@/convex/_generated/api";
   ```

4. **Real-time Data**: Convex queries automatically sync - no manual refresh needed

5. **Type Safety**: Use generated types from Convex - don't create manual types for database entities

6. **Next.js App Router Layout Inheritance**:
   - Layouts are inherited by all child routes - avoid duplicating components
   - Navigation components should only be in the root layout or specific route groups
   - See INC-003 for a real example of this issue

7. **Testing Patterns**: 
   - Use React Testing Library for component testing
   - Write tests in `__tests__/` directory mirroring source structure
   - Use descriptive test names with behavior-driven descriptions
   - Mock Convex hooks and external dependencies appropriately

8. **VSA Feature Organization**:
   - Each feature is self-contained with its own components, hooks, and types
   - Features export their public API through index.ts files
   - Cross-feature dependencies should be minimized
   - Common utilities can be placed in `/src/shared` for shared functionality
   - Current features:
     - **auth**: Authentication with Clerk integration (UserProfile, useAuth hook)
     - **numbers**: Real-time number management (NumbersManager, useNumbers/useAddNumber hooks)
     - **resources**: Resource display system (ResourceGrid/List/Card components)

9. **Chakra UI v3 Usage**:
   - Use Chakra UI components for consistent design system
   - Leverage built-in theme tokens for colors, spacing, and typography
   - Utilize Chakra's responsive utilities instead of custom CSS
   - Prefer Chakra's style props over inline styles or CSS classes
   - Example:
     ```typescript
     import { Box, Text, Button } from "@chakra-ui/react"
     
     <Box bg="blue.500" p={4} borderRadius="md">
       <Text color="white" fontSize="lg">Content</Text>
       <Button colorScheme="teal" size="sm">Action</Button>
     </Box>
     ```

10. **Shared Components Architecture**:
   - Sidenav component (`/src/shared/components/layout/Sidenav/`) includes:
     - Custom SSR-safe hooks (useSafeBreakpointValue, useReducedMotion, useSwipeToClose)
     - Error boundaries for graceful SSR error handling
     - Context-based state management
     - Accessibility features (focus trap, reduced motion support)
     - Animation transitions with performance optimizations
   - Utility Classes:
     - **CleanupManager** (`/src/shared/utils/client-utils.ts`): Manages multiple cleanup functions safely with error handling and Sentry reporting
   - Custom Hooks:
     - **useLocalStorage**: SSR-safe localStorage management with hydration mismatch prevention
     - **useAutoSave**: Debounced auto-save functionality with infinite loop protection (see INC-002)

## Icon Usage Guidelines

This project uses **lucide-react** as the primary icon library, following Chakra UI v3's recommendations:

1. **Icon Library**: Always use `lucide-react` for icons
   - DO NOT use `react-icons` or other icon libraries
   - lucide-react provides a consistent, modern icon set with better tree-shaking

2. **Import Pattern**:
   ```typescript
   import { Home, Settings, User } from "lucide-react";
   ```

3. **Usage with Chakra UI's Icon Component** (Recommended):
   ```typescript
   import { Icon } from "@chakra-ui/react";
   import { Home } from "lucide-react";

   // Using Chakra UI's Icon wrapper for consistent styling
   <Icon>
     <Home />
   </Icon>

   // With Chakra UI props
   <Icon color="blue.500" boxSize="6">
     <Home />
   </Icon>
   ```

4. **Direct Usage** (When needed):
   ```typescript
   import { Home } from "lucide-react";

   // Direct usage with size and color props
   <Home size={24} color="#3182ce" />
   ```

5. **Best Practices**:
   - Prefer Chakra UI's Icon component for consistency with the design system
   - Use semantic icon names that clearly indicate their purpose
   - Apply consistent sizing using Chakra UI's boxSize prop
   - For interactive icons (buttons, links), ensure proper accessibility with aria-labels

6. **Example Component**:
   ```typescript
   import { Icon, IconButton } from "@chakra-ui/react";
   import { Settings, Menu, X } from "lucide-react";

   // Icon button with lucide-react icon
   <IconButton aria-label="Settings">
     <Icon>
       <Settings />
     </Icon>
   </IconButton>

   // Conditional icon rendering
   <Icon boxSize="5">
     {isOpen ? <X /> : <Menu />}
   </Icon>
   ```

## Styling Migration Note

**IMPORTANT**: The `cn` utility function has been updated from Tailwind CSS to Chakra UI:

1. **Previous Implementation** (Tailwind CSS):
   ```typescript
   // Used clsx for conditional classes with tailwind-merge
   import { clsx, type ClassValue } from "clsx"
   import { twMerge } from "tailwind-merge"
   
   export function cn(...inputs: ClassValue[]) {
     return twMerge(clsx(inputs))
   }
   ```

2. **Current Implementation** (Chakra UI):
   ```typescript
   // Now uses Chakra UI's cx utility for merging style props
   import { cx } from "@chakra-ui/react"
   
   export function cn(...inputs: Parameters<typeof cx>) {
     return cx(...inputs)
   }
   ```

3. **Usage Changes**:
   - The `cn` function now merges Chakra UI style props and class names
   - It's primarily used for combining conditional styles
   - Example:
     ```typescript
     // Combining conditional styles
     <Box className={cn("base-class", isActive && "active-class")} />
     
     // However, prefer Chakra UI's style props:
     <Box bg={isActive ? "blue.500" : "gray.100"} />
     ```

4. **Best Practices**:
   - Prefer Chakra UI's style props over class names
   - Use `cn` only when necessary for complex conditional styling
   - Leverage Chakra's built-in responsive and conditional utilities

## Git Worktree Management

This project uses **Phantom** for Git worktree management with the following configuration (`phantom.config.json`):

```json
{
  "worktreesDirectory": "../kadou-delta-next.phantom",
  "postCreate": {
    "copyFiles": [
      ".mcp.json",
      "apps/main/.env.local",
      ".claude/settings.local.json",
      ".scratch/"
    ],
    "commands": ["pnpm install --frozen-lockfile", "git submodule update --init --recursive"]
  }
}
```

**Features**:
- Automatically copies MCP configuration and environment variables to new worktrees
- Runs pnpm install with frozen lockfile and initializes submodules after worktree creation
- Worktrees are created in a separate `.phantom` directory

## Current Configuration

- **MCP Servers**: DeepWiki, Linear, Convex, Sentry, LSMCP, O3 Search, and Beds24 API integrations configured in `.mcp.json`
  - **O3 Search**: AI-powered web search capabilities for finding latest information and troubleshooting errors
  - **Beds24 API**: Property management system API integration for hospitality features
- **Path Aliases**: 
  - `@/*` maps to project root
  - `@/features/*` maps to `/src/features`
  - `@/shared/*` maps to `/src/shared`
- **TypeScript**: Strict mode enabled
- **Environment Variables** (required in `.env.local`):
  - `CONVEX_DEPLOYMENT`: Convex deployment identifier (used by `npx convex dev`)
  - `NEXT_PUBLIC_CONVEX_URL`: Convex backend URL for client-side connection
  - `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`: Clerk public key for authentication
  - `CLERK_SECRET_KEY`: Clerk secret key for server-side authentication
  - `SENTRY_AUTH_TOKEN`: Sentry authentication token (for build-time integration and source maps)
  - `NEXT_PUBLIC_CLERK_FRONTEND_API_URL`: (Optional) Clerk frontend API URL - only needed for custom domains or proxy configurations
  - **Note**: Use `pnpm envcheck` to validate your `.env.local` against `.env.example` using dotenv-linter
- **Architecture**: Vertical Slice Architecture (VSA) with feature modules
- **Version Consistency**: 
  - Biome version mismatch between root (`2.0.6`) and apps/main (`^2.0.5`) - consider unifying
  - Always check for version consistency across workspaces when updating dependencies
- **Security Note**: 
  - **CRITICAL**: Ensure `.mcp.json` is in `.gitignore` as it contains sensitive API keys
  - The O3 Search MCP server configuration includes an OpenAI API key - never commit this file
  - **ACTION REQUIRED**: Migrate API keys from `.mcp.json` to environment variables

## Library Documentation Priority

**IMPORTANT**: When working with libraries and frameworks, follow this priority order:

1. **DeepWiki MCP** (`mcp__deepwiki`) - **Always use first**
   - **Always** query DeepWiki for **ALL** library and framework questions (React, Next.js, Convex, Chakra UI, Clerk, Vitest, React Testing Library, Sentry, Webpack, Vite, TypeScript, etc.)
   - Use natural language queries to search for specific API references, usage patterns, or examples
   - Provides the most accurate and up-to-date documentation

2. **Chakra UI v3 LLM Documentation** - For Chakra UI specific queries
   - Pre-generated documentation optimized for LLMs located in `.claude/llms-txts/chakra-ui-llms.txt`
   - Available documentation sets:
     - [Complete documentation](chakra-v3-docs-94r4j7b19-chakra-ui.vercel.app/llms-full.txt): All components, styling and theming
     - [Components](chakra-v3-docs-94r4j7b19-chakra-ui.vercel.app/llms-components.txt): All Chakra UI v3 components
     - [Charts](chakra-v3-docs-94r4j7b19-chakra-ui.vercel.app/llms-charts.txt): Chart components documentation
     - [Styling](chakra-v3-docs-94r4j7b19-chakra-ui.vercel.app/llms-styling.txt): Styling system documentation
     - [Theming](chakra-v3-docs-94r4j7b19-chakra-ui.vercel.app/llms-theming.txt): Theming documentation
     - [Migrating to v3](chakra-v3-docs-94r4j7b19-chakra-ui.vercel.app/llms-v3-migration.txt): Migration guide
   - Use WebFetch to retrieve specific documentation when needed

3. **O3 Search MCP** (`mcp__o3__o3-search`) - Use for complex troubleshooting
   - Advanced AI-powered search for finding solutions to errors
   - Useful for debugging complex issues or finding latest community solutions
   - Can provide insights when DeepWiki doesn't have specific answers

4. **Web Search** (`WebSearch`) - Use when MCP tools are unavailable
   - Search for official documentation sites, GitHub repos, or recent blog posts
   - Useful for finding the latest updates, bug fixes, or community solutions
   - Include library version in search queries when relevant

5. **Internal Knowledge** - Use only as last resort
   - Only when all other search methods are unavailable or return no results
   - For general programming concepts not specific to library versions
   - When the user explicitly asks to use internal knowledge

Example usage:
- ❌ **Never**: Use remembered syntax from training data
- ✅ **Always**: Query DeepWiki first, then O3 Search or WebSearch if needed
- ✅ Example queries:
  - DeepWiki: "How to use useQuery hook in Convex"
  - DeepWiki: "Chakra UI v3 theme customization"
  - DeepWiki: "Vitest setup and configuration for Next.js project"
  - Chakra UI LLM Docs: `WebFetch` the components documentation for comprehensive component reference
  - O3 Search: "Next.js 15 hydration error window is not defined Chakra UI"
  - O3 Search: "Convex websocket connection failed NODE_NO_HTTP2"
  - WebSearch: "Chakra UI v3 migration guide site:chakra-ui.com"

## LSMCP Code Intelligence Tools

I have lsmcp MCP server connected, which provides LSP-based code intelligence tools.

Available tools:

### Language Server Protocol (LSP) Tools
- **lsp_find_references** - Find all usages of a symbol
- **lsp_get_definitions** - Jump to definition
- **lsp_rename_symbol** - Rename across project
- **lsp_get_diagnostics** - Get errors/warnings

### TypeScript-Specific Tools (when using `--language typescript`)
- **lsmcp_rename_symbol** - TypeScript-aware rename
- **lsmcp_move_file** - Move files with import updates
- **lsmcp_move_directory** - Move directories with automatic import updates
- **lsmcp_search_symbols** - Fast symbol search using pre-built index
- **lsmcp_get_module_symbols** - Get all exported symbols from a module
- **lsmcp_get_type_in_module** - Get detailed type information for specific symbols
- **lsmcp_get_type_at_symbol** - Get type information at specific code locations
- **lsmcp_get_symbols_in_scope** - Get all symbols visible at a location
- **lsmcp_find_import_candidates** - Find potential imports for a symbol
- **ts_find_references** - Find all references to a TypeScript symbol
- **ts_get_definitions** - Get TypeScript symbol definitions
- **ts_get_diagnostics** - Get TypeScript diagnostics for a file
- **ts_rename_symbol** - Rename TypeScript symbols across codebase
- **ts_delete_symbol** - Delete symbols and their references

### Usage Guidelines

1. **Code Exploration**: Use these tools to explore the codebase structure and understand relationships between modules
2. **Refactoring Operations**: Leverage rename and move operations for safe, automated refactoring
3. **Type Information**: Get detailed type information to understand complex type relationships
4. **Error Checking**: Use diagnostics tools to identify and fix TypeScript errors
5. **Import Management**: Use import candidate finding to resolve missing imports

Please use these tools to explore the codebase and perform refactoring operations.

## AI Assistant Collaboration

**IMPORTANT**: This project has a unique AI collaboration feature that enables Claude Code to consult with Gemini for complex decisions.

**Gemini Integration**: Claude Code can collaborate with Gemini assistant for enhanced problem-solving and decision-making.

### Usage
```bash
gemini -p "<message to Gemini>"
```

### When to Consult Gemini

Consider collaborating with Gemini in these scenarios:

1. **Alternative Approaches**: When you need a second opinion on implementation strategies
2. **Complex Problem Solving**: For challenging technical issues that benefit from multiple perspectives
3. **Architecture Decisions**: When evaluating different architectural patterns or technology choices
4. **Code Review**: Getting additional insights on code quality, patterns, or potential improvements
5. **Performance Optimization**: Discussing optimization strategies and trade-offs
6. **Security Considerations**: Reviewing security implications of implementation choices

### Best Practices

- **Clear Communication**: Provide context and specific questions when consulting Gemini
- **Synthesize Insights**: Combine perspectives from both assistants for optimal solutions
- **Document Decisions**: Record important decisions made through collaboration in code comments or documentation
- **Maintain Focus**: Use collaboration for complex tasks; handle straightforward tasks independently

### Example Collaboration Scenarios

```bash
# Architecture discussion
gemini -p "プロジェクトでConvexの代わりにSupabaseを使用することについてどう思いますか？現在のアーキテクチャとの比較を教えてください。"

# Performance optimization
gemini -p "このReactコンポーネントのレンダリングパフォーマンスを改善する方法についてアドバイスをください。[コンポーネントの詳細を共有]"

# Security review
gemini -p "この認証フローにセキュリティ上の懸念点はありますか？[実装の詳細を共有]"
```

## Checklist Progress Management

**IMPORTANT**: When a checklist file is provided for implementation tasks:

1. **Supported Formats**:
   - Markdown checklist format: `- [ ] Task description` (unchecked) / `- [x] Task description` (checked)
   - Numbered lists with status: `1. [TODO] Task` / `1. [DONE] Task` / `1. [IN PROGRESS] Task`

2. **Progress Updates**:
   - **Always** update the checklist file when starting a task (mark as IN PROGRESS or similar)
   - **Always** update the checklist file immediately after completing each task
   - Use the Edit tool to update checkbox states (`[ ]` → `[x]`) or status markers
   - Add brief completion notes if helpful: `- [x] Task description (completed: brief note)`

3. **Update Timing**:
   - Before starting: Mark task as "IN PROGRESS" or similar
   - After completion: Mark task as completed with checkbox or status update
   - If blocked: Add note about blocker: `- [ ] Task ⚠️ (blocked: reason)`

4. **Example Checklist Updates**:
   ```markdown
   # Implementation Checklist
   
   - [x] Setup project structure
   - [x] Install dependencies
   - [→] Configure authentication (IN PROGRESS)
   - [ ] Implement user dashboard
   - [ ] Add test coverage
   ```

**Note**: This is separate from the TodoWrite/TodoRead tools. Use those for your own task management, and update external checklist files for user visibility.

## SSR/Client Component Guidelines

**IMPORTANT**: When working with Chakra UI components and hooks in Next.js App Router:

1. **useBreakpointValue SSR Issue**: Always use `useSafeBreakpointValue` custom hook instead of Chakra UI's `useBreakpointValue` to avoid "window is not defined" errors
   ```typescript
   // ❌ Avoid
   const isMobile = useBreakpointValue({ base: true, md: false })
   
   // ✅ Use instead
   import { useSafeBreakpointValue } from "@/shared/components/layout/Sidenav/hooks/useSafeBreakpointValue"
   const isMobile = useSafeBreakpointValue({ base: true, md: false }, false)
   ```
   
   **Why this happens**: 
   - React hooks must always be called at the top level and cannot be conditional
   - Chakra UI's `useBreakpointValue` accesses `window.matchMedia` during initialization
   - SSR checks (`mounted` state) can only happen after hook execution
   - The custom hook uses `useEffect` to ensure window access only happens on client

2. **localStorage SSR Safety**: Use the custom `useLocalStorage` hook for SSR-safe localStorage access
   ```typescript
   import { useLocalStorage } from "@/shared/hooks/useLocalStorage"
   
   const [value, setValue] = useLocalStorage("key", defaultValue)
   ```
   
   **Features**:
   - Returns consistent initial value during SSR to prevent hydration mismatches
   - Syncs with localStorage only after component mounts
   - Handles JSON serialization/deserialization automatically
   - Includes error handling for localStorage access

3. **Environment Checks**: Use environment utilities when accessing browser APIs
   ```typescript
   import { isClient, isServer } from "@/shared/utils/environment"
   
   if (isClient) {
     // Safe to use window, document, etc.
   }
   ```

4. **Error Boundaries**: Wrap critical UI components with error boundaries to gracefully handle SSR errors
   ```typescript
   import { SidenavErrorBoundary } from "./SidenavErrorBoundary"
   
   <SidenavErrorBoundary>
     <YourComponent />
   </SidenavErrorBoundary>
   ```

5. **Client Component Declaration**: Always add `"use client"` directive to components using Chakra UI hooks
   ```typescript
   "use client"
   
   import { useBreakpointValue } from "@chakra-ui/react"
   // ... component implementation
   ```
   
   **Note**: Even with `"use client"`, components may partially execute on the server during initialization.

6. **Dynamic Imports for Client-Only Components**: 
   ```typescript
   import dynamic from "next/dynamic"
   
   const ClientOnlyComponent = dynamic(
     () => import("./ClientOnlyComponent"),
     { ssr: false }
   )
   ```

7. **Conditional Rendering for SSR/Client differences**:
   ```typescript
   const [mounted, setMounted] = useState(false)
   
   useEffect(() => {
     setMounted(true)
   }, [])
   
   if (!mounted) {
     return <DefaultComponent /> // SSR時のデフォルト表示
   }
   
   return <ClientComponent /> // クライアントサイドの表示
   ```

See `/docs/ssr-chakra-ui-guidelines.md` for detailed SSR troubleshooting guide.

## Next.js Link and Chakra UI Integration

**IMPORTANT**: When using Next.js Link with Chakra UI v3:

1. **Never use legacyBehavior**: This prop is deprecated in Next.js 15
   ```typescript
   // ❌ Avoid - deprecated pattern
   <NextLink href="/path" passHref legacyBehavior>
     <Link>Link text</Link>
   </NextLink>
   
   // ✅ Use instead - Chakra UI v3 pattern
   <Link asChild>
     <NextLink href="/path">
       Link text
     </NextLink>
   </Link>
   ```

2. **External Links**: Use Chakra UI Link directly for external links
   ```typescript
   <Link href="https://example.com" isExternal>
     External Link
   </Link>
   ```

3. **Conditional Rendering**: Handle edge cases properly
   ```typescript
   if (!href) return <Button>{content}</Button>
   
   return (
     <Link asChild>
       <NextLink href={href}>
         {content}
       </NextLink>
     </Link>
   )
   ```

See `/docs/framework-integration-guidelines.md` for detailed integration patterns.

## Convex Guidelines

@.cursor/rules/convex_rules.mdc

### Convex実行環境のルール

**重要**: Convexでは実行環境が厳密に分離されています。

#### "use node"ディレクティブの使用ルール

1. **V8環境（`"use node"`なし）**：
   - 定義可能：`query`、`mutation`、`internalQuery`、`internalMutation`
   - 用途：データベース操作、純粋な計算処理
   - 例：`otaReviews.ts`、`beds24Properties.ts`、`bookingComSlugQueries.ts`
   
2. **Node.js環境（`"use node"`あり）**：
   - 定義可能：`action`、`internalAction`のみ
   - 用途：外部API呼び出し、Node.jsライブラリの使用
   - 例：`otaReviewsSync.ts`、`bookingComSlugSync.ts`

#### ファイル命名規則

- **V8環境ファイル**：
  - クエリ/ミューテーション：`{feature}.ts`または`{feature}Queries.ts`
  - 例：`otaReviews.ts`、`bookingComSlugQueries.ts`
  
- **Node.js環境ファイル**：
  - アクション：`{feature}Actions.ts`または`{feature}Sync.ts`
  - 例：`otaReviewsSync.ts`、`bookingComSlugSync.ts`

#### アーキテクチャパターン

外部API連携を含む機能は必ず以下のパターンに従う：
1. クエリ/ミューテーション用ファイル（V8環境）
2. アクション用ファイル（Node.js環境）
3. 型定義は共有ファイルまたは各ファイルに配置

**エラー例**：
```typescript
// ❌ 悪い例：bookingComSlugSync.ts
"use node";
import { internalQuery, internalAction } from "./_generated/server";

// エラー: "use node"があるファイルでqueryは定義できない
export const getSlugSyncTask = internalQuery({...});

// ✅ これは問題ない
export const scrapeBookingComSlug = internalAction({...});
```

**正しい実装**：
```typescript
// ✅ 良い例：bookingComSlugQueries.ts（V8環境）
// "use node"なし
import { internalQuery } from "./_generated/server";

export const getSlugSyncTask = internalQuery({...});

// ✅ 良い例：bookingComSlugSync.ts（Node.js環境）
"use node";
import { internalAction } from "./_generated/server";

export const scrapeBookingComSlug = internalAction({...});
```

### Current Schema

The database schema (`convex/schema.ts`) includes:
- **numbers**: Simple number storage for testing/demo purposes
- **userSettings**: User preferences with Clerk integration and Beds24 configuration
  - Indexed by `userId` for fast lookups
  - Includes theme preferences and optional Beds24 refresh tokens
- **beds24AccessTokens**: Beds24 API access token management
  - Indexed by `userId` for fast lookups
  - Stores access tokens with expiration tracking
  - Includes timestamps for creation, update, and last refresh

### Convex Utilities

The project includes several utility modules for Convex:
- **Error Handling** (`/convex/lib/errors.ts`): Structured error classes and handling
- **Logging System** (`/convex/lib/logging.ts`): Comprehensive logging with different log levels
- **Token Management** (`/convex/beds24/tokens.ts`): Internal functions for Beds24 token lifecycle

### Convex Development Tips

```bash
# If Convex dashboard doesn't open automatically
npx convex dashboard

# After updating Convex dependencies
npx convex dev --until-success  # Ensures schema migrations

# Clear Convex cache if experiencing issues
rm -rf node_modules/.cache/convex
```

## Sentry guidline

@.cursor/rules/sentry_rules.mdc

## Incident Reports

When critical issues occur in production or development, document them using the incident report structure:

**Location**: `/docs/incident-reports/`

**Format**: Use `/docs/incident-reports/TEMPLATE.md` as a base
- Filename: `INC-XXX-brief-description.md` (e.g., `INC-001-infinite-loop-toast.md`)
- Include: Date (from environment), Severity, Description, Root Cause, Resolution, Lessons Learned
- Document immediately after resolution to capture details while fresh

**Recent Incidents**:
- `INC-001-infinite-loop-toast.md`: Toast notification infinite loop issue
- `INC-002-autosave-infinite-loop-issue25.md`: useAutoSave hook infinite loop with Convex
- `INC-003-duplicate-nav-element.md`: DOM nav element duplication due to Next.js App Router layout inheritance
- `INC-004-clerk-userbutton-hydration-mismatch.md`: Clerk UserButton hydration mismatch in SSR

**Important Lessons from Incidents**:
- **Layout Inheritance**: Next.js App Router layouts are inherited by all child routes - avoid duplicating navigation elements
- **Hook Dependencies**: Be extremely careful with dependency arrays when using Convex mutations
- **Toast Notifications**: Always clear toasts on component unmount to prevent memory leaks

**Example**:
```bash
# Create new incident report
cp docs/incident-reports/TEMPLATE.md docs/incident-reports/INC-003-new-issue.md
# Edit with incident details
```

## Troubleshooting

### Common Issues

1. **Pre-commit hook failures**:
   ```bash
   # Run verification manually to see detailed errors
   pnpm verify:all
   
   # Fix individual issues:
   pnpm format    # Fix formatting
   pnpm lint      # Fix linting with auto-fix
   pnpm typecheck # Check TypeScript errors (manual fix needed)
   ```

2. **Convex connection issues**:
   - Check if `NODE_NO_HTTP2=1` is set (handled automatically in pnpm scripts)
   - Ensure `.env.local` has correct `CONVEX_DEPLOYMENT` value
   - Try clearing Convex cache: `rm -rf node_modules/.cache/convex`

3. **Test failures**:
   - Run specific failing test with verbose output: `pnpm test -- --reporter=verbose [test-file]`
   - Check if mocks are properly set up for Convex hooks
   - Ensure test environment matches expected state

4. **MCP server issues**:
   - Verify `.mcp.json` exists and has correct server configurations
   - For local MCP servers (like beds24-api), ensure the path exists
   - Check MCP server logs in Claude Code output panel

5. **pnpm Installation Issues**:
   - Ensure mise is installed: `curl https://mise.run | sh`
   - Run `mise install` to set up correct Node.js and pnpm versions
   - Check pnpm version: `pnpm --version` should show 10.13.1
