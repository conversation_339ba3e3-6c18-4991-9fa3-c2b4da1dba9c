# Protected Files List
# This file contains patterns for files that should not be modified without explicit permission.
# One pattern per line. Supports glob patterns.

# Specification documents (for future use)
/docs/spec/**

# AI agent guidelines
.cursor/rules/*.mdc

# Incident reports documentation
docs/incident-reports/*.md

# Environment configuration templates
.env.example
apps/main/.env.example
apps/fumadocs/.env.example

# Git hooks configuration
lefthook.yml

# GitHub Actions CI/CD configurations
.github/workflows/*.yml

# Package configuration
pnpm-workspace.yaml
mise.toml

# Testing configurations
vitest.setup.ts
vitest.config.ts
apps/*/vitest.config.ts