{
	// Base TypeScript configuration for the monorepo
	// Contains common settings shared across all projects
	"compilerOptions": {
		// Language and Environment
		"target": "ESNext", // Use latest ECMAScript features
		"lib": ["dom", "dom.iterable", "esnext"], // Include DOM and ESNext libraries

		// Module Resolution
		"module": "ESNext", // Use ESNext module system
		"moduleResolution": "Bundler", // Use bundler module resolution (for Next.js/Vite)
		"resolveJsonModule": true, // Allow importing JSON files
		"esModuleInterop": true, // Enable interoperability between CommonJS and ES Modules
		"allowSyntheticDefaultImports": true, // Allow default imports from modules with no default export

		// Type Checking
		"strict": true, // Enable all strict type checking options
		"skipLibCheck": true, // Skip type checking of declaration files
		"forceConsistentCasingInFileNames": true, // Ensure consistent casing in file names

		// JavaScript Support
		"allowJs": true, // Allow JavaScript files to be compiled

		// Emit
		"noEmit": true, // Don't emit output (handled by bundlers)
		"isolatedModules": true, // Ensure each file can be transpiled in isolation

		// Project References
		"composite": true, // Enable project references
		"incremental": true // Enable incremental compilation for faster builds
	}
}
