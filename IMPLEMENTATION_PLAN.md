# ReviewMetricsChart 実装計画書

レビュー分析ページに月別レビュー推移チャートを追加する実装計画です。

## 実装概要

- **目的**: 各施設のタブ内に、その施設の全期間のレビュー推移を月別で集計したチャートを表示
- **表示期間**: 全期間
- **集計単位**: 月別
- **使用コンポーネント**: 既存のReviewMetricsChartコンポーネントを活用

## 実装タスク

### Phase 1: バックエンド実装 (Convex)

#### 1.1 月別レビュー統計クエリの実装

- [x] **ファイル**: `convex/otaReviews.ts`
  - [x] `getMonthlyReviewStatsByProperties` クエリ関数の実装
    - [x] 引数: `propertyIds: v.array(v.id("beds24Properties"))`
    - [x] 戻り値: 施設IDと累積統計データのペアの配列
      ```typescript
      // 戻り値の型
      v.array(v.object({
        propertyId: v.id("beds24Properties"),
        stats: v.array(v.object({
          date: v.string(),        // "YYYY-MM"形式
          reviewCount: v.number(), // 累積レビュー件数
          averageScore: v.number() // 累積平均スコア（小数第1位まで）
        }))
      }))
      ```
    - [x] 認証チェックの実装
    - [x] 各施設のレビューを全件取得
    - [x] 月別（YYYY-MM形式）での集計ロジック
    - [x] 累積値の計算
      - [x] 累積レビュー件数の計算
      - [x] 累積平均スコアの計算（累積総スコア÷累積件数）
    - [x] 平均スコアの計算（小数第1位まで）
    - [x] 日付順でのソート
    - [x] エラーハンドリング

### Phase 2: データ取得層の実装

#### 2.1 カスタムフックの拡張

- [ ] **ファイル**: `src/features/review-analysis/hooks/usePropertiesWithReviews.ts`
  - [ ] 型定義の更新
    - [ ] `PropertyWithReviews` インターフェースに `monthlyStats` フィールドを追加
    - [ ] 月別累積統計データの型定義
      ```typescript
      monthlyStats?: {
        date: string;        // "YYYY-MM"形式 (注: フロントエンドでluxonに変換)
        reviewCount: number; // 累積レビュー件数
        averageScore: number; // 累積平均スコア
      }[]
      ```
  - [ ] `useQuery` での月別統計データ取得
    - [ ] `api.otaReviews.getMonthlyReviewStatsByProperties` の呼び出し
    - [ ] スキップ条件の設定（propertyIdsが空の場合）
  - [ ] データ整形ロジックの更新
    - [ ] 既存のレビューデータと月別統計データの結合
    - [ ] 各施設に対応する統計データのマッピング
    - [ ] **重要**: 日付文字列（"YYYY-MM"形式）をluxonのDateTimeインスタンスに変換
      ```typescript
      import { DateTime } from "luxon";
      
      // 例: 月別統計データの日付変換
      const formattedStats = monthlyStats.map(stat => ({
        ...stat,
        date: DateTime.fromFormat(stat.date, "yyyy-MM")
      }));
      ```
  - [ ] ローディング状態の更新
    - [ ] 月別統計データの取得状態を含める

### Phase 3: UI層の実装

#### 3.1 ReviewAnalysisTabsコンポーネントの更新

- [ ] **ファイル**: `src/features/review-analysis/components/ReviewAnalysisTabs.tsx`
  - [ ] インポートの追加
    - [ ] `ReviewMetricsChart` コンポーネントのインポート
    - [ ] `Stack` コンポーネントのインポート（必要に応じて）
  - [ ] タブコンテンツの更新
    - [ ] 既存のコンテンツを `Stack` でラップ
    - [ ] `ReviewMetricsChart` の条件付きレンダリング
    - [ ] チャートのプロパティ設定
      - [ ] `data`: 月別統計データ
      - [ ] `title`: 施設名を含むタイトル
      - [ ] `description`: 説明文
    - [ ] 既存の `RecentReviews` コンポーネントとの配置調整

### Phase 4: 動作確認

- [ ] **動作確認**
  - [ ] チャートの表示位置確認
  - [ ] レスポンシブデザインの確認
  - [ ] ローディング状態の確認
  - [ ] エラー状態の確認
  - [ ] データなし状態の確認

### Phase 5: 最適化と改善

#### 5.1 パフォーマンス最適化

- [ ] **クエリ最適化**
  - [ ] 必要に応じてインデックスの追加検討
  - [ ] データ取得量の最適化
  - [ ] キャッシュ戦略の検討

#### 5.2 エラーハンドリング

- [ ] **エラー処理の実装**
  - [ ] Convex側のエラーハンドリング
  - [ ] フロントエンド側のエラー表示
  - [ ] Sentryへのエラー報告

## 注意事項

- 既存のコードへの影響を最小限に抑える
- ReviewMetricsChartコンポーネントは既に実装済みのため、新規作成は不要
- Convexのクエリは認証を必須とする
- 月別集計では、レビュー日付（reviewDate）を使用する
- **重要**: レビュー件数と平均スコアは両方とも累積値として計算する
  - レビュー件数：その月までの全レビュー件数の累積
  - 平均スコア：その月までの全レビューの累積平均（累積総スコア÷累積件数）
- **重要**: 日付処理の違いに注意
  - Convex側：日付は文字列（"YYYY-MM"形式）として返す
  - フロントエンド側：受け取った日付文字列をluxonのDateTimeインスタンスに変換してから扱う
  - ReviewMetricsChartコンポーネントはluxonのDateTimeインスタンスを期待している

## 完了条件

- [ ] 各施設のタブ内に月別レビュー推移チャートが表示される
- [ ] チャートには全期間のデータが月別で表示される
- [ ] レビュー件数（累積）と平均スコア（累積）の両方が表示される
  - [ ] レビュー件数は右肩上がりの累積グラフとして表示
  - [ ] 平均スコアは累積平均の推移として表示
- [ ] データがない施設ではチャートが非表示になる
- [ ] エラーが発生した場合は適切にハンドリングされる