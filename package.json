{"name": "kadou-delta-next", "version": "0.1.0", "private": true, "workspaces": ["apps/*"], "packageManager": "pnpm@10.13.1", "scripts": {"dev:all": "concurrently \"pnpm:dev:main\" \"pnpm:dev:fumadocs\"", "dev:main": "pnpm --filter @kadou-delta-next/main dev", "dev:fumadocs": "pnpm --filter @kadou-delta-next/fumadocs dev -- -p 3838", "build:all": "pnpm run build:main && pnpm run build:fumadocs", "build:main": "pnpm --filter @kadou-delta-next/main build", "build:fumadocs": "pnpm --filter @kadou-delta-next/fumadocs build", "storybook": "pnpm --filter @kadou-delta-next/main storybook", "build-storybook": "pnpm --filter @kadou-delta-next/main build-storybook", "test:all": "pnpm --filter=\"*\" test:run", "lint:all": "pnpm --filter=\"*\" lint", "typecheck:all": "pnpm --filter=\"*\" typecheck", "verify:all": "concurrently \"pnpm:check:all\" \"pnpm:typecheck:all\" \"pnpm:test:all\"", "check:all": "biome check", "format": "biome format --write", "lint": "biome check --write", "prepare": "lefthook install", "knip": "knip"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@types/node": "^22", "concurrently": "^9.1.2", "knip": "^5.61.3", "lefthook": "^1.12.2", "typescript": "^5.8.3", "typescript-language-server": "^4.3.4"}}