# フレームワーク統合ガイドライン

このドキュメントでは、Next.js 15とChakra UI v3の統合における重要な注意事項と推奨パターンを記載します。

## Next.js Link コンポーネントとChakra UI Link の統合

### ❌ 非推奨パターン（Next.js 15以降）
```tsx
import NextLink from "next/link"
import { Link } from "@chakra-ui/react"

// legacyBehaviorは非推奨
<NextLink href="/path" passHref legacyBehavior>
  <Link>リンクテキスト</Link>
</NextLink>
```

### ✅ 推奨パターン（Chakra UI v3 + Next.js 15）
```tsx
import NextLink from "next/link"
import { Link } from "@chakra-ui/react"

// asChildプロパティを使用
<Link asChild>
  <NextLink href="/path">
    リンクテキスト
  </NextLink>
</Link>
```

## エッジケースの処理

### 1. hrefがundefinedの場合
```tsx
// hrefの存在をチェック
if (item.href) {
  return (
    <Link asChild>
      <NextLink href={item.href}>
        {content}
      </NextLink>
    </Link>
  )
}
return content
```

### 2. 外部リンクの処理
```tsx
if (item.isExternal) {
  return (
    <Link href={item.href} isExternal>
      {content}
    </Link>
  )
}
```

## フレームワークアップデート時の確認事項

1. **Breaking Changes の確認**
   - Next.js: https://nextjs.org/docs/pages/building-your-application/upgrading
   - Chakra UI: https://v3.chakra-ui.com/docs/migration

2. **統合パターンの更新**
   - 各フレームワークの推奨パターンが変更されていないか確認
   - DeepWikiまたは公式ドキュメントで最新の統合方法を調査

3. **テスト実行**
   - `npm run test`でユニットテストを実行
   - `npm run typecheck`で型チェックを実行
   - 手動でのリンク動作確認

## 参考リンク

- [Next.js 15 Link Component](https://nextjs.org/docs/app/api-reference/components/link)
- [Chakra UI v3 asChild prop](https://v3.chakra-ui.com/docs/components/concepts/as-child)
- [Next.js Breaking Changes](https://nextjs.org/docs/app/building-your-application/upgrading/version-15)