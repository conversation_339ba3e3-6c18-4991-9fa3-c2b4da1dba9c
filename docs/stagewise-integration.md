# Stagewise Integration Guide

## 概要

Stagewiseは、ブラウザ内で動作するツールバーで、エディタ（Cursor、VS Code等）のAIエージェントと連携します。ブラウザ上で要素をクリックしてコメントを残すと、DOMスナップショットとメタデータがエディタに送信され、AIがコード変更を提案します。

## 統合完了内容

### 1. 依存関係のインストール

以下のパッケージが開発依存関係として追加されました：
- `@stagewise/toolbar-next` (v0.5.0) - Next.js用のStagewiseラッパー
- `@stagewise-plugins/react` (v0.5.0) - Reactコンテキストの強化プラグイン

### 2. 設定ファイル

`apps/main/src/stagewise.config.ts`に設定ファイルを作成：
```typescript
import type { ToolbarConfig } from "@stagewise/toolbar";
import reactPlugin from "@stagewise-plugins/react";

export const stagewiseConfig: ToolbarConfig = {
	plugins: [reactPlugin()],
};
```

### 3. レイアウトへの統合

`apps/main/app/layout.tsx`にStagewiseToolbarコンポーネントを追加：
- 開発環境でのみ自動的に表示
- 本番環境では自動的に無効化
- bodyタグ直下に配置（他のプロバイダーより前）

## 使用方法

### 1. 開発サーバーを起動

```bash
npm run dev
```

### 2. Stagewise拡張機能をインストール

エディタ（Cursor、VS Code等）のマーケットプレイスからStagewise拡張機能をインストール

### 3. ツールバーの使用

- http://localhost:3232 を開く
- 画面右下にStagewiseアイコンが表示される
- 要素をクリックしてコメントやプロンプトを入力
- エディタでAIが提案するコード変更を確認

## トラブルシューティング

### ツールバーが表示されない場合

1. 開発モード（`npm run dev`）で実行していることを確認
2. エディタ（Cursor/VS Code）のウィンドウが1つだけ開いていることを確認
3. Stagewise拡張機能がインストールされ、有効になっていることを確認

### TypeScriptエラー

`ToolbarConfig`型が見つからない場合は、`@stagewise/toolbar`からインポート：
```typescript
import type { ToolbarConfig } from "@stagewise/toolbar";
```

## セキュリティ

- Stagewiseは開発環境でのみ動作
- 本番ビルドでは自動的に除外される
- ツリーシェイキングにより、本番バンドルには含まれない

## 今後の拡張

必要に応じて、以下の設定を追加可能：
- カスタムプラグインの追加
- 環境変数による有効/無効の制御
- Storybook、Cypress等での利用