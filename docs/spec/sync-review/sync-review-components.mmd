---
config:
  look: neo
  theme: base
  themeVariables:
    # 主要色設定
    primaryColor: "#6366f1"          # Indigo
    primaryTextColor: "#1f2937"      # Dark gray
    primaryBorderColor: "#c7d2fe"    # Light indigo
    lineColor: "#9ca3af"             # Medium gray
    secondaryColor: "#fef3c7"        # Light amber
    tertiaryColor: "#ddd6fe"         # Light purple
    background: "#ffffff"
    mainBkg: "#f9fafb"
    secondBkg: "#f3f4f6"
    tertiaryBkg: "#e5e7eb"
---

graph TB
    subgraph "Convexバックエンド層"
        Cron[fa:fa-clock Convex Cron<br/>定期実行ジョブ<br/>convex/crons.ts]
        
        subgraph "同期処理"
            beds24SyncActions["fa:fa-sync Beds24 Sync<br/>Internal Actions<br/>プロパティ・レビュー同期"]
            enqueueDailySync["fa:fa-calendar enqueueDailySync<br/>Internal Action<br/>施設同期タスク登録"]
            enqueueDailyReviewSync["fa:fa-calendar-plus enqueueDailyReviewSync<br/>Internal Action<br/>レビュー同期タスク登録"]
            processQueue["fa:fa-tasks processQueue<br/>Internal Mutation<br/>キュー処理"]
        end
        
        subgraph "レビュー取得処理"
            syncReviewsFromOTA["fa:fa-cog syncReviewsFromOTA<br/>Internal Action<br/>レビュー取得ワーカー"]
            getReviewSyncTask["fa:fa-search getReviewSyncTask<br/>Internal Query<br/>タスク詳細取得"]
            processReviewBatch["fa:fa-check-circle processReviewBatch<br/>Internal Mutation<br/>レビューバッチ処理"]
        end
        
        subgraph "Booking.comスラッグ取得"
            scrapeBookingComSlug["fa:fa-globe scrapeBookingComSlug<br/>Internal Action<br/>スラッグ取得ワーカー"]
            getSlugSyncTask["fa:fa-search getSlugSyncTask<br/>Internal Query<br/>スラッグタスク詳細取得"]
        end
        
        subgraph "公開API"
            otaReviewsQueries["fa:fa-database OTA Reviews<br/>Public Queries<br/>レビュー照会"]
            otaReviewsMutations["fa:fa-database OTA Reviews<br/>Public Mutations<br/>レビュー管理"]
        end
    end

    subgraph "データ層"
        syncQueue[(fa:fa-database syncQueue<br/>汎用同期タスクキュー)]
        otaReviews[(fa:fa-database otaReviews<br/>レビューデータ)]
        otaMaster[(fa:fa-database otaMaster<br/>OTAマスタデータ)]
        beds24Properties[(fa:fa-database beds24Properties<br/>施設データ)]
        userSettings[(fa:fa-database userSettings<br/>ユーザー設定)]
        beds24AccessTokens[(fa:fa-database beds24AccessTokens<br/>アクセストークン)]
        beds24SyncHistory[(fa:fa-database beds24SyncHistory<br/>同期履歴)]
        userProperties[(fa:fa-database userProperties<br/>ユーザー施設関連)]
    end

    subgraph "外部サービス"
        ScrapingAnt[fa:fa-spider ScrapingAnt<br/>スクレイピングAPI]
    end

    %% Cronジョブのスケジューリング
    Cron -->|"毎日2時（JST）<br/>施設同期"| enqueueDailySync
    Cron -->|"毎日3時（JST）<br/>レビュー同期"| enqueueDailyReviewSync
    Cron -->|"5分毎<br/>キュー処理"| processQueue
    
    %% 施設同期フロー
    enqueueDailySync -->|"タスク追加<br/>jobType='sync_properties'"| syncQueue
    
    %% レビュー同期フロー
    enqueueDailyReviewSync -->|"施設毎にタスク追加<br/>jobType='scrape_booking_com_slug'<br/>jobType='sync_reviews'"| syncQueue
    
    %% キュー処理
    processQueue <-->|"① 未処理取得<br/>② status更新<br/>③ 並列実行"| syncQueue
    processQueue -->|"scheduler.runAfter"| syncReviewsFromOTA
    processQueue -->|"scheduler.runAfter"| scrapeBookingComSlug
    
    %% スラッグ取得処理
    scrapeBookingComSlug -->|"タスク詳細<br/>照会"| getSlugSyncTask
    getSlugSyncTask -->|"読み取り"| syncQueue
    scrapeBookingComSlug -->|"スクレイピング<br/>依頼"| ScrapingAnt
    scrapeBookingComSlug -->|"スラッグ更新"| beds24Properties
    scrapeBookingComSlug -->|"履歴記録"| beds24SyncHistory
    scrapeBookingComSlug -->|"レビュータスク<br/>自動登録"| syncQueue
    
    %% レビュー取得処理
    syncReviewsFromOTA -->|"タスク詳細<br/>照会"| getReviewSyncTask
    getReviewSyncTask -->|"読み取り"| syncQueue
    syncReviewsFromOTA -->|"スクレイピング<br/>依頼"| ScrapingAnt
    syncReviewsFromOTA -->|"バッチ処理<br/>委譲"| processReviewBatch
    
    %% レビューバッチ処理
    processReviewBatch -->|"レビュー<br/>upsert"| otaReviews
    processReviewBatch -->|"status更新<br/>条件付き次ページ追加"| syncQueue
    
    %% 公開APIの利用
    otaReviewsQueries -->|"読み取り"| otaReviews
    otaReviewsQueries -->|"読み取り"| beds24Properties
    otaReviewsQueries -->|"読み取り"| userProperties
    otaReviewsMutations -->|"書き込み"| otaReviews

    %% スタイリング
    classDef cronStyle fill:#fef3c7,stroke:#f59e0b,stroke-width:2px,color:#78350f
    classDef mutationStyle fill:#ddd6fe,stroke:#6366f1,stroke-width:2px,color:#1e1b4b
    classDef actionStyle fill:#fee2e2,stroke:#dc2626,stroke-width:2px,color:#7f1d1d
    classDef queryStyle fill:#dbeafe,stroke:#3b82f6,stroke-width:2px,color:#1e3a8a
    classDef dbStyle fill:#e0e7ff,stroke:#4338ca,stroke-width:2px,color:#312e81
    classDef externalStyle fill:#fecaca,stroke:#ef4444,stroke-width:2px,color:#991b1b

    class Cron cronStyle
    class beds24SyncActions,enqueueDailySync,enqueueDailyReviewSync,syncReviewsFromOTA,scrapeBookingComSlug actionStyle
    class processQueue,processReviewBatch,otaReviewsMutations mutationStyle
    class getReviewSyncTask,getSlugSyncTask,otaReviewsQueries queryStyle
    class syncQueue,otaReviews,otaMaster,beds24Properties,userSettings,beds24AccessTokens,beds24SyncHistory,userProperties dbStyle
    class ScrapingAnt externalStyle