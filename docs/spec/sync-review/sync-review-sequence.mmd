---
config:
  look: neo
  theme: base
  themeVariables:
    # 主要色設定
    primaryColor: "#6366f1"          # Indigo
    primaryTextColor: "#1f2937"      # Dark gray
    primaryBorderColor: "#c7d2fe"    # Light indigo
    lineColor: "#9ca3af"             # Medium gray
    secondaryColor: "#fef3c7"        # Light amber
    tertiaryColor: "#ddd6fe"         # Light purple
    background: "#ffffff"
    mainBkg: "#f9fafb"
    secondBkg: "#f3f4f6"
    tertiaryBkg: "#e5e7eb"
    
    # アクター設定
    actorBorder: "#6366f1"
    actorBkg: "#ede9fe"
    actorTextColor: "#1e1b4b"
    actorLineColor: "#9ca3af"
    
    # ノート設定
    noteBkgColor: "#fef3c7"
    noteTextColor: "#78350f"
    noteBorderColor: "#f59e0b"
    
    # ラベル設定
    labelBoxBkgColor: "#e0e7ff"
    labelTextColor: "#312e81"
    labelBoxBorderColor: "#a5b4fc"
    
    # ループ・代替設定
    loopTextColor: "#374151"
    altSectionBkgColor: "#f3f4f6"
    
    # アクティベーション設定
    activationBorderColor: "#6366f1"
    activationBkgColor: "#e0e7ff"
    
    # シグナル設定
    signalColor: "#1f2937"
    signalTextColor: "#1f2937"
---

sequenceDiagram
    participant Cron
    participant enqueueDailyReviewSync as enqueueDailyReviewSync<br/>(Internal Action)
    participant syncQueue as syncQueue<br/>(DB Table)
    participant beds24Properties as beds24Properties<br/>(DB Table)
    participant processQueue as processQueue<br/>(Internal Mutation)
    participant scrapeBookingComSlug as scrapeBookingComSlug<br/>(Internal Action)
    participant getSlugSyncTask as getSlugSyncTask<br/>(Internal Query)
    participant syncReviewsFromOTA as syncReviewsFromOTA<br/>(Internal Action)
    participant getReviewSyncTask as getReviewSyncTask<br/>(Internal Query)
    participant processReviewBatch as processReviewBatch<br/>(Internal Mutation)
    participant ScrapingAnt
    participant beds24SyncHistory as beds24SyncHistory<br/>(DB Table)

    Note over Cron: 毎日3時（JST）に実行 (convex/crons.ts)
    Cron->>enqueueDailyReviewSync: 定期実行
    activate enqueueDailyReviewSync
    Note right of enqueueDailyReviewSync: 全施設分のスラッグ取得タスクと<br/>レビュー取得タスクを登録
    
    loop 各施設に対して
        enqueueDailyReviewSync->>beds24Properties: 施設情報を取得
        beds24Properties-->>enqueueDailyReviewSync: 施設データ
        
        alt bookingComFacilitySlugが未設定の場合
            enqueueDailyReviewSync->>syncQueue: スラッグ取得タスクを追加<br/>(jobType='scrape_booking_com_slug')
        else スラッグが設定済みの場合
            enqueueDailyReviewSync->>syncQueue: レビュー取得タスクを追加<br/>(jobType='sync_reviews')
        end
    end
    deactivate enqueueDailyReviewSync

    Note over Cron: 5分間隔で実行 (convex/crons.ts)
    Cron->>processQueue: 定期実行
    activate processQueue
    Note right of processQueue: 全jobTypeの<br/>未処理タスクを取得して処理
    processQueue->>syncQueue: 1. status='pending'のタスクを取得（最大20件）
    syncQueue-->>processQueue: 未処理タスクリスト
    processQueue->>syncQueue: 2. 取得したタスクのstatusを'processing'に更新

    par スラッグ取得タスクの処理
        processQueue->>scrapeBookingComSlug: ctx.scheduler.runAfter(0, internal.bookingComSlugSync.scrapeBookingComSlug, {taskId})
        activate scrapeBookingComSlug
        
        scrapeBookingComSlug->>getSlugSyncTask: ctx.runQuery(internal.bookingComSlugQueries.getSlugSyncTask, {taskId})
        activate getSlugSyncTask
        getSlugSyncTask->>syncQueue: タスク詳細を照会
        syncQueue-->>getSlugSyncTask: タスク詳細（propertyName等）
        deactivate getSlugSyncTask
        getSlugSyncTask-->>scrapeBookingComSlug: タスク詳細
        
        scrapeBookingComSlug->>beds24SyncHistory: 同期履歴を記録開始
        
        scrapeBookingComSlug->>ScrapingAnt: Booking.comで施設を検索
        activate ScrapingAnt
        ScrapingAnt-->>scrapeBookingComSlug: 検索結果HTML
        deactivate ScrapingAnt
        
        scrapeBookingComSlug->>beds24Properties: スラッグを更新
        scrapeBookingComSlug->>syncQueue: タスクを完了に更新
        
        Note over scrapeBookingComSlug: レビュー同期タスクを自動登録
        scrapeBookingComSlug->>syncQueue: レビュー取得タスクを追加<br/>(jobType='sync_reviews')
        
        scrapeBookingComSlug->>beds24SyncHistory: 同期履歴を成功として記録
        deactivate scrapeBookingComSlug
        
    and レビュー取得タスクの処理
        processQueue->>syncReviewsFromOTA: ctx.scheduler.runAfter(0, internal.otaReviewsSync.syncReviewsFromOTA, {taskId})
        activate syncReviewsFromOTA
        
        syncReviewsFromOTA->>getReviewSyncTask: ctx.runQuery(internal.otaReviews.getReviewSyncTask, {taskId})
        activate getReviewSyncTask
        getReviewSyncTask->>syncQueue: タスク詳細を照会
        syncQueue-->>getReviewSyncTask: タスク詳細（URL、OTAタイプ等）
        deactivate getReviewSyncTask
        getReviewSyncTask-->>syncReviewsFromOTA: タスク詳細
        
        alt 処理が成功した場合
            syncReviewsFromOTA->>ScrapingAnt: レビューページのスクレイピングを依頼
            activate ScrapingAnt
            ScrapingAnt-->>syncReviewsFromOTA: HTMLコンテンツ
            deactivate ScrapingAnt
            
            Note over syncReviewsFromOTA: ReviewParserで<br/>HTMLをパースして<br/>レビュー情報を抽出
            
            syncReviewsFromOTA->>processReviewBatch: ctx.runMutation(internal.otaReviews.processReviewBatch, {taskId, reviews, hasNextPage})
            activate processReviewBatch
            Note over processReviewBatch: トランザクション内で以下を実行:<br/>- レビューデータをotaReviewsに保存<br/>- タスク状態を 'completed' に更新<br/>- 新規レビューありの場合、次ページタスクを登録
            processReviewBatch->>syncQueue: タスクstatusを 'completed' に更新
            opt 次ページが存在し、新規レビューがある場合
                processReviewBatch->>syncQueue: 次ページのタスクを追加 (status='pending')
            end
            deactivate processReviewBatch
            
        else 処理中にエラーが発生した場合
            syncReviewsFromOTA->>syncQueue: タスクstatusを 'failed' に更新
        end
        deactivate syncReviewsFromOTA
    end
    deactivate processQueue
