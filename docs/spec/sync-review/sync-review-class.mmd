---
config:
  look: neo
  theme: base
  themeVariables:
    # 主要色設定
    primaryColor: "#6366f1"          # Indigo
    primaryTextColor: "#1f2937"      # Dark gray
    primaryBorderColor: "#c7d2fe"    # Light indigo
    lineColor: "#9ca3af"             # Medium gray
    secondaryColor: "#fef3c7"        # Light amber
    tertiaryColor: "#ddd6fe"         # Light purple
    background: "#ffffff"
    mainBkg: "#f9fafb"
    secondBkg: "#f3f4f6"
    tertiaryBkg: "#e5e7eb"
---

classDiagram
    %% エンティティ
    class SyncQueue {
        <<Entity>>
        +_id: Id~"syncQueue"~
        +_creationTime: number
        +userId: string
        +jobType: "sync_properties" | "sync_reviews" | "scrape_booking_com_slug" | "booking_initial_sync" | "booking_monthly_sync" | "booking_daily_sync"
        +priority: number
        +status: "pending" | "processing" | "completed" | "failed"
        +attempts: number
        +maxAttempts: number
        +scheduledFor: number
        +startedAt?: number
        +completedAt?: number
        +lastError?: string
        +nextRetryAt?: number
        +metadata?: any
        +result?: any
        +createdAt: number
        +updatedAt: number
    }

    class OtaReview {
        <<Entity>>
        +_id: Id~"otaReviews"~
        +_creationTime: number
        +beds24PropertyId: Id~"beds24Properties"~
        +otaId: Id~"otaMaster"~
        +uniqueHash: string
        +score: number
        +title?: string
        +reviewContent: string
        +reviewContentStructured?: {positive?: string, negative?: string}
        +reviewerName: string
        +reviewerCountry?: string
        +reviewDate: number
        +createdAt: number
        +updatedAt: number
    }

    class OtaMaster {
        <<Entity>>
        +_id: Id~"otaMaster"~
        +_creationTime: number
        +fullName: string
        +shortName: string
        +createdAt: number
        +updatedAt: number
    }

    class Beds24Property {
        <<Entity>>
        +_id: Id~"beds24Properties"~
        +_creationTime: number
        +beds24PropertyId: string
        +beds24PropertyKey: string
        +name: string
        +propertyType: string
        +currency: string
        +country?: string
        +city?: string
        +data: any
        +lastSyncedAt: number
        +createdAt: number
        +updatedAt: number
        +isDeleted: boolean
        +deletedAt?: number
        +bookingComFacilitySlug?: string
        +bookingComLastScrapedAt?: number
    }

    class UserSettings {
        <<Entity>>
        +_id: Id~"userSettings"~
        +_creationTime: number
        +userId: string
        +theme: "light" | "dark" | "system"
        +beds24?: {refreshToken?: string}
        +has_beds24_token: boolean
        +createdAt: number
        +updatedAt: number
    }

    class Beds24AccessTokens {
        <<Entity>>
        +_id: Id~"beds24AccessTokens"~
        +_creationTime: number
        +userId: string
        +accessToken: string
        +expiresAt: number
        +lastRefreshedAt: number
        +createdAt: number
        +updatedAt: number
    }

    class Beds24SyncHistory {
        <<Entity>>
        +_id: Id~"beds24SyncHistory"~
        +_creationTime: number
        +userId: string
        +jobType: string
        +status: "processing" | "success" | "partial_success" | "failed"
        +startedAt: number
        +completedAt?: number
        +duration: number
        +totalItems: number
        +successCount: number
        +failedCount: number
        +errors: array
        +metadata?: any
        +createdAt: number
        +updatedAt: number
    }

    class UserProperties {
        <<Entity>>
        +_id: Id~"userProperties"~
        +_creationTime: number
        +userId: string
        +propertyId: Id~"beds24Properties"~
        +createdAt: number
    }

    %% Convex Functions
    class CronJobs {
        <<crons.ts>>
        +cron("Daily sync", "0 17 * * *", internal.beds24Sync.enqueueDailySync)
        +cron("Daily review sync", "0 18 * * *", internal.beds24Sync.enqueueDailyReviewSync)
        +interval("Process queue", {minutes: 5}, internal.beds24Sync.processQueue)
        +cron("Cleanup", "0 19 * * *", internal.crons.cleanupOldJobs)
    }

    class Beds24SyncActions {
        <<beds24Sync.ts - Internal Action>>
        +enqueueDailySync(args: {}, returns: v.object({enqueuedCount, errors})): Promise~EnqueueResult~
        +enqueueDailyReviewSync(args: {}, returns: v.object({enqueuedReviewCount, enqueuedSlugCount, errors})): Promise~ReviewEnqueueResult~
    }

    class Beds24SyncMutations {
        <<beds24Sync.ts - Internal>>
        +processQueue(args: {}, returns: v.null()): Promise~null~
        +getUsersWithBeds24Token(args: {}, returns: v.array(...)): Promise~UsersWithBeds24[]~
    }

    class OtaReviewsQueries {
        <<otaReviews.ts - Public>>
        +getReviewsByProperty(args: {beds24PropertyId, paginationOpts}, returns: paginatedReviewsValidator): Promise~PaginatedReviews~
        +getReviewsByUser(args: listOptionsValidator, returns: v.array(reviewWithDetailsValidator)): Promise~ReviewWithDetails[]~
        +getReviewStats(args: reviewFilterOptionsValidator, returns: reviewStatsValidator): Promise~ReviewStats~
        +getRecentReviews(args: {...}, returns: v.array(reviewSummaryValidator)): Promise~ReviewSummary[]~
    }

    class OtaReviewsMutations {
        <<otaReviews.ts - Public/Internal>>
        +upsertReview(args: {...}, returns: v.object({reviewId, isNew})): Promise~ReviewResult~
        +batchUpsertReviews(args: {reviews}, returns: v.object({...})): Promise~BatchResult~
        +deleteReviewsByProperty(args: {beds24PropertyId}, returns: v.object({deletedCount})): Promise~DeleteResult~
        +_upsertReview(args: {userId, ...}, returns: v.object({reviewId, isNew})): Promise~ReviewResult~
    }

    %% Model層
    class OtaReviewsModel {
        <<model/otaReviews.ts>>
        +checkPropertyAccess(ctx, userId, propertyId): AccessCheckResult
        +getUserAccessiblePropertyIds(ctx, userId): Set~Id~
        +getReviewsByPropertyWithAuth(ctx, userId, propertyId, pagination): PaginatedReviews
        +enrichReviewsWithDetails(ctx, reviews): ReviewWithDetails[]
        +calculateReviewStatsOptimized(ctx, userId, filters): ReviewStats
        +generateReviewHash(data): string
        +validateReviewData(data): ValidationResult
    }

    %% 実装済みのアクションとヘルパー
    class OtaReviewsSyncAction {
        <<otaReviewsSync.ts - Internal Action>>
        +syncReviewsFromOTA(args: {taskId}, returns: v.null()): Promise~null~
    }

    class OtaReviewsInternalQueries {
        <<otaReviews.ts - Internal>>
        +getReviewSyncTask(args: {taskId}, returns: v.union(...)): Promise~TaskData|null~
    }

    class OtaReviewsInternalMutations {
        <<otaReviews.ts - Internal>>
        +processReviewBatch(args: {taskId, reviews, hasNextPage}, returns: v.object(...)): Promise~BatchResult~
    }

    class BookingComSlugSyncAction {
        <<bookingComSlugSync.ts - Internal Action>>
        +scrapeBookingComSlug(args: {taskId}, returns: v.null()): Promise~null~
    }

    class BookingComSlugQueries {
        <<bookingComSlugQueries.ts - Internal>>
        +getSlugSyncTask(args: {taskId}, returns: v.union(...)): Promise~TaskData|null~
    }

    class ScrapingAntHelper {
        <<lib/scrapingAnt.ts>>
        +createScrapingAntClient(logger): ScrapingAntWrapper
        +ScrapingAntWrapper.scrapeBookingReviews(url, options): Promise~ScrapeResult~
    }

    class ReviewParser {
        <<lib/reviewParser.ts>>
        +createReviewParser(logger): ReviewParserInterface
        +parseBookingReviews(html): ParsedReviewsResult
    }

    class ReviewUrlBuilder {
        <<lib/reviewUrlBuilder.ts>>
        +createBookingReviewUrl(property, pageNumber?): string
        +updateBookingReviewPage(url, pageNumber): string
    }

    %% 関係の定義
    CronJobs ..> Beds24SyncActions : "schedules"
    CronJobs ..> Beds24SyncMutations : "schedules"
    
    Beds24SyncActions ..> SyncQueue : "enqueues tasks"
    Beds24SyncMutations ..> SyncQueue : "processes"
    
    OtaReviewsQueries ..> OtaReviewsModel : "uses"
    OtaReviewsMutations ..> OtaReviewsModel : "uses"
    
    OtaReviewsModel ..> OtaReview : "reads/writes"
    OtaReviewsModel ..> Beds24Property : "reads"
    OtaReviewsModel ..> OtaMaster : "reads"
    OtaReviewsModel ..> UserProperties : "reads"
    
    OtaReviewsSyncAction ..> OtaReviewsInternalQueries : "ctx.runQuery"
    OtaReviewsSyncAction ..> OtaReviewsInternalMutations : "ctx.runMutation"
    OtaReviewsSyncAction ..> ScrapingAntHelper : "uses"
    OtaReviewsSyncAction ..> ReviewParser : "uses"
    OtaReviewsSyncAction ..> ReviewUrlBuilder : "uses"
    
    BookingComSlugSyncAction ..> BookingComSlugQueries : "ctx.runQuery"
    BookingComSlugSyncAction ..> ScrapingAntHelper : "uses"
    BookingComSlugSyncAction ..> Beds24SyncHistory : "records"
    
    OtaReviewsInternalMutations ..> OtaReview : "writes"
    OtaReviewsInternalMutations ..> SyncQueue : "updates"
    
    OtaReview --> Beds24Property : "belongs to"
    OtaReview --> OtaMaster : "from"
    UserProperties --> Beds24Property : "references"
    UserSettings ..> Beds24AccessTokens : "related"

    %% スタイリング
    %% エンティティ（テーブル）
    style SyncQueue fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    style OtaReview fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    style OtaMaster fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    style Beds24Property fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    style UserSettings fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    style Beds24AccessTokens fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    style Beds24SyncHistory fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    style UserProperties fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    
    %% Cron Jobs
    style CronJobs fill:#fef3c7,stroke:#f59e0b,stroke-width:2px
    
    %% Internal Actions
    style Beds24SyncActions fill:#fee2e2,stroke:#dc2626,stroke-width:2px
    style OtaReviewsSyncAction fill:#fee2e2,stroke:#dc2626,stroke-width:2px
    style BookingComSlugSyncAction fill:#fee2e2,stroke:#dc2626,stroke-width:2px
    
    %% Mutations
    style Beds24SyncMutations fill:#ddd6fe,stroke:#6366f1,stroke-width:2px
    style OtaReviewsMutations fill:#ddd6fe,stroke:#6366f1,stroke-width:2px
    style OtaReviewsInternalMutations fill:#ddd6fe,stroke:#6366f1,stroke-width:2px
    
    %% Queries
    style OtaReviewsQueries fill:#dbeafe,stroke:#3b82f6,stroke-width:2px
    style OtaReviewsInternalQueries fill:#dbeafe,stroke:#3b82f6,stroke-width:2px
    style BookingComSlugQueries fill:#dbeafe,stroke:#3b82f6,stroke-width:2px
    
    %% Model層
    style OtaReviewsModel fill:#e5e7eb,stroke:#6b7280,stroke-width:2px
    
    %% ヘルパー・ライブラリ
    style ScrapingAntHelper fill:#fecaca,stroke:#ef4444,stroke-width:2px
    style ReviewParser fill:#fecaca,stroke:#ef4444,stroke-width:2px
    style ReviewUrlBuilder fill:#fecaca,stroke:#ef4444,stroke-width:2px