---
config:
  look: neo
  theme: base
  themeVariables:
    # 主要色設定
    primaryColor: "#6366f1"          # Indigo
    primaryTextColor: "#1f2937"      # Dark gray
    primaryBorderColor: "#c7d2fe"    # Light indigo
    lineColor: "#9ca3af"             # Medium gray
    secondaryColor: "#fef3c7"        # Light amber
    tertiaryColor: "#ddd6fe"         # Light purple
    background: "#ffffff"
    mainBkg: "#f9fafb"
    secondBkg: "#f3f4f6"
    tertiaryBkg: "#e5e7eb"
    
    # アクター設定
    actorBorder: "#6366f1"
    actorBkg: "#ede9fe"
    actorTextColor: "#1e1b4b"
    actorLineColor: "#9ca3af"
    
    # ノート設定
    noteBkgColor: "#fef3c7"
    noteTextColor: "#78350f"
    noteBorderColor: "#f59e0b"
    
    # ラベル設定
    labelBoxBkgColor: "#e0e7ff"
    labelTextColor: "#312e81"
    labelBoxBorderColor: "#a5b4fc"
    
    # ループ・代替設定
    loopTextColor: "#374151"
    altSectionBkgColor: "#f3f4f6"
    
    # アクティベーション設定
    activationBorderColor: "#6366f1"
    activationBkgColor: "#e0e7ff"
    
    # シグナル設定
    signalColor: "#1f2937"
    signalTextColor: "#1f2937"
---

sequenceDiagram
    participant Cron
    participant syncQueue as syncQueue<br/>(DB Table)
    participant userProperties as userProperties<br/>(DB Table)
    participant beds24Properties as beds24Properties<br/>(DB Table)
    participant beds24AccessTokens as beds24AccessTokens<br/>(DB Table)
    participant processQueue as processQueue<br/>(Internal Mutation)
    participant syncBookingsFromBeds24 as syncBookingsFromBeds24<br/>(Internal Action)
    participant getBookingSyncTask as getBookingSyncTask<br/>(Internal Query)
    participant refreshBeds24Token as refreshBeds24Token<br/>(Internal Action)
    participant Beds24API as Beds24 API
    participant processBookingBatch as processBookingBatch<br/>(Internal Mutation)
    participant beds24Bookings as beds24Bookings<br/>(DB Table)
    participant beds24SyncHistory as beds24SyncHistory<br/>(DB Table)

    Note over Cron: 5分間隔で実行 (convex/crons.ts)
    Note over syncQueue: sync_bookingsタスクは手動で追加<br/>または別のプロセスから登録<br/>metadata: {<br/>  propertyId,<br/>  propertyName,<br/>  userId,<br/>  pageNumber: 1,<br/>  dateFrom,<br/>  dateTo,<br/>  isRecursiveBacktrack?,<br/>  recursionDepth?,<br/>  originalStartDate?,<br/>  hasMoreInDateRange?<br/>}
    Cron->>processQueue: 定期実行
    activate processQueue
    Note right of processQueue: 全jobTypeの<br/>未処理タスクを取得して処理
    processQueue->>syncQueue: 1. status='pending'のタスクを取得（最大20件）
    syncQueue-->>processQueue: 未処理タスクリスト
    processQueue->>syncQueue: 2. 取得したタスクのstatusを'processing'に更新

    %% 予約同期タスクの処理
    processQueue->>syncBookingsFromBeds24: ctx.scheduler.runAfter(0, internal.beds24BookingSync.syncBookingsFromBeds24, {taskId})
    activate syncBookingsFromBeds24
    
    syncBookingsFromBeds24->>getBookingSyncTask: ctx.runQuery(internal.beds24BookingQueries.getBookingSyncTask, {taskId})
    activate getBookingSyncTask
    getBookingSyncTask->>syncQueue: タスク詳細を照会
    syncQueue-->>getBookingSyncTask: タスク詳細（userId, propertyId, dateRange等）
    deactivate getBookingSyncTask
    getBookingSyncTask-->>syncBookingsFromBeds24: タスク詳細
    
    %% トークンの取得と検証
    syncBookingsFromBeds24->>beds24AccessTokens: ユーザーのアクセストークンを取得
    beds24AccessTokens-->>syncBookingsFromBeds24: トークン情報
    
    alt トークンの有効期限が切れている場合
        syncBookingsFromBeds24->>refreshBeds24Token: トークンをリフレッシュ
        activate refreshBeds24Token
        refreshBeds24Token->>Beds24API: リフレッシュトークンでアクセストークンを更新
        Beds24API-->>refreshBeds24Token: 新しいアクセストークン
        refreshBeds24Token->>beds24AccessTokens: 新しいトークンを保存
        deactivate refreshBeds24Token
    end
    
    %% 予約データの取得
    syncBookingsFromBeds24->>beds24SyncHistory: 同期履歴を記録開始
    
    loop ページネーション処理
        syncBookingsFromBeds24->>Beds24API: GET /v2/bookings<br/>params: {<br/>  propId: propertyId,<br/>  modifiedSince: dateFrom,<br/>  includeInvoice: true,<br/>  includeInfoItems: true,<br/>  page: pageNumber<br/>}
        activate Beds24API
        Beds24API-->>syncBookingsFromBeds24: 予約データ（最大100件）
        deactivate Beds24API
        
        syncBookingsFromBeds24->>processBookingBatch: ctx.runMutation(internal.beds24Bookings.processBookingBatch, {taskId, bookings, hasMore})
        activate processBookingBatch
        Note over processBookingBatch: トランザクション内で以下を実行:<br/>- 既存予約の確認（bookingId）<br/>- 新規・更新予約をupsert<br/>- キャンセル済み予約の状態更新<br/>- 予約金額・ゲスト情報の更新<br/>- APIレスポンス全体をdataフィールドに保存
        
        loop 各予約に対して
            processBookingBatch->>beds24Bookings: 予約データをupsert<br/>（bookingIdで重複チェック）<br/>構造化フィールド + data(API全体)
        end
        
        opt 次ページが存在し、新規予約がある場合
            processBookingBatch->>syncQueue: 次ページのタスクを追加<br/>(status='pending', pageNumber=次ページ番号)
            Note over syncQueue: 新規予約がなくなったら<br/>ページネーションを停止
        end
        deactivate processBookingBatch
    end
    
    opt 全ページ処理完了 & 新規予約が0件の場合
        participant DateUtils as DateUtils<br/>(lib/dateUtils.ts)
        
        syncBookingsFromBeds24->>DateUtils: calculatePreviousDateRange(currentFrom, currentTo)
        activate DateUtils
        DateUtils-->>syncBookingsFromBeds24: 新しい日付範囲<br/>(2ヶ月前の期間)
        deactivate DateUtils
        
        syncBookingsFromBeds24->>syncQueue: 遡りタスクを追加<br/>(status='pending',<br/>dateFrom=2ヶ月前,<br/>dateTo=現在の開始日,<br/>isRecursiveBacktrack=true,<br/>recursionDepth=現在+1)
        
        Note over syncQueue: 再帰的遡り処理:<br/>- 最大遡り深度のチェック<br/>- 既存データのみの場合は停止<br/>- エラー連続時は停止
    end
    
    syncBookingsFromBeds24->>syncQueue: タスクstatusを 'completed' に更新
    syncBookingsFromBeds24->>beds24SyncHistory: 同期履歴を成功として記録<br/>（同期件数、新規/更新/キャンセル数を含む）
    
    deactivate syncBookingsFromBeds24
    deactivate processQueue

    Note over beds24Bookings: 保存される予約データ:<br/>- bookingId（一意）<br/>- propertyId<br/>- userId（関連ユーザー）<br/>- guestName<br/>- checkIn/checkOut<br/>- status（confirmed/cancelled等）<br/>- totalPrice<br/>- data（APIレスポンス全体）<br/>- modifiedDate<br/>- lastSyncedAt