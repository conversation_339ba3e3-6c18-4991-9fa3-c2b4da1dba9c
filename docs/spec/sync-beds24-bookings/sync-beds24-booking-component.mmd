---
config:
  look: neo
  theme: base
  themeVariables:
    # 主要色設定
    primaryColor: "#6366f1"          # Indigo
    primaryTextColor: "#1f2937"      # Dark gray
    primaryBorderColor: "#c7d2fe"    # Light indigo
    lineColor: "#9ca3af"             # Medium gray
    secondaryColor: "#fef3c7"        # Light amber
    tertiaryColor: "#ddd6fe"         # Light purple
    background: "#ffffff"
    mainBkg: "#f9fafb"
    secondBkg: "#f3f4f6"
    tertiaryBkg: "#e5e7eb"
---

graph TB
    subgraph "スケジューリング層"
        Cron[Cron Jobs<br/><i>convex/crons.ts</i>]
    end

    subgraph "Convex Actions層 <i>(Node.js環境)</i>"
        SyncAction[syncBookingsFromBeds24<br/><i>Internal Action</i>]
        RefreshToken[refreshBeds24Token<br/><i>Internal Action</i>]
    end

    subgraph "Convex Functions層 <i>(V8環境)</i>"
        subgraph "Query Functions"
            GetSyncTask[getBookingSyncTask<br/><i>Internal Query</i>]
            GetUsersWithToken[getUsersWithBeds24Token<br/><i>Internal Query</i>]
        end
        
        subgraph "Mutation Functions"
            ProcessQueue[processQueue<br/><i>Internal Mutation</i>]
            ProcessBatch[processBookingBatch<br/><i>Internal Mutation</i>]
            UpdateToken[updateAccessToken<br/><i>Internal Mutation</i>]
            RecordHistory[recordSyncHistory<br/><i>Internal Mutation</i>]
        end
    end

    subgraph "データ層 <i>(Convex Database)</i>"
        subgraph "認証・設定テーブル"
            UserSettings[userSettings<br/><i>ユーザー設定</i>]
            AccessTokens[beds24AccessTokens<br/><i>アクセストークン</i>]
        end
        
        subgraph "施設関連テーブル"
            Properties[beds24Properties<br/><i>施設情報</i>]
            UserProperties[userProperties<br/><i>ユーザー×施設<br/>多対多中間テーブル</i>]
        end
        
        subgraph "同期管理テーブル"
            SyncQueue[syncQueue<br/><i>ジョブキュー</i>]
            SyncHistory[beds24SyncHistory<br/><i>同期履歴</i>]
        end
        
        subgraph "ビジネスデータテーブル"
            Bookings[beds24Bookings<br/><i>予約データ</i>]
        end
    end

    subgraph "外部API層"
        Beds24API[Beds24 API<br/><i>GET /v2/bookings</i>]
    end

    %% Cron起動フロー
    Cron -->|"5分間隔実行"| ProcessQueue

    %% キュー処理フロー
    ProcessQueue -->|"未処理タスク取得"| SyncQueue
    ProcessQueue -->|"非同期実行"| SyncAction

    %% 同期処理フロー
    SyncAction -->|"タスク詳細取得"| GetSyncTask
    GetSyncTask --> SyncQueue
    
    SyncAction -->|"トークン取得"| AccessTokens
    SyncAction -->|"トークン更新"| RefreshToken
    RefreshToken -->|"API呼び出し"| Beds24API
    RefreshToken -->|"保存"| UpdateToken
    UpdateToken --> AccessTokens
    
    SyncAction -->|"予約データ取得"| Beds24API
    SyncAction -->|"バッチ処理"| ProcessBatch
    ProcessBatch -->|"upsert"| Bookings
    ProcessBatch -->|"次ページタスク"| SyncQueue
    
    SyncAction -->|"履歴更新"| RecordHistory
    RecordHistory --> SyncHistory
    
    %% データ参照関係
    Bookings -.->|"施設ID参照"| Properties
    Bookings -.->|"ユーザーID参照"| UserSettings
    UserProperties -.->|"施設ID"| Properties
    UserProperties -.->|"ユーザーID"| UserSettings

    %% 処理内容の注記
    Note over ProcessBatch: バッチ処理結果に<br/>allExisting: booleanを含む<br/><br/>新規データの有無で<br/>再帰的遡り処理を判定
    Note over SyncAction: 再帰的日付範囲処理<br/>- 初回: 現在日時以降<br/>- 遡り: 2ヶ月単位で過去へ<br/><br/>停止条件:<br/>- 全て既存データ<br/>- APIレスポンス0件<br/>- エラー連続発生

    %% スタイリング
    classDef actionClass fill:#fef3c7,stroke:#f59e0b,stroke-width:2px
    classDef queryClass fill:#e0e7ff,stroke:#6366f1,stroke-width:2px
    classDef mutationClass fill:#ddd6fe,stroke:#8b5cf6,stroke-width:2px
    classDef tableClass fill:#d1fae5,stroke:#10b981,stroke-width:2px
    classDef externalClass fill:#fee2e2,stroke:#ef4444,stroke-width:2px
    classDef cronClass fill:#e5e7eb,stroke:#6b7280,stroke-width:2px

    class Cron cronClass
    class SyncAction,RefreshToken actionClass
    class GetSyncTask,GetUsersWithToken queryClass
    class ProcessQueue,ProcessBatch,UpdateToken,RecordHistory mutationClass
    class UserSettings,AccessTokens,Properties,UserProperties,SyncQueue,SyncHistory,Bookings tableClass
    class Beds24API externalClass

%% 凡例
subgraph "凡例"
    L1[Cron Job]:::cronClass
    L2[Internal Action]:::actionClass
    L3[Internal Query]:::queryClass
    L4[Internal Mutation]:::mutationClass
    L5[Database Table]:::tableClass
    L6[External API]:::externalClass
end