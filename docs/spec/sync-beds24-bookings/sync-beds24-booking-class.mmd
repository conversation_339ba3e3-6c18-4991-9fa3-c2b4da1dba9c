---
config:
  look: neo
  theme: base
  themeVariables:
    # 主要色設定
    primaryColor: "#6366f1"          # Indigo
    primaryTextColor: "#1f2937"      # Dark gray
    primaryBorderColor: "#c7d2fe"    # Light indigo
    lineColor: "#9ca3af"             # Medium gray
    secondaryColor: "#fef3c7"        # Light amber
    tertiaryColor: "#ddd6fe"         # Light purple
    background: "#ffffff"
    mainBkg: "#f9fafb"
    secondBkg: "#f3f4f6"
    tertiaryBkg: "#e5e7eb"
---

classDiagram
    %% エンティティ（データモデル層）
    class Beds24Bookings {
        <<Entity>>
        +_id: Id~"beds24Bookings"~
        +_creationTime: number
        +bookingId: string
        +propertyId: Id~"beds24Properties"~
        +userId: string
        +guestName: string
        +checkIn: string
        +checkOut: string
        +status: "confirmed" | "cancelled" | "checked_in" | "checked_out" | "no_show"
        +totalPrice: number
        +currency: string
        +data: any
        +modifiedDate: number
        +lastSyncedAt: number
        +createdAt: number
        +updatedAt: number
        +isDeleted: boolean
        +deletedAt?: number
    }

    class SyncQueue {
        <<Entity>>
        +_id: Id~"syncQueue"~
        +_creationTime: number
        +userId: string
        +jobType: "sync_properties" | "sync_reviews" | "scrape_booking_com_slug" | "sync_bookings"
        +priority: number
        +status: "pending" | "processing" | "completed" | "failed"
        +attempts: number
        +maxAttempts: number
        +scheduledFor: number
        +startedAt?: number
        +completedAt?: number
        +lastError?: string
        +nextRetryAt?: number
        +metadata?: any
        +result?: any
        +createdAt: number
        +updatedAt: number
    }

    class Beds24SyncHistory {
        <<Entity>>
        +_id: Id~"beds24SyncHistory"~
        +_creationTime: number
        +userId: string
        +jobType: string
        +status: "processing" | "success" | "partial_success" | "failed"
        +startedAt: number
        +completedAt?: number
        +duration: number
        +totalItems: number
        +successCount: number
        +failedCount: number
        +errors: array
        +metadata?: any
        +createdAt: number
        +updatedAt: number
    }

    class UserProperties {
        <<Entity>>
        +_id: Id~"userProperties"~
        +_creationTime: number
        +userId: string
        +propertyId: Id~"beds24Properties"~
        +createdAt: number
    }

    class Beds24Property {
        <<Entity>>
        +_id: Id~"beds24Properties"~
        +_creationTime: number
        +beds24PropertyId: string
        +beds24PropertyKey: string
        +name: string
        +propertyType: string
        +currency: string
        +country?: string
        +city?: string
        +data: any
        +lastSyncedAt: number
        +createdAt: number
        +updatedAt: number
        +isDeleted: boolean
        +deletedAt?: number
    }

    class Beds24AccessTokens {
        <<Entity>>
        +_id: Id~"beds24AccessTokens"~
        +_creationTime: number
        +userId: string
        +accessToken: string
        +expiresAt: number
        +lastRefreshedAt: number
        +createdAt: number
        +updatedAt: number
    }

    class UserSettings {
        <<Entity>>
        +_id: Id~"userSettings"~
        +_creationTime: number
        +userId: string
        +theme: "light" | "dark" | "system"
        +beds24?: object
        +has_beds24_token: boolean
        +createdAt: number
        +updatedAt: number
    }

    %% Convex Functions
    class CronJobs {
        <<crons.ts>>
        +processBookingQueue(): interval
        +cleanupBookingJobs(): cron
    }
    class Beds24BookingSyncActions {
        <<beds24BookingSync.ts - Internal Action>>
        +syncBookingsFromBeds24(taskId: string): Promise~null~
    }

    class Beds24BookingsSyncMutations {
        <<beds24BookingSync.ts - Internal>>
        +processQueue(): Promise~null~
        +getUsersWithBeds24Token(): Promise~UsersWithBeds24[]~
    }

    class Beds24BookingsQueries {
        <<beds24Bookings.ts - Public>>
        +getBookingsByProperty(propertyId: Id, paginationOpts: PaginationOpts): Promise~PaginatedBookings~
        +getBookingsByUser(listOptions: ListOptions): Promise~BookingWithDetails[]~
        +getBookingStats(filterOptions: BookingFilterOptions): Promise~BookingStats~
        +getRecentBookings(limit: number, propertyId?: Id): Promise~BookingSummary[]~
    }

    class Beds24BookingsMutations {
        <<beds24Bookings.ts - Public/Internal>>
        +upsertBooking(bookingData: BookingData): Promise~BookingResult~
        +batchUpsertBookings(bookings: BookingData[]): Promise~BatchResult~
        +softDeleteInactiveBookings(propertyId: Id, activeIds: string[]): Promise~DeleteResult~
        +_upsertBooking(userId: string, bookingData: BookingData): Promise~BookingResult~
    }

    class Beds24BookingsInternalQueries {
        <<beds24Bookings.ts - Internal>>
        +getBookingSyncTask(taskId: Id): Promise~TaskData|null~
    }

    class Beds24BookingsInternalMutations {
        <<beds24Bookings.ts - Internal>>
        +processBookingBatch(taskId: Id, bookings: BookingData[], hasNextPage: boolean): Promise~BatchResult & {allExisting: boolean}~
    }

    %% Model層
    class Beds24BookingsModel {
        <<model/beds24Bookings.ts>>
        +checkPropertyAccess(ctx, userId, propertyId): AccessCheckResult
        +getUserAccessiblePropertyIds(ctx, userId): Set~Id~
        +getBookingsByPropertyWithAuth(ctx, userId, propertyId, pagination): PaginatedBookings
        +enrichBookingsWithDetails(ctx, bookings): BookingWithDetails[]
        +getBookingDetails(ctx, bookingId): BookingDetail
        +calculateBookingStatsOptimized(ctx, userId, filters): BookingStats
        +validateBookingData(data): ValidationResult
    }

    %% ヘルパー・ライブラリ
    class Beds24ApiHelper {
        <<lib/beds24Api.ts>>
        +createBeds24ApiClient(logger): Beds24ApiWrapper
        +Beds24ApiWrapper.fetchBookings(token, params): Promise~BookingsResult~
        +Beds24ApiWrapper.fetchBookingDetails(token, bookingId): Promise~BookingDetail~
    }

    class BookingDataParser {
        <<lib/bookingParser.ts>>
        +createBookingParser(logger): BookingParserInterface
        +parseBookingData(data): ParsedBookingResult
        +normalizeBookingStatus(status): NormalizedStatus
    }

    class DateUtils {
        <<lib/dateUtils.ts>>
        +calculatePreviousDateRange(currentFrom: string, currentTo: string): DateRange
        +formatDateForBeds24(date: Date): string
        +parseISODate(dateString: string): Date
        +addMonths(date: Date, months: number): Date
    }

    %% メタデータ型
    class BookingSyncMetadata {
        <<interface>>
        +propertyId: number
        +propertyName: string
        +userId: string
        +pageNumber: number
        +dateFrom?: string
        +dateTo?: string
        +url?: string
        +isRecursiveBacktrack?: boolean
        +recursionDepth?: number
        +originalStartDate?: string
        +hasMoreInDateRange?: boolean
    }


    %% 関連の定義
    CronJobs ..> Beds24BookingSyncActions : "schedules"
    CronJobs ..> Beds24BookingsSyncMutations : "schedules"
    
    Beds24BookingSyncActions ..> SyncQueue : "enqueues tasks"
    Beds24BookingsSyncMutations ..> SyncQueue : "processes"
    
    Beds24BookingsQueries ..> Beds24BookingsModel : "uses"
    Beds24BookingsMutations ..> Beds24BookingsModel : "uses"
    
    Beds24BookingsModel ..> Beds24Bookings : "reads/writes"
    Beds24BookingsModel ..> Beds24Property : "reads"
    Beds24BookingsModel ..> UserProperties : "reads"
    
    Beds24BookingSyncActions ..> Beds24BookingsInternalQueries : "ctx.runQuery"
    Beds24BookingSyncActions ..> Beds24BookingsInternalMutations : "ctx.runMutation"
    Beds24BookingSyncActions ..> Beds24ApiHelper : "uses"
    Beds24BookingSyncActions ..> BookingDataParser : "uses"
    Beds24BookingSyncActions ..> DateUtils : "uses"
    
    Beds24BookingsInternalMutations ..> Beds24Bookings : "writes"
    Beds24BookingsInternalMutations ..> SyncQueue : "updates"
    
    Beds24Bookings --> Beds24Property : "belongs to"
    UserProperties --> Beds24Property : "references"
    UserSettings ..> Beds24AccessTokens : "related"

    SyncQueue --> BookingSyncMetadata : "metadata"

    %% スタイリング
    %% エンティティ（テーブル）
    style Beds24Bookings fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    style SyncQueue fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    style Beds24SyncHistory fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    style UserProperties fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    style Beds24Property fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    style Beds24AccessTokens fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    style UserSettings fill:#e0e7ff,stroke:#4338ca,stroke-width:2px
    
    %% Cron Jobs
    style CronJobs fill:#fef3c7,stroke:#f59e0b,stroke-width:2px
    
    %% Internal Actions
    style Beds24BookingSyncActions fill:#fee2e2,stroke:#dc2626,stroke-width:2px
    
    %% Mutations
    style Beds24BookingsSyncMutations fill:#ddd6fe,stroke:#6366f1,stroke-width:2px
    style Beds24BookingsMutations fill:#ddd6fe,stroke:#6366f1,stroke-width:2px
    style Beds24BookingsInternalMutations fill:#ddd6fe,stroke:#6366f1,stroke-width:2px
    
    %% Queries
    style Beds24BookingsQueries fill:#dbeafe,stroke:#3b82f6,stroke-width:2px
    style Beds24BookingsInternalQueries fill:#dbeafe,stroke:#3b82f6,stroke-width:2px
    
    %% Model層
    style Beds24BookingsModel fill:#e5e7eb,stroke:#6b7280,stroke-width:2px
    
    %% ヘルパー・ライブラリ
    style Beds24ApiHelper fill:#fecaca,stroke:#ef4444,stroke-width:2px
    style BookingDataParser fill:#fecaca,stroke:#ef4444,stroke-width:2px
    style DateUtils fill:#fecaca,stroke:#ef4444,stroke-width:2px