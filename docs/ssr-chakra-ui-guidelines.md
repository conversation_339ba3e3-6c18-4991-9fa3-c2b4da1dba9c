# SSR対応Chakra UIガイドライン

## 概要

このドキュメントは、Next.js App RouterとChakra UI v3を使用する際のSSR関連の問題を回避するためのガイドラインです。

## 既知の問題

### 1. `useBreakpointValue`のSSRエラー（解決済み）

**問題**: `useBreakpointValue`フックが`{ ssr: false }`オプションを指定していても、SSR時に`window is not defined`エラーが発生することがある。

**原因**: 
- Chakra UI v3の内部実装で、初期化時に`window.matchMedia`にアクセスしようとする
- Next.js App RouterのSSR処理とChakra UIのクライアントサイド機能の境界での競合状態
- Reactのルールにより、フックは条件付きで呼び出すことができないため、SSRチェックの前にフックが実行される

**根本原因の詳細**:
1. フックの実行順序の問題：
   - `useBreakpointValue`は常にトップレベルで実行される必要がある
   - SSRチェック（`mounted`状態）はフック実行後にしか行えない
   - 結果として、SSR時でもChakra UIのフックが実行されてしまう

2. Chakra UI v3の内部動作：
   - `useBreakpointValue`は内部で`useMediaQuery`を使用
   - `{ ssr: false }`を指定しても、初期化時に`window.matchMedia`へのアクセスが発生する可能性がある

## 推奨される解決策

### 1. SSRセーフなカスタムフックの使用（推奨）

`useBreakpointValue`の代わりに、以下のカスタムフックを使用してください：

```typescript
import { useSafeBreakpointValue } from "@/shared/components/layout/Sidenav/hooks/useSafeBreakpointValue"

// 使用例
const isMobile = useSafeBreakpointValue({ base: true, md: false }, false)
```

**実装の特徴**:
- Chakra UIのフックを使用せず、独自にブレークポイント検出を実装
- `useEffect`内でのみ`window`オブジェクトにアクセス
- SSR時は常にデフォルト値を返す
- リサイズイベントにデバウンス処理を適用してパフォーマンスを最適化

### 2. 環境チェックユーティリティの活用

```typescript
import { isClient, isServer } from "@/shared/utils/environment"

// windowオブジェクトにアクセスする前にチェック
if (isClient) {
  // クライアントサイドのコード
}
```

### 3. エラーバウンダリの実装

SSR関連のエラーをキャッチして適切にハンドリングするため、重要なコンポーネントをエラーバウンダリでラップしてください：

```typescript
import { SidenavErrorBoundary } from "./SidenavErrorBoundary"

<SidenavErrorBoundary>
  <YourComponent />
</SidenavErrorBoundary>
```

## ベストプラクティス

### 1. クライアントコンポーネントの明示的な宣言

Chakra UIのフックを使用するコンポーネントは必ず`"use client"`ディレクティブを追加してください：

```typescript
"use client"

import { useBreakpointValue } from "@chakra-ui/react"
// ... コンポーネントの実装
```

**重要**: `"use client"`ディレクティブがあっても、コンポーネントの初期化時にサーバーサイドで部分的に実行される可能性があることに注意してください。

### 2. 条件付きレンダリング

SSR時とクライアントサイドで異なる動作が必要な場合は、マウント状態を確認してください：

```typescript
const [mounted, setMounted] = useState(false)

useEffect(() => {
  setMounted(true)
}, [])

if (!mounted) {
  return <DefaultComponent /> // SSR時のデフォルト表示
}

return <ClientComponent /> // クライアントサイドの表示
```

### 3. Dynamic Importの使用

必要に応じて、クライアントサイドのみのコンポーネントは動的インポートを使用してください：

```typescript
import dynamic from "next/dynamic"

const ClientOnlyComponent = dynamic(
  () => import("./ClientOnlyComponent"),
  { ssr: false }
)
```

## トラブルシューティング

### エラー: `window is not defined`

**対処法**:
1. コンポーネントに`"use client"`ディレクティブが追加されているか確認
2. `useSafeBreakpointValue`カスタムフックの使用を検討
3. 環境チェックユーティリティを使用してwindowアクセスを保護

### エラー: ハイドレーションの不一致

**対処法**:
1. SSR時とクライアントサイドで同じ初期値を使用
2. `suppressHydrationWarning`属性の追加を検討（最後の手段として）
3. 条件付きレンダリングでマウント後に表示を切り替える

### 2. `localStorage`のHydrationミスマッチ（解決済み）

**問題**: useStateの初期化関数内でlocalStorageにアクセスすると、サーバーとクライアントで異なる値が返されるため、Hydrationミスマッチが発生。

**症状**:
```
throwOnHydrationMismatch
Error: Hydration failed because the initial UI does not match what was rendered on the server
```

**原因**:
```typescript
// ❌ 問題のあるコード
const [isOpen, setIsOpen] = useState(() => {
  if (typeof window !== "undefined") {
    const saved = localStorage.getItem("key");
    return saved !== null ? saved === "true" : defaultValue;
  }
  return defaultValue;
});
```

**解決策**:
```typescript
// ✅ SSRセーフな実装
const [isOpen, setIsOpen] = useState(defaultValue);

useEffect(() => {
  try {
    const saved = localStorage.getItem("key");
    if (saved !== null) {
      setIsOpen(saved === "true");
    }
  } catch (error) {
    console.error("Failed to load from localStorage:", error);
  }
}, []);
```

### 3. カスタムフックの使用（推奨）

プロジェクトで提供されているSSRセーフなlocalStorageフックを使用してください：

```typescript
import { useLocalStorage, useSimpleLocalStorage } from "@/shared/hooks/useLocalStorage";

// 型定義
interface UserSettings {
  theme: "light" | "dark";
  language: string;
  notifications: boolean;
}

const defaultSettings: UserSettings = {
  theme: "light",
  language: "ja",
  notifications: true
};

// 複雑なオブジェクトの場合（ジェネリック型パラメータ付き）
const [settings, setSettings, isLoading] = useLocalStorage<UserSettings>("settings", defaultSettings);

// 単純な値の場合（リテラル型を明示）
const [theme, setTheme] = useSimpleLocalStorage<"light" | "dark">("theme", "light");
```

## 今回の修正から得られた教訓

### フックの実行順序に関する重要な理解

1. **Reactのルール**: フックは常にトップレベルで呼び出される必要があり、条件付きで実行することはできない
2. **SSRチェックのタイミング**: SSRかどうかのチェック（`mounted`状態など）は、フック呼び出しの**後**でしか行えない
3. **根本的な解決策**: SSR時に問題を起こすフックは使用せず、`useEffect`内でクライアントサイドの処理を行う独自実装が必要

### アーキテクチャ設計の重要性

1. **後付けのSSR対策の限界**: 既存のクライアントサイドコードにSSR対策を後から追加することには限界がある
2. **エラーバウンダリの重要性**: SSRエラーは予測が困難なため、適切なエラーハンドリングが必須
3. **テストの重要性**: SSR環境でのテストを含む包括的なテストスイートが必要

## 参考リンク

- [Chakra UI v3 Server Components](https://www.chakra-ui.com/docs/get-started/frameworks/next-app)
- [Next.js App Router](https://nextjs.org/docs/app)
- [React Server Components](https://react.dev/reference/rsc/server-components)