# Windows環境でのBiome + pnpmエラー対策

## 問題の概要

Windows環境（特に自己ホスト型ランナー）でBiomeとpnpmを使用する際に、以下のエラーが発生することがあります：

```
EPERM: operation not permitted, unlink 'node_modules\@biomejs\cli-win32-x64\biome.exe'
```

## 原因

1. **Windowsの実行ファイル制限**: Windowsでは実行中の.exeファイルを削除・更新できない
2. **pnpmのシンボリックリンク**: Windows環境では管理者権限なしでシンボリックリンクを作成できない
3. **VS Code拡張機能**: Biome VS Code拡張機能がbiome.exeを使用中の場合、パッケージの更新に失敗する

## 実装済みの対策

### 1. .npmrcファイルの設定

プロジェクトルートに`.npmrc`を作成し、Windows環境での問題を回避：

```ini
# Windowsでのシンボリックリンクエラーを回避
symlink=false

# Windowsでのパフォーマンス向上
node-linker=hoisted

# pnpmのキャッシュ設定
prefer-offline=true
prefer-frozen-lockfile=true
```

### 2. Biomeクリーンアップスクリプト

`.github/scripts/cleanup-biome-windows.ps1`スクリプトが以下を実行：

- 実行中のBiomeプロセスを強制終了
- Biome実行ファイルディレクトリを削除
- pnpmキャッシュからBiome関連エントリをクリア

### 3. CI Workflowの改善

各ジョブで依存関係インストール前にBiomeクリーンアップを実行：

```yaml
- name: Clean Biome artifacts (Windows)
  run: |
    & "${{ github.workspace }}/.github/scripts/cleanup-biome-windows.ps1"
  shell: pwsh
```

## ローカル開発での対処法

### VS Codeを使用している場合

1. パッケージ更新前にVS Codeを閉じる
2. 新しいターミナルからインストールコマンドを実行：
   ```bash
   pnpm install
   ```

### Windows開発者モードの有効化（推奨）

管理者権限なしでシンボリックリンクを作成可能にする：

1. 設定 → 更新とセキュリティ → 開発者向け
2. 「開発者モード」を有効化

### 代替手段

`.npmrc`に以下を追加してシンボリックリンクを無効化：

```ini
symlink=false
```

## トラブルシューティング

### エラーが継続する場合

1. **プロセスの確認**
   ```powershell
   Get-Process -Name "biome" -ErrorAction SilentlyContinue
   ```

2. **手動でのクリーンアップ**
   ```powershell
   Remove-Item -Recurse -Force node_modules/@biomejs
   pnpm store prune
   ```

3. **Biomeの最新版へのアップデート**
   ```bash
   pnpm update @biomejs/biome
   ```

## 参考情報

- [Biome VS Code Issue #8](https://github.com/biomejs/biome-vscode/issues/8)
- [pnpm Windows Symlink Discussion](https://github.com/orgs/pnpm/discussions/3516)
- [Windows Developer Mode Documentation](https://docs.microsoft.com/en-us/windows/apps/get-started/developer-mode-features-and-debugging)