# Self-hosted GitHub Actions Runner セットアップガイド

## 概要

このドキュメントは、Self-hosted GitHub Actions Runnerでプロジェクトを実行する際の要件とセットアップ手順を説明します。

## Playwrightブラウザインストールの権限問題対策

### 問題の背景

Self-hostedランナーでPlaywrightを使用する際、デフォルトのキャッシュディレクトリ（`~/.cache/ms-playwright`）への書き込み権限がないため、以下のエラーが発生することがあります：

```
Error: EACCES: permission denied, mkdir '/home/<USER>/.cache/ms-playwright'
```

### 解決策

本プロジェクトでは、以下の対策を実装しています：

1. **プロジェクトローカルディレクトリの使用**
   - `PLAYWRIGHT_BROWSERS_PATH`環境変数を使用してブラウザのインストール先を変更
   - GitHub Workspaceディレクトリ内にブラウザをインストール

2. **キャッシュの活用**
   - GitHub Actionsのキャッシュ機能を使用してブラウザの再インストールを最小化
   - キャッシュキーは`package-lock.json`のハッシュに基づく

## Self-hostedランナーの要件

### 1. 基本要件

- **OS**: Ubuntu 20.04以上推奨
- **Node.js**: v22（プロジェクトで指定されたバージョン）
- **メモリ**: 最低4GB（TypeScriptビルド用）
- **ディスク容量**: 最低10GB（依存関係とビルドアーティファクト用）

### 2. 必要なソフトウェア

```bash
# Node.jsのインストール（nvm使用推奨）
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 22
nvm use 22

# Playwrightの依存関係
# CI内で --with-deps フラグを使用してインストールされます
```

### 3. ディレクトリ権限

Self-hostedランナーを実行するユーザーは、以下のディレクトリへの書き込み権限が必要です：

- GitHub Workspaceディレクトリ（通常は`_work`ディレクトリ）
- Tempディレクトリ（`/tmp`）

### 4. 環境変数

必要に応じて以下の環境変数を設定できます：

```bash
# メモリ制限の調整（TypeScriptビルド用）
export NODE_OPTIONS="--max-old-space-size=4096"

# Playwrightのタイムアウト調整（必要に応じて）
export PLAYWRIGHT_TIMEOUT=30000
```

## トラブルシューティング

### Playwrightのブラウザインストールが失敗する場合

1. **権限エラーの場合**
   - CI設定で`PLAYWRIGHT_BROWSERS_PATH`が正しく設定されているか確認
   - GitHub Workspaceディレクトリへの書き込み権限を確認

2. **ディスク容量不足の場合**
   - Playwrightのブラウザは約500MB必要
   - `df -h`でディスク容量を確認

3. **ネットワークエラーの場合**
   - プロキシ設定が必要な場合は`HTTPS_PROXY`環境変数を設定
   - ファイアウォールでMicrosoftのCDNへのアクセスが許可されているか確認

### キャッシュのクリア

問題が解決しない場合は、キャッシュをクリアして再実行：

```bash
# ローカルでの実行時
rm -rf playwright-browsers/
npm run test
```

## セキュリティ考慮事項

1. **ランナーの分離**
   - プロダクション環境とは別のネットワークセグメントで実行
   - 適切なファイアウォールルールを設定

2. **シークレット管理**
   - GitHub Secretsを使用して機密情報を管理
   - ランナーのログに機密情報が出力されないよう注意

3. **定期的な更新**
   - ランナーソフトウェアとNode.jsを定期的に更新
   - セキュリティパッチを適用

## 参考リンク

- [GitHub Actions Self-hosted runners](https://docs.github.com/en/actions/hosting-your-own-runners)
- [Playwright Installation](https://playwright.dev/docs/intro#installation)
- [Playwright CI Guide](https://playwright.dev/docs/ci)