# CI/CDベストプラクティス

## 概要

このドキュメントは、本プロジェクトのCI/CDパイプラインにおけるベストプラクティスと、今後の改善提案をまとめています。

## 現在の構成の特徴

### 1. Self-hostedランナーの活用

- **利点**: 
  - 専用リソースによる高速ビルド
  - カスタム環境の構築が可能
  - コスト削減（GitHub Actionsの使用分数を節約）

- **課題と対策**:
  - 権限管理の複雑性 → 適切なドキュメントとセットアップガイドで対応
  - メンテナンス負担 → 自動化とモニタリングで軽減

### 2. 効率的なキャッシュ戦略

```yaml
# 実装済みのキャッシュ戦略
- Node modules キャッシュ
- TypeScript buildinfo キャッシュ  
- Next.js ビルドキャッシュ
- Playwright ブラウザキャッシュ（新規追加）
```

### 3. 並列ジョブ実行

- lint-and-typecheck、test、buildを並列実行
- 各ジョブに専用のSelf-hostedランナーラベルを割り当て

## 推奨される改善策

### 1. 環境変数の一元管理

```yaml
# グローバル環境変数の定義を検討
env:
  NODE_VERSION: '22'
  PLAYWRIGHT_BROWSERS_PATH: ${{ github.workspace }}/playwright-browsers
  NODE_OPTIONS: '--max-old-space-size=4096'
```

### 2. エラーハンドリングの強化

```yaml
- name: Install Playwright browsers with retry
  uses: nick-invision/retry@v2
  with:
    timeout_minutes: 10
    max_attempts: 3
    command: npx playwright install chromium --with-deps
```

### 3. 成果物の保存とレポート

```yaml
- name: Upload test results
  if: always()
  uses: actions/upload-artifact@v4
  with:
    name: test-results
    path: |
      apps/main/test-results/
      apps/main/playwright-report/
    retention-days: 30
```

## セキュリティのベストプラクティス

### 1. シークレット管理

- すべての環境変数をGitHub Secretsで管理
- シークレットのローテーション計画を策定
- 最小権限の原則を適用

### 2. 依存関係の管理

```yaml
# 定期的な依存関係の更新チェック
- name: Check for dependency updates
  uses: dependabot/dependabot-core@v1
```

### 3. ビルド成果物の検証

```yaml
# ビルド成果物の署名と検証
- name: Sign build artifacts
  uses: sigstore/cosign-installer@v3
```

## パフォーマンス最適化

### 1. キャッシュの最適化

- キャッシュキーの戦略的な設計
- 部分的なキャッシュヒットの活用（restore-keys）
- キャッシュサイズの監視と最適化

### 2. ジョブの最適化

```yaml
# 条件付き実行で不要なジョブをスキップ
- name: Check if tests needed
  id: check-tests
  run: |
    if git diff --name-only HEAD^ | grep -E '\.(test|spec)\.(ts|tsx|js|jsx)$'; then
      echo "tests_needed=true" >> $GITHUB_OUTPUT
    fi
```

### 3. 並列化の活用

- テストの並列実行
- マトリックスビルドの活用
- 独立したジョブの並列化

## モニタリングとアラート

### 1. メトリクスの収集

- ビルド時間の追跡
- 成功率の監視
- リソース使用率の確認

### 2. アラートの設定

```yaml
# ビルド失敗時の通知
- name: Notify on failure
  if: failure()
  uses: 8398a7/action-slack@v3
  with:
    status: ${{ job.status }}
```

## 災害復旧計画

### 1. バックアップランナー

- メインのSelf-hostedランナーが利用できない場合のフォールバック
- GitHub-hostedランナーへの自動切り替え

### 2. キャッシュの復旧

- キャッシュ破損時の自動検出と再構築
- 定期的なキャッシュの検証

## 将来の拡張計画

### 1. マルチプラットフォーム対応

```yaml
strategy:
  matrix:
    os: [ubuntu-latest, windows-latest, macos-latest]
    node: [20, 22]
```

### 2. プレビューデプロイメント

- PR毎の自動プレビュー環境
- E2Eテストの自動実行
- パフォーマンス測定の自動化

### 3. セキュリティスキャン

```yaml
- name: Run security scan
  uses: github/super-linter@v4
  with:
    DEFAULT_BRANCH: main
    GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
```

## まとめ

CI/CDパイプラインは継続的な改善が必要です。定期的にこのドキュメントを見直し、新しいベストプラクティスを取り入れていくことが重要です。