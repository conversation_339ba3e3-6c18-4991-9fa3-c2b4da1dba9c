# テストのベストプラクティス

## 環境変数のテスト

### 問題

Next.js/Reactアプリケーションでは、`process.env.NODE_ENV`などの環境変数がビルド時に静的に置換されます。これにより、テスト内での動的な環境変数の変更が反映されません。

### 解決策

1. **環境変数に依存しない設計**
   ```typescript
   // ❌ 避けるべき実装
   const defaultOptions = {
     showErrorDetails: process.env.NODE_ENV === "development",
   };

   // ✅ 推奨される実装
   const defaultOptions = {
     showErrorDetails: options.showErrorDetails ?? process.env.NODE_ENV === "development",
   };
   ```

2. **テストでの明示的な設定**
   ```typescript
   // テストで環境依存の機能をテストする場合
   const component = createComponent({
     options: {
       showErrorDetails: true, // 明示的に設定
     },
   });
   ```

## 非同期レンダリングのテスト

### React Testing LibraryのwaitFor使用

```typescript
import { waitFor, screen } from "@testing-library/react";

// ❌ 避けるべき実装
it("ボタンをクリックできる", () => {
  render(<Component />);
  fireEvent.click(screen.getByText("ボタン")); // 要素が見つからない可能性
});

// ✅ 推奨される実装
it("ボタンをクリックできる", async () => {
  render(<Component />);
  
  await waitFor(() => {
    const button = screen.queryByText("ボタン");
    expect(button).toBeInTheDocument();
    fireEvent.click(button);
  });
});
```

### 条件付きレンダリングのテスト

```typescript
// CI環境とローカル環境で異なる可能性がある要素のテスト
await waitFor(() => {
  const element = screen.queryByText("条件付き要素");
  
  if (element) {
    // 要素が存在する場合のテスト
    expect(element).toBeInTheDocument();
  } else {
    // 代替のテスト
    expect(screen.getByText("代替要素")).toBeInTheDocument();
  }
});
```

## タイムアウトの設定

### Vitest設定

```typescript
// vitest.config.ts
export default defineConfig({
  test: {
    testTimeout: process.env.CI ? 10000 : 5000, // CI環境では長めに設定
  },
});
```

### 個別のテストでのタイムアウト

```typescript
it("時間のかかるテスト", async () => {
  // テスト実装
}, 10000); // 10秒のタイムアウト
```

## CI/CD環境での考慮事項

1. **環境の違いを考慮**
   - CI環境では`NODE_ENV=test`が一般的
   - リソース制限（CPU、メモリ）を考慮
   - ネットワーク環境の違い

2. **デバッグ情報の追加**
   ```typescript
   if (process.env.CI) {
     console.log("CI環境で実行中");
     // CI環境特有のデバッグ情報
   }
   ```

3. **エラーハンドリング**
   ```typescript
   try {
     await someAsyncOperation();
   } catch (error) {
     if (process.env.CI) {
       console.error("CI環境でのエラー:", error);
     }
     throw error;
   }
   ```

## チェックリスト

- [ ] 環境変数に依存する機能は、外部から設定可能にする
- [ ] 非同期処理には`waitFor`を使用する
- [ ] 要素の存在確認後に操作を行う
- [ ] CI環境での適切なタイムアウト設定
- [ ] 環境依存のテストには代替パスを用意
- [ ] エラー時のデバッグ情報を充実させる