# INC-001: 設定ページで保存完了トーストが毎秒表示される無限ループ

## メタデータ
- **発生日**: 2025-07-01
- **影響範囲**: 中（UXに大きく影響するが、データ破損はない）
- **カテゴリ**: state-management, data-sync
- **関連技術**: React, Convex, Chakra UI, 自動保存機能
- **発見方法**: 開発中

## 症状
設定ページを開いただけで、「保存しました」というトーストが毎秒表示され続ける。ユーザーは何も操作していないにも関わらず、保存処理が繰り返し実行される。

## 根本原因
設定ページを開いただけで、以下の無限ループが発生：

1. コンポーネントマウント時にConvexから既存の設定値を取得
2. useEffectがConvexの値をローカル状態（useState）に設定
3. useAutoSaveが初回レンダリング後の状態変更を検知し、保存処理を実行
4. Convexでデータ更新（実際には同じ値で更新）
5. useQueryが「新しい」値を取得（実際は同じ値）
6. useEffectが再度反応し、setStateでローカル状態を更新
7. useAutoSaveが再度変更を検知 → 3に戻る（無限ループ）

この結果、ユーザーは何も操作していないのに、毎秒「保存しました」というトーストが表示される状態になりました。

## 調査プロセス
1. トーストが繰り返し表示される現象を確認
2. Beds24Settingsコンポーネントのコードを調査
3. useAutoSaveフックの実装を確認し、初回レンダリングスキップ機能があることを確認
4. useEffectとConvexのリアクティブクエリの相互作用を分析
5. 無限ループの原因を特定：Convexの値更新 → useEffect → setState → useAutoSave → Convexの値更新

## 解決策

### 1. サーバー値とローカル値の分離管理
```typescript
// サーバーから取得した最新の値
const serverValue = userSettings?.beds24?.refreshToken || ""

// ローカル状態を管理（ユーザー入力用）
const [refreshToken, setRefreshToken] = useState(serverValue)

// 最後に保存した値を追跡（重複保存防止用）
const lastSavedValueRef = useRef(serverValue)
```

### 2. ユーザー編集状態の追跡
```typescript
// ユーザーが編集中かどうかを追跡
const isUserEditingRef = useRef(false)

const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
  isUserEditingRef.current = true
  setRefreshToken(e.target.value)
  // 編集完了後、短時間で編集フラグをリセット
  setTimeout(() => {
    isUserEditingRef.current = false
  }, 2000)
}, [])
```

### 3. 条件付き同期
```typescript
// サーバー値の変更を監視（ただし、自分の保存による変更は除外）
React.useEffect(() => {
  if (!isUserEditingRef.current && serverValue !== lastSavedValueRef.current) {
    setRefreshToken(serverValue)
    lastSavedValueRef.current = serverValue
  }
}, [serverValue])
```

### 4. 重複保存の防止
```typescript
onSave: async (value) => {
  // 既に保存済みの値と同じ場合は保存しない
  if (value === lastSavedValueRef.current) {
    return
  }
  
  await updateBeds24RefreshToken(value)
  // 保存成功時に最後の保存値を更新
  lastSavedValueRef.current = value
}
```

### 5. トーストの重複防止
```typescript
toastSuccess("保存しました", "Beds24のリフレッシュトークンを更新しました", {
  id: "beds24-settings-saved", // 同じIDのトーストは重複表示されない
})
```

## 学んだこと
1. リアクティブなデータソース（Convex）とローカル状態を同期する際は、循環参照に注意が必要
2. 単一の値に対して複数の真実の源を持つことは避けるべき
3. ユーザーの操作とシステムの自動更新を明確に区別する必要がある
4. トーストなどのUIフィードバックは、重複防止機構を持つべき
5. 初回レンダリング時の状態変更も自動保存のトリガーになりうるため、特別な考慮が必要
6. useAutoSaveフックは初回レンダリングをスキップする機能を持つが、それだけでは不十分な場合がある

## 予防策
1. 自動保存機能を実装する際は、必ず重複保存防止を考慮する
2. リアクティブデータソースを使用する場合は、ローカル状態との同期方法を慎重に設計する
3. UIフィードバック（トースト等）には必ずIDベースの重複防止を実装する
4. コンポーネントマウント時の状態変更にも注意を払う
5. 値の変更元（ユーザー操作 vs システム更新）を明確に区別する仕組みを導入する

## 関連リンク
- 修正コミット: [a1e87de](https://github.com/wh3at/kadou-delta-next/commit/a1e87de) (Merge pull request #18 from wh3at/feature/kdn-19)
- 影響ファイル: 
  - [`/src/features/settings/components/Beds24Settings.tsx`](https://github.com/wh3at/kadou-delta-next/blob/a1e87de/src/features/settings/components/Beds24Settings.tsx)
  - [`/src/features/settings/hooks/useAutoSave.ts`](https://github.com/wh3at/kadou-delta-next/blob/a1e87de/src/features/settings/hooks/useAutoSave.ts)
  - [`/src/shared/lib/toast.ts`](https://github.com/wh3at/kadou-delta-next/blob/a1e87de/src/shared/lib/toast.ts)