# INC-004: Clerk UserButton Hydration Mismatch

## Date
2025-07-10

## Severity
High - アプリケーションのヘッダー部分でハイドレーションエラーが発生し、ユーザー体験に影響

## Description
ClerkのUserButtonコンポーネントでハイドレーションミスマッチエラーが発生しました。エラーはNext.js App RouterとClerkの統合において、SSR時とクライアントサイドでのレンダリング結果が一致しないことが原因です。

### エラーメッセージ
```
throwOnHydrationMismatch@http://localhost:3232/_next/static/chunks/node_modules_next_dist_compiled_dae1c9ab._.js:5667:56
div@unknown:0:0
render@http://localhost:3232/_next/static/chunks/node_modules_%40clerk_clerk-react_dist_b9b86609._.js:1068:609
[project]/node_modules/@clerk/clerk-react/dist/index.mjs [app-client] (ecmascript) <locals>/_UserButton<@http://localhost:3232/_next/static/chunks/node_modules_%40clerk_clerk-react_dist_b9b86609._.js:1205:241
```

## Root Cause
1. **ClerkProviderの`dynamic`プロパティ**: 認証データをSSR時に利用可能にするが、UserButtonコンポーネント内部でタイミングの問題が発生
2. **内部的なDOM操作**: ClerkのUserButtonコンポーネント内部でdiv要素の動的な生成・更新が行われており、SSR時とクライアントサイドで異なる状態になる
3. **既存のSSR対策の限界**: AppHeaderコンポーネントにmounted状態による条件付きレンダリングが実装されていたが、Clerkコンポーネント内部の問題までは対処できなかった

## Resolution

### 実装した修正

1. **suppressHydrationWarningの追加**
   - UserButtonを包むBoxコンポーネントに`suppressHydrationWarning`属性を追加
   - ハイドレーションの警告を抑制

2. **レイアウトの安定化**
   - コンテナBoxに`minHeight`と`minWidth`を設定し、レイアウトシフトを防止

3. **代替案: 動的インポート**
   - `DynamicUserButton`コンポーネントを作成
   - Next.jsのdynamic importを使用し、SSRを完全に無効化
   - ローディング中はSkeletonコンポーネントを表示

### 修正コード
```typescript
// AppHeader.tsx の修正
<Box minHeight="32px" minWidth="32px">
  {mounted ? (
    <Box suppressHydrationWarning>
      <UserButton
        appearance={{
          elements: {
            avatarBox: "h-8 w-8",
          },
        }}
      />
    </Box>
  ) : (
    <Skeleton height="32px" width="32px" borderRadius="full" />
  )}
</Box>
```

## Lessons Learned

1. **サードパーティライブラリのSSR対応**
   - Clerkのような認証ライブラリは内部で複雑なDOM操作を行うため、SSR時に予期しない動作をすることがある
   - ライブラリのアップデートでSSR対応が改善される可能性があるため、定期的な確認が必要

2. **段階的な対策の重要性**
   - 最初は`suppressHydrationWarning`で警告を抑制
   - 問題が継続する場合は動的インポートでSSRを無効化
   - パフォーマンスと安定性のバランスを考慮

3. **エラーの詳細な記録**
   - スタックトレースから問題の発生箇所を特定
   - 既存の対策が機能していることを確認してから新たな対策を追加

## Prevention
1. 新しいサードパーティコンポーネントを導入する際は、SSR対応を事前に確認
2. 認証関連のUIコンポーネントは動的インポートの使用を検討
3. ハイドレーションエラーの早期発見のため、開発環境でのテストを徹底

## Related Issues
- SSR対応Chakra UIガイドライン (`/docs/ssr-chakra-ui-guidelines.md`)
- フレームワーク統合ガイドライン (`/docs/framework-integration-guidelines.md`)