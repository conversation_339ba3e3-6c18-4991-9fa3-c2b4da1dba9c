# INC-003: Playwright Cache Permission Error in Self-hosted Runner

**Date**: 2025-01-06
**Severity**: Medium
**Status**: Resolved

## Description

CIのtestジョブで「Install Playwright browsers」ステップが以下のエラーで失敗していました：

```
Error: EACCES: permission denied, mkdir '/home/<USER>/.cache/ms-playwright'
```

Self-hostedランナーでPlaywrightブラウザをインストールする際、デフォルトのキャッシュディレクトリ（`~/.cache/ms-playwright`）に対する書き込み権限がないことが原因でした。

## Impact

- CIパイプラインのtestジョブが失敗
- PRのマージがブロックされる
- 開発フローの停滞

## Root Cause Analysis

### 1. 直接的な原因
- Self-hostedランナーのプロセスが`/home/<USER>/.cache`ディレクトリへの書き込み権限を持っていない
- Playwrightがデフォルトで`~/.cache/ms-playwright`にブラウザをインストールしようとする

### 2. アーキテクチャ上の問題
- Self-hostedランナーの環境設定が不完全
- ホームディレクトリの権限管理が適切に構成されていない
- GitHub-hostedランナーとSelf-hostedランナーの環境差異への考慮不足

### 3. エッジケースの考慮漏れ
- Self-hostedランナーでのPlaywright使用ケースが十分にテストされていなかった
- 権限問題に対するフォールバック機構がなかった

## Resolution

### 実施した修正

1. **CI設定の更新（`.github/workflows/ci.yml`）**
   - `PLAYWRIGHT_BROWSERS_PATH`環境変数を使用してブラウザのインストール先を変更
   - プロジェクトのワークスペース内にブラウザをインストールするよう設定
   - Playwrightブラウザ用のキャッシュを追加

2. **.gitignoreの更新**
   - `playwright-browsers/`ディレクトリを追加してバージョン管理から除外

3. **ドキュメントの作成**
   - Self-hostedランナーのセットアップガイド作成
   - CI/CDベストプラクティスドキュメント作成

### 修正のポイント

```yaml
# 権限問題を回避するための環境変数設定
- name: Setup Playwright cache directory
  run: |
    export PLAYWRIGHT_BROWSERS_PATH="${GITHUB_WORKSPACE}/playwright-browsers"
    echo "PLAYWRIGHT_BROWSERS_PATH=${PLAYWRIGHT_BROWSERS_PATH}" >> $GITHUB_ENV
    mkdir -p "${PLAYWRIGHT_BROWSERS_PATH}"

# キャッシュの活用で再インストールを最小化
- name: Cache Playwright browsers
  uses: actions/cache@v4
  with:
    path: playwright-browsers
    key: ${{ runner.os }}-playwright-${{ hashFiles('**/package-lock.json') }}
```

## Lessons Learned

### 1. 環境差異への対応
- Self-hostedランナーとGitHub-hostedランナーの環境差異を事前に把握し、適切な対策を講じる必要がある
- 環境依存の問題に対しては、プロジェクト内で制御可能な解決策を優先すべき

### 2. キャッシュ戦略の重要性
- 外部ツールのインストール場所は明示的に制御し、キャッシュを活用することでCI時間を短縮できる
- キャッシュキーは依存関係の変更を適切に検出できるよう設計する

### 3. ドキュメントの必要性
- Self-hostedランナーのセットアップと運用には詳細なドキュメントが不可欠
- トラブルシューティングガイドを事前に準備することで、問題解決時間を短縮できる

## Prevention Measures

1. **環境テストの自動化**
   - Self-hostedランナーの環境をテストするヘルスチェックジョブの追加を検討

2. **フォールバック機構**
   - 権限エラー時の自動リトライや代替パスの使用

3. **定期的なレビュー**
   - CI設定とランナー環境の定期的なレビューとアップデート

## References

- [Playwright Installation Documentation](https://playwright.dev/docs/intro#installation)
- [GitHub Actions Self-hosted Runners](https://docs.github.com/en/actions/hosting-your-own-runners)
- 内部ドキュメント: `/docs/ci/self-hosted-runner-setup.md`
- 内部ドキュメント: `/docs/ci/ci-best-practices.md`