# INC-003: DOM上のnav要素重複問題

## 概要

**日付**: 2025-07-04  
**報告者**: Claude Code  
**影響範囲**: 設定ページ  
**重要度**: 中

## 問題の説明

設定ページでサイドバーを閉じると、閉じた状態のサイドバーの下に開いた状態のサイドバーが表示される問題が発生しました。

## 初期の誤診断

当初、以下のようなSSR関連の問題と誤診断しました：
- SSRとクライアントサイドレンダリングの不一致
- `useSafeBreakpointValue`のハイドレーション問題
- レンダリングタイミングの問題

しかし、詳細な調査の結果、これらは根本原因ではありませんでした。

## 真の根本原因

### Next.js App Routerのレイアウト継承の誤解

**原因**: 
1. `app/app/layout.tsx`で既にAppLayoutコンポーネントを使用
2. `app/app/settings/page.tsx`でも再度AppLayoutを使用
3. これによりSidenavが二重にレンダリングされていた

**詳細**:
- Next.js App Routerでは、親のlayout.tsxで適用されたレイアウトは自動的に子ページに継承される
- 設定ページで再度AppLayoutを使用したことで、レイアウトが二重に適用された
- 結果として、2つのSidenavコンポーネントがDOM上に存在していた

## 解決策

### 実装した修正

**設定ページ（`app/app/settings/page.tsx`）**:
- 不要なAppLayoutコンポーネントの使用を削除
- 不要なインポート文を削除
- 親レイアウトで適用されているAppLayoutを利用

```typescript
// 修正前
export default function SettingsPage() {
  const navItems = useNavigationItems();
  return (
    <AppLayout navItems={navItems}>  // ❌ 二重適用
      <Container>...</Container>
    </AppLayout>
  );
}

// 修正後
export default function SettingsPage() {
  return (
    <Container>...</Container>  // ✅ 親のレイアウトを利用
  );
}
```

## 教訓と再発防止策

### 1. Next.js App Routerの理解
- **レイアウトの継承**: layout.tsxは自動的に子ルートに継承される
- **ネストされたレイアウト**: 各階層でレイアウトを追加できるが、重複に注意
- **明示的な設計**: レイアウトの階層構造を明確にドキュメント化

### 2. コーディング規約
- **レイアウトの使用場所**: レイアウトコンポーネントはlayout.tsxファイルでのみ使用
- **ページコンポーネント**: page.tsxではコンテンツのみを返す
- **共通レイアウトの確認**: 新しいページを作成する前に親のlayout.tsxを確認

### 3. デバッグのベストプラクティス
- **問題の切り分け**: 最初に単純な原因（コンポーネントの重複など）を確認
- **React DevTools**: コンポーネントツリーを確認して重複を検出
- **段階的な調査**: 複雑な原因（SSR問題など）を疑う前に基本的な部分を確認

### 4. プロジェクト構造の文書化
```
app/
├── layout.tsx          # ルートレイアウト
├── app/
│   ├── layout.tsx      # AppLayoutを適用（全ての子ページに継承）
│   ├── page.tsx        # ダッシュボード（AppLayout継承）
│   ├── settings/
│   │   ├── layout.tsx  # 設定固有のレイアウト（必要な場合）
│   │   └── page.tsx    # 設定ページ（AppLayout継承）
│   └── ...
```

## 影響を受けたファイル

- `/apps/main/app/app/settings/page.tsx` - 修正済み

## 関連リンク

- [Next.js App Router Layouts](https://nextjs.org/docs/app/building-your-application/routing/pages-and-layouts)
- [Next.js Layout Patterns](https://nextjs.org/docs/app/building-your-application/routing/layouts-and-templates)