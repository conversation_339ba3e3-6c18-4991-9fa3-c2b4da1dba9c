# インシデントレポート: CIでのテストタイムアウト（ErrorBoundary詳細表示）

**インシデントID**: INC-003
**日付**: 2025-01-06
**重要度**: 中
**ステータス**: 解決済み

## 概要

CI環境でのみ`factory.test.tsx`の「開発環境でエラー詳細を表示できる」テストが5秒でタイムアウトし、失敗する問題が発生した。

## 影響

- CI/CDパイプラインの失敗
- 開発フローの妨げ

## タイムライン

- **2025-01-06 04:55:48**: CI環境でテストが失敗開始
- **2025-01-06**: 根本原因の調査と解決策の実装

## 根本原因

### 1. ビルド時の環境変数の静的置換
- Next.js/ReactのWebpackビルドプロセスで`process.env.NODE_ENV`がコンパイル時に静的に置換される
- CI環境では`NODE_ENV=test`でビルドされるため、テスト内での動的変更が反映されない

### 2. 条件付きレンダリング
- ErrorBoundaryコンポーネントは`process.env.NODE_ENV === "development"`の時のみ「詳細を表示」ボタンをレンダリング
- テストで`process.env.NODE_ENV = "development"`に変更しても、既にコンパイルされたコードには反映されない

### 3. テストの実装問題
- `screen.getByText("詳細を表示")`が要素を見つけられずに5秒でタイムアウト
- 非同期レンダリングへの対応不足

## 解決策

### 即時の修正

1. **環境変数に依存しない明示的な設定**
   ```typescript
   const CustomErrorBoundary = createFeatureErrorBoundary({
     featureName: "TestFeature",
     options: {
       showErrorDetails: true, // process.env.NODE_ENVに依存せず明示的に設定
     },
   });
   ```

2. **非同期処理への対応**
   ```typescript
   await waitFor(() => {
     const detailsButton = screen.queryByText("詳細を表示");
     if (detailsButton) {
       fireEvent.click(detailsButton);
       // テストを継続
     }
   }, { timeout: 10000 });
   ```

3. **CI環境での代替テストパス**
   - ボタンが表示されない場合の代替テストを用意

### 長期的な改善提案

1. **環境変数の取り扱い改善**
   - ビルド時に固定される環境変数に依存しない設計
   - テスト可能性を考慮したオプション設計

2. **テストタイムアウトの設定**
   ```typescript
   // vitest.config.ts
   test: {
     testTimeout: process.env.CI ? 10000 : 5000,
   }
   ```

3. **アーキテクチャの改善**
   - 環境依存の機能は、明示的な設定として外部から注入できるようにする
   - Feature flagパターンの採用を検討

## 教訓

1. **環境変数の動的変更は避ける**
   - ビルドシステムによって静的に置換される値に依存しない
   - テストでは明示的な設定を使用する

2. **CI環境とローカル環境の違いを考慮**
   - CI環境特有の制約を理解する
   - 環境に依存しないテストを書く

3. **非同期処理への適切な対応**
   - React Testing Libraryの`waitFor`を活用
   - 要素の存在確認を行ってから操作する

## 予防策

1. **コーディング標準の更新**
   - 環境変数に依存する機能の実装ガイドライン
   - テスト可能性を考慮した設計原則

2. **テスト戦略の改善**
   - CI環境での事前テスト
   - タイムアウト設定の標準化

3. **ドキュメントの整備**
   - 環境依存機能のテスト方法
   - CI/CD環境の制約事項

## 関連情報

- Pull Request: #XXX
- 関連ファイル:
  - `__tests__/shared/components/ErrorBoundary/factory.test.tsx`
  - `src/shared/components/ErrorBoundary/factory.tsx`
  - `vitest.config.ts`