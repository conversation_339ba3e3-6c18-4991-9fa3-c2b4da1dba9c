# INC-XXX: [問題の簡潔な説明]

## メタデータ
- **発生日**: YYYY-MM-DD
- **影響範囲**: [低/中/高]
- **カテゴリ**: [state-management / performance / ui-ux / data-sync / auth / etc.]
- **関連技術**: [React / Convex / Chakra UI / Next.js / etc.]
- **発見方法**: [ユーザー報告 / 開発中 / テスト / モニタリング]

## 症状
<!-- ユーザーが体験した問題を具体的に記述 -->

## 根本原因
<!-- 技術的な原因の詳細な分析 -->

## 調査プロセス
<!-- どのように問題を調査・特定したか -->
1. 
2. 
3. 

## 解決策
<!-- 実装した修正内容をコード例と共に記述 -->

### 修正前
```typescript
// 問題のあったコード
```

### 修正後
```typescript
// 修正されたコード
```

## 学んだこと
<!-- この問題から得られた教訓 -->
- 
- 

## 予防策
<!-- 同様の問題を防ぐための対策 -->
- 
- 

## 関連リンク
- PR: #
- コミット: 
- 関連ドキュメント: 
- 参考資料: 