# Incident Report: INC-003 - Storybook Vitest CI Timeout Issue

**Date**: 2025-07-06
**Severity**: High
**Status**: Resolved

## Description

CI環境（GitHub Actions self-hosted runner）でVitestテストを実行した際に、以下のエラーが発生：
1. `markdown-to-jsx`依存関係の解決エラー
2. Playwrightブラウザセッションへの接続タイムアウト（81.86秒後）
3. Storybook 8.5.0での`test.include`非推奨警告

## Root Cause Analysis

### 1. Vitest設定の構造的問題
- **原因**: トップレベルとプロジェクトレベルで`test.include`が重複定義
- **影響**: Storybook 8.5.0-alpha.18では非推奨となり警告が発生
- **根本原因**: Vitest設定の階層構造の理解不足とStorybookプラグインの新仕様への対応遅れ

### 2. 依存関係の最適化問題
- **原因**: `markdown-to-jsx`がViteの最適化プロセスで適切に処理されていない
- **影響**: Viteがビルド時に依存関係を解決できずエラー発生
- **根本原因**: Storybookの内部依存関係の事前最適化設定の欠如

### 3. CI環境でのブラウザセッション接続問題
- **原因**: 
  - CI環境でのリソース制限
  - StorybookサーバーのURL設定不備
  - ヘッドレスChromiumの起動オプション不足
- **影響**: Storybookテストが実行不可
- **根本原因**: CI環境特有の制約への考慮不足

## Resolution

### 即時対応
1. **Vitest設定の修正**
   - トップレベルの`test.include`を削除
   - Unit testとStorybook testのプロジェクトを明確に分離
   - CI環境用のタイムアウト延長（60秒）

2. **Vite最適化設定の追加**
   ```javascript
   optimizeDeps: {
     exclude: ["markdown-to-jsx"],
     include: ["@storybook/addon-docs", "@mdx-js/react"],
   }
   ```

3. **CI環境対応**
   - Storybookテスト用のヘルパースクリプト作成
   - Playwrightの起動オプション追加（`--no-sandbox`等）
   - テストの分離実行（Unit testとStorybook test）

### 長期的対策

1. **テストアーキテクチャの改善**
   - Unit testとIntegration test（Storybook）の明確な分離
   - CI/CD環境専用の設定ファイル管理
   - テスト実行の並列化と最適化

2. **依存関係管理の強化**
   - Viteの`optimizeDeps`設定の定期的な見直し
   - Storybookアップデート時の影響調査プロセス確立
   - 依存関係の事前最適化設定の文書化

3. **CI環境の最適化**
   - Self-hosted runner環境の制約事項の文書化
   - ブラウザテスト用の専用設定管理
   - エラー発生時の自動リトライメカニズム

## Lessons Learned

1. **アーキテクチャ上の考慮事項**
   - フロントエンドのテストツール統合は複雑であり、各ツールの制約を理解する必要がある
   - CI環境とローカル環境の差異を考慮した設計が重要
   - 依存関係の最適化設定は、パフォーマンスと安定性の両面で重要

2. **エッジケースの特定**
   - Self-hosted runner環境での権限問題
   - ブラウザプロセスの起動タイムアウト
   - 間接的な依存関係の解決問題

3. **予防策**
   - テスト実行環境の差異を吸収する抽象化層の実装
   - 環境別の設定管理システムの確立
   - エラー発生時の詳細なログ出力とモニタリング

## Prevention Measures

1. **設定管理の改善**
   - 環境変数による設定の外部化（`.env.test`）
   - CI専用のテストスクリプトの作成
   - 設定変更時の影響範囲の自動チェック

2. **テストの安定性向上**
   - タイムアウト値の適切な設定
   - リトライメカニズムの実装
   - 失敗を許容する段階的なテスト実行

3. **ドキュメンテーション**
   - CI環境のセットアップ手順書作成
   - トラブルシューティングガイドの整備
   - 設定変更履歴の記録

## References

- [Storybook Vitest Addon Documentation](https://storybook.js.org/docs/next/writing-tests/integrations/vitest-addon)
- [Vite Dependency Optimization](https://vitejs.dev/guide/dep-pre-bundling.html)
- [Playwright CI Configuration](https://playwright.dev/docs/ci)

## Action Items

- [x] Vitest設定ファイルの修正
- [x] CI workflow の更新
- [x] テストヘルパースクリプトの作成
- [x] 環境設定ファイルの作成
- [ ] CI環境でのテスト実行確認
- [ ] ドキュメンテーションの更新