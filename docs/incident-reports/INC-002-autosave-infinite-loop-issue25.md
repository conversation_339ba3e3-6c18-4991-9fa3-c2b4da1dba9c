# INC-002: 設定ページで保存トーストが繰り返し表示される（GitHub Issue #25）

## メタデータ
- **発生日**: 2025-07-03
- **影響範囲**: 中（UXに大きく影響するが、データ破損はない）
- **カテゴリ**: state-management, ui-ux
- **関連技術**: React, Convex, useAutoSave Hook
- **発見方法**: ユーザー報告（GitHub Issue #25）

## 症状
設定ページ（Beds24連携設定）を開くと、一定間隔で「保存しました」のトーストが繰り返し表示される。ユーザーが何も操作していないにも関わらず、自動保存が継続的にトリガーされる。

## 根本原因
`useAutoSave`フックの`useEffect`内で`isSaving`状態を依存配列に含めていたため、以下の無限ループが発生：

1. 値が変更される → useEffect実行
2. タイマー設定 → 1秒後にperformSave実行
3. performSave内で`setIsSaving(true)` → 再レンダリング
4. useEffectが`isSaving`の変更を検知して再実行
5. 新しいタイマーが設定される → 2に戻る（無限ループ）

さらに、Convex Mutation側で値の変更チェックが不足していたため、同じ値でも毎回データベース更新が実行されていた。

## 調査プロセス
1. GitHub Issue #25の内容を確認
2. ブラウザコンソールに詳細なログを追加して動作を追跡
3. ログ分析により、`useAutoSave`の`isSaving`状態変化が再レンダリングを引き起こしていることを特定
4. 依存配列の問題を発見し、修正方針を決定

## 解決策

### 修正1: Convex Mutation側での値比較
**修正前**
```typescript
// convex/features/userSettings/userSettings.mutations.ts
export const updateBeds24Settings = mutation({
  handler: async (ctx, args) => {
    // ... 既存の設定を取得
    if (existingSettings) {
      // 常に更新を実行していた
      const updatedBeds24 = {
        ...currentBeds24,
        ...(args.refreshToken !== undefined && {
          refreshToken: args.refreshToken,
        }),
      };

      await ctx.db.patch(existingSettings._id, {
        beds24: updatedBeds24,
        updatedAt: now,
      });
    }
  },
});
```

**修正後**
```typescript
export const updateBeds24Settings = mutation({
  handler: async (ctx, args) => {
    // ... 既存の設定を取得
    if (existingSettings) {
      const currentBeds24 = existingSettings.beds24 || {};
      
      // 値が変更されていない場合はスキップ
      if (currentBeds24.refreshToken === args.refreshToken) {
        console.log("Beds24設定に変更がないため、更新をスキップします");
        return existingSettings._id;
      }
      
      // 変更がある場合のみ更新
      const updatedBeds24 = {
        ...currentBeds24,
        ...(args.refreshToken !== undefined && {
          refreshToken: args.refreshToken,
        }),
      };

      await ctx.db.patch(existingSettings._id, {
        beds24: updatedBeds24,
        updatedAt: now,
      });
    }
  },
});
```

### 修正2: useAutoSaveフックの依存配列最適化
**修正前**
```typescript
// src/features/settings/hooks/useAutoSave.ts
useEffect(() => {
  // ... タイマー設定ロジック
}, [value, debounceMs, performSave, isSaving]); // isSavingが含まれている
```

**修正後**
```typescript
// isSavingRefを追加
const isSavingRef = useRef(false);

// performSave内でrefを更新
const performSave = useCallback(async () => {
  // ...
  isSavingRef.current = true;
  setIsSaving(true);
  // ...
  finally {
    isSavingRef.current = false;
    setIsSaving(false);
  }
}, [onSave, validate, onSuccess, onError]);

// useEffectの依存配列からperformSaveとisSavingを除外
useEffect(() => {
  // ... 
  // 現在保存中の場合は新しいタイマーを設定しない
  if (isSavingRef.current) {
    return;
  }
  // ...
}, [value, debounceMs]); // performSaveとisSavingを除外
```

### 修正3: クライアント側の初期化フラグ追加
```typescript
// src/features/settings/components/Beds24Settings.tsx
const isInitializedRef = useRef(false);

// 自動保存のセットアップ
useAutoSave(refreshToken, {
  onSave: async (value) => {
    // 初期化が完了していない場合は保存しない
    if (!isInitializedRef.current) {
      return;
    }
    
    // 既に保存済みの値と同じ場合は保存しない
    if (value === lastSavedValueRef.current) {
      return;
    }
    // ...
  },
});
```

## 学んだこと
- React Hook の依存配列に状態変数を含める際は、その状態変化が新たな実行を引き起こす可能性を慎重に検討する必要がある
- 非同期処理の実行状態を追跡する場合、`ref`を使用することで不要な再レンダリングを防げる
- データベース更新前に値の変更チェックを行うことで、不要な更新とそれに伴う副作用を防げる
- 詳細なログ出力により、複雑な状態管理の問題を効率的に特定できる

## 予防策
- useEffect の依存配列は必要最小限に留め、特に状態変数の追加は慎重に行う
- 自動保存などの周期的な処理では、実行状態の管理に`ref`の使用を検討する
- Mutation では実際に値が変更された場合のみ更新を行うよう実装する
- 開発時には十分なログを仕込み、状態変化のフローを追跡可能にする
- `useAutoSave`のような汎用フックは、様々なユースケースでテストする

## 関連リンク
- GitHub Issue: [#25](https://github.com/wh3at/kadou-delta-next/issues/25)
- 影響ファイル: 
  - `/apps/main/convex/features/userSettings/userSettings.mutations.ts`
  - `/apps/main/src/features/settings/hooks/useAutoSave.ts`
  - `/apps/main/src/features/settings/components/Beds24Settings.tsx`