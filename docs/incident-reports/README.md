# インシデントレポート

このディレクトリには、開発中に遭遇した重要な問題とその解決策を記録します。

## 目的
- 同様の問題の再発防止
- チーム内での知識共有
- 問題解決パターンの蓄積
- 新メンバーのオンボーディング支援

## フォーマット
各インシデントレポートは `INC-XXX-[簡潔な説明].md` の形式で命名され、
`TEMPLATE.md` に従って記述されます。

## インシデント一覧
| ID | 日付 | 問題の概要 | カテゴリ |
|---|---|---|---|
| INC-001 | 2025-07-01 | 設定ページで保存完了トーストが毎秒表示される無限ループ | state-management, data-sync |
| INC-002 | 2025-07-03 | 設定ページで保存トーストが繰り返し表示される（GitHub Issue #25） | state-management, ui-ux |

## 新しいインシデントレポートの作成方法
1. `TEMPLATE.md` をコピー
2. `INC-XXX-[問題の簡潔な説明].md` として保存（XXXは連番）
3. テンプレートに従って詳細を記入
4. このREADMEのインシデント一覧を更新