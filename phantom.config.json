{"worktreesDirectory": "../kadou-delta-next.phantom", "postCreate": {"copyFiles": [".mcp.json", "apps/main/.env.local", ".claude/settings.local.json"], "commands": ["mkdir -p .scratch", "if [ -d \"../../kadou-delta-next/.scratch\" ]; then cp -a ../../kadou-delta-next/.scratch/. .scratch/ 2>/dev/null || true; fi", "pnpm install --frozen-lockfile", "git submodule update --init --recursive", "mise trust"]}}