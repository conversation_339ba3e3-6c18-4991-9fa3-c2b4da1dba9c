{"name": "@kadou-delta-next/fumadocs", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo", "start": "next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"next": "15.3.4", "react": "^19.1.0", "react-dom": "^19.1.0", "fumadocs-ui": "15.6.0", "fumadocs-core": "15.6.0", "fumadocs-mdx": "11.6.10"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "typescript": "^5.8.3", "@types/mdx": "^2.0.13", "@tailwindcss/postcss": "^4.1.11", "tailwindcss": "^4.1.11", "postcss": "^8.5.6"}}