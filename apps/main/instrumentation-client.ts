// This file configures the initialization of Sentry on the client.
// The added config here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

// 共通のSentry DSN（サーバー側と同じ）
const SENTRY_DSN =
	"https://<EMAIL>/4509557822652416";

Sentry.init({
	dsn: process.env.NEXT_PUBLIC_SENTRY_DSN || SENTRY_DSN,

	// 環境設定（サーバー側と統一）
	environment: process.env.NODE_ENV || "development",
	debug: process.env.NODE_ENV !== "production",

	// パフォーマンストレーシング（サーバー側と統一）
	tracesSampleRate: process.env.NODE_ENV === "production" ? 0.1 : 1.0,

	// セッションリプレイ（クライアント固有）
	replaysSessionSampleRate: process.env.NODE_ENV === "production" ? 0.1 : 1.0,
	replaysOnErrorSampleRate: 1.0,

	// 統合機能
	integrations: [
		// HTTPリクエストの追跡
		Sentry.browserTracingIntegration(),
		// セッションリプレイ
		Sentry.replayIntegration({
			maskAllText: true,
			blockAllMedia: true,
			// プライバシー設定
			maskAllInputs: true,
			// パフォーマンス設定
			networkDetailAllowUrls: [window.location.origin],
		}),
		// グローバルエラーハンドラー
		Sentry.globalHandlersIntegration({
			onerror: true,
			onunhandledrejection: true,
		}),
		// ブラウザAPIエラー
		Sentry.browserApiErrorsIntegration(),
	],

	// ルートPII（個人識別情報）処理を設定
	beforeSend(event, hint) {
		// 開発環境でのエラーログ
		if (process.env.NODE_ENV !== "production") {
			const error = hint.originalException;
			console.error("Sentry captured error:", error);
		}

		// 特定のエラーを無視
		const error = hint.originalException;

		// Next.jsの開発エラーを無視
		if (error instanceof Error && error.message.includes("Hydration")) {
			return null;
		}

		// ネットワークエラーの無視（一時的な接続問題）
		if (error instanceof Error && error.message.includes("NetworkError")) {
			return null;
		}

		// キャンセルされたリクエストの無視
		if (error instanceof Error && error.name === "AbortError") {
			return null;
		}

		// サードパーティのエラーを無視
		if (
			event.exception?.values?.[0]?.stacktrace?.frames?.some((frame) =>
				frame.filename?.includes("chrome-extension://"),
			)
		) {
			return null;
		}

		return event;
	},

	// ブレッドクラムの設定
	beforeBreadcrumb(breadcrumb) {
		// コンソールログのブレッドクラムを無視（ノイズ削減）
		if (breadcrumb.category === "console") {
			return null;
		}

		// フェッチリクエストのブレッドクラムを強化
		if (breadcrumb.category === "fetch") {
			if (breadcrumb.data?.url?.includes("/api/")) {
				breadcrumb.level = "info";
			}
		}

		return breadcrumb;
	},

	// 追加のタグとコンテキスト（サーバー側と統一された形式）
	initialScope: {
		tags: {
			component: "client",
			runtime: "browser",
			locale: "ja-JP",
		},
		user: {
			// ユーザー情報は後でClerkから設定
		},
	},

	// トランスポート設定
	tunnel: "/monitoring",

	// 実験的機能
	_experiments: {
		// ログの有効化
		enableLogs: true,
	},
});

export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;
