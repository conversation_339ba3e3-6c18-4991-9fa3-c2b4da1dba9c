import "@testing-library/jest-dom/vitest";
import { ChakraProvider, defaultSystem } from "@chakra-ui/react";
import { type RenderOptions, render } from "@testing-library/react";
import type { ReactElement } from "react";
import * as React from "react";

// Mock /@vite/env module
if (typeof window !== "undefined") {
	// @ts-ignore
	window["/@vite/env"] = {
		MODE: "test",
		DEV: true,
		PROD: false,
		SSR: false,
	};
}

// Mock ResizeObserver for jsdom
if (typeof global !== "undefined") {
	global.ResizeObserver = class ResizeObserver {
		observe() {}
		unobserve() {}
		disconnect() {}
	};
}

// Mock scrollIntoView for jsdom
if (typeof Element !== "undefined" && !Element.prototype.scrollIntoView) {
	Element.prototype.scrollIntoView = () => {};
}

// Chakra UI v3テスト用のカスタムレンダー関数
export function renderWithChakra(
	ui: ReactElement,
	options?: Omit<RenderOptions, "wrapper">,
): ReturnType<typeof render> {
	const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
		return React.createElement(ChakraProvider, {
			value: defaultSystem,
			children,
		});
	};

	return render(ui, { wrapper: AllTheProviders, ...options });
}

// re-export everything
export * from "@testing-library/react";
