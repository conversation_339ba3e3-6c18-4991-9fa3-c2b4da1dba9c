"use client";

import { useUser } from "@clerk/nextjs";
import { useMemo } from "react";
import type { AuthUser } from "../types/auth.types";
import { toAuthUser } from "../types/auth.types";

/**
 * 現在ログイン中のユーザー情報を取得するフック
 *
 * Clerkのユーザー情報を取得し、アプリケーション用の形式に変換して提供します。
 * useAuthフックと異なり、ユーザー情報の取得に特化しており、
 * サインアウトなどの操作メソッドは含まれません。
 *
 * @returns 現在のユーザー情報と認証状態を含むオブジェクト
 * @returns {AuthUser | null} user - 現在のユーザー情報（未認証の場合はnull）
 * @returns {boolean} isLoaded - ユーザー情報の読み込みが完了したかどうか
 * @returns {boolean} isSignedIn - ユーザーがサインイン済みかどうか
 *
 * @example
 * // ユーザー情報の表示
 * const { user, isLoaded, isSignedIn } = useCurrentUser();
 *
 * if (!isLoaded) {
 *   return <LoadingSpinner />;
 * }
 *
 * if (!isSignedIn || !user) {
 *   return <div>ログインしてください</div>;
 * }
 *
 * return (
 *   <div>
 *     <img src={user.imageUrl} alt={user.name} />
 *     <h2>{user.name}</h2>
 *     <p>{user.email}</p>
 *   </div>
 * );
 */
export function useCurrentUser(): {
	user: AuthUser | null;
	isLoaded: boolean;
	isSignedIn: boolean;
} {
	const { user: clerkUser, isLoaded, isSignedIn } = useUser();

	const user = useMemo(() => toAuthUser(clerkUser), [clerkUser]);

	return {
		user,
		isLoaded,
		isSignedIn: isSignedIn ?? false,
	};
}
