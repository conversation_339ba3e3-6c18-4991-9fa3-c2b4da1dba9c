"use client";

import { useAuth as useClerkAuth, useUser } from "@clerk/nextjs";
import { useCallback } from "react";
import type { AuthState } from "../types/auth.types";
import { toAuthUser } from "../types/auth.types";

/**
 * 認証機能のメインフック
 *
 * Clerkの認証機能をラップし、アプリケーション用の統一されたインターフェースを提供します。
 * このフックを使用することで、アプリケーション全体で一貫した認証状態の管理が可能になります。
 *
 * @returns 認証状態とサインアウト関数を含むオブジェクト
 * @returns {boolean} isLoaded - 認証情報の読み込みが完了したかどうか
 * @returns {boolean} isSignedIn - ユーザーがサインイン済みかどうか
 * @returns {AuthUser | null} user - 現在のユーザー情報（サインイン済みの場合）
 * @returns {() => Promise<void>} signOut - サインアウトを実行する非同期関数
 *
 * @example
 * // コンポーネント内での使用例
 * const { isLoaded, isSignedIn, user, signOut } = useAuth();
 *
 * if (!isLoaded) {
 *   return <LoadingSpinner />;
 * }
 *
 * if (!isSignedIn) {
 *   return <SignInPrompt />;
 * }
 *
 * return (
 *   <div>
 *     <p>ようこそ、{user.name}さん</p>
 *     <button onClick={signOut}>サインアウト</button>
 *   </div>
 * );
 */
export function useAuth(): AuthState & {
	signOut: () => Promise<void>;
} {
	const { isLoaded, isSignedIn, signOut: clerkSignOut } = useClerkAuth();
	const { user: clerkUser } = useUser();

	const signOut = useCallback(async () => {
		await clerkSignOut();
	}, [clerkSignOut]);

	return {
		isLoaded,
		isSignedIn: isSignedIn ?? false,
		user: toAuthUser(clerkUser),
		signOut,
	};
}
