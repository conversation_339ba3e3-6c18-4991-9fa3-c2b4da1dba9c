# Auth フィーチャー

## 概要

Auth フィーチャーは、アプリケーションの認証・認可機能を提供するモジュールです。Clerkを基盤として、ユーザー認証、セッション管理、ユーザープロファイル表示などの機能を提供します。

## ディレクトリ構造

```
src/features/auth/
├── components/          # UIコンポーネント
│   ├── AuthErrorBoundary.tsx    # 認証エラーハンドリング
│   └── UserProfile.tsx          # ユーザープロファイル表示
├── hooks/              # カスタムフック
│   ├── index.ts                 # フックのエクスポート
│   ├── useAuth.ts               # 認証状態管理
│   └── useCurrentUser.ts        # 現在のユーザー情報取得
├── types/              # 型定義
│   └── auth.types.ts            # Auth関連の型定義
├── index.ts            # フィーチャーのエクスポート
└── README.md           # このファイル
```

## 主要なコンポーネントとその役割

### コンポーネント

#### UserProfile
- **役割**: ユーザープロファイルの表示
- **責務**: ログインユーザーの情報表示とプロファイル管理UI
- **特徴**: Clerkのユーザー情報を活用、日本語対応

#### AuthErrorBoundary
- **役割**: 認証関連エラーのハンドリング
- **責務**: 認証エラーをキャッチし、適切なフォールバックUIを表示
- **特徴**: セッションエラー、認証エラーの適切な処理

### カスタムフック

#### useAuth
- **役割**: 認証状態の管理
- **戻り値**: 
  ```typescript
  {
    isAuthenticated: boolean;
    isLoading: boolean;
    user: User | null;
    signIn: () => void;
    signOut: () => void;
  }
  ```
- **特徴**: Clerkの認証状態をラップし、使いやすいインターフェースを提供

#### useCurrentUser
- **役割**: 現在のユーザー情報の取得
- **戻り値**: 現在のユーザー情報またはnull
- **特徴**: Convexと連携してユーザーデータを取得

### 型定義

```typescript
// ユーザー基本情報
export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  imageUrl?: string;
  createdAt: Date;
}

// 認証状態
export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
}

// 認証コンテキスト
export interface AuthContextValue extends AuthState {
  signIn: () => void;
  signOut: () => void;
  refreshUser: () => Promise<void>;
}
```

## 使用例

### 基本的な使用方法

```tsx
import { useAuth } from '@/features/auth';

export default function HomePage() {
  const { isAuthenticated, user, signIn, signOut } = useAuth();

  if (!isAuthenticated) {
    return (
      <button onClick={signIn}>
        ログイン
      </button>
    );
  }

  return (
    <div>
      <p>ようこそ、{user?.firstName}さん！</p>
      <button onClick={signOut}>
        ログアウト
      </button>
    </div>
  );
}
```

### ユーザープロファイルの表示

```tsx
import { UserProfile } from '@/features/auth/components/UserProfile';

export default function ProfilePage() {
  return (
    <div className="container mx-auto p-4">
      <h1>プロファイル</h1>
      <UserProfile />
    </div>
  );
}
```

### 認証保護されたコンポーネント

```tsx
import { useAuth } from '@/features/auth';

export function ProtectedComponent({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <div>認証状態を確認中...</div>;
  }

  if (!isAuthenticated) {
    return <div>ログインが必要です</div>;
  }

  return <>{children}</>;
}
```

### エラーハンドリング

```tsx
import { AuthErrorBoundary } from '@/features/auth/components/AuthErrorBoundary';

export default function App() {
  return (
    <AuthErrorBoundary>
      <YourAuthenticatedApp />
    </AuthErrorBoundary>
  );
}
```

## テスト方法

### ユニットテスト

```bash
# Auth関連のテストを実行
npm run test src/features/auth

# 特定のフックのテスト
npm run test src/features/auth/hooks/useAuth.test.ts
```

### テストの書き方

```tsx
import { renderHook } from '@testing-library/react';
import { useAuth } from '../useAuth';

// Clerkのモック
jest.mock('@clerk/nextjs', () => ({
  useUser: jest.fn(),
  useClerk: jest.fn()
}));

describe('useAuth', () => {
  it('認証済みユーザーの情報を返す', () => {
    const { result } = renderHook(() => useAuth());
    
    expect(result.current.isAuthenticated).toBe(true);
    expect(result.current.user).toBeDefined();
  });
});
```

## 今後の拡張方法

### 新機能の追加

1. **ロールベースアクセス制御（RBAC）**
   ```tsx
   // src/features/auth/hooks/usePermissions.ts
   export function usePermissions() {
     const { user } = useCurrentUser();
     
     return {
       hasRole: (role: string) => user?.roles?.includes(role),
       hasPermission: (permission: string) => 
         user?.permissions?.includes(permission)
     };
   }
   ```

2. **セッション管理**
   ```tsx
   // src/features/auth/hooks/useSession.ts
   export function useSession() {
     // セッションの有効期限管理
     // 自動更新機能
     // マルチデバイス対応
   }
   ```

3. **ソーシャルログイン統合**
   ```tsx
   // src/features/auth/components/SocialLogin.tsx
   export function SocialLogin() {
     return (
       <div className="social-login-buttons">
         <GoogleLoginButton />
         <GitHubLoginButton />
         <LineLoginButton /> {/* 日本向け */}
       </div>
     );
   }
   ```

### 高度な認証フロー

```tsx
// 二要素認証
export function useTwoFactorAuth() {
  const [isEnabled, setIsEnabled] = useState(false);
  
  const enable2FA = async () => {
    // QRコード生成とセットアップ
  };
  
  const verify2FA = async (code: string) => {
    // 認証コードの検証
  };
  
  return { isEnabled, enable2FA, verify2FA };
}

// パスワードレス認証
export function usePasswordlessAuth() {
  const sendMagicLink = async (email: string) => {
    // マジックリンクの送信
  };
  
  const verifyMagicLink = async (token: string) => {
    // トークンの検証とログイン
  };
  
  return { sendMagicLink, verifyMagicLink };
}
```

## セキュリティ考慮事項

### トークン管理
- JWTトークンの安全な保存
- リフレッシュトークンの適切な管理
- XSS/CSRF対策の実装

### セッションセキュリティ
```tsx
// セッションのセキュリティ設定
export const sessionConfig = {
  maxAge: 30 * 24 * 60 * 60, // 30日
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax' as const,
  httpOnly: true
};
```

### 認証フローのベストプラクティス
- ログイン試行回数の制限
- アカウントロックアウト機能
- 異常なアクセスパターンの検出

## 依存関係

- **Clerk**: 認証基盤
- **Convex**: ユーザーデータの永続化
- **React**: UIフレームワーク
- **TypeScript**: 型安全性

## 注意事項

- Clerkの設定は環境変数で管理（.env.local）
- 日本語ローカライゼーションの設定を確認
- プロダクション環境では必ずHTTPSを使用
- セッションの有効期限を適切に設定
- 個人情報の取り扱いに注意（GDPR/個人情報保護法準拠）