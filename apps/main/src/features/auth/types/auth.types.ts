/**
 * Authentication Types
 *
 * 認証機能で使用される型定義
 * Clerk認証との統合を前提とした型定義を提供
 */

import type { User } from "@clerk/nextjs/server";
import type { UserResource } from "@clerk/types";

/**
 * アプリケーション内でのユーザー情報
 */
export interface AuthUser {
	id: string;
	email: string | null;
	name: string | null;
	imageUrl: string | null;
}

/**
 * 認証状態
 */
export interface AuthState {
	isLoaded: boolean;
	isSignedIn: boolean;
	user: AuthUser | null;
}

/**
 * 認証エラー
 */
export interface AuthError {
	code: string;
	message: string;
}

/**
 * Clerk User から AuthUser への変換
 */
export function toAuthUser(
	clerkUser: User | UserResource | null | undefined,
): AuthUser | null {
	if (!clerkUser) return null;

	return {
		id: clerkUser.id,
		email: clerkUser.primaryEmailAddress?.emailAddress ?? null,
		name: clerkUser.fullName ?? clerkUser.firstName ?? null,
		imageUrl: clerkUser.imageUrl,
	};
}

/**
 * Convex認証コンテキストでのユーザー情報
 */
export interface ConvexAuthUser {
	subject: string;
	name?: string;
	email?: string;
	pictureUrl?: string;
}
