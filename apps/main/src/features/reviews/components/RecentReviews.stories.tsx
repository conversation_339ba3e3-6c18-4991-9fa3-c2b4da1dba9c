import type { Meta, StoryObj } from "@storybook/react";
import type { RecentReviewsData } from "../types";
import { RecentReviews } from "./RecentReviews";

const meta = {
	title: "Features/Reviews/RecentReviews",
	component: RecentReviews,
	parameters: {
		layout: "padded",
	},
	tags: ["autodocs"],
	argTypes: {
		isLoading: {
			control: { type: "boolean" },
			description: "ローディング状態を表示するかどうか",
		},
		className: {
			control: { type: "text" },
			description: "追加のCSSクラス",
		},
	},
} satisfies Meta<typeof RecentReviews>;

export default meta;
type Story = StoryObj<typeof meta>;

// サンプルデータ
const sampleData: RecentReviewsData = {
	facilityName: "ホテル東京",
	goodReviews: [
		{
			id: "1",
			score: 9.5,
			title: "素晴らしい滞在でした",
			positiveReview:
				"スタッフの対応が素晴らしく、部屋も清潔で快適でした。朝食のバリエーションも豊富で大満足です。",
			reviewerName: "田中太郎",
			reviewerCountry: "日本",
			reviewDate: "2025-01-03",
			source: "Booking.com",
		},
		{
			id: "2",
			score: 8.8,
			title: "立地が最高",
			positiveReview:
				"駅から近く、周辺にはレストランやショッピング施設が充実していて便利でした。",
			negativeReview: "部屋が少し狭かった。",
			reviewerName: "鈴木花子",
			reviewerCountry: "日本",
			reviewDate: "2025-01-02",
			source: "AirBnB",
		},
		{
			id: "3",
			score: 9.2,
			title: "また利用したい",
			positiveReview:
				"清潔で静かな環境でゆっくり休めました。アメニティも充実していて良かったです。",
			reviewerName: "John Smith",
			reviewerCountry: "アメリカ",
			reviewDate: "2025-01-01",
			source: "Expedia",
		},
	],
	badReviews: [
		{
			id: "4",
			score: 4.5,
			title: "期待外れでした",
			negativeReview:
				"Wi-Fiが繋がりにくく、仕事に支障が出ました。エアコンの音も気になりました。",
			reviewerName: "佐藤次郎",
			reviewerCountry: "日本",
			reviewDate: "2024-12-30",
			source: "Booking.com",
		},
		{
			id: "5",
			score: 5.2,
			title: "改善が必要",
			positiveReview: "立地は良い。",
			negativeReview:
				"部屋の清掃が行き届いていなかった。シャワーの水圧も弱く不便でした。",
			reviewerName: "Mary Johnson",
			reviewerCountry: "イギリス",
			reviewDate: "2024-12-28",
			source: "Hotels.com",
		},
	],
};

// デフォルト表示
export const Default: Story = {
	args: {
		data: sampleData,
	},
};

// ローディング状態
export const Loading: Story = {
	args: {
		isLoading: true,
	},
};

// データなし
export const NoReviews: Story = {
	args: {
		data: {
			facilityName: "ホテル新規オープン",
			goodReviews: [],
			badReviews: [],
		},
	},
};

// 高評価のみ
export const OnlyGoodReviews: Story = {
	args: {
		data: {
			facilityName: "最高評価ホテル",
			goodReviews: sampleData.goodReviews,
			badReviews: [],
		},
	},
};

// 低評価のみ
export const OnlyBadReviews: Story = {
	args: {
		data: {
			facilityName: "要改善ホテル",
			goodReviews: [],
			badReviews: sampleData.badReviews,
		},
	},
};

// 長いレビューリスト（スクロール確認用）
export const ManyReviews: Story = {
	args: {
		data: {
			facilityName: "人気ホテル",
			goodReviews: [
				...sampleData.goodReviews,
				...Array.from({ length: 10 }, (_, i) => ({
					id: `good-${i + 10}`,
					score: 8 + Math.random() * 2,
					title: `素晴らしい体験 ${i + 1}`,
					positiveReview: `とても良かったです。スタッフの対応も素晴らしく、また利用したいと思います。${i + 1}`,
					reviewerName: `レビュアー ${i + 1}`,
					reviewerCountry: "日本",
					reviewDate: new Date(Date.now() - i * 24 * 60 * 60 * 1000)
						.toISOString()
						.split("T")[0],
					source: ["Booking.com", "AirBnB", "Expedia", "Hotels.com"][i % 4],
				})),
			],
			badReviews: [
				...sampleData.badReviews,
				...Array.from({ length: 10 }, (_, i) => ({
					id: `bad-${i + 10}`,
					score: 3 + Math.random() * 3,
					title: `改善点あり ${i + 1}`,
					negativeReview: `改善が必要な点がいくつかありました。次回は期待しています。${i + 1}`,
					reviewerName: `レビュアー ${i + 100}`,
					reviewerCountry: "日本",
					reviewDate: new Date(Date.now() - i * 24 * 60 * 60 * 1000)
						.toISOString()
						.split("T")[0],
					source: ["Hotels.com", "Expedia", "AirBnB", "Booking.com"][i % 4],
				})),
			],
		},
	},
};

// モバイル表示（375px - iPhone SE）
export const Mobile: Story = {
	args: {
		data: sampleData,
	},
	globals: {
		viewport: {
			value: "mobile1",
		},
	},
};

// タブレット表示（768px - iPad）
export const Tablet: Story = {
	args: {
		data: sampleData,
	},
	globals: {
		viewport: {
			value: "tablet",
		},
	},
};
