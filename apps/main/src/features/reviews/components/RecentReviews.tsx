"use client";

import {
	Badge,
	Box,
	Card,
	Flex,
	Grid,
	Heading,
	Icon,
	Skeleton,
	SkeletonCircle,
	SkeletonText,
	Stack,
	Text,
} from "@chakra-ui/react";
import { Calendar, Frown, Smile, User } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { handleError } from "@/shared/lib/error-handling";
import type { RecentReviewsData } from "../types";

export interface RecentReviewsProps {
	data?: RecentReviewsData;
	isLoading?: boolean;
	className?: string;
}

/**
 * レビュースコアバッジコンポーネント
 *
 * レビューの評価スコアを視覚的に表現するバッジを表示します。
 * スコアが8.0以上の場合は緑色、それ未満の場合は赤色で表示されます。
 *
 * @param props - コンポーネントのプロパティ
 * @param props.score - レビューの評価スコア（0-10の範囲）
 * @returns スコアバッジコンポーネント
 *
 * @example
 * // 高評価（緑色）のバッジ
 * <ReviewScore score={9.2} />
 *
 * // 低評価（赤色）のバッジ
 * <ReviewScore score={6.5} />
 */
function ReviewScore({ score }: { score: number }) {
	const isHighScore = score >= 8;

	return (
		<Badge
			colorPalette={isHighScore ? "green" : "red"}
			variant="subtle"
			fontSize="sm"
			display="inline-flex"
			alignItems="center"
			justifyContent="center"
			width="32px"
			height="32px"
			borderRadius="full"
			fontWeight="semibold"
		>
			{score.toFixed(1)}
		</Badge>
	);
}

/**
 * 個別レビューアイテムコンポーネント
 *
 * 単一のレビューを表示するカードコンポーネントです。
 * レビューのタイトル、スコア、良い点・悪い点、投稿者情報、日付などを表示します。
 * ローディング状態の場合はスケルトンUIを表示します。
 *
 * @param props - コンポーネントのプロパティ
 * @param props.review - レビューデータ（オプション）
 * @param props.review.score - レビューのスコア
 * @param props.review.title - レビューのタイトル（オプション）
 * @param props.review.positiveReview - 良い点のコメント（オプション）
 * @param props.review.negativeReview - 悪い点のコメント（オプション）
 * @param props.review.reviewerName - レビュー投稿者名（オプション）
 * @param props.review.reviewerCountry - レビュー投稿者の国（オプション）
 * @param props.review.reviewDate - レビュー投稿日
 * @param props.review.source - レビューの出典（オプション）
 * @param props.isLoading - ローディング状態かどうか
 * @returns レビューアイテムコンポーネント
 *
 * @example
 * // 通常のレビュー表示
 * <ReviewItem
 *   review={{
 *     score: 8.5,
 *     title: "素晴らしい滞在",
 *     positiveReview: "スタッフが親切でした",
 *     reviewDate: "2025-01-20",
 *     reviewerName: "田中太郎",
 *     reviewerCountry: "日本"
 *   }}
 *   isLoading={false}
 * />
 *
 * // ローディング状態
 * <ReviewItem isLoading={true} />
 */
function ReviewItem({
	review,
	isLoading,
}: {
	review?: {
		score: number;
		title?: string;
		positiveReview?: string;
		negativeReview?: string;
		reviewerName?: string;
		reviewerCountry?: string;
		reviewDate: string;
		source?: string;
	};
	isLoading: boolean;
}) {
	if (isLoading) {
		return (
			<Card.Root borderWidth="1px" borderRadius="lg" padding="4" spaceY="3">
				<Flex justifyContent="space-between" alignItems="flex-start">
					<Skeleton width="200px" height="20px" />
					<SkeletonCircle size="32px" />
				</Flex>
				<SkeletonText noOfLines={2} />
				<Flex
					justifyContent="space-between"
					alignItems="center"
					fontSize="sm"
					color="fg.subtle"
				>
					<Skeleton width="100px" height="16px" />
					<Skeleton width="80px" height="16px" />
					<Skeleton width="100px" height="16px" />
				</Flex>
			</Card.Root>
		);
	}

	if (!review) {
		return null;
	}

	const formatDate = (dateString: string) => {
		if (!dateString || dateString.trim() === "") {
			return "日付不明";
		}
		const date = new Date(dateString);
		if (Number.isNaN(date.getTime())) {
			return "日付不明";
		}
		return date.toLocaleDateString("ja-JP", {
			year: "numeric",
			month: "2-digit",
			day: "2-digit",
		});
	};

	return (
		<Card.Root
			borderWidth="1px"
			borderRadius="lg"
			padding="4"
			spaceY="2"
			_hover={{ boxShadow: "sm" }}
			transition="box-shadow 0.2s"
		>
			<Flex justifyContent="space-between" alignItems="flex-start" gap="4">
				<Box flex="1">
					{review.title && (
						<Heading size="sm" color="fg">
							{review.title}
						</Heading>
					)}
				</Box>
				<ReviewScore score={review.score} />
			</Flex>

			{review.positiveReview && (
				<Stack direction="column" spaceY="1">
					<Flex alignItems="center" gap="1" mt="2" mb="-0.5">
						<Icon color="fg.success" boxSize="4" aria-hidden="true">
							<Smile />
						</Icon>
					</Flex>
					<Text fontSize="sm" color="fg">
						{review.positiveReview}
					</Text>
				</Stack>
			)}

			{review.negativeReview && (
				<Stack direction="column" spaceY="1">
					<Flex alignItems="center" gap="1" mt="2" mb="-0.5">
						<Icon color="fg.error" boxSize="4" aria-hidden="true">
							<Frown />
						</Icon>
					</Flex>
					<Text fontSize="sm" color="fg">
						{review.negativeReview}
					</Text>
				</Stack>
			)}

			<Flex justifyContent="space-between" alignItems="center" mt="4">
				<Text
					fontSize="xs"
					color="fg.subtle"
					display="flex"
					alignItems="center"
					gap="1"
				>
					<Icon boxSize="3.5" aria-hidden="true">
						<Calendar />
					</Icon>
					{formatDate(review.reviewDate)}
				</Text>
				{review.source && (
					<Text
						fontSize="xs"
						color="fg.subtle"
						marginLeft={{ base: "auto", md: "0" }}
					>
						{review.source}
					</Text>
				)}
				<Flex
					fontSize="xs"
					color="fg.subtle"
					display={{ base: "none", md: "flex" }}
					alignItems="center"
					gap="1"
				>
					<Icon boxSize="3" aria-hidden="true">
						<User />
					</Icon>
					{review.reviewerName || "匿名"}{" "}
					{review.reviewerCountry && `(${review.reviewerCountry})`}
				</Flex>
			</Flex>
		</Card.Root>
	);
}

/**
 * 最近のレビュー表示コンポーネント
 *
 * 施設の最新レビューを高評価と低評価に分けて表示するコンポーネントです。
 * IntersectionObserverを使用してスクロール位置を検知し、グラデーションで
 * スクロール可能エリアを視覚的に表現します。
 *
 * @param props - コンポーネントのプロパティ
 * @param props.data - レビューデータ（施設名、高評価レビュー、低評価レビュー）
 * @param props.isLoading - データ読み込み中かどうか（デフォルト: false）
 * @param props.className - カスタムCSSクラス名
 * @returns 最近のレビュー表示コンポーネント
 *
 * @example
 * // 基本的な使用例
 * const reviewData = {
 *   facilityName: "ホテル東京",
 *   goodReviews: [
 *     { id: "1", score: 9.0, title: "素晴らしい", reviewDate: "2025-01-20" }
 *   ],
 *   badReviews: [
 *     { id: "2", score: 5.0, title: "改善の余地あり", reviewDate: "2025-01-18" }
 *   ]
 * };
 *
 * <RecentReviews data={reviewData} />
 *
 * // ローディング状態
 * <RecentReviews isLoading={true} />
 */
export function RecentReviews({
	data,
	isLoading = false,
	className,
}: RecentReviewsProps) {
	const [isGoodReviewsAtBottom, setIsGoodReviewsAtBottom] = useState(false);
	const [isBadReviewsAtBottom, setIsBadReviewsAtBottom] = useState(false);
	const [isGoodReviewsAtTop, setIsGoodReviewsAtTop] = useState(true);
	const [isBadReviewsAtTop, setIsBadReviewsAtTop] = useState(true);
	const goodReviewsSentinelRef = useRef<HTMLDivElement>(null);
	const badReviewsSentinelRef = useRef<HTMLDivElement>(null);
	const goodReviewsTopSentinelRef = useRef<HTMLDivElement>(null);
	const badReviewsTopSentinelRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		try {
			const observerOptions = {
				threshold: 0.3,
			};

			const goodReviewsObserver = new IntersectionObserver(([entry]) => {
				setIsGoodReviewsAtBottom(entry.isIntersecting);
			}, observerOptions);

			const badReviewsObserver = new IntersectionObserver(([entry]) => {
				setIsBadReviewsAtBottom(entry.isIntersecting);
			}, observerOptions);

			const goodReviewsTopObserver = new IntersectionObserver(([entry]) => {
				setIsGoodReviewsAtTop(entry.isIntersecting);
			}, observerOptions);

			const badReviewsTopObserver = new IntersectionObserver(([entry]) => {
				setIsBadReviewsAtTop(entry.isIntersecting);
			}, observerOptions);

			if (goodReviewsSentinelRef.current) {
				goodReviewsObserver.observe(goodReviewsSentinelRef.current);
			}

			if (badReviewsSentinelRef.current) {
				badReviewsObserver.observe(badReviewsSentinelRef.current);
			}

			if (goodReviewsTopSentinelRef.current) {
				goodReviewsTopObserver.observe(goodReviewsTopSentinelRef.current);
			}

			if (badReviewsTopSentinelRef.current) {
				badReviewsTopObserver.observe(badReviewsTopSentinelRef.current);
			}

			return () => {
				goodReviewsObserver.disconnect();
				badReviewsObserver.disconnect();
				goodReviewsTopObserver.disconnect();
				badReviewsTopObserver.disconnect();
			};
		} catch (error) {
			handleError(error, {
				feature: "RecentReviews",
				context: "IntersectionObserver setup",
			});
			// UIは機能し続けるので、エラーを再スローしない
		}
	}, []);

	return (
		<Card.Root className={className} width="full">
			<Card.Header>
				<Card.Title>
					{isLoading ? (
						<Skeleton width="150px" height="20px" />
					) : (
						data?.facilityName || "直近のレビュー"
					)}
				</Card.Title>
				<Card.Description>
					施設の最新のレビューを表示しています
				</Card.Description>
			</Card.Header>
			<Card.Body spaceY="6">
				<Grid
					templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }}
					gap={{ base: 4, md: 6 }}
				>
					<Stack direction="column" spaceY="4">
						<Heading
							size="sm"
							color="fg.success"
							display="flex"
							alignItems="center"
							gap="2"
						>
							<Box width="2" height="2" bg="bg.success" borderRadius="full" />
							高評価レビュー
						</Heading>
						<Box position="relative">
							<Box
								spaceY="3"
								maxHeight={{ base: "400px", md: "480px", lg: "600px" }}
								overflowY="auto"
								WebkitOverflowScrolling="touch"
								paddingRight="2"
							>
								<Box ref={goodReviewsTopSentinelRef} height="1px" />
								{isLoading
									? Array.from({ length: 3 }).map((_, index) => (
											<ReviewItem
												// biome-ignore lint/suspicious/noArrayIndexKey: ローディング中の静的なプレースホルダーのため、indexをキーとして使用しても問題ない
												key={`loading-good-${index}`}
												isLoading={true}
											/>
										))
									: data?.goodReviews.map((review) => (
											<ReviewItem
												key={review.id}
												review={review}
												isLoading={false}
											/>
										))}
								{!isLoading && data?.goodReviews.length === 0 && (
									<Text
										fontSize="sm"
										color="fg.subtle"
										textAlign="center"
										paddingY="8"
									>
										高評価レビューはまだありません
									</Text>
								)}
								<Box ref={goodReviewsSentinelRef} height="1px" />
							</Box>
							<Box
								position="absolute"
								top="0"
								left="0"
								right="0"
								height="16"
								bgGradient="linear(to-b, white, transparent)"
								pointerEvents="none"
								transition="opacity 0.5s"
								opacity={isGoodReviewsAtTop ? 0 : 1}
							/>
							<Box
								position="absolute"
								bottom="0"
								left="0"
								right="0"
								height="16"
								bgGradient="linear(to-t, white, transparent)"
								pointerEvents="none"
								transition="opacity 0.5s"
								opacity={isGoodReviewsAtBottom ? 0 : 1}
							/>
						</Box>
					</Stack>

					<Stack direction="column" spaceY="4">
						<Heading
							size="sm"
							color="fg.error"
							display="flex"
							alignItems="center"
							gap="2"
						>
							<Box width="2" height="2" bg="bg.error" borderRadius="full" />
							低評価レビュー
						</Heading>
						<Box position="relative">
							<Box
								spaceY="3"
								maxHeight={{ base: "400px", md: "480px", lg: "600px" }}
								overflowY="auto"
								WebkitOverflowScrolling="touch"
								paddingRight="2"
							>
								<Box ref={badReviewsTopSentinelRef} height="1px" />
								{isLoading
									? Array.from({ length: 3 }).map((_, index) => (
											<ReviewItem
												// biome-ignore lint/suspicious/noArrayIndexKey: ローディング中の静的なプレースホルダーのため、indexをキーとして使用しても問題ない
												key={`loading-bad-${index}`}
												isLoading={true}
											/>
										))
									: data?.badReviews.map((review) => (
											<ReviewItem
												key={review.id}
												review={review}
												isLoading={false}
											/>
										))}
								{!isLoading && data?.badReviews.length === 0 && (
									<Text
										fontSize="sm"
										color="fg.subtle"
										textAlign="center"
										paddingY="8"
									>
										低評価レビューはまだありません
									</Text>
								)}
								<Box ref={badReviewsSentinelRef} height="1px" />
							</Box>
							<Box
								position="absolute"
								top="0"
								left="0"
								right="0"
								height="16"
								bgGradient="linear(to-b, white, transparent)"
								pointerEvents="none"
								transition="opacity 0.5s"
								opacity={isBadReviewsAtTop ? 0 : 1}
							/>
							<Box
								position="absolute"
								bottom="0"
								left="0"
								right="0"
								height="16"
								bgGradient="linear(to-t, white, transparent)"
								pointerEvents="none"
								transition="opacity 0.5s"
								opacity={isBadReviewsAtBottom ? 0 : 1}
							/>
						</Box>
					</Stack>
				</Grid>
			</Card.Body>
		</Card.Root>
	);
}
