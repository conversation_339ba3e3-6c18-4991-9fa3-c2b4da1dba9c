"use client";

import {
	Card,
	Center,
	Container,
	Heading,
	Skeleton,
	SkeletonText,
	Stack,
	Text,
} from "@chakra-ui/react";
import { RecentReviews } from "@/features/reviews";
import {
	type ScrollableTab,
	ScrollableTabs,
} from "@/shared/components/ScrollableTabs";
import { usePropertiesWithReviews } from "../hooks/usePropertiesWithReviews";

/**
 * レビュー分析タブコンポーネントのプロパティ
 *
 * @property className - カスタムCSSクラス名
 */
export interface ReviewAnalysisTabsProps {
	className?: string;
}

/**
 * レビュー分析タブコンポーネント
 *
 * 各施設のレビューデータをタブ形式で表示します。
 * 施設ごとに高評価と低評価のレビューを分類して表示し、
 * スクロール可能なタブインターフェースで施設を切り替えることができます。
 *
 * @param props - コンポーネントのプロパティ
 * @returns タブ形式のレビュー分析UI
 *
 * @example
 * ```tsx
 * <ReviewAnalysisTabs className="my-custom-class" />
 * ```
 */
export function ReviewAnalysisTabs({ className }: ReviewAnalysisTabsProps) {
	const { properties, isLoading } = usePropertiesWithReviews();

	// ローディング状態
	if (isLoading) {
		return (
			<Container maxW="7xl" py={8} className={className}>
				<Stack spaceY={6}>
					<Skeleton height="32px" width="200px" />
					<Card.Root>
						<Card.Body>
							<Stack spaceY={4}>
								<Skeleton height="40px" />
								<SkeletonText noOfLines={4} />
							</Stack>
						</Card.Body>
					</Card.Root>
				</Stack>
			</Container>
		);
	}

	// データがない場合
	if (!properties || properties.length === 0) {
		return (
			<Container maxW="7xl" py={8} className={className}>
				<Card.Root>
					<Card.Body>
						<Center minHeight="400px">
							<Stack align="center" spaceY={4}>
								<Heading size="md" color="fg.subtle">
									施設データがありません
								</Heading>
								<Text color="fg.subtle" textAlign="center">
									施設データを同期してください。
									<br />
									設定画面からBeds24との連携を確認してください。
								</Text>
							</Stack>
						</Center>
					</Card.Body>
				</Card.Root>
			</Container>
		);
	}

	// ScrollableTab形式にデータを変換
	const scrollableTabs: ScrollableTab[] = properties.map((property: any) => ({
		value: property.propertyId,
		label: property.propertyName,
		content: property.reviewsData ? (
			<RecentReviews
				data={property.reviewsData}
				isLoading={property.isLoading}
			/>
		) : (
			<Card.Root>
				<Card.Body>
					<Center minHeight="300px">
						<Text color="fg.subtle">この施設のレビューデータはありません</Text>
					</Center>
				</Card.Body>
			</Card.Root>
		),
	}));

	return (
		<Container maxW="7xl" py={8} className={className}>
			<Stack spaceY={6}>
				<Heading size="lg" color="fg">
					レビュー分析
				</Heading>

				<ScrollableTabs
					tabs={scrollableTabs}
					defaultValue={properties[0]?.propertyId}
					variant="line"
					size="md"
					showScrollButtons={true}
					enableSnapScroll={true}
				/>
			</Stack>
		</Container>
	);
}
