"use client";

import { useQuery } from "convex/react";
import { useMemo } from "react";
import { api } from "@/convex/_generated/api";
import type { RecentReviewsData } from "@/features/reviews/types";

/**
 * レビュー付き施設情報のインターフェース
 *
 * @property propertyId - 施設ID
 * @property propertyName - 施設名
 * @property reviewsData - レビューデータ（高評価・低評価に分類済み）
 * @property isLoading - データロード中フラグ
 */
export interface PropertyWithReviews {
	propertyId: string;
	propertyName: string;
	reviewsData: RecentReviewsData | null;
	isLoading: boolean;
}

/**
 * 施設一覧とそのレビューデータを取得するカスタムフック
 *
 * 各施設の最新レビュー10件を取得し、高評価（8.0以上）と低評価（8.0未満）に分類して返します。
 *
 * @returns 施設とレビューデータの配列、ローディング状態、エラー情報を含むオブジェクト
 *
 * @example
 * ```tsx
 * const { properties, isLoading, error } = usePropertiesWithReviews();
 *
 * if (isLoading) return <LoadingSpinner />;
 * if (error) return <ErrorMessage error={error} />;
 *
 * properties?.forEach(property => {
 *   console.log(property.propertyName, property.reviewsData);
 * });
 * ```
 */
export function usePropertiesWithReviews() {
	// 施設一覧を取得
	const properties = useQuery(api.beds24.getProperties, {
		includeDeleted: false,
	});

	// 施設IDの配列を準備
	const propertyIds = useMemo(() => {
		return properties?.map((p: any) => p._id) ?? [];
	}, [properties]);

	// 各施設のレビューデータを取得（施設ごとに10件）
	const reviewsByPropertyArray = useQuery(
		api.otaReviews.getRecentReviewsByProperties,
		propertyIds.length > 0 ? { propertyIds, limitPerProperty: 10 } : "skip",
	);

	// データを整形
	const propertiesWithReviews = useMemo(() => {
		if (!properties || !reviewsByPropertyArray) {
			return null;
		}

		return properties.map((property: any) => {
			const propertyReviewData = reviewsByPropertyArray.find(
				(item: any) => item.propertyId === property._id,
			);
			const propertyReviews = propertyReviewData?.reviews ?? [];

			// 高評価（8.0以上）と低評価（8.0未満）に分類
			const goodReviews = propertyReviews
				.filter((review: any) => review.score >= 8.0)
				.slice(0, 10) // 最大10件
				.map((review: any) => ({
					id: review._id,
					score: review.score,
					title: review.title || undefined,
					positiveReview: review.reviewContentStructured?.positive || undefined,
					negativeReview: review.reviewContentStructured?.negative || undefined,
					reviewerName: review.reviewerName || undefined,
					reviewerCountry: review.reviewerCountry || undefined,
					reviewDate: new Date(review.reviewDate).toISOString(),
					source: review.otaShortName || undefined,
				}));

			const badReviews = propertyReviews
				.filter((review: any) => review.score < 8.0)
				.slice(0, 10) // 最大10件
				.map((review: any) => ({
					id: review._id,
					score: review.score,
					title: review.title || undefined,
					positiveReview: review.reviewContentStructured?.positive || undefined,
					negativeReview: review.reviewContentStructured?.negative || undefined,
					reviewerName: review.reviewerName || undefined,
					reviewerCountry: review.reviewerCountry || undefined,
					reviewDate: new Date(review.reviewDate).toISOString(),
					source: review.otaShortName || undefined,
				}));

			const reviewData: RecentReviewsData = {
				facilityName: property.name,
				goodReviews,
				badReviews,
			};

			return {
				propertyId: property._id,
				propertyName: property.name,
				reviewsData: reviewData,
				isLoading: false,
			};
		});
	}, [properties, reviewsByPropertyArray]);

	return {
		properties: propertiesWithReviews,
		isLoading: properties === undefined || reviewsByPropertyArray === undefined,
		error: null,
	};
}
