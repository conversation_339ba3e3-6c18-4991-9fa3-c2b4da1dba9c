/**
 * ユーザー設定の型定義
 */

// テーマ設定の型
export type Theme = "light" | "dark" | "system";

// Beds24設定の型
export interface Beds24Settings {
	apiKey?: string;
	propKey?: string;
	isConnected: boolean;
	lastSync?: number;
}

// ユーザー設定全体の型
export interface UserSettings {
	_id: string;
	_creationTime: number;
	userId: string;
	theme: string;
	beds24?: Beds24Settings;
	createdAt: number;
	updatedAt: number;
}

// テーマのラベルマッピング
export const THEME_LABELS: Record<Theme, string> = {
	light: "ライト",
	dark: "ダーク",
	system: "システム設定に従う",
};
