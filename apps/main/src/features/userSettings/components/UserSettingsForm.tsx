"use client";

import {
	Alert,
	Box,
	Card,
	Field,
	Heading,
	Skeleton,
	Stack,
	Text,
} from "@chakra-ui/react";
import { createListCollection } from "@chakra-ui/react/collection";
import { Select } from "@chakra-ui/react/select";
import { toastError, toastSuccess } from "@/shared/lib/toast";
import { useUserSettings } from "../hooks/useUserSettings";
import { THEME_LABELS, type Theme } from "../types";

/**
 * ユーザー設定フォームコンポーネント
 *
 * ユーザーの基本設定（テーマなど）を表示・編集するためのフォームを提供します。
 * 認証状態に応じて適切なUIを表示し、設定の更新時にはトースト通知を表示します。
 *
 * @returns ユーザー設定フォームコンポーネント
 *
 * @example
 * // ページ内での使用例
 * function SettingsPage() {
 *   return (
 *     <div>
 *       <h1>設定</h1>
 *       <UserSettingsForm />
 *     </div>
 *   );
 * }
 */
export function UserSettingsForm() {
	const { userSettings, isLoading, isAuthenticated, updateTheme } =
		useUserSettings();

	// 非ログイン時の表示
	if (!isAuthenticated) {
		return (
			<Alert.Root status="warning">
				<Alert.Indicator />
				<Alert.Title>ログインしてください</Alert.Title>
			</Alert.Root>
		);
	}

	// ローディング中の表示
	if (isLoading) {
		return (
			<Stack gap={4}>
				<Skeleton height="60px" />
				<Skeleton height="60px" />
				<Skeleton height="60px" />
				<Skeleton height="120px" />
			</Stack>
		);
	}

	// 設定が取得できなかった場合
	if (!userSettings) {
		return (
			<Alert.Root status="error">
				<Alert.Indicator />
				<Alert.Title>設定の読み込みに失敗しました</Alert.Title>
			</Alert.Root>
		);
	}

	// テーマオプションの配列を作成
	const themeOptions = Object.entries(THEME_LABELS).map(([value, label]) => ({
		value,
		label,
	}));

	return (
		<Stack gap={6}>
			<Card.Root>
				<Card.Header>
					<Heading size="md" color="fg">
						基本設定
					</Heading>
				</Card.Header>
				<Card.Body>
					<Stack gap={4}>
						<Field.Root>
							<Field.Label>テーマ</Field.Label>
							<Select.Root
								collection={createListCollection({ items: themeOptions })}
								value={[userSettings.theme]}
								onValueChange={(details) => {
									const newTheme = details.value[0];
									if (
										newTheme &&
										["light", "dark", "system"].includes(newTheme)
									) {
										updateTheme(newTheme as "light" | "dark" | "system")
											.then(() => toastSuccess("テーマを更新しました"))
											.catch(() => toastError("更新に失敗しました"));
									}
								}}
							>
								<Select.Control>
									<Select.Trigger>
										<Select.ValueText>
											{THEME_LABELS[userSettings.theme as Theme] ||
												userSettings.theme}
										</Select.ValueText>
										<Select.Indicator />
									</Select.Trigger>
								</Select.Control>
								<Select.Positioner>
									<Select.Content>
										{themeOptions.map((option) => (
											<Select.Item key={option.value} item={option}>
												<Select.ItemText>{option.label}</Select.ItemText>
											</Select.Item>
										))}
									</Select.Content>
								</Select.Positioner>
							</Select.Root>
						</Field.Root>
					</Stack>
				</Card.Body>
			</Card.Root>

			<Box>
				<Text fontSize="sm" color="fg.subtle">
					最終更新: {new Date(userSettings.updatedAt).toLocaleString("ja-JP")}
				</Text>
			</Box>
		</Stack>
	);
}
