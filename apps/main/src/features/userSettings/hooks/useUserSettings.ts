"use client";

import { useAction, useConvexAuth, useMutation, useQuery } from "convex/react";
import type { FunctionReference } from "convex/server";
import { useEffect } from "react";
import { api } from "@/convex/_generated/api";
import type { UserSettingsResult } from "@/convex/types/userSettings";

// Convex関数の型を明示的に定義して深度制限を回避
type GetCurrentUserSettingsType = FunctionReference<
	"query",
	"public",
	Record<string, never>,
	UserSettingsResult
>;
type UpsertUserSettingsType = FunctionReference<"mutation", "public", any, any>;
type UpdateUserSettingsFieldType = FunctionReference<
	"mutation",
	"public",
	any,
	any
>;
type DeleteUserSettingsType = FunctionReference<"mutation", "public", any, any>;
type UpdateBeds24SettingsType = FunctionReference<
	"mutation",
	"public",
	any,
	any
>;
type InitializeUserSettingsType = FunctionReference<
	"action",
	"public",
	any,
	any
>;

/**
 * ユーザー設定を管理するためのカスタムフック
 *
 * 現在のユーザーの設定を取得・更新する機能を提供します。
 * 初回ログイン時には自動的に設定を初期化します。
 *
 * @returns ユーザー設定の状態と操作関数
 * @returns userSettings - 現在のユーザー設定（読み込み中はundefined、未設定時はnull）
 * @returns isLoading - 設定の読み込み中かどうか
 * @returns isAuthenticated - ユーザーが認証済みかどうか
 * @returns updateUserSettings - ユーザー設定全体を更新する関数
 * @returns updateUserSettingsField - 特定のフィールドのみを更新する関数
 * @returns deleteUserSettings - ユーザー設定を削除する関数
 * @returns updateBeds24Settings - Beds24設定を更新する関数
 * @returns initializeUserSettings - ユーザー設定を初期化する関数
 * @returns updateTheme - テーマ設定を更新するヘルパー関数
 * @returns updateBeds24RefreshToken - Beds24リフレッシュトークンを更新するヘルパー関数
 *
 * @example
 * // 基本的な使用例
 * const { userSettings, updateTheme } = useUserSettings();
 *
 * // テーマを変更
 * await updateTheme("dark");
 *
 * // 特定のフィールドを更新
 * await updateUserSettingsField({
 *   update: { field: "language", value: "ja" }
 * });
 */
export function useUserSettings() {
	const { isAuthenticated } = useConvexAuth();

	// 現在のユーザー設定を取得
	const userSettings = useQuery(
		api.userSettings.getCurrentUserSettings as GetCurrentUserSettingsType,
		isAuthenticated ? {} : "skip",
	);

	// ユーザー設定を更新
	const updateUserSettings = useMutation(
		api.userSettings.upsertUserSettings as UpsertUserSettingsType,
	);

	// 特定のフィールドのみを更新
	const updateUserSettingsField = useMutation(
		api.userSettings.updateUserSettingsField as UpdateUserSettingsFieldType,
	);

	// ユーザー設定を削除
	const deleteUserSettings = useMutation(
		api.userSettings.deleteUserSettings as DeleteUserSettingsType,
	);

	// Beds24設定を更新
	const updateBeds24Settings = useMutation(
		api.userSettings.updateBeds24Settings as UpdateBeds24SettingsType,
	);

	// ユーザー設定を初期化（初回ログイン時）
	const initializeUserSettings = useAction(
		api.userSettings.initializeUserSettings as InitializeUserSettingsType,
	);

	// 初回ログイン時に設定を初期化
	useEffect(() => {
		if (isAuthenticated && userSettings === null) {
			// 設定を初期化
			initializeUserSettings({}).catch(console.error);
		}
	}, [isAuthenticated, userSettings, initializeUserSettings]);

	return {
		// 状態
		userSettings,
		isLoading: userSettings === undefined,
		isAuthenticated,

		// 更新関数
		updateUserSettings,
		updateUserSettingsField,
		deleteUserSettings,
		updateBeds24Settings,

		// アクション
		initializeUserSettings,

		// ヘルパー関数
		updateTheme: (theme: "light" | "dark" | "system") =>
			updateUserSettingsField({ update: { field: "theme", value: theme } }),
		updateBeds24RefreshToken: (refreshToken: string | undefined) =>
			updateBeds24Settings({ refreshToken }),
	};
}
