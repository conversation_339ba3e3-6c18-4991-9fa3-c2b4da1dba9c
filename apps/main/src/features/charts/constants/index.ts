/**
 * チャートのデフォルト設定
 */
export const CHART_DEFAULTS = {
	height: 300,
	containerClass: "mx-auto",
	margins: { top: 10, right: 10, bottom: 10, left: 10 },
} as const;

/**
 * チャートの色パレット（Chakra UIのテーマトークン）
 * @deprecated 代わりに CHART_SEMANTIC_COLORS を使用してください
 * @chakra-ui/chartsでは色トークンに"solid"サフィックスを付けて使用
 */
export const CHART_COLORS = {
	primary: [
		"blue.solid", // メインブルー
		"teal.solid", // ティール
		"purple.solid", // パープル
		"pink.solid", // ピンク
		"orange.solid", // オレンジ
		"yellow.solid", // イエロー
		"green.solid", // グリーン
		"red.solid", // レッド
		"cyan.solid", // シアン
		"gray.solid", // グレー
	],
} as const;

/**
 * チャート用のセマンティックカラートークン
 * theme/system.ts で定義されたセマンティックトークンを使用
 */
export const CHART_SEMANTIC_COLORS = [
	"chart.0", // blue.400
	"chart.1", // red.400
	"chart.2", // purple.400
	"chart.3", // pink.400
	"chart.4", // cyan.400
	"chart.5", // green.400
	"chart.6", // yellow.400
	"chart.7", // teal.400
	"chart.8", // orange.400
] as const;
