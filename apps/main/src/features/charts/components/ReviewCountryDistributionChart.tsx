"use client";

import { Chart, useChart } from "@chakra-ui/charts";
import { Box, Card, Skeleton, SkeletonText } from "@chakra-ui/react";
import { Cell, Legend, Pie, PieChart, Tooltip } from "recharts";
import { CHART_DEFAULTS, CHART_SEMANTIC_COLORS } from "../constants";
import type { ReviewCountryDistributionChartProps } from "../types";

/**
 * レビュー国別分布チャートコンポーネント
 *
 * レビューの国別分布を円グラフで視覚化するコンポーネントです。
 * 各国のレビュー件数と割合を表示し、5%未満の小さなセグメントのラベルは
 * 見やすさのために非表示にします。
 *
 * @param props - コンポーネントのプロパティ
 * @param props.data - 国別レビューデータの配列
 * @param props.data[].country - 国名
 * @param props.data[].count - レビュー件数
 * @param props.data[].percentage - 全体に対する割合（0-100）
 * @param props.className - カスタムCSSクラス名
 * @param props.title - チャートのタイトル（デフォルト: "レビュー国別分布"）
 * @param props.description - チャートの説明文（オプション）
 * @param props.isLoading - データ読み込み中かどうか（デフォルト: false）
 * @returns レビュー国別分布チャートコンポーネント
 *
 * @example
 * // 基本的な使用例
 * const countryData = [
 *   { country: "日本", count: 120, percentage: 40.0 },
 *   { country: "アメリカ", count: 90, percentage: 30.0 },
 *   { country: "中国", count: 60, percentage: 20.0 },
 *   { country: "その他", count: 30, percentage: 10.0 }
 * ];
 *
 * <ReviewCountryDistributionChart
 *   data={countryData}
 *   title="2025年1月のレビュー国別分布"
 *   description="施設に投稿されたレビューの国別内訳"
 * />
 *
 * // ローディング状態
 * <ReviewCountryDistributionChart isLoading={true} />
 */
export function ReviewCountryDistributionChart({
	data = [],
	className,
	title = "レビュー国別分布",
	description,
	isLoading = false,
}: ReviewCountryDistributionChartProps) {
	// データを割合で降順ソートし、色情報を追加
	const chartData = [...data]
		.sort((a, b) => b.percentage - a.percentage)
		.map((item, index) => ({
			name: item.country,
			value: item.count,
			percentage: item.percentage,
			color: CHART_SEMANTIC_COLORS[index % CHART_SEMANTIC_COLORS.length],
		}));

	// useChartフックでチャートを設定
	const chart = useChart({
		data: chartData,
	});

	// 最大のセグメントの始点が12時の位置（90度）になるように設定
	const startAngle = 90;

	return (
		<Card.Root className={className} width="full">
			<Card.Header>
				<Card.Title>
					{isLoading ? <Skeleton width="100px" height="24px" /> : title}
				</Card.Title>
				{description && <Card.Description>{description}</Card.Description>}
			</Card.Header>
			<Card.Body>
				{isLoading ? (
					<Box>
						<Skeleton height={`${CHART_DEFAULTS.height}px`} />
						<SkeletonText mt={4} noOfLines={3} />
					</Box>
				) : (
					<Chart.Root
						boxSize="full"
						height={`${CHART_DEFAULTS.height}px`}
						chart={chart}
					>
						<PieChart margin={CHART_DEFAULTS.margins}>
							<Pie
								data={chart.data}
								dataKey={chart.key("value")}
								nameKey="name"
								cx="50%"
								cy="50%"
								outerRadius={90}
								startAngle={startAngle}
								endAngle={startAngle + 360}
								// 5%未満の要素のラベルを非表示にするカスタムレンダラー
								label={({ name, percent }) =>
									!percent || percent * 100 < 5
										? null
										: `${name} (${(percent * 100).toFixed(1)}%)`
								}
								// Rechartsの仕様上、ラベルがnullの場合でもラベルラインは表示されるため、
								// 全てのラベルラインを非表示にする
								labelLine={false}
							>
								{chart.data.map((entry) => (
									<Cell
										key={`cell-${entry.name}`}
										fill={chart.color(entry.color)}
										stroke="var(--chakra-colors-border)"
										strokeWidth={2}
									/>
								))}
							</Pie>
							<Tooltip
								content={({ active, payload }) => {
									if (!active || !payload?.length) return null;
									const data = payload[0].payload;
									return (
										<Box
											borderRadius="md"
											borderWidth="1px"
											bg="white"
											_dark={{ bg: "gray.800" }}
											px={3}
											py={2}
											fontSize="sm"
											boxShadow="sm"
										>
											<Box display="grid" gridTemplateColumns="1fr 1fr" gap={2}>
												<Box fontWeight="medium">国</Box>
												<Box textAlign="right">{data.name}</Box>
												<Box fontWeight="medium">件数</Box>
												<Box textAlign="right">{data.value}件</Box>
												<Box fontWeight="medium">割合</Box>
												<Box textAlign="right">
													{data.percentage.toFixed(1)}%
												</Box>
											</Box>
										</Box>
									);
								}}
							/>
							<Legend content={<Chart.Legend />} />
						</PieChart>
					</Chart.Root>
				)}
			</Card.Body>
		</Card.Root>
	);
}
