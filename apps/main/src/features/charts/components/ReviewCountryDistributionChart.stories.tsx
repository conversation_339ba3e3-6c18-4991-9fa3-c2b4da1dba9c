import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ReviewCountryDistributionChart } from "./ReviewCountryDistributionChart";

// サンプルデータ
const sampleData = [
	{ country: "日本", count: 450, percentage: 35.2 },
	{ country: "アメリカ", count: 380, percentage: 29.7 },
	{ country: "ドイツ", count: 200, percentage: 15.6 },
	{ country: "フランス", count: 150, percentage: 11.7 },
	{ country: "イギリス", count: 50, percentage: 3.9 },
	{ country: "カナダ", count: 30, percentage: 2.3 },
	{ country: "その他", count: 20, percentage: 1.6 },
];

// 少数データのサンプル
const fewCountriesData = [
	{ country: "日本", count: 800, percentage: 66.7 },
	{ country: "アメリカ", count: 300, percentage: 25.0 },
	{ country: "その他", count: 100, percentage: 8.3 },
];

const meta = {
	title: "Features/Charts/ReviewCountryDistributionChart",
	component: ReviewCountryDistributionChart,
	parameters: {
		layout: "padded",
	},
	tags: ["autodocs"],
	argTypes: {
		isLoading: {
			control: { type: "boolean" },
			description: "ローディング状態の表示",
		},
		title: {
			control: { type: "text" },
			description: "チャートのタイトル",
		},
		description: {
			control: { type: "text" },
			description: "チャートの説明文",
		},
		className: {
			control: { type: "text" },
			description: "追加のCSSクラス名",
		},
		data: {
			description: "チャートデータの配列",
		},
	},
} satisfies Meta<typeof ReviewCountryDistributionChart>;

export default meta;
type Story = StoryObj<typeof meta>;

// デフォルト表示
export const Default: Story = {
	args: {
		data: sampleData,
		isLoading: false,
	},
};

// カスタムタイトルと説明文付き
export const WithDescription: Story = {
	args: {
		data: sampleData,
		title: "ゲストレビュー国別分布",
		description: "過去30日間のレビュー投稿者の国別分布を表示しています",
		isLoading: false,
	},
};

// ローディング状態
export const Loading: Story = {
	args: {
		isLoading: true,
		title: "レビュー国別分布",
	},
};

// データなし
export const NoData: Story = {
	args: {
		data: [],
		isLoading: false,
		title: "レビュー国別分布",
		description: "表示可能なデータがありません",
	},
};

// 少数の国のみ
export const FewCountries: Story = {
	args: {
		data: fewCountriesData,
		isLoading: false,
		title: "主要国からのレビュー",
	},
};

// 多数の小さなセグメント（5%未満のラベル非表示のテスト）
export const ManySmallSegments: Story = {
	args: {
		data: [
			{ country: "日本", count: 500, percentage: 41.7 },
			{ country: "アメリカ", count: 400, percentage: 33.3 },
			{ country: "ドイツ", count: 100, percentage: 8.3 },
			{ country: "フランス", count: 50, percentage: 4.2 },
			{ country: "イギリス", count: 40, percentage: 3.3 },
			{ country: "カナダ", count: 30, percentage: 2.5 },
			{ country: "オーストラリア", count: 25, percentage: 2.1 },
			{ country: "イタリア", count: 20, percentage: 1.7 },
			{ country: "スペイン", count: 15, percentage: 1.3 },
			{ country: "その他", count: 20, percentage: 1.6 },
		],
		isLoading: false,
		title: "詳細な国別分布",
		description: "5%未満の国はラベルが非表示になります",
	},
};

// モバイル表示
export const Mobile: Story = {
	args: {
		data: sampleData,
		isLoading: false,
		title: "レビュー国別分布",
	},
	globals: {
		viewport: {
			value: "mobile1",
		},
	},
};

// タブレット表示
export const Tablet: Story = {
	args: {
		data: sampleData,
		isLoading: false,
		title: "レビュー国別分布",
	},
	globals: {
		viewport: {
			value: "tablet",
		},
	},
};
