// Beds24同期システムの型定義

// 同期ステータス
export const SyncStatus = {
	PENDING: "pending",
	PROCESSING: "processing",
	COMPLETED: "completed",
	FAILED: "failed",
} as const;

export type SyncStatus = (typeof SyncStatus)[keyof typeof SyncStatus];

// 同期エラーコード
export const SyncErrorCode = {
	TOKEN_EXPIRED: "TOKEN_EXPIRED",
	INVALID_CREDENTIALS: "INVALID_CREDENTIALS",
	RATE_LIMITED: "RATE_LIMITED",
	QUOTA_EXCEEDED: "QUOTA_EXCEEDED",
	NETWORK_ERROR: "NETWORK_ERROR",
	API_MAINTENANCE: "API_MAINTENANCE",
	VALIDATION_ERROR: "VALIDATION_ERROR",
	UNKNOWN_ERROR: "UNKNOWN_ERROR",
	PARTIAL_FAILURE: "PARTIAL_FAILURE",
} as const;

export type SyncErrorCode = (typeof SyncErrorCode)[keyof typeof SyncErrorCode];

// キュージョブタイプ
export const QueueJobType = {
	SYNC_PROPERTIES: "sync_properties",
} as const;

export type QueueJobType = (typeof QueueJobType)[keyof typeof QueueJobType];

// 同期履歴ステータス
export const SyncHistoryStatus = {
	SUCCESS: "success",
	PARTIAL_SUCCESS: "partial_success",
	FAILED: "failed",
	PROCESSING: "processing",
} as const;

export type SyncHistoryStatus =
	(typeof SyncHistoryStatus)[keyof typeof SyncHistoryStatus];

// ジョブ優先度
export const JobPriority = {
	HIGH: 1,
	NORMAL: 5,
	LOW: 10,
} as const;

export type JobPriority = (typeof JobPriority)[keyof typeof JobPriority];

// エラー詳細
export interface SyncError {
	code: SyncErrorCode;
	message: string;
	detail?: unknown;
	occurredAt: number;
}

// ジョブメタデータ
export interface JobMetadata {
	retryCount?: number;
	lastAttemptAt?: number;
	propertyIds?: number[];
	[key: string]: unknown;
}

// ジョブ結果
export interface JobResult {
	processedItems?: number;
	successCount?: number;
	failedCount?: number;
	errors?: SyncError[];
	[key: string]: unknown;
}
