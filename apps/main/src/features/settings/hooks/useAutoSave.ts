"use client";

import { useCallback, useEffect, useRef, useState } from "react";

export interface UseAutoSaveOptions<T> {
	/** 保存処理を実行する関数 */
	onSave: (value: T) => Promise<void>;
	/** バリデーション関数（オプション） */
	validate?: (value: T) => boolean;
	/** デバウンス時間（ミリ秒） */
	debounceMs?: number;
	/** 保存成功時のコールバック */
	onSuccess?: () => void;
	/** 保存失敗時のコールバック */
	onError?: (error: Error) => void;
}

export interface UseAutoSaveResult {
	/** 保存処理中かどうか */
	isSaving: boolean;
	/** 最後の保存成功状態 */
	isSuccess: boolean;
	/** エラー状態 */
	error: Error | null;
	/** 即座に保存を実行 */
	save: () => Promise<void>;
	/** 保存状態をリセット */
	reset: () => void;
}

/**
 * 自動保存機能を提供するカスタムフック
 * @param value 監視する値
 * @param options 自動保存のオプション
 * @returns 自動保存の状態と制御関数
 */
export function useAutoSave<T>(
	value: T,
	options: UseAutoSaveOptions<T>,
): UseAutoSaveResult {
	const { onSave, validate, debounceMs = 500, onSuccess, onError } = options;

	const [isSaving, setIsSaving] = useState(false);
	const [isSuccess, setIsSuccess] = useState(false);
	const [error, setError] = useState<Error | null>(null);

	const timeoutRef = useRef<NodeJS.Timeout | null>(null);
	const valueRef = useRef<T>(value);
	const isFirstRender = useRef(true);
	const isSavingRef = useRef(false);

	// 値の参照を更新
	useEffect(() => {
		valueRef.current = value;
	}, [value]);

	// 保存処理
	const performSave = useCallback(async () => {
		const currentValue = valueRef.current;

		// バリデーション
		if (validate && !validate(currentValue)) {
			setIsSuccess(false);
			return;
		}

		isSavingRef.current = true;
		setIsSaving(true);
		setError(null);
		setIsSuccess(false);

		try {
			await onSave(currentValue);
			setIsSuccess(true);
			onSuccess?.();
		} catch (err) {
			const error =
				err instanceof Error ? err : new Error("保存に失敗しました");
			setError(error);
			onError?.(error);
		} finally {
			isSavingRef.current = false;
			setIsSaving(false);
		}
	}, [onSave, validate, onSuccess, onError]);

	// 即座に保存を実行
	const save = useCallback(async () => {
		// 既存のタイマーをクリア
		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
			timeoutRef.current = null;
		}

		await performSave();
	}, [performSave]);

	// 状態のリセット
	const reset = useCallback(() => {
		setIsSaving(false);
		setIsSuccess(false);
		setError(null);
		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
			timeoutRef.current = null;
		}
	}, []);

	// 値の変更を監視してデバウンス付き自動保存
	useEffect(() => {
		// 初回レンダリング時はスキップ
		if (isFirstRender.current) {
			isFirstRender.current = false;
			return;
		}

		// 既存のタイマーをクリア
		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
		}

		// 現在保存中の場合は新しいタイマーを設定しない
		if (isSavingRef.current) {
			return;
		}

		// 新しいタイマーを設定
		timeoutRef.current = setTimeout(() => {
			performSave();
		}, debounceMs);

		// クリーンアップ
		return () => {
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}
		};
	}, [value, debounceMs]); // performSaveとisSavingを依存配列から除外

	// コンポーネントのアンマウント時にタイマーをクリア
	useEffect(() => {
		return () => {
			if (timeoutRef.current) {
				clearTimeout(timeoutRef.current);
			}
		};
	}, []);

	return {
		isSaving,
		isSuccess,
		error,
		save,
		reset,
	};
}
