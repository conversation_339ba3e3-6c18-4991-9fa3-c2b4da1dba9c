"use client";

import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { useUserSettings } from "@/features/userSettings";

/**
 * ConvexのuserSettings.themeとnext-themesのcolorModeを同期するフック
 * - userSettingsのthemeが変更されたらnext-themesのthemeを更新
 * - SSR対応のためmounted状態を管理
 * - systemテーマの場合はブラウザの設定に従う
 */
export function useThemeSync() {
	const { userSettings } = useUserSettings();
	const { theme, setTheme, systemTheme } = useTheme();
	const [mounted, setMounted] = useState(false);

	// クライアントサイドでのみ実行
	useEffect(() => {
		setMounted(true);
	}, []);

	// userSettingsのthemeとnext-themesのthemeを同期
	useEffect(() => {
		if (!mounted || !userSettings) return;

		// userSettingsのthemeが設定されている場合、next-themesのthemeを更新
		if (userSettings.theme && userSettings.theme !== theme) {
			setTheme(userSettings.theme);
		}
	}, [mounted, userSettings, theme, setTheme]);

	// デバッグ用のログ（本番環境では削除してください）
	useEffect(() => {
		if (!mounted) return;

		console.log("Theme sync state:", {
			userSettingsTheme: userSettings?.theme,
			nextThemesTheme: theme,
			systemTheme,
			resolvedTheme: theme === "system" ? systemTheme : theme,
		});
	}, [mounted, userSettings?.theme, theme, systemTheme]);

	return {
		mounted,
		currentTheme: theme,
		resolvedTheme: theme === "system" ? systemTheme : theme,
		systemTheme,
	};
}
