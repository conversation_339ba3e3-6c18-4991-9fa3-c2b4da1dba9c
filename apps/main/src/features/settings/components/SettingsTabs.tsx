"use client";

import { Icon, Tabs } from "@chakra-ui/react";
import { Key, Palette } from "lucide-react";
import { ApiIntegrationTab } from "./ApiIntegrationTab";
import { DisplaySettingsTab } from "./DisplaySettingsTab";

/**
 * 設定画面のタブコンポーネント
 *
 * アプリケーションの設定を管理するためのタブ形式のインターフェースを提供します。
 * API連携設定と表示設定の2つのタブで構成されています。
 *
 * @returns 設定タブコンポーネント
 * @example
 * ```tsx
 * // 設定ページでの使用例
 * function SettingsPage() {
 *   return (
 *     <div>
 *       <h1>設定</h1>
 *       <SettingsTabs />
 *     </div>
 *   );
 * }
 * ```
 */
export function SettingsTabs() {
	return (
		<Tabs.Root colorPalette="blue" defaultValue="api">
			<Tabs.List>
				<Tabs.Trigger value="api">
					<Icon>
						<Key />
					</Icon>
					API連携
				</Tabs.Trigger>
				<Tabs.Trigger value="display">
					<Icon>
						<Palette />
					</Icon>
					表示設定
				</Tabs.Trigger>
			</Tabs.List>
			<Tabs.Content value="api">
				<ApiIntegrationTab />
			</Tabs.Content>
			<Tabs.Content value="display">
				<DisplaySettingsTab />
			</Tabs.Content>
		</Tabs.Root>
	);
}
