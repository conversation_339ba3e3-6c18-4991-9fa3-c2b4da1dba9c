"use client";

import { Card, VStack } from "@chakra-ui/react";
import { ThemeSelector } from "./ThemeSelector";

/**
 * 表示設定タブコンポーネント
 *
 * アプリケーションの表示に関する設定を管理するタブです。
 * 現在はテーマ設定のみを含んでいますが、将来的にフォントサイズや
 * レイアウトオプションなどの表示設定を追加できます。
 *
 * @returns {JSX.Element} 表示設定タブのUI要素
 *
 * @example
 * ```tsx
 * // 設定ページ内で使用
 * <TabPanel>
 *   <DisplaySettingsTab />
 * </TabPanel>
 * ```
 */
export function DisplaySettingsTab() {
	return (
		<VStack gap={6} align="stretch">
			{/* テーマ設定セクション */}
			<Card.Root>
				<Card.Header>
					<Card.Title>テーマ設定</Card.Title>
					<Card.Description>
						アプリケーションの表示テーマを選択します
					</Card.Description>
				</Card.Header>
				<Card.Body>
					<ThemeSelector />
				</Card.Body>
			</Card.Root>
		</VStack>
	);
}
