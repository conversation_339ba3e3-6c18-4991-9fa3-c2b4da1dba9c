"use client";

import { Text } from "@chakra-ui/react";
import { createListCollection } from "@chakra-ui/react/collection";
import { Select } from "@chakra-ui/react/select";
import * as Sentry from "@sentry/nextjs";
import { useState } from "react";
import { useUserSettings } from "@/features/userSettings/hooks/useUserSettings";

/**
 * アプリケーションで使用可能なテーマの値
 * @typedef {'light' | 'dark' | 'system'} ThemeValue
 */
type ThemeValue = "light" | "dark" | "system";

/**
 * テーマ選択肢の型定義
 * @interface ThemeOption
 * @property {ThemeValue} value - テーマの値
 * @property {string} label - ユーザーに表示されるラベル
 */
interface ThemeOption {
	value: ThemeValue;
	label: string;
}

/**
 * 利用可能なテーマの選択肢リスト
 * @constant {ThemeOption[]}
 */
const themeOptions: ThemeOption[] = [
	{ value: "light", label: "ライト" },
	{ value: "dark", label: "ダーク" },
	{ value: "system", label: "システム設定に従う" },
];

/**
 * アプリケーションのテーマを選択・変更するためのコンポーネント
 *
 * ユーザーがライト、ダーク、システム設定のいずれかのテーマを選択できるドロップダウンを提供します。
 * テーマの変更はConvexデータベースに保存され、エラー時はSentryに報告されます。
 *
 * @returns {JSX.Element} テーマ選択用のドロップダウンコンポーネント
 *
 * @example
 * ```tsx
 * // 設定ページでの使用例
 * import { ThemeSelector } from '@/features/settings/components/ThemeSelector';
 *
 * function SettingsPage() {
 *   return (
 *     <div>
 *       <h2>テーマ設定</h2>
 *       <ThemeSelector />
 *     </div>
 *   );
 * }
 * ```
 */
export function ThemeSelector() {
	const { userSettings, isLoading, updateTheme } = useUserSettings();
	const [isUpdating, setIsUpdating] = useState(false);
	const [error, setError] = useState<string | null>(null);

	/**
	 * テーマ変更ハンドラー
	 *
	 * 選択されたテーマを検証し、データベースを更新します。
	 * エラーが発生した場合はSentryに報告し、ユーザーにエラーメッセージを表示します。
	 *
	 * @param {Object} details - Select.Rootから渡される選択値の詳細
	 * @param {string[]} details.value - 選択された値の配列（単一選択なので要素は1つ）
	 * @returns {Promise<void>}
	 */
	const handleThemeChange = async (details: { value: string[] }) => {
		const newTheme = details.value[0];
		if (!newTheme) return;

		// 型ガード: 有効なテーマ値かチェック
		if (!["light", "dark", "system"].includes(newTheme)) {
			console.error("無効なテーマ値:", newTheme);
			setError("無効なテーマが選択されました。");
			return;
		}

		setIsUpdating(true);
		setError(null);

		try {
			await updateTheme(newTheme as "light" | "dark" | "system");
		} catch (err) {
			console.error("テーマの更新に失敗しました:", err);

			// Sentryにエラーを報告
			Sentry.captureException(err, {
				tags: {
					component: "ThemeSelector",
					feature: "settings",
					action: "theme_update",
				},
				contexts: {
					theme_update: {
						current_theme: currentTheme,
						new_theme: newTheme,
						user_settings: userSettings
							? {
									has_settings: true,
									theme: userSettings.theme,
								}
							: { has_settings: false },
					},
				},
				level: "error",
			});

			setError("テーマの更新に失敗しました。もう一度お試しください。");
		} finally {
			setIsUpdating(false);
		}
	};

	const currentTheme = userSettings?.theme || "light";
	const collection = createListCollection({ items: themeOptions });

	return (
		<>
			<Select.Root
				collection={collection}
				value={[currentTheme]}
				onValueChange={handleThemeChange}
				disabled={isLoading || isUpdating}
				invalid={!!error}
			>
				<Select.Control>
					<Select.Trigger aria-label="テーマを選択">
						<Select.ValueText>
							{themeOptions.find((opt) => opt.value === currentTheme)?.label ||
								currentTheme}
						</Select.ValueText>
						<Select.Indicator />
					</Select.Trigger>
				</Select.Control>
				<Select.Positioner>
					<Select.Content>
						{themeOptions.map((option) => (
							<Select.Item key={option.value} item={option}>
								<Select.ItemText>{option.label}</Select.ItemText>
							</Select.Item>
						))}
					</Select.Content>
				</Select.Positioner>
			</Select.Root>
			{error && (
				<Text color="fg.error" fontSize="sm" mt={2}>
					{error}
				</Text>
			)}
		</>
	);
}
