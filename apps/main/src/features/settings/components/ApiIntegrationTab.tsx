"use client";

import { Card, Text, VStack } from "@chakra-ui/react";
import { Beds24Settings } from "./Beds24Settings";

/**
 * API連携設定タブコンポーネント
 *
 * 外部サービスとのAPI連携を管理するための設定画面を提供します。
 * 現在はBeds24連携のみ実装されており、将来的に他のサービスとの連携も追加予定です。
 *
 * @returns {JSX.Element} API連携設定のUI
 *
 * @example
 * ```tsx
 * // 設定画面のタブコンテンツとして使用
 * <Tabs.Content value="api">
 *   <ApiIntegrationTab />
 * </Tabs.Content>
 * ```
 */
export function ApiIntegrationTab() {
	return (
		<VStack gap={6} align="stretch">
			{/* Beds24設定セクション */}
			<Card.Root>
				<Card.Header>
					<Card.Title>Beds24連携設定</Card.Title>
					<Card.Description>
						予約管理システムBeds24との連携を設定します
					</Card.Description>
				</Card.Header>
				<Card.Body>
					<Beds24Settings />
				</Card.Body>
			</Card.Root>

			{/* 将来の拡張用セクション */}
			<Card.Root>
				<Card.Header>
					<Card.Title>その他のAPI連携</Card.Title>
					<Card.Description>
						今後追加されるAPI連携がここに表示されます
					</Card.Description>
				</Card.Header>
				<Card.Body>
					<Text color="fg.muted">他のAPI連携オプションは準備中です</Text>
				</Card.Body>
			</Card.Root>
		</VStack>
	);
}
