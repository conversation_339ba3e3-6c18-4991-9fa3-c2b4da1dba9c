"use client";

import {
	Box,
	Field,
	HStack,
	Icon,
	IconButton,
	Input,
	InputGroup,
	Popover,
} from "@chakra-ui/react";
import { AlertCircle, Eye, EyeOff, HelpCircle } from "lucide-react";
import React, { useCallback, useRef, useState } from "react";
import { useAutoSave } from "@/features/settings/hooks/useAutoSave";
import { useUserSettings } from "@/features/userSettings/hooks/useUserSettings";
import { useSafeBreakpointValue } from "@/shared/components/layout/Sidenav/hooks/useSafeBreakpointValue";
import { toastError, toastSuccess } from "@/shared/lib/toast";
import { Tooltip } from "@/src/components/ui/tooltip";

/**
 * Beds24連携設定コンポーネント
 *
 * Beds24 APIとの連携に必要なリフレッシュトークンを管理するための設定フォームを提供します。
 * 自動保存機能により、ユーザーが入力を完了してから1秒後に自動的に保存されます。
 *
 * @returns {JSX.Element} Beds24設定フォームコンポーネント
 *
 * @example
 * ```tsx
 * // ユーザー設定ページ内での使用例
 * function UserSettingsPage() {
 *   return (
 *     <div>
 *       <h2>API連携設定</h2>
 *       <Beds24Settings />
 *     </div>
 *   );
 * }
 * ```
 */
export function Beds24Settings() {
	const { userSettings, updateBeds24Settings, isLoading } = useUserSettings();
	const [showToken, setShowToken] = useState(false);

	// モバイル判定
	const isMobile = useSafeBreakpointValue({ base: true, md: false }, true);

	// サーバーから取得した最新の値
	const serverValue = userSettings?.beds24?.refreshToken || "";

	// ローカル状態を管理（ユーザー入力用）
	const [refreshToken, setRefreshToken] = useState(serverValue);

	// 最後に保存した値を追跡（重複保存防止用）
	const lastSavedValueRef = useRef(serverValue);

	/**
	 * ユーザー編集中フラグ
	 * サーバーからの値の同期を制御するために使用
	 */
	const isUserEditingRef = useRef(false);

	/**
	 * 初期化完了フラグ
	 * 初回レンダリング時の意図しない自動保存を防ぐ
	 */
	const isInitializedRef = useRef(false);

	/**
	 * サーバー値の同期処理
	 * ユーザーが編集中でない場合にのみ、サーバーから取得した値をローカル状態に反映します。
	 * 自分が保存した値による更新は無視します。
	 */
	React.useEffect(() => {
		if (
			!isUserEditingRef.current &&
			serverValue !== lastSavedValueRef.current
		) {
			setRefreshToken(serverValue);
			lastSavedValueRef.current = serverValue;
		}
		// 初期化完了
		if (!isInitializedRef.current && userSettings !== undefined) {
			isInitializedRef.current = true;
		}
	}, [serverValue, userSettings]);

	/**
	 * リフレッシュトークンのバリデーション
	 *
	 * @param {string} token - 検証するトークン文字列
	 * @returns {boolean} トークンが有効な場合はtrue（空文字列または152〜172文字）
	 */
	const isValidToken = (token: string) => {
		return token === "" || (token.length >= 152 && token.length <= 172);
	};

	const hasError = refreshToken !== "" && !isValidToken(refreshToken);

	/**
	 * 入力フィールドの変更ハンドラー
	 *
	 * ユーザーがリフレッシュトークンを入力した際の処理を行います。
	 * 編集中フラグを立てて、2秒後に自動的にリセットします。
	 *
	 * @param {React.ChangeEvent<HTMLInputElement>} e - 入力イベント
	 */
	const handleInputChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			isUserEditingRef.current = true;
			setRefreshToken(e.target.value);
			// 入力完了後、短時間で編集フラグをリセット
			setTimeout(() => {
				isUserEditingRef.current = false;
			}, 2000);
		},
		[],
	);

	/**
	 * 自動保存のセットアップ
	 * リフレッシュトークンの入力が完了してから1秒後に自動的に保存処理を実行します。
	 */
	useAutoSave(refreshToken, {
		/**
		 * 保存処理のコールバック
		 *
		 * @param {string} value - 保存するリフレッシュトークンの値
		 */
		onSave: async (value) => {
			// 初期化が完了していない場合は保存しない
			if (!isInitializedRef.current) {
				return;
			}

			// 既に保存済みの値と同じ場合は保存しない（クライアント側の事前チェック）
			if (value === lastSavedValueRef.current) {
				return;
			}

			const result = await updateBeds24Settings({ refreshToken: value });

			// 実際に更新が行われた場合のみ、最後の保存値を更新しトーストを表示
			if (result.wasUpdated) {
				// 保存成功時に最後の保存値を更新
				lastSavedValueRef.current = value;

				// トーストIDを使用して重複表示を防ぐ
				if (result.wasCreated) {
					toastSuccess(
						"設定を作成しました",
						"Beds24連携の初期設定が完了しました",
						{
							id: "beds24-settings-created",
						},
					);
				} else {
					toastSuccess(
						"保存しました",
						"Beds24のリフレッシュトークンを更新しました",
						{
							id: "beds24-settings-saved",
						},
					);
				}
			}
			// wasUpdatedがfalseの場合（値に変更がない場合）はトーストを表示しない
		},
		/**
		 * バリデーション関数
		 * @param {string} value - 検証する値
		 * @returns {boolean} 有効な場合はtrue
		 */
		validate: (value) => isValidToken(value),
		debounceMs: 1000, // 1秒後に自動保存
		/**
		 * エラー処理コールバック
		 * @param {Error} error - 発生したエラー
		 */
		onError: (error) => {
			// エラートーストも重複防止
			toastError(
				"保存に失敗しました",
				error.message || "もう一度お試しください",
				{
					id: "beds24-settings-error",
				},
			);
		},
	});

	const helpContent = "空欄の場合はBeds24連携が無効になります";

	const helpIcon = (
		<Icon color="fg.muted" boxSize={4}>
			<HelpCircle />
		</Icon>
	);

	return (
		<Box>
			<Field.Root invalid={hasError}>
				<Field.Label>
					<HStack gap={2}>
						<span>Beds24 リフレッシュトークン</span>
						{isMobile ? (
							<Popover.Root>
								<Popover.Trigger asChild>
									<Box cursor="pointer">{helpIcon}</Box>
								</Popover.Trigger>
								<Popover.Positioner>
									<Popover.Content>
										<Popover.Arrow>
											<Popover.ArrowTip />
										</Popover.Arrow>
										<Popover.Body>{helpContent}</Popover.Body>
									</Popover.Content>
								</Popover.Positioner>
							</Popover.Root>
						) : (
							<Tooltip content={helpContent} showArrow>
								{helpIcon}
							</Tooltip>
						)}
					</HStack>
				</Field.Label>
				<InputGroup
					endElement={
						<IconButton
							aria-label={showToken ? "トークンを隠す" : "トークンを表示"}
							onClick={() => setShowToken(!showToken)}
							variant="ghost"
							size="sm"
							disabled={isLoading}
							cursor="pointer"
							touchAction="manipulation"
							userSelect="none"
							WebkitTapHighlightColor="transparent"
							WebkitTouchCallout="none"
						>
							<Icon>{showToken ? <EyeOff /> : <Eye />}</Icon>
						</IconButton>
					}
				>
					<Input
						type={showToken ? "text" : "password"}
						value={refreshToken}
						onChange={handleInputChange}
						placeholder="リフレッシュトークンを入力"
						disabled={isLoading}
					/>
				</InputGroup>
				{hasError && (
					<Field.ErrorText>
						<Icon color="fg.error" mr={2}>
							<AlertCircle />
						</Icon>
						リフレッシュトークンは152〜172文字である必要があります
					</Field.ErrorText>
				)}
			</Field.Root>
		</Box>
	);
}
