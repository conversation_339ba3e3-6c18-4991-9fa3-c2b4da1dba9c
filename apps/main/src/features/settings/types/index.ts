/**
 * Settings feature types
 *
 * Type definitions for the settings feature module
 */

// API Integration types
export interface ApiIntegrationConfig {
	id: string;
	name: string;
	apiKey?: string;
	endpoint?: string;
	isActive: boolean;
}

// Beds24 specific types
export interface Beds24Config {
	apiKey: string;
	propertyId?: string;
	syncEnabled: boolean;
	lastSyncDate?: Date;
}

// General settings types
export interface SettingsTabConfig {
	id: string;
	label: string;
	icon?: React.ReactNode;
	disabled?: boolean;
}
