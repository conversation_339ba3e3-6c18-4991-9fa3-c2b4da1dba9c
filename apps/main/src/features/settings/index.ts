/**
 * Settings feature module
 *
 * This module provides settings management functionality including:
 * - API integration settings
 * - Beds24 integration configuration
 * - Settings tabs navigation
 * - Auto-save functionality for forms
 */

export { ApiIntegrationTab } from "./components/ApiIntegrationTab";
export { Beds24Settings } from "./components/Beds24Settings";
export { DisplaySettingsTab } from "./components/DisplaySettingsTab";
// Components
export { SettingsTabs } from "./components/SettingsTabs";
export type {
	UseAutoSaveOptions,
	UseAutoSaveResult,
} from "./hooks/useAutoSave";
// Hooks
export { useAutoSave } from "./hooks/useAutoSave";

// Types
export type {
	ApiIntegrationConfig,
	Beds24Config,
	SettingsTabConfig,
} from "./types";
