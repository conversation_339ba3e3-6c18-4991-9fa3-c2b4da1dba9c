import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { BookingChannelDistributionChart } from "./BookingChannelDistributionChart";

// 過去12ヶ月のサンプルデータを生成
const generateSampleData = (
	channels?: string[],
	minValue: number = 20,
	maxValue: number = 100,
): {
	month: string;
	[channel: string]: number | string;
}[] => {
	const defaultChannels = [
		"Booking.com",
		"Airbnb",
		"Agoda",
		"Direct",
		"Expedia",
		"その他",
	];
	const targetChannels = channels || defaultChannels;
	const months = [
		"2024年1月",
		"2024年2月",
		"2024年3月",
		"2024年4月",
		"2024年5月",
		"2024年6月",
		"2024年7月",
		"2024年8月",
		"2024年9月",
		"2024年10月",
		"2024年11月",
		"2024年12月",
	];

	return months.map((month) => {
		const data: { month: string; [channel: string]: number | string } = {
			month,
		};
		targetChannels.forEach((channel) => {
			// ランダムな予約数を生成
			const range = maxValue - minValue;
			data[channel] = Math.floor(Math.random() * range) + minValue;
		});
		return data;
	});
};

const meta = {
	title: "Features/Dashboard/BookingChannelDistributionChart",
	component: BookingChannelDistributionChart,
	parameters: {
		layout: "padded",
		docs: {
			description: {
				component:
					"予約チャネル（OTA）の構成比を100%積み上げ棒グラフで表示するコンポーネントです。過去12ヶ月間の推移を可視化します。",
			},
		},
	},
	tags: ["autodocs"],
	argTypes: {
		isLoading: {
			control: "boolean",
			description: "ローディング状態の表示",
		},
		title: {
			control: "text",
			description: "チャートのタイトル",
		},
		description: {
			control: "text",
			description: "チャートの説明文",
		},
		className: {
			control: "text",
			description: "追加のCSSクラス名",
		},
	},
} satisfies Meta<typeof BookingChannelDistributionChart>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基本的な使用例
export const Default: Story = {
	args: {
		data: generateSampleData(),
		channels: ["Booking.com", "Airbnb", "Agoda", "Direct", "Expedia", "その他"],
		isLoading: false,
	},
};

// ローディング状態
export const Loading: Story = {
	args: {
		isLoading: true,
	},
};

// データがない状態
export const NoData: Story = {
	args: {
		data: [],
		channels: [],
		isLoading: false,
	},
};

// カスタムタイトルと説明
export const CustomTitleAndDescription: Story = {
	args: {
		data: generateSampleData(),
		channels: ["Booking.com", "Airbnb", "Agoda", "Direct", "Expedia", "その他"],
		title: "OTA別予約比率の年間推移",
		description: "2024年の予約チャネル別構成比の月次推移を表示しています",
		isLoading: false,
	},
};

// より多くのチャネルを含む例
export const ManyChannels: Story = {
	args: {
		data: generateSampleData(
			[
				"Booking.com",
				"Airbnb",
				"Agoda",
				"Trip",
				"VacationSTAY",
				"Direct",
				"Expedia",
				"Hotels.com",
				"Rakuten Travel",
				"じゃらん",
				"その他",
			],
			10,
			60,
		),
		channels: [
			"Booking.com",
			"Airbnb",
			"Agoda",
			"Trip",
			"VacationSTAY",
			"Direct",
			"Expedia",
			"Hotels.com",
			"Rakuten Travel",
			"じゃらん",
			"その他",
		],
		isLoading: false,
	},
};

// 少ないデータ（3ヶ月分）
export const QuarterlyData: Story = {
	args: {
		data: [
			{
				month: "2024年10月",
				"Booking.com": 45,
				Airbnb: 32,
				Agoda: 28,
				Direct: 15,
				その他: 10,
			},
			{
				month: "2024年11月",
				"Booking.com": 52,
				Airbnb: 28,
				Agoda: 35,
				Direct: 18,
				その他: 12,
			},
			{
				month: "2024年12月",
				"Booking.com": 58,
				Airbnb: 35,
				Agoda: 30,
				Direct: 22,
				その他: 15,
			},
		],
		channels: ["Booking.com", "Airbnb", "Agoda", "Direct", "その他"],
		title: "直近3ヶ月の予約チャネル構成比",
		description: "2024年第4四半期の予約サイト比率推移",
		isLoading: false,
	},
};
