"use client";

import {
	Box,
	Card,
	Grid,
	Skeleton,
	SkeletonText,
	Text,
} from "@chakra-ui/react";
import { memo, useMemo } from "react";

export type PropertyMetric = {
	propertyId: number;
	propertyName: string;
	totalRoomCount: number;
	totalCapacity: number;
	accommodationVariableCost?: number;
	operationFixedCost?: number;
};

export type PropertyMetricsProps = {
	propertyMetrics: PropertyMetric[] | null;
	selectedPropertyId: number | null;
};

/**
 * 金額を読みやすい形式にフォーマット
 *
 * 大きな金額を短縮表記（k、M）を使用して読みやすくします。
 *
 * @param amount - フォーマットする金額
 * @returns フォーマットされた金額文字列
 *
 * @example
 * // 千単位の場合
 * formatCurrency(15000);    // "¥15k"
 * formatCurrency(1500);     // "¥1k"
 *
 * // 百万単位の場合
 * formatCurrency(1500000);  // "¥1.5M"
 * formatCurrency(1000000);  // "¥1M"
 *
 * // 千未満の場合
 * formatCurrency(999);      // "¥999"
 */
const formatCurrency = (amount: number): string => {
	if (amount >= 1000000) {
		return `¥${(amount / 1000000).toFixed(1).replace(/\.0$/, "")}M`;
	}
	if (amount >= 1000) {
		return `¥${Math.floor(amount / 1000)}k`;
	}
	return `¥${amount}`;
};

/**
 * メトリクスアイテムの表示用内部コンポーネント
 *
 * ラベル、値、単位を持つメトリクス情報を統一されたフォーマットで表示します。
 *
 * @param props - コンポーネントのプロパティ
 * @param props.label - メトリクスのラベル（例: "部屋数"、"定員"）
 * @param props.value - 表示する値
 * @param props.unit - 値の単位（オプション。例: "室"、"名"）
 * @returns メトリクスアイテムコンポーネント
 *
 * @example
 * // 単位付きの場合
 * <MetricItem label="部屋数" value={10} unit="室" />
 *
 * // 単位なしの場合（金額など）
 * <MetricItem label="宿泊変動費" value="¥15k" />
 */
type MetricItemProps = {
	label: string;
	value: string | number;
	unit?: string;
};

const MetricItem = memo(function MetricItem({
	label,
	value,
	unit,
}: MetricItemProps) {
	return (
		<Box>
			<Text fontSize="sm" color="fg.muted" mb={1}>
				{label}
			</Text>
			<Text fontSize="2xl" fontWeight="bold" color="fg">
				{value}
				{unit && (
					<Text as="span" fontSize="sm" fontWeight="normal" ml={1}>
						{unit}
					</Text>
				)}
			</Text>
		</Box>
	);
});

/**
 * 宿のメトリクスの計算と表示を担当するコンポーネント
 *
 * 選択された宿の情報、または全ての宿の集計情報を表示します。
 * 部屋数、定員、宿泊変動費、運営固定費などの主要な指標を
 * グリッドレイアウトで視覚的に分かりやすく表示します。
 *
 * @param props - コンポーネントのプロパティ
 * @param props.propertyMetrics - 施設メトリクスデータの配列（nullの場合はローディング表示）
 * @param props.selectedPropertyId - 選択された施設のID（nullの場合は全施設の集計を表示）
 * @returns 施設メトリクス表示コンポーネント
 *
 * @example
 * // 特定の施設を選択して表示
 * const metrics = [
 *   {
 *     propertyId: 1,
 *     propertyName: "ホテル東京",
 *     totalRoomCount: 20,
 *     totalCapacity: 60,
 *     accommodationVariableCost: 500000,
 *     operationFixedCost: 2000000
 *   }
 * ];
 * <PropertyMetrics
 *   propertyMetrics={metrics}
 *   selectedPropertyId={1}
 * />
 *
 * // 全施設の集計を表示
 * <PropertyMetrics
 *   propertyMetrics={metrics}
 *   selectedPropertyId={null}
 * />
 *
 * // ローディング状態
 * <PropertyMetrics
 *   propertyMetrics={null}
 *   selectedPropertyId={null}
 * />
 */
export const PropertyMetrics = memo(function PropertyMetrics({
	propertyMetrics,
	selectedPropertyId = null,
}: PropertyMetricsProps) {
	// 選択された宿の情報
	const selectedProperty = useMemo(
		() =>
			propertyMetrics
				? propertyMetrics.find(
						(property: PropertyMetric) =>
							property.propertyId === selectedPropertyId,
					)
				: null,
		[propertyMetrics, selectedPropertyId],
	);

	// 全ての宿の合計部屋数と定員
	const { totalRoomCount, totalCapacity } = useMemo(() => {
		if (!propertyMetrics?.length) {
			return { totalRoomCount: 0, totalCapacity: 0 };
		}

		return propertyMetrics.reduce(
			(
				acc: { totalRoomCount: number; totalCapacity: number },
				property: PropertyMetric,
			) => ({
				totalRoomCount: acc.totalRoomCount + property.totalRoomCount,
				totalCapacity: acc.totalCapacity + property.totalCapacity,
			}),
			{ totalRoomCount: 0, totalCapacity: 0 },
		);
	}, [propertyMetrics]);

	// 宿情報カードのプロパティ
	const propertyInfoProps = useMemo(
		() =>
			selectedProperty
				? {
						totalRoomCount: selectedProperty.totalRoomCount,
						totalCapacity: selectedProperty.totalCapacity,
						accommodationVariableCost:
							selectedProperty.accommodationVariableCost,
						operationFixedCost: selectedProperty.operationFixedCost,
						description: `${selectedProperty.propertyName}の基本情報`,
					}
				: {
						totalRoomCount,
						totalCapacity,
						accommodationVariableCost: undefined,
						operationFixedCost: undefined,
						description: "全ての宿の集計情報",
					},
		[selectedProperty, totalRoomCount, totalCapacity],
	);

	if (!propertyMetrics) {
		return <PropertyMetricsSkeleton />;
	}

	return (
		<Card.Root width="full">
			<Card.Header>
				<Card.Title fontSize="lg" fontWeight="semibold">
					宿情報
				</Card.Title>
				{propertyInfoProps.description && (
					<Card.Description fontSize="sm" color="fg.muted">
						{propertyInfoProps.description}
					</Card.Description>
				)}
			</Card.Header>
			<Card.Body>
				<Grid
					templateColumns={{ base: "repeat(2, 1fr)", md: "repeat(4, 1fr)" }}
					gap={{ base: 4, md: 6 }}
				>
					<MetricItem
						label="部屋数"
						value={propertyInfoProps.totalRoomCount}
						unit="室"
					/>
					<MetricItem
						label="定員"
						value={propertyInfoProps.totalCapacity}
						unit="名"
					/>
					{propertyInfoProps.accommodationVariableCost !== undefined && (
						<MetricItem
							label="宿泊変動費"
							value={formatCurrency(
								propertyInfoProps.accommodationVariableCost,
							)}
						/>
					)}
					{propertyInfoProps.operationFixedCost !== undefined && (
						<MetricItem
							label="運営固定費"
							value={formatCurrency(propertyInfoProps.operationFixedCost)}
						/>
					)}
				</Grid>
			</Card.Body>
		</Card.Root>
	);
});

/**
 * ローディング状態を表示するスケルトンコンポーネント
 *
 * PropertyMetricsコンポーネントのローディング中に表示されるスケルトンUIです。
 * 実際のレイアウトと同じ構造でプレースホルダーを表示し、
 * ユーザーに読み込み中であることを視覚的に伝えます。
 *
 * @returns ローディングスケルトンコンポーネント
 *
 * @example
 * // 単独で使用する場合
 * <PropertyMetricsSkeleton />
 */
const PropertyMetricsSkeleton = memo(function PropertyMetricsSkeleton() {
	return (
		<Box width="full">
			<Card.Root bg="gray.50">
				<Card.Header>
					<Skeleton height="24px" width="120px" mb={2} />
					<SkeletonText noOfLines={1} width="240px" />
				</Card.Header>
				<Card.Body>
					<Grid
						templateColumns={{ base: "repeat(2, 1fr)", md: "repeat(4, 1fr)" }}
						gap={{ base: 4, md: 6 }}
					>
						{["room-count", "capacity", "variable-cost", "fixed-cost"].map(
							(key) => (
								<Box key={key}>
									<SkeletonText noOfLines={1} width="80px" mb={2} />
									<Skeleton height="32px" width="120px" />
								</Box>
							),
						)}
					</Grid>
				</Card.Body>
			</Card.Root>
		</Box>
	);
});
