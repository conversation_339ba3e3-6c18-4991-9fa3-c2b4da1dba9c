import type { Meta, StoryObj } from "@storybook/react";
import { type PropertyMetric, PropertyMetrics } from "./PropertyMetrics";

// サンプルデータの生成
const generateSampleProperties = (count: number = 3): PropertyMetric[] => {
	const propertyNames = [
		"ホテル東京",
		"旅館京都",
		"リゾート沖縄",
		"ビジネスホテル大阪",
		"温泉旅館箱根",
		"シティホテル横浜",
		"民宿北海道",
		"ゲストハウス福岡",
	];

	return Array.from({ length: count }, (_, index) => ({
		propertyId: index + 1,
		propertyName: propertyNames[index % propertyNames.length],
		totalRoomCount: Math.floor(Math.random() * 30) + 10,
		totalCapacity: Math.floor(Math.random() * 100) + 50,
		accommodationVariableCost: Math.floor(Math.random() * 200000) + 50000,
		operationFixedCost: Math.floor(Math.random() * 500000) + 200000,
	}));
};

const meta = {
	title: "Features/Dashboard/PropertyMetrics",
	component: PropertyMetrics,
	parameters: {
		layout: "padded",
		docs: {
			description: {
				component:
					"宿のメトリクスの計算と表示を担当する単一コンポーネント。選択された宿の情報、または全ての宿の集計情報をカード形式で表示します。",
			},
		},
	},
	tags: ["autodocs"],
	argTypes: {
		propertyMetrics: {
			description: "宿データの配列",
		},
		selectedPropertyId: {
			control: "number",
			description: "選択された宿のID",
		},
	},
} satisfies Meta<typeof PropertyMetrics>;

export default meta;
type Story = StoryObj<typeof meta>;

// 基本的な使用例（全ての宿の集計）
export const Default: Story = {
	args: {
		propertyMetrics: generateSampleProperties(3),
		selectedPropertyId: null,
	},
};

// 特定の宿が選択された状態
export const SelectedProperty: Story = {
	args: {
		propertyMetrics: [
			{
				propertyId: 1,
				propertyName: "ホテル東京",
				totalRoomCount: 24,
				totalCapacity: 120,
				accommodationVariableCost: 150000,
				operationFixedCost: 500000,
			},
			{
				propertyId: 2,
				propertyName: "旅館京都",
				totalRoomCount: 18,
				totalCapacity: 90,
				accommodationVariableCost: 120000,
				operationFixedCost: 400000,
			},
			{
				propertyId: 3,
				propertyName: "リゾート沖縄",
				totalRoomCount: 30,
				totalCapacity: 150,
				accommodationVariableCost: 200000,
				operationFixedCost: 600000,
			},
		],
		selectedPropertyId: 2,
	},
};

// ローディング状態
export const Loading: Story = {
	args: {
		propertyMetrics: null,
		selectedPropertyId: null,
	},
};

// 単一の宿のみ
export const SingleProperty: Story = {
	args: {
		propertyMetrics: [
			{
				propertyId: 1,
				propertyName: "ホテル東京",
				totalRoomCount: 24,
				totalCapacity: 120,
				accommodationVariableCost: 150000,
				operationFixedCost: 500000,
			},
		],
		selectedPropertyId: 1,
	},
};

// 多数の宿
export const ManyProperties: Story = {
	args: {
		propertyMetrics: generateSampleProperties(8),
		selectedPropertyId: null,
	},
};

// 空のデータ
export const EmptyData: Story = {
	args: {
		propertyMetrics: [],
		selectedPropertyId: null,
	},
};

// 大規模な宿グループ
export const LargePropertyGroup: Story = {
	args: {
		propertyMetrics: [
			{
				propertyId: 1,
				propertyName: "大型ホテルチェーンA",
				totalRoomCount: 200,
				totalCapacity: 1000,
				accommodationVariableCost: 5000000,
				operationFixedCost: 10000000,
			},
			{
				propertyId: 2,
				propertyName: "大型ホテルチェーンB",
				totalRoomCount: 150,
				totalCapacity: 750,
				accommodationVariableCost: 3750000,
				operationFixedCost: 7500000,
			},
			{
				propertyId: 3,
				propertyName: "大型ホテルチェーンC",
				totalRoomCount: 180,
				totalCapacity: 900,
				accommodationVariableCost: 4500000,
				operationFixedCost: 9000000,
			},
		],
		selectedPropertyId: null,
	},
};
