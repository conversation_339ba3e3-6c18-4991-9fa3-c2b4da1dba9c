"use client";

import { Chart, useChart } from "@chakra-ui/charts";
import { Card, Skeleton, SkeletonText, Text } from "@chakra-ui/react";
import {
	<PERSON>,
	Bar<PERSON>hart,
	CartesianGrid,
	Legend,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	YAxis,
} from "recharts";

/**
 * チャネル名とOTAブランドカラーセマンティックトークンのマッピング
 *
 * 各OTAプラットフォームには専用のブランドカラーが定義されています。
 * OTA以外のチャネル（Direct、その他）には汎用のチャートカラーを使用します。
 */
const CHANNEL_COLOR_MAPPING: Record<string, string> = {
	"Booking.com": "ota.booking",
	Airbnb: "ota.airbnb",
	Agoda: "ota.agoda",
	Expedia: "ota.expedia",
	Trivago: "ota.trivago",
	// 以下、その他のOTAプラットフォーム（必要に応じて追加）
	"Hotels.com": "ota.expedia", // Expediaグループなので同じ色を使用
	// OTA以外のチャネル用の色
	Direct: "blue.400",
	その他: "purple.400",
	"Rakuten Travel": "chart.2", // purple
	じゃらん: "chart.3", // pink
	Trip: "chart.5", // green
	VacationSTAY: "chart.6", // yellow
};

export interface BookingChannelDistributionChartProps {
	data?: {
		month: string;
		[channel: string]: number | string;
	}[];
	channels?: string[];
	className?: string;
	title?: string;
	description?: string;
	isLoading: boolean;
}

/**
 * 予約チャネル分布チャートコンポーネント
 *
 * 月別の予約チャネル構成比を100%積み上げ棒グラフで視覚化します。
 * 各チャネルには専用のブランドカラーが設定され、ツールチップで
 * 詳細情報（件数と割合）を確認できます。
 *
 * @param props - コンポーネントのプロパティ
 * @param props.data - 月別のチャネル別予約数データ
 * @param props.channels - 表示するチャネルの配列
 * @param props.className - カスタムCSSクラス名
 * @param props.title - チャートのタイトル（デフォルト: "予約チャネル構成比推移"）
 * @param props.description - チャートの説明（デフォルト: "過去12ヶ月間の予約サイト比率推移"）
 * @param props.isLoading - ローディング状態かどうか
 * @returns 予約チャネル分布チャートコンポーネント
 *
 * @example
 * // 基本的な使用例
 * const data = [
 *   { month: "2025/01", "Booking.com": 50, "Agoda": 30, "Direct": 20 },
 *   { month: "2025/02", "Booking.com": 45, "Agoda": 35, "Direct": 20 }
 * ];
 * const channels = ["Booking.com", "Agoda", "Direct"];
 *
 * <BookingChannelDistributionChart
 *   data={data}
 *   channels={channels}
 *   title="2025年の予約チャネル推移"
 *   isLoading={false}
 * />
 *
 * // ローディング状態
 * <BookingChannelDistributionChart isLoading={true} />
 */
export function BookingChannelDistributionChart({
	data = [],
	channels = [],
	className,
	title = "予約チャネル構成比推移",
	description = "過去12ヶ月間の予約サイト比率推移", // 12ヶ月に変更
	isLoading = false,
}: BookingChannelDistributionChartProps) {
	// 100%積み上げ棒グラフ用のデータに変換
	const normalizedData = data.map((monthData) => {
		const { month, ...channelData } = monthData;
		const total = Object.values(channelData).reduce<number>(
			(sum, val) => sum + (typeof val === "number" ? val : 0),
			0,
		);

		// 各チャネルの値を100%基準のパーセンテージに変換
		const normalizedChannels: Record<string, number | string> = { month };
		if (total > 0) {
			for (const [channel, value] of Object.entries(channelData)) {
				if (typeof value === "number") {
					normalizedChannels[channel] = (value / total) * 100;
				}
			}
		}
		return normalizedChannels;
	});

	// Chakra UI Chartsフックの設定
	const chart = useChart({
		data: normalizedData,
		series: channels.map((channel, index) => ({
			name: channel,
			dataKey: channel,
			// セマンティックトークンを直接指定
			color: CHANNEL_COLOR_MAPPING[channel] || `chart.${index % 9}`,
		})),
	});

	// カスタムツールチップ
	const CustomTooltip = ({
		active,
		payload,
		label,
	}: {
		active?: boolean;
		payload?: Array<{
			dataKey: string;
			value: number;
			color: string;
		}>;
		label?: string;
	}) => {
		if (active && payload && payload.length) {
			const monthData = data.find((d) => d.month === label);
			if (!monthData) return null;

			return (
				<Card.Root bg="bg" borderWidth="1px" shadow="sm" p={3} fontSize="sm">
					<Text fontWeight="medium" mb={2}>
						{label}
					</Text>
					<Card.Body gap={1}>
						{payload.map((entry) => {
							const count = monthData[entry.dataKey] as number;
							const percentage = entry.value as number;
							return (
								<div
									key={entry.dataKey}
									style={{
										display: "flex",
										justifyContent: "space-between",
										gap: "1rem",
									}}
								>
									<span style={{ color: entry.color }}>{entry.dataKey}</span>
									<span>
										{count}件 ({percentage.toFixed(1)}%)
									</span>
								</div>
							);
						})}
					</Card.Body>
				</Card.Root>
			);
		}
		return null;
	};

	return (
		<Card.Root className={className}>
			<Card.Header>
				<Card.Title>
					{isLoading ? <Skeleton width="200px" height="24px" /> : title}
				</Card.Title>
				{description && (
					<Card.Description>
						{isLoading ? <SkeletonText /> : description}
					</Card.Description>
				)}
			</Card.Header>
			<Card.Body>
				{isLoading ? (
					<Skeleton height="300px" />
				) : normalizedData.length === 0 ? (
					<div
						style={{
							display: "flex",
							minHeight: "300px",
							alignItems: "center",
							justifyContent: "center",
							color: "var(--chakra-colors-fg-muted)",
						}}
					>
						予約データがありません
					</div>
				) : (
					<Chart.Root chart={chart}>
						<ResponsiveContainer width="100%" height={300}>
							<BarChart
								data={chart.data}
								margin={{ top: 20, right: 30, bottom: 20, left: 30 }}
							>
								<CartesianGrid strokeDasharray="3 3" />
								<XAxis dataKey="month" />
								<YAxis
									tickFormatter={(value) => `${Math.round(value)}%`}
									domain={[0, 100]}
									ticks={[0, 25, 50, 75, 100]}
								/>
								<Tooltip content={<CustomTooltip />} />
								<Legend />
								{channels.map((channel, index) => {
									const colorToken =
										CHANNEL_COLOR_MAPPING[channel] || `chart.${index % 9}`;
									return (
										<Bar
											key={channel}
											dataKey={channel}
											stackId="a"
											fill={chart.color(colorToken)}
										/>
									);
								})}
							</BarChart>
						</ResponsiveContainer>
					</Chart.Root>
				)}
			</Card.Body>
		</Card.Root>
	);
}
