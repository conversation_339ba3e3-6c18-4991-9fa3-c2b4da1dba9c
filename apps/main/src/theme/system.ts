import { createSystem, defaultConfig, defineConfig } from "@chakra-ui/react";

// カスタム設定の定義
const customConfig = defineConfig({
	theme: {
		semanticTokens: {
			colors: {
				chart: {
					0: { value: "{colors.blue.400}" },
					1: { value: "{colors.cyan.400}" },
					2: { value: "{colors.purple.400}" },
					3: { value: "{colors.pink.400}" },
					4: { value: "{colors.green.400}" },
					5: { value: "{colors.red.400}" },
					6: { value: "{colors.yellow.400}" },
					7: { value: "{colors.teal.400}" },
					8: { value: "{colors.orange.400}" },
				},
				// キーは otaMaster:shortName と一致
				ota: {
					agoda: { value: "{colors.teal.600}" },
					airbnb: { value: "{colors.pink.600}" },
					booking: { value: "{colors.blue.700}" },
					expedia: { value: "{colors.yellow.500}" },
					trivago: { value: "{colors.orange.500}" },
				},
			},
		},
	},
	globalCss: {
		":root": {
			// セーフエリア変数の定義
			"--safe-area-bottom": "env(safe-area-inset-bottom, 0)",
			"--safe-area-top": "env(safe-area-inset-top, 0)",
			"--safe-area-left": "env(safe-area-inset-left, 0)",
			"--safe-area-right": "env(safe-area-inset-right, 0)",
		},
		html: {
			// iOS Safariでの100vhの問題を修正
			height: "100%",
		},
		body: {
			height: "100%",
		},
	},
});

export const system = createSystem(defaultConfig, customConfig);
