import * as Sentry from "@sentry/nextjs";
import { ConvexError } from "convex/values";
import type { ErrorCode, ErrorData } from "@/convex/lib/errors";
import { toastError } from "@/shared/lib/toast";

/**
 * ConvexErrorを処理してユーザーフレンドリーなメッセージを表示
 */
export function handleConvexError(
	error: unknown,
	defaultMessage = "エラーが発生しました",
): void {
	console.error("Convex Error:", error);

	// Sentryにエラー情報を送信
	Sentry.captureException(error, {
		tags: {
			error_source: "convex",
		},
	});

	// ConvexErrorの場合、構造化されたエラー情報を取得
	if (error instanceof ConvexError) {
		const errorData = error.data as ErrorData;

		// Sentryに追加のコンテキストを送信
		Sentry.setContext("convex_error", {
			code: errorData.code,
			details: errorData.details,
			context: errorData.context,
			timestamp: new Date(errorData.timestamp).toISOString(),
		});

		// エラーコードに応じたメッセージ表示
		const userMessage = getUserFriendlyMessage(
			errorData.code,
			errorData.message,
		);

		toastError("エラー", userMessage, { duration: 5000 });

		// 開発環境では詳細情報も表示
		if (process.env.NODE_ENV === "development") {
			console.group("Convex Error Details");
			console.log("Code:", errorData.code);
			console.log("Message:", errorData.message);
			console.log("Details:", errorData.details);
			console.log("Context:", errorData.context);
			console.log("Timestamp:", new Date(errorData.timestamp));
			console.groupEnd();
		}
	} else {
		// 通常のエラーの場合
		toastError("エラー", defaultMessage, { duration: 5000 });
	}
}

/**
 * エラーコードに基づいてユーザーフレンドリーなメッセージを返す
 */
function getUserFriendlyMessage(
	code: ErrorCode,
	originalMessage: string,
): string {
	const messageMap: Partial<Record<ErrorCode, string>> = {
		UNAUTHORIZED: "認証が必要です。ログインしてください。",
		INVALID_TOKEN: "認証情報が無効です。再度ログインしてください。",
		TOKEN_EXPIRED: "認証情報の有効期限が切れました。再度ログインしてください。",
		NOT_FOUND: "データが見つかりませんでした。",
		ALREADY_EXISTS: "すでに存在するデータです。",
		VALIDATION_ERROR: "入力内容に誤りがあります。",
		INVALID_REQUEST: "リクエストが無効です。",
		API_ERROR: "外部サービスとの通信でエラーが発生しました。",
		NETWORK_ERROR: "ネットワークエラーが発生しました。接続を確認してください。",
		RATE_LIMIT_EXCEEDED:
			"リクエストが多すぎます。しばらく待ってから再試行してください。",
		INTERNAL_ERROR: "システムエラーが発生しました。",
		SERVICE_UNAVAILABLE: "サービスが一時的に利用できません。",
		// レビュー同期関連のエラー
		REVIEW_SYNC_FAILED: "レビューの同期に失敗しました。",
		REVIEW_BATCH_FAILED: "レビューの一括処理に失敗しました。",
		INVALID_OTA_TYPE: "無効なOTAタイプです。",
		SCRAPING_FAILED:
			"データの取得に失敗しました。しばらくしてから再試行してください。",
	};

	return messageMap[code] || originalMessage;
}

/**
 * Convexクエリ/ミューテーションのローディング状態を管理するヘルパー
 */
export function withErrorHandling<T extends (...args: any[]) => any>(
	fn: T,
	options?: {
		defaultMessage?: string;
		onSuccess?: () => void;
		onError?: (error: unknown) => void;
	},
): T {
	return (async (...args: Parameters<T>) => {
		try {
			const result = await fn(...args);
			options?.onSuccess?.();
			return result;
		} catch (error) {
			handleConvexError(error, options?.defaultMessage);
			options?.onError?.(error);
			throw error;
		}
	}) as T;
}
