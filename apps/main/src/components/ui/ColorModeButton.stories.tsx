import { Box, Heading, HStack, Text, VStack } from "@chakra-ui/react";
import type { Meta, StoryObj } from "@storybook/react";
import {
	ColorModeButton,
	DarkMode,
	LightMode,
	useColorMode,
} from "./color-mode";

const meta: Meta<typeof ColorModeButton> = {
	title: "UI/ColorModeButton",
	component: ColorModeButton,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		size: {
			control: { type: "select" },
			options: ["xs", "sm", "md", "lg"],
			description: "ボタンのサイズ",
		},
		variant: {
			control: { type: "select" },
			options: ["ghost", "outline", "solid", "subtle"],
			description: "ボタンのバリアント",
		},
	},
};

export default meta;
type Story = StoryObj<typeof meta>;

// デフォルトのカラーモードボタン
export const Default: Story = {
	args: {},
};

// サイズバリエーション
export const Sizes: Story = {
	render: () => (
		<HStack gap={4}>
			<ColorModeButton size="xs" />
			<ColorModeButton size="sm" />
			<ColorModeButton size="md" />
			<ColorModeButton size="lg" />
		</HStack>
	),
};

// バリアントバリエーション
export const Variants: Story = {
	render: () => (
		<HStack gap={4}>
			<ColorModeButton variant="ghost" />
			<ColorModeButton variant="outline" />
			<ColorModeButton variant="solid" />
			<ColorModeButton variant="subtle" />
		</HStack>
	),
};

// 使用例：実際のアプリケーションコンテキスト
const ColorModeDemo = () => {
	const { colorMode } = useColorMode();

	return (
		<VStack gap={4} align="stretch" p={6} borderRadius="md" bg="bg.subtle">
			<HStack justify="space-between">
				<Heading size="md">設定</Heading>
				<ColorModeButton />
			</HStack>
			<Text>
				現在のカラーモード:{" "}
				<strong>{colorMode === "dark" ? "ダーク" : "ライト"}</strong>
			</Text>
			<Box p={4} bg="bg" borderRadius="md">
				<Text>
					このボックスはカラーモードに応じて背景色が変わります。
					ボタンをクリックしてモードを切り替えてみてください。
				</Text>
			</Box>
		</VStack>
	);
};

export const InContext: Story = {
	render: () => <ColorModeDemo />,
};

// 強制的なライトモード・ダークモード表示
export const ForcedModes: Story = {
	render: () => (
		<VStack gap={4}>
			<Box p={4} bg="gray.100" borderRadius="md">
				<LightMode>
					<HStack justify="space-between">
						<Text color="fg">常にライトモードで表示</Text>
						<ColorModeButton />
					</HStack>
				</LightMode>
			</Box>
			<Box p={4} bg="gray.800" borderRadius="md">
				<DarkMode>
					<HStack justify="space-between">
						<Text color="fg">常にダークモードで表示</Text>
						<ColorModeButton />
					</HStack>
				</DarkMode>
			</Box>
		</VStack>
	),
};

// アクセシビリティ確認
export const Accessibility: Story = {
	parameters: {
		a11y: {
			config: {
				rules: [
					{
						// カラーコントラストのチェックを有効化
						id: "color-contrast",
						enabled: true,
					},
				],
			},
		},
	},
	render: () => (
		<VStack gap={4}>
			<Text>アクセシビリティテスト用のカラーモードボタン</Text>
			<ColorModeButton />
			<Text fontSize="sm" color="fg.muted">
				aria-label属性が正しく設定されていることを確認
			</Text>
		</VStack>
	),
};
