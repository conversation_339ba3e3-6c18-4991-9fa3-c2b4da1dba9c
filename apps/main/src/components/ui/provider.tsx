"use client";

import { <PERSON>kraProvider } from "@chakra-ui/react";
import { system } from "../../theme/system";
import { ColorModeProvider, type ColorModeProviderProps } from "./color-mode";

/**
 * Providerコンポーネント
 *
 * アプリケーション全体をChakra UIのテーマプロバイダーとカラーモードプロバイダーでラップします。
 * このコンポーネントは、アプリケーションのルートレベルで使用され、
 * Chakra UIのスタイリングシステムとカラーモード機能を子コンポーネントで利用可能にします。
 *
 * @param {ColorModeProviderProps} props - カラーモードプロバイダーに渡されるプロパティ
 * @param {React.ReactNode} props.children - ラップされる子コンポーネント
 * @param {boolean} [props.disableTransitionOnChange] - カラーモード変更時のトランジションを無効化するかどうか
 *
 * @example
 * ```tsx
 * // app/layout.tsx
 * import { Provider } from '@/components/ui/provider'
 *
 * export default function RootLayout({ children }: { children: React.ReactNode }) {
 *   return (
 *     <html lang="ja">
 *       <body>
 *         <Provider>
 *           {children}
 *         </Provider>
 *       </body>
 *     </html>
 *   )
 * }
 * ```
 *
 * @remarks
 * このコンポーネントは、カスタムテーマシステム（`system`）に依存しています。
 * テーマは `../../theme/system` からインポートされ、Chakra UIのデフォルトテーマを拡張しています。
 *
 * @see {@link ColorModeProvider} - カラーモード管理の詳細
 * @see {@link system} - カスタムテーマ設定
 */
export function Provider(props: ColorModeProviderProps) {
	return (
		<ChakraProvider value={system}>
			<ColorModeProvider {...props} />
		</ChakraProvider>
	);
}
