"use client";

import { <PERSON>, But<PERSON>, Heading, Icon, Text, VStack } from "@chakra-ui/react";
import * as Sentry from "@sentry/nextjs";
import { AlertTriangle } from "lucide-react";
import type { ReactNode } from "react";
import { ErrorBoundary } from "@/shared/components/ErrorBoundary";

interface SimpleErrorBoundaryConfig {
	featureName: string;
	errorType: string;
	title: string;
	description: string;
	actionLabel?: string;
	onAction?: () => void;
	colorScheme?: {
		bg: string;
		borderColor: string;
		iconColor: string;
	};
}

/**
 * シンプルなErrorBoundaryファクトリ関数
 * Chakra UIを使用した軽量実装
 */
export function createSimpleErrorBoundary({
	featureName,
	errorType,
	title,
	description,
	actionLabel = "再試行",
	onAction,
	colorScheme = {
		bg: "bg.error.subtle",
		borderColor: "border.error",
		iconColor: "fg.error",
	},
}: SimpleErrorBoundaryConfig) {
	return function FeatureErrorBoundary({ children }: { children: ReactNode }) {
		const handleError = (error: Error) => {
			// Sentryへのレポート
			Sentry.withScope((scope) => {
				scope.setTag("feature", featureName);
				scope.setContext("errorBoundary", {
					featureName,
					errorType,
				});
				Sentry.captureException(error);
			});
		};

		return (
			<ErrorBoundary
				errorType={errorType}
				onError={handleError}
				fallback={
					<VStack
						gap={4}
						p={8}
						bg={colorScheme.bg}
						borderWidth={1}
						borderColor={colorScheme.borderColor}
						borderRadius="lg"
						align="center"
					>
						<Icon boxSize={12} color={colorScheme.iconColor}>
							<AlertTriangle />
						</Icon>
						<Heading as="h2" size="md" color="fg">
							{title}
						</Heading>
						<Text fontSize="sm" color="fg.muted" textAlign="center">
							{description.split("\n").map((line, index, lines) => {
								// Use content hash to create stable key
								const stableKey = `${line.substring(0, 20)}-${line.length}-${index}`;
								return (
									<Box as="span" key={stableKey}>
										{line}
										{index < lines.length - 1 && <br />}
									</Box>
								);
							})}
						</Text>
						{onAction && (
							<Button onClick={onAction} colorScheme="blue" size="md">
								{actionLabel}
							</Button>
						)}
					</VStack>
				}
			>
				{children}
			</ErrorBoundary>
		);
	};
}
