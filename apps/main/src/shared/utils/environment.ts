/**
 * 環境検出ユーティリティ
 * SSR/CSRの判定とブラウザ環境の検出を行う
 */

/**
 * クライアントサイドかどうかを判定
 */
export const isClient = typeof window !== "undefined";

/**
 * サーバーサイドかどうかを判定
 */
export const isServer = !isClient;

/**
 * 開発環境かどうかを判定
 */
export const isDev = process.env.NODE_ENV === "development";

/**
 * 本番環境かどうかを判定
 */
export const isProd = process.env.NODE_ENV === "production";

/**
 * Next.jsのサーバーデータ型定義
 */
interface NextData {
	isPreview?: boolean;
}

/**
 * Next.js拡張Window型
 */
type NextWindow = Window & {
	__NEXT_DATA__?: NextData;
};

/**
 * SSR中かどうかを判定
 * Next.jsのハイドレーション完了前を検出
 * @returns {boolean} SSR中の場合はtrue、クライアントサイドの場合はfalse
 * @example
 * if (isSSR()) {
 *   // SSR時の処理
 * } else {
 *   // クライアントサイドの処理
 * }
 */
export const isSSR = (): boolean => {
	return isServer || !!(window as NextWindow).__NEXT_DATA__?.isPreview;
};

/**
 * ブラウザがmatchMediaをサポートしているかチェック
 * @returns {boolean} matchMediaがサポートされている場合はtrue
 * @example
 * if (supportsMatchMedia()) {
 *   const isMobile = window.matchMedia('(max-width: 768px)').matches;
 * }
 */
export const supportsMatchMedia = (): boolean => {
	return isClient && "matchMedia" in window;
};

/**
 * 安全にwindowオブジェクトにアクセスするためのゲッター
 * @returns {Window | undefined} クライアントサイドの場合はwindowオブジェクト、サーバーサイドの場合はundefined
 * @example
 * const win = getWindow();
 * if (win) {
 *   win.scrollTo(0, 0);
 * }
 */
export const getWindow = (): Window | undefined => {
	if (isClient) {
		return window;
	}
	return undefined;
};

/**
 * 安全にdocumentオブジェクトにアクセスするためのゲッター
 * @returns {Document | undefined} クライアントサイドの場合はdocumentオブジェクト、サーバーサイドの場合はundefined
 * @example
 * const doc = getDocument();
 * if (doc) {
 *   const element = doc.getElementById('app');
 * }
 */
export const getDocument = (): Document | undefined => {
	if (isClient) {
		return document;
	}
	return undefined;
};
