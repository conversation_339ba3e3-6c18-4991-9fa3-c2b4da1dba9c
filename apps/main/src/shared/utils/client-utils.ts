/**
 * クライアントサイドでのみ実行する処理のためのユーティリティ
 */

import { handleError } from "@/shared/lib/error-handling";

/**
 * ブラウザ環境かどうかをチェック
 */
export const isBrowser = (): boolean => {
	return (
		typeof window !== "undefined" && typeof window.document !== "undefined"
	);
};

/**
 * デバウンス処理を適用した関数を返す
 * @param func 実行する関数
 * @param delay 遅延時間（ミリ秒）
 * @returns デバウンス処理が適用された関数。最後の呼び出しから指定時間経過後に元の関数を実行する
 * @example
 * const debouncedSearch = debounce((query: string) => {
 *   console.log('検索:', query);
 * }, 500);
 *
 * debouncedSearch('React'); // 500ms後に実行される
 */
export const debounce = <T extends (...args: any[]) => any>(
	func: T,
	delay: number,
): ((...args: Parameters<T>) => void) => {
	let timeoutId: NodeJS.Timeout | null = null;

	return (...args: Parameters<T>) => {
		if (timeoutId) {
			clearTimeout(timeoutId);
		}
		timeoutId = setTimeout(() => {
			func(...args);
		}, delay);
	};
};

/**
 * クリーンアップ関数を管理するヘルパー
 */
export class CleanupManager {
	private cleanupFns: Set<() => void> = new Set();

	/**
	 * クリーンアップ関数を登録する
	 * @param fn 登録するクリーンアップ関数。cleanup()メソッド実行時に呼び出される
	 * @example
	 * const manager = new CleanupManager();
	 * manager.add(() => clearInterval(intervalId));
	 * manager.add(() => element.removeEventListener('click', handler));
	 */
	add(fn: () => void): void {
		this.cleanupFns.add(fn);
	}

	/**
	 * 登録されたすべてのクリーンアップ関数を実行し、管理リストをクリアする
	 * エラーが発生しても他のクリーンアップ関数の実行を継続し、
	 * すべてのエラーをまとめてSentryに報告する
	 * @example
	 * const manager = new CleanupManager();
	 * // クリーンアップ関数を登録
	 * manager.add(() => subscription.unsubscribe());
	 * manager.add(() => timer.cancel());
	 *
	 * // コンポーネントのアンマウント時などに実行
	 * manager.cleanup(); // すべてのクリーンアップ関数が実行される
	 */
	cleanup(): void {
		const errors: Array<{ error: unknown; index: number }> = [];

		let index = 0;
		this.cleanupFns.forEach((fn) => {
			try {
				fn();
			} catch (error) {
				console.error(`Cleanup function ${index} failed:`, error);
				errors.push({ error, index });
			}
			index++;
		});

		// エラーをSentryに送信
		if (errors.length > 0) {
			handleError(new Error(`${errors.length} cleanup functions failed`), {
				failedCount: errors.length,
				totalCount: this.cleanupFns.size,
				errors: errors.map(({ error, index }) => ({
					index,
					message: error instanceof Error ? error.message : String(error),
					stack: error instanceof Error ? error.stack : undefined,
				})),
				feature: "CleanupManager",
			});
		}

		this.cleanupFns.clear();
	}
}
