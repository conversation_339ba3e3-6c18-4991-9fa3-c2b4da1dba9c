"use client";

import {
	Book,
	CalendarRange,
	Hash,
	List,
	MessageSquareHeart,
	Plus,
	Settings,
} from "lucide-react";
import type { ReactElement } from "react";
import { memo } from "react";
import type { NavItem } from "../components/layout/Sidenav/types";
import type { NavItemData } from "../config/navigation-data";

// アイコンコンポーネントを事前に生成してメモ化
const MemoizedIcons = {
	CalendarRange: memo(() => <CalendarRange />),
	Book: memo(() => <Book />),
	Hash: memo(() => <Hash />),
	List: memo(() => <List />),
	MessageSquareHeart: memo(() => <MessageSquareHeart />),
	Plus: memo(() => <Plus />),
	Settings: memo(() => <Settings />),
} as const;

/**
 * アイコンマッピング定義
 * 文字列からReactコンポーネントへのマッピング
 * 静的に生成されたメモ化されたコンポーネントを使用
 */
const iconMap: Record<string, ReactElement> = {
	CalendarRange: <MemoizedIcons.CalendarRange />,
	Book: <MemoizedIcons.Book />,
	Hash: <MemoizedIcons.Hash />,
	List: <MemoizedIcons.List />,
	MessageSquareHeart: <MemoizedIcons.MessageSquareHeart />,
	Plus: <MemoizedIcons.Plus />,
	Settings: <MemoizedIcons.Settings />,
};

/**
 * アイコン名から実際のReactコンポーネントを解決する
 * @param iconName アイコン名
 * @returns Reactコンポーネント または null
 */
export function resolveIcon(iconName?: string): ReactElement | null {
	if (!iconName) return null;
	return iconMap[iconName] || null;
}

/**
 * ナビゲーションデータにアイコンコンポーネントを追加する
 * @param items ナビゲーションアイテム（アイコン名付き）
 * @returns アイコンコンポーネント付きのナビゲーションアイテム
 */
export function resolveNavigationIcons(items: NavItemData[]): NavItem[] {
	return items.map((item) => ({
		...item,
		icon: resolveIcon(item.iconName),
		children: item.children ? resolveNavigationIcons(item.children) : undefined,
	}));
}
