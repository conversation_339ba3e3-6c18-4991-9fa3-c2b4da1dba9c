export class AppError extends Error {
	constructor(
		message: string,
		public code: string,
		public statusCode?: number,
		public details?: unknown,
	) {
		super(message);
		this.name = "AppError";
		Object.setPrototypeOf(this, AppError.prototype);
	}
}

export class AuthError extends AppError {
	constructor(message: string, details?: unknown) {
		super(message, "AUTH_ERROR", 401, details);
		this.name = "AuthError";
	}
}

export class ValidationError extends AppError {
	constructor(message: string, details?: unknown) {
		super(message, "VALIDATION_ERROR", 400, details);
		this.name = "ValidationError";
	}
}

export class NetworkError extends AppError {
	constructor(message: string, details?: unknown) {
		super(message, "NETWORK_ERROR", 0, details);
		this.name = "NetworkError";
	}
}

export class ConvexError extends AppError {
	constructor(message: string, details?: unknown) {
		super(message, "CONVEX_ERROR", 500, details);
		this.name = "ConvexError";
	}
}

// エラーメッセージの定数
export const ERROR_MESSAGES = {
	// 認証関連
	AUTH_REQUIRED: "ログインが必要です",
	AUTH_EXPIRED: "セッションの有効期限が切れました。再度ログインしてください",
	AUTH_INVALID: "認証情報が無効です",

	// ネットワーク関連
	NETWORK_ERROR: "ネットワークエラーが発生しました。接続を確認してください",
	SERVER_ERROR:
		"サーバーエラーが発生しました。しばらく待ってから再試行してください",
	TIMEOUT: "リクエストがタイムアウトしました",

	// データ関連
	DATA_NOT_FOUND: "データが見つかりませんでした",
	DATA_INVALID: "無効なデータです",
	DATA_DUPLICATE: "既に存在するデータです",

	// 一般的なエラー
	UNKNOWN_ERROR: "予期しないエラーが発生しました",
	PERMISSION_DENIED: "この操作を実行する権限がありません",
	RATE_LIMIT_EXCEEDED:
		"リクエスト制限を超えました。しばらく待ってから再試行してください",
} as const;

// エラーユーティリティ関数
export const getErrorMessage = (error: unknown): string => {
	if (error instanceof AppError) {
		return error.message;
	}

	if (error instanceof Error) {
		// Convexのエラーメッセージをパース
		if (error.message.includes("Unauthenticated")) {
			return ERROR_MESSAGES.AUTH_REQUIRED;
		}
		if (error.message.includes("Network")) {
			return ERROR_MESSAGES.NETWORK_ERROR;
		}
		return error.message;
	}

	if (typeof error === "string") {
		return error;
	}

	return ERROR_MESSAGES.UNKNOWN_ERROR;
};

export const isAuthError = (error: unknown): boolean => {
	return (
		error instanceof AuthError ||
		(error instanceof Error &&
			(error.message.includes("Unauthenticated") ||
				error.message.includes("Unauthorized")))
	);
};
