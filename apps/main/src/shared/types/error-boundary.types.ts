import type { ComponentType, ErrorInfo, ReactNode } from "react";

/**
 * Error Boundary の基本的なPropsインターフェース
 */
export interface ErrorBoundaryProps {
	/** 子要素 */
	children: ReactNode;
	/** カスタムフォールバックUI */
	fallback?: ComponentType<ErrorFallbackProps> | ReactNode;
	/** エラータイプの識別子 */
	errorType?: ErrorType;
	/** エラー発生時のカスタムハンドラー */
	onError?: (error: Error, errorInfo: ErrorInfo) => void;
	/** リセット時のカスタムハンドラー */
	onReset?: () => void;
	/** 開発環境でのエラー詳細表示フラグ */
	showErrorDetails?: boolean;
}

/**
 * Error Boundary の状態管理インターフェース
 */
export interface ErrorBoundaryState {
	/** エラー発生フラグ */
	hasError: boolean;
	/** エラーオブジェクト */
	error: Error | null;
	/** ReactのErrorInfo（コンポーネントスタック含む） */
	errorInfo: ErrorInfo | null;
}

/**
 * エラーフォールバックコンポーネント用Props
 */
export interface ErrorFallbackProps {
	error: Error;
	errorInfo?: ErrorInfo | null;
	resetError?: () => void;
	errorType?: ErrorType;
}

/**
 * エラータイプの列挙
 */
export const ErrorTypes = {
	GENERAL: "general",
	NETWORK: "network",
	PERMISSION: "permission",
	VALIDATION: "validation",
	CHUNK_LOAD: "chunk_load",
	RUNTIME: "runtime",
	SSR: "ssr",
} as const;

export type ErrorType = (typeof ErrorTypes)[keyof typeof ErrorTypes];

/**
 * エラー詳細情報
 */
export interface ErrorDetails {
	/** エラーメッセージ */
	message: string;
	/** スタックトレース */
	stack?: string;
	/** コンポーネントスタック */
	componentStack?: string;
	/** 追加のメタデータ */
	metadata?: Record<string, unknown>;
}

/**
 * カラースキーム設定
 */
export interface ErrorColorScheme {
	/** コンテナの背景色 */
	containerBackground: string;
	/** コンテナのボーダー色 */
	containerBorder: string;
	/** コンテナ内のテキスト色 */
	containerText: string;
	/** エラーメッセージの色 */
	errorText: string;
	/** ボタンの背景色 */
	buttonBackground: string;
	/** ボタンのテキスト色 */
	buttonText: string;
	/** ボタンのホバー時背景色 */
	buttonHoverBackground: string;
}

/**
 * メッセージ設定
 */
export interface ErrorMessages {
	/** タイトル */
	title: ReactNode;
	/** 説明文 */
	description: ReactNode;
	/** リトライボタンのテキスト */
	retryButtonText?: string;
	/** 詳細表示ボタンのテキスト */
	detailsButtonText?: string;
	/** エラー詳細のヘッダー */
	detailsHeader?: string;
}

/**
 * エラーハンドリングオプション
 */
export interface ErrorOptions {
	/** 自動リカバリーを有効にするか */
	enableAutoRecovery?: boolean;
	/** 自動リカバリーのタイムアウト（ミリ秒） */
	autoRecoveryTimeout?: number;
	/** Sentryにレポートするか */
	reportToSentry?: boolean;
	/** Sentryレポート時の追加タグ */
	sentryTags?: Record<string, string>;
	/** SSRエラー時の処理 */
	handleSSRError?: boolean;
	/** カスタムエラー判定関数 */
	isCustomError?: (error: Error) => boolean;
}

/**
 * カスタムアクション設定
 */
export interface ErrorAction {
	/** アクションのラベル */
	label: string;
	/** アクションのハンドラー */
	handler: (error: Error, errorInfo?: ErrorInfo | null) => void | Promise<void>;
	/** アクションのアイコン（ReactNode） */
	icon?: ReactNode;
	/** アクションを表示する条件 */
	shouldShow?: (error: Error) => boolean;
}

/**
 * Error Boundary の拡張可能な設定オブジェクト
 */
export interface ErrorBoundaryConfig {
	/** フィーチャー名（ログ用） */
	featureName?: string;
	/** カスタマイズ可能なカラースキーム */
	colorScheme?: Partial<ErrorColorScheme>;
	/** 表示メッセージ設定 */
	messages?: Partial<ErrorMessages>;
	/** 高度なエラー処理オプション */
	options?: ErrorOptions;
	/** カスタムアクション設定 */
	actions?: ErrorAction[];
}

/**
 * プリセットカラースキーム
 */
export const ErrorColorSchemes: Record<string, ErrorColorScheme> = {
	default: {
		containerBackground: "white",
		containerBorder: "gray.200",
		containerText: "gray.700",
		errorText: "red.600",
		buttonBackground: "blue.500",
		buttonText: "white",
		buttonHoverBackground: "blue.600",
	},
	dark: {
		containerBackground: "gray.800",
		containerBorder: "gray.600",
		containerText: "gray.100",
		errorText: "red.400",
		buttonBackground: "blue.600",
		buttonText: "white",
		buttonHoverBackground: "blue.700",
	},
	critical: {
		containerBackground: "red.50",
		containerBorder: "red.200",
		containerText: "red.900",
		errorText: "red.700",
		buttonBackground: "red.600",
		buttonText: "white",
		buttonHoverBackground: "red.700",
	},
	warning: {
		containerBackground: "yellow.50",
		containerBorder: "yellow.200",
		containerText: "yellow.900",
		errorText: "yellow.700",
		buttonBackground: "yellow.600",
		buttonText: "white",
		buttonHoverBackground: "yellow.700",
	},
};

/**
 * デフォルトメッセージ
 */
export const DefaultErrorMessages: Record<ErrorType, ErrorMessages> = {
	[ErrorTypes.GENERAL]: {
		title: "エラーが発生しました",
		description: "申し訳ございません。予期しないエラーが発生しました。",
		retryButtonText: "再試行",
		detailsButtonText: "詳細を表示",
		detailsHeader: "エラー詳細",
	},
	[ErrorTypes.NETWORK]: {
		title: "ネットワークエラー",
		description:
			"ネットワーク接続に問題が発生しました。接続を確認してください。",
		retryButtonText: "再接続",
		detailsButtonText: "詳細を表示",
		detailsHeader: "接続エラー詳細",
	},
	[ErrorTypes.PERMISSION]: {
		title: "権限エラー",
		description: "このリソースへのアクセス権限がありません。",
		retryButtonText: "再試行",
		detailsButtonText: "詳細を表示",
		detailsHeader: "権限エラー詳細",
	},
	[ErrorTypes.VALIDATION]: {
		title: "検証エラー",
		description: "入力データに問題があります。入力内容を確認してください。",
		retryButtonText: "再試行",
		detailsButtonText: "詳細を表示",
		detailsHeader: "検証エラー詳細",
	},
	[ErrorTypes.CHUNK_LOAD]: {
		title: "読み込みエラー",
		description:
			"アプリケーションの一部を読み込めませんでした。ページを更新してください。",
		retryButtonText: "ページを更新",
		detailsButtonText: "詳細を表示",
		detailsHeader: "読み込みエラー詳細",
	},
	[ErrorTypes.RUNTIME]: {
		title: "実行時エラー",
		description: "アプリケーションの実行中にエラーが発生しました。",
		retryButtonText: "再試行",
		detailsButtonText: "詳細を表示",
		detailsHeader: "実行時エラー詳細",
	},
	[ErrorTypes.SSR]: {
		title: "サーバーサイドエラー",
		description: "サーバーでの処理中にエラーが発生しました。",
		retryButtonText: "再試行",
		detailsButtonText: "詳細を表示",
		detailsHeader: "SSRエラー詳細",
	},
};

/**
 * Error Boundary のコンテキスト値
 */
export interface ErrorBoundaryContextValue {
	/** 現在のエラー状態 */
	error: Error | null;
	/** エラー情報 */
	errorInfo: ErrorInfo | null;
	/** エラーをリセットする関数 */
	resetError: () => void;
	/** エラーが発生しているか */
	hasError: boolean;
}

/**
 * Error Boundary のユーティリティ型
 */
export type ErrorBoundaryComponent<P = ErrorBoundaryProps> = ComponentType<P>;

/**
 * Error Boundary の高階コンポーネント用Props
 */
export interface WithErrorBoundaryProps {
	/** Error Boundary の設定 */
	errorBoundaryConfig?: ErrorBoundaryConfig;
	/** フォールバックコンポーネント */
	fallbackComponent?: ComponentType<ErrorFallbackProps>;
}
