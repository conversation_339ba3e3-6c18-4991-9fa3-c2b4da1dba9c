export interface ButtonProps {
	variant?: "primary" | "secondary" | "ghost" | "danger";
	size?: "sm" | "md" | "lg";
	fullWidth?: boolean;
	disabled?: boolean;
}

export interface CardProps {
	variant?: "default" | "bordered" | "elevated";
	padding?: "none" | "sm" | "md" | "lg";
}

export interface ModalProps {
	isOpen: boolean;
	onClose: () => void;
	title?: string;
	size?: "sm" | "md" | "lg" | "xl";
}
