/**
 * 認証状態の型定義
 *
 * @see {@link @/features/auth} - 詳細な実装
 */
export type { AuthState } from "@/features/auth";

/**
 * 認証状態を管理するカスタムフック
 *
 * アプリケーション全体で使用される認証状態の管理フック。
 * Clerk認証と連携し、ユーザーのログイン状態、ユーザー情報、
 * 認証関連の操作を提供します。
 *
 * @returns 認証状態とヘルパー関数を含むオブジェクト
 * @example
 * ```typescript
 * const { isAuthenticated, user, signIn, signOut } = useAuth();
 *
 * if (isAuthenticated) {
 *   console.log(`ログイン中: ${user?.email}`);
 * }
 * ```
 *
 * @see {@link @/features/auth/hooks/useAuth} - 実装の詳細
 */
export { useAuth } from "@/features/auth";
