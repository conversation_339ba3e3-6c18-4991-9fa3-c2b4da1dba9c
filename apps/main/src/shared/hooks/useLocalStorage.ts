import { useCallback, useEffect, useRef, useState } from "react";

/**
 * SSRセーフなlocalStorageフック
 *
 * Features:
 * - Hydrationミスマッチを防ぐ設計
 * - エラーハンドリング付き
 * - TypeScript型安全性
 * - ローディング状態の管理
 *
 * @param key - localStorageのキー
 * @param initialValue - 初期値（SSRとクライアントで同じ値を使用）
 * @returns [value, setValue, isLoading] - 値、値を設定する関数、ローディング状態
 *
 * @example
 * ```tsx
 * const [theme, setTheme, isLoading] = useLocalStorage('theme', 'light');
 *
 * if (isLoading) {
 *   return <LoadingSpinner />;
 * }
 *
 * return (
 *   <button onClick={() => setTheme('dark')}>
 *     Current theme: {theme}
 *   </button>
 * );
 * ```
 */
export function useLocalStorage<T>(
	key: string,
	initialValue: T,
): [T, (value: T | ((val: T) => T)) => void, boolean] {
	// 初期値でstateを初期化（SSRセーフ）
	const [storedValue, setStoredValue] = useState<T>(initialValue);
	const [isLoading, setIsLoading] = useState(true);
	const isHydrated = useRef(false);

	// クライアントサイドでのみlocalStorageから値を読み込む
	useEffect(() => {
		try {
			if (typeof window !== "undefined") {
				const item = window.localStorage.getItem(key);
				if (item) {
					const parsed = JSON.parse(item) as T;
					setStoredValue(parsed);
				}
			}
		} catch (error) {
			console.error(`Error loading localStorage key "${key}":`, error);
		} finally {
			setIsLoading(false);
			isHydrated.current = true;
		}
	}, [key]);

	// 値を設定する関数
	const setValue = useCallback(
		(value: T | ((val: T) => T)) => {
			try {
				const valueToStore =
					value instanceof Function ? value(storedValue) : value;
				setStoredValue(valueToStore);

				// Hydrationが完了してからlocalStorageに保存
				if (isHydrated.current && typeof window !== "undefined") {
					window.localStorage.setItem(key, JSON.stringify(valueToStore));
				}
			} catch (error) {
				console.error(`Error setting localStorage key "${key}":`, error);
			}
		},
		[key, storedValue],
	);

	return [storedValue, setValue, isLoading];
}

/**
 * 単純な値のための軽量版useLocalStorage
 *
 * @param key - localStorageのキー
 * @param initialValue - 初期値
 * @returns [value, setValue] - 値と値を設定する関数
 *
 * @example
 * ```tsx
 * const [isCollapsed, setIsCollapsed] = useSimpleLocalStorage('sidebar-collapsed', false);
 * ```
 */
export function useSimpleLocalStorage<T extends string | number | boolean>(
	key: string,
	initialValue: T,
): [T, (value: T) => void] {
	const [value, setValue] = useState<T>(initialValue);

	// クライアントサイドでのみlocalStorageから読み込み
	useEffect(() => {
		try {
			const saved = localStorage.getItem(key);
			if (saved !== null) {
				if (typeof initialValue === "boolean") {
					setValue((saved === "true") as T);
				} else if (typeof initialValue === "number") {
					setValue(Number(saved) as T);
				} else {
					setValue(saved as T);
				}
			}
		} catch (error) {
			console.error(`Failed to load "${key}" from localStorage:`, error);
		}
	}, [key, initialValue]);

	// 値を保存する関数
	const saveValue = useCallback(
		(newValue: T) => {
			setValue(newValue);
			try {
				localStorage.setItem(key, String(newValue));
			} catch (error) {
				console.error(`Failed to save "${key}" to localStorage:`, error);
			}
		},
		[key],
	);

	return [value, saveValue];
}
