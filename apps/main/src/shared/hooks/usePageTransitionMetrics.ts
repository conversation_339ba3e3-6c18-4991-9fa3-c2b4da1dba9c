"use client";

import { usePathname } from "next/navigation";
import { useEffect, useRef } from "react";

/**
 * ページ遷移メトリクスのデータ構造
 */
interface TransitionMetrics {
	/** 遷移元のパス */
	fromPath: string;
	/** 遷移先のパス */
	toPath: string;
	/** 遷移にかかった時間（ミリ秒） */
	duration: number;
	/** 遷移完了時のタイムスタンプ */
	timestamp: number;
}

/**
 * ページ遷移時間を計測するカスタムフック
 *
 * Next.jsのApp Routerでページ遷移にかかる時間を計測し、
 * パフォーマンス分析のためのメトリクスを収集します。
 * 開発環境でのみ動作し、本番環境では無効化されます。
 *
 * 機能：
 * - ページ遷移時間の自動計測
 * - コンソールへのメトリクス出力
 * - 遅い遷移（3秒以上）の警告表示
 * - ローカルストレージへの履歴保存（最新50件）
 * - デバッグ用グローバル関数の提供
 *
 * @example
 * ```typescript
 * // コンポーネントで使用
 * function MyApp() {
 *   usePageTransitionMetrics(); // 自動的に計測開始
 *   return <div>...</div>;
 * }
 *
 * // 開発コンソールでメトリクスを確認
 * window.getPageTransitionMetrics(); // 計測履歴を取得
 * window.clearPageTransitionMetrics(); // 履歴をクリア
 * ```
 */
export function usePageTransitionMetrics() {
	const pathname = usePathname();
	const previousPathRef = useRef<string | undefined>(undefined);
	const transitionStartRef = useRef<number | undefined>(undefined);
	const isFirstRenderRef = useRef(true);

	useEffect(() => {
		// 開発環境でのみ動作
		if (process.env.NODE_ENV !== "development") {
			return;
		}

		// 初回レンダリング時はスキップ
		if (isFirstRenderRef.current) {
			isFirstRenderRef.current = false;
			previousPathRef.current = pathname;
			return;
		}

		// ページ遷移が発生した場合
		if (previousPathRef.current && previousPathRef.current !== pathname) {
			const now = performance.now();

			// 遷移開始時刻が記録されている場合は遷移完了として処理
			if (transitionStartRef.current) {
				const duration = now - transitionStartRef.current;
				const metrics: TransitionMetrics = {
					fromPath: previousPathRef.current,
					toPath: pathname,
					duration: Math.round(duration),
					timestamp: Date.now(),
				};

				// メトリクスをコンソールに出力
				console.group(
					`%c📊 Page Transition Metrics`,
					"color: #3182CE; font-weight: bold;",
				);
				console.log(`From: ${metrics.fromPath}`);
				console.log(`To: ${metrics.toPath}`);
				console.log(
					`Duration: ${metrics.duration}ms (${(metrics.duration / 1000).toFixed(
						2,
					)}s)`,
				);
				console.log(`Timestamp: ${new Date(metrics.timestamp).toISOString()}`);
				console.groupEnd();

				// パフォーマンス警告
				if (metrics.duration > 3000) {
					console.warn(
						`%c⚠️ Slow page transition detected: ${metrics.duration}ms`,
						"color: #E53E3E; font-weight: bold;",
					);
				}

				// ローカルストレージに保存（分析用）
				try {
					const storedMetrics = localStorage.getItem("pageTransitionMetrics");
					const metricsArray: TransitionMetrics[] = storedMetrics
						? JSON.parse(storedMetrics)
						: [];
					metricsArray.push(metrics);
					// 最新の50件のみ保持
					if (metricsArray.length > 50) {
						metricsArray.shift();
					}
					localStorage.setItem(
						"pageTransitionMetrics",
						JSON.stringify(metricsArray),
					);
				} catch (_error) {
					// ストレージエラーは無視
				}
			}

			// 次の遷移のために現在のパスを記録
			previousPathRef.current = pathname;
			transitionStartRef.current = undefined;
		}
	}, [pathname]);

	// 遷移開始を記録する関数
	useEffect(() => {
		if (process.env.NODE_ENV !== "development") {
			return;
		}

		const handleRouteChangeStart = () => {
			transitionStartRef.current = performance.now();
		};

		// Next.js Router eventsの代替として、クリックイベントをリッスン
		const handleLinkClick = (event: MouseEvent) => {
			const target = event.target as HTMLElement;
			const link = target.closest("a");
			if (link?.href && !link.href.startsWith("#")) {
				handleRouteChangeStart();
			}
		};

		document.addEventListener("click", handleLinkClick);

		return () => {
			document.removeEventListener("click", handleLinkClick);
		};
	}, []);

	// 開発環境でのデバッグ用ユーティリティ
	if (process.env.NODE_ENV === "development") {
		// グローバルオブジェクトに計測データ取得関数を追加
		if (typeof window !== "undefined") {
			(window as any).getPageTransitionMetrics = () => {
				try {
					const stored = localStorage.getItem("pageTransitionMetrics");
					return stored ? JSON.parse(stored) : [];
				} catch {
					return [];
				}
			};
			(window as any).clearPageTransitionMetrics = () => {
				localStorage.removeItem("pageTransitionMetrics");
				console.log("Page transition metrics cleared");
			};
		}
	}
}
