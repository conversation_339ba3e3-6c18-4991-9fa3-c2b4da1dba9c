/**
 * 認証済みユーザーの型定義
 *
 * @see {@link @/features/auth} - 詳細な実装
 */
export type { AuthUser } from "@/features/auth";

/**
 * 現在のユーザー情報を取得するカスタムフック
 *
 * 認証済みユーザーの情報を取得し、リアルタイムで更新される
 * ユーザーデータを提供します。Clerk認証とConvexデータベースを
 * 連携させ、ユーザー設定やプロファイル情報にアクセスできます。
 *
 * @returns 現在のユーザー情報（未認証の場合はnull）
 * @example
 * ```typescript
 * const user = useCurrentUser();
 *
 * if (user) {
 *   console.log(`ユーザー名: ${user.name}`);
 *   console.log(`メールアドレス: ${user.email}`);
 * }
 * ```
 *
 * @see {@link @/features/auth/hooks/useCurrentUser} - 実装の詳細
 */
export { useCurrentUser } from "@/features/auth";
