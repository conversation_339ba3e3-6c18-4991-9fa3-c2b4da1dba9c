"use client";

import type { NavItem } from "../components/layout/Sidenav/types";
import {
	bottomNavigationData,
	navigationData,
} from "../config/navigation-data";
import { resolveNavigationIcons } from "../utils/icon-resolver";

// ナビゲーションアイテムを事前に解決（モジュールレベルで一度だけ実行）
const resolvedNavigationItems = resolveNavigationIcons(
	navigationData,
) as NavItem[];

const resolvedBottomNavigationItems = resolveNavigationIcons(
	bottomNavigationData,
) as NavItem[];

/**
 * ナビゲーションアイテムを取得するカスタムフック
 *
 * アプリケーションのサイドナビゲーションで使用されるナビゲーションアイテムを提供します。
 * このフックは事前に解決された静的データを返すため、パフォーマンスへの影響が最小限です。
 * アイコンは初回インポート時に一度だけ解決され、その後はキャッシュされたデータを使用します。
 *
 * @returns ナビゲーションアイテムのオブジェクト
 * @returns topItems - メインナビゲーションアイテムの配列
 * @returns bottomItems - 下部固定ナビゲーションアイテムの配列
 *
 * @example
 * ```typescript
 * const { topItems, bottomItems } = useNavigationItems();
 *
 * // メインナビゲーションをレンダリング
 * topItems.map(item => (
 *   <NavLink key={item.href} href={item.href}>
 *     <item.icon />
 *     {item.label}
 *   </NavLink>
 * ))
 * ```
 */
export function useNavigationItems(): {
	topItems: NavItem[];
	bottomItems: NavItem[];
} {
	return {
		topItems: resolvedNavigationItems,
		bottomItems: resolvedBottomNavigationItems,
	};
}
