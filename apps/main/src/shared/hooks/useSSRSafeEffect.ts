"use client";

import { useEffect, useLayoutEffect } from "react";
import { isClient } from "@/shared/utils/environment";

/**
 * SSR環境でも安全に使用できるuseLayoutEffect
 *
 * Next.jsのSSR/SSG環境でuseLayoutEffectを使用すると警告が発生するため、
 * サーバーサイドではuseEffectを、クライアントサイドではuseLayoutEffectを
 * 自動的に切り替えて使用します。
 *
 * useLayoutEffectが必要な場面：
 * - DOM測定が必要な場合
 * - スクロール位置の復元
 * - アニメーションの初期化
 * - レイアウトの調整
 *
 * @param effect - 実行する副作用関数
 * @param deps - 依存配列
 *
 * @example
 * ```typescript
 * // DOM要素のサイズを測定
 * useSSRSafeLayoutEffect(() => {
 *   const element = ref.current;
 *   if (element) {
 *     const { width, height } = element.getBoundingClientRect();
 *     setSize({ width, height });
 *   }
 * }, []);
 *
 * // スクロール位置の復元
 * useSSRSafeLayoutEffect(() => {
 *   window.scrollTo(0, savedScrollPosition);
 * }, [savedScrollPosition]);
 * ```
 *
 * @see {@link https://react.dev/reference/react/useLayoutEffect} - React公式ドキュメント
 */
export const useSSRSafeLayoutEffect = isClient ? useLayoutEffect : useEffect;
