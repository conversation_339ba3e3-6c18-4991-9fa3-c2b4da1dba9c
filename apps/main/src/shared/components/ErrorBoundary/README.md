# ErrorBoundary Factory

ErrorBoundaryのファクトリ関数を使用して、カスタマイズされたエラーハンドリングコンポーネントを作成できます。

## 基本的な使い方

```typescript
import { createFeatureErrorBoundary } from "@/shared/components/ErrorBoundary"

// カスタムErrorBoundaryの作成
const MyFeatureErrorBoundary = createFeatureErrorBoundary({
  featureName: "MyFeature",
  colorScheme: {
    background: "red.50",
    border: "red.200",
    errorText: "red.600",
  },
  messages: {
    title: "機能エラー",
    description: "この機能で問題が発生しました。",
    retryButtonText: "もう一度試す",
  },
  options: {
    reportToSentry: true,
    enableAutoRecovery: false,
  },
})

// 使用例
function MyFeature() {
  return (
    <MyFeatureErrorBoundary>
      <MyFeatureContent />
    </MyFeatureErrorBoundary>
  )
}
```

## 高度な設定

### カスタムアクションの追加

```typescript
const CustomErrorBoundary = createFeatureErrorBoundary({
  featureName: "CustomFeature",
  actions: [
    {
      label: "サポートに連絡",
      handler: async (error, errorInfo) => {
        // サポートチケットを作成
        await createSupportTicket(error)
      },
      icon: <HelpCircle className="w-4 h-4" />,
      shouldShow: (error) => error.message.includes("critical"),
    },
  ],
})
```

### カスタムフォールバックコンポーネント

```typescript
function CustomFallback({ error, resetError }: ErrorFallbackProps) {
  return (
    <div className="custom-error-ui">
      <h1>カスタムエラー画面</h1>
      <p>{error.message}</p>
      <button onClick={resetError}>再試行</button>
    </div>
  )
}

// 使用例
<MyFeatureErrorBoundary fallback={CustomFallback}>
  <MyFeatureContent />
</MyFeatureErrorBoundary>
```

### 自動リカバリーの設定

```typescript
const AutoRecoveringBoundary = createFeatureErrorBoundary({
  featureName: "AutoRecovering",
  options: {
    enableAutoRecovery: true,
    autoRecoveryTimeout: 3000, // 3秒後に自動リカバリー
  },
})
```

## 事前定義されたErrorBoundary

### GenericErrorBoundary

汎用的なエラーハンドリング用。

```typescript
import { GenericErrorBoundary } from "@/shared/components/ErrorBoundary"

<GenericErrorBoundary>
  <YourContent />
</GenericErrorBoundary>
```

### NetworkErrorBoundary

ネットワークエラー専用。自動リトライ機能付き。

```typescript
import { NetworkErrorBoundary } from "@/shared/components/ErrorBoundary"

<NetworkErrorBoundary>
  <DataFetchingComponent />
</NetworkErrorBoundary>
```

### PermissionErrorBoundary

権限エラー専用。ログインページへの誘導付き。

```typescript
import { PermissionErrorBoundary } from "@/shared/components/ErrorBoundary"

<PermissionErrorBoundary>
  <ProtectedContent />
</PermissionErrorBoundary>
```

## カラースキームプリセット

```typescript
import { ErrorColorSchemes } from "@/shared/components/ErrorBoundary"

// 利用可能なプリセット
- ErrorColorSchemes.default    // デフォルト
- ErrorColorSchemes.dark       // ダークモード
- ErrorColorSchemes.critical   // クリティカルエラー
- ErrorColorSchemes.warning    // 警告
```

## エラータイプ

```typescript
import { ErrorTypes } from "@/shared/components/ErrorBoundary"

// 利用可能なエラータイプ
- ErrorTypes.GENERAL      // 一般的なエラー
- ErrorTypes.NETWORK      // ネットワークエラー
- ErrorTypes.PERMISSION   // 権限エラー
- ErrorTypes.VALIDATION   // バリデーションエラー
- ErrorTypes.CHUNK_LOAD   // チャンク読み込みエラー
- ErrorTypes.RUNTIME      // 実行時エラー
- ErrorTypes.SSR          // SSRエラー
```

## ベストプラクティス

1. **フィーチャーごとにErrorBoundaryを作成**
   - 各機能モジュールで専用のErrorBoundaryを定義
   - エラーの影響範囲を限定

2. **適切なエラータイプの設定**
   - エラーの種類に応じて適切なメッセージとUIを表示
   - ユーザーが次に取るべきアクションを明確に

3. **Sentryレポートの制御**
   - ネットワークエラーなど一時的なエラーはレポートしない
   - クリティカルなエラーのみをレポート

4. **開発環境での詳細表示**
   - 開発時はスタックトレースを表示
   - 本番環境では詳細を隠す