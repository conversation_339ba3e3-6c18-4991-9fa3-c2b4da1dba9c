import type { ReactNode } from "react";

/**
 * ErrorBoundaryの設定
 */
export interface ErrorBoundaryConfig {
	/** フィーチャー名 */
	featureName: string;
	/** カラースキーム設定 */
	colorScheme?: {
		/** 背景色 */
		background?: string;
		/** ボーダー色 */
		border?: string;
		/** アイコン色 */
		iconColor?: string;
		/** テキスト色 */
		textColor?: string;
		/** ボタンカラースキーム */
		buttonColor?: string;
	};
	/** メッセージ設定 */
	messages?: {
		/** エラータイトル */
		title?: string;
		/** エラー説明 */
		description?: string;
		/** 再試行ボタンのテキスト */
		retryButtonText?: string;
	};
	/** オプション設定 */
	options?: {
		/** Sentryへのレポートを有効にするか */
		enableSentryReporting?: boolean;
		/** 自動リカバリーを有効にするか */
		enableAutoRecovery?: boolean;
		/** 自動リカバリーのタイムアウト（ミリ秒） */
		autoRecoveryTimeout?: number;
		/** エラー詳細を表示するか */
		showErrorDetails?: boolean;
		/** コンソールにエラーをログ出力するか */
		logErrorsToConsole?: boolean;
		/** Sentryに送信するカスタムタグ */
		customTags?: Record<string, string | number | boolean>;
	};
	/** カスタムアクション */
	customActions?: Array<{
		/** ボタンのラベル */
		label: string;
		/** クリック時のハンドラ */
		onClick: () => void;
	}>;
}

/**
 * ErrorBoundaryのProps
 */
export interface ErrorBoundaryProps {
	children: ReactNode;
	/** エラー発生時のフォールバックUI（オプション） */
	fallback?: (error: Error, reset: () => void) => ReactNode;
}

/**
 * ErrorBoundaryの状態
 */
export interface ErrorBoundaryState {
	hasError: boolean;
	error: Error | null;
	errorInfo: React.ErrorInfo | null;
	errorCount: number;
	showDetails: boolean;
}
