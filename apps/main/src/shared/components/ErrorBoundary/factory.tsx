"use client";

import {
	<PERSON><PERSON>,
	<PERSON>,
	Button,
	Code,
	Collapsible,
	Heading,
	Stack,
	Text,
	VStack,
} from "@chakra-ui/react";
import * as Sentry from "@sentry/nextjs";
import { AlertCircle, ChevronDown, ChevronUp, RefreshCw } from "lucide-react";
import React, { Component } from "react";
import type {
	ErrorBoundaryConfig,
	ErrorBoundaryProps,
	ErrorBoundaryState,
} from "./types";

/**
 * ErrorBoundaryのファクトリ関数
 *
 * カスタマイズされたErrorBoundaryコンポーネントを生成します。
 * 各機能モジュールで独自のエラーハンドリングを実装できます。
 *
 * @param config - ErrorBoundaryの設定オブジェクト
 * @param config.featureName - 機能名（Sentryログに含まれる）
 * @param config.colorScheme - カラースキーム設定
 * @param config.messages - 表示メッセージのカスタマイズ
 * @param config.options - 動作オプション（Sentryレポート、自動リカバリなど）
 * @param config.customActions - カスタムアクションボタンの配列
 * @returns メモ化されたErrorBoundaryコンポーネント
 * @example
 * const MyErrorBoundary = createFeatureErrorBoundary({
 *   featureName: "MyFeature",
 *   messages: {
 *     title: "エラーが発生しました",
 *   },
 *   options: {
 *     enableAutoRecovery: true,
 *   },
 * });
 */
export function createFeatureErrorBoundary(
	config: ErrorBoundaryConfig,
): React.MemoExoticComponent<React.FC<ErrorBoundaryProps>> {
	const {
		featureName,
		colorScheme = {},
		messages = {},
		options = {},
		customActions = [],
	} = config;

	// デフォルト値を設定
	const defaultColorScheme = {
		background: "red.50",
		border: "red.200",
		iconColor: "red.500",
		textColor: "red.900",
		buttonColor: "red",
		...colorScheme,
	};

	const defaultMessages = {
		title: "エラーが発生しました",
		description: "申し訳ございません。予期しないエラーが発生しました。",
		retryButtonText: "再試行",
		...messages,
	};

	const defaultOptions = {
		enableSentryReporting: true,
		enableAutoRecovery: false,
		autoRecoveryTimeout: 5000,
		showErrorDetails: process.env.NODE_ENV === "development",
		logErrorsToConsole: process.env.NODE_ENV === "development",
		customTags: {},
		...options,
	};

	// カスタムErrorBoundaryコンポーネント
	class CustomErrorBoundary extends Component<
		ErrorBoundaryProps,
		ErrorBoundaryState
	> {
		constructor(props: ErrorBoundaryProps) {
			super(props);
			this.state = {
				hasError: false,
				error: null,
				errorInfo: null,
				errorCount: 0,
				showDetails: false,
			};
		}

		/**
		 * エラー発生時に呼ばれる静的メソッド
		 *
		 * @param error - 発生したエラー
		 * @returns 更新する状態の一部
		 */
		static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
			return { hasError: true, error };
		}

		/**
		 * エラーキャッチ時の処理
		 *
		 * エラーログの出力、Sentryへのレポート、自動リカバリーの設定を行います。
		 *
		 * @param error - 発生したエラー
		 * @param errorInfo - Reactからのエラー情報（コンポーネントスタックを含む）
		 */
		componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
			// ログ出力
			if (defaultOptions.logErrorsToConsole) {
				console.error(`[${featureName}] Error caught:`, error, errorInfo);
			}

			// Sentryへのレポート
			if (defaultOptions.enableSentryReporting) {
				Sentry.withScope((scope) => {
					scope.setTag("feature", featureName);
					scope.setContext("errorBoundary", {
						featureName,
						componentStack: errorInfo.componentStack,
						...defaultOptions.customTags,
					});
					Sentry.captureException(error);
				});
			}

			this.setState((prevState) => ({
				hasError: true,
				error,
				errorInfo,
				errorCount: prevState.errorCount + 1,
			}));

			// 自動リカバリーの処理
			if (defaultOptions.enableAutoRecovery && this.state.errorCount < 3) {
				setTimeout(() => {
					this.setState({
						hasError: false,
						error: null,
						errorInfo: null,
						showDetails: false,
					});
				}, defaultOptions.autoRecoveryTimeout);
			}
		}

		/**
		 * エラー状態をリセットする
		 *
		 * 再試行ボタンがクリックされた時に呼ばれ、
		 * エラー状態をクリアして子コンポーネントを再レンダリングします。
		 */
		handleReset = () => {
			this.setState({
				hasError: false,
				error: null,
				errorInfo: null,
				showDetails: false,
			});
		};

		/**
		 * エラー詳細の表示/非表示を切り替える
		 *
		 * 開発環境でのみ使用され、スタックトレースなどの
		 * 詳細情報の表示を制御します。
		 */
		toggleDetails = () => {
			this.setState((prev) => ({
				showDetails: !prev.showDetails,
			}));
		};

		render() {
			if (this.state.hasError && this.state.error) {
				// カスタムフォールバックが提供されている場合
				if (this.props.fallback) {
					return <>{this.props.fallback(this.state.error, this.handleReset)}</>;
				}

				const isDevelopment = process.env.NODE_ENV === "development";

				return (
					<Box
						bg={defaultColorScheme.background}
						borderWidth="1px"
						borderColor={defaultColorScheme.border}
						borderRadius="lg"
						p={6}
						my={4}
					>
						<VStack gap={4} align="stretch">
							<Alert.Root status="error" variant="subtle">
								<Alert.Indicator color={defaultColorScheme.iconColor}>
									<AlertCircle />
								</Alert.Indicator>
								<Alert.Content>
									<Alert.Title color={defaultColorScheme.textColor}>
										{defaultMessages.title}
									</Alert.Title>
									<Alert.Description color={defaultColorScheme.textColor}>
										{defaultMessages.description}
									</Alert.Description>
								</Alert.Content>
							</Alert.Root>

							<Stack direction={{ base: "column", sm: "row" }} gap={3}>
								<Button
									colorPalette={defaultColorScheme.buttonColor}
									onClick={this.handleReset}
									size="sm"
								>
									<RefreshCw size={16} />
									{defaultMessages.retryButtonText}
								</Button>

								{customActions.map((action, index) => (
									<Button
										key={`${action.label}-${index}`}
										variant="outline"
										colorPalette={defaultColorScheme.buttonColor}
										onClick={action.onClick}
										size="sm"
									>
										{action.label}
									</Button>
								))}

								{defaultOptions.showErrorDetails && isDevelopment && (
									<Button
										variant="ghost"
										size="sm"
										onClick={this.toggleDetails}
									>
										詳細を表示
										{this.state.showDetails ? (
											<ChevronUp size={16} />
										) : (
											<ChevronDown size={16} />
										)}
									</Button>
								)}
							</Stack>

							{defaultOptions.showErrorDetails && isDevelopment && (
								<Collapsible.Root open={this.state.showDetails}>
									<Collapsible.Content>
										<Box
											bg="gray.50"
											p={4}
											borderRadius="md"
											fontSize="sm"
											fontFamily="mono"
										>
											<Heading size="sm" mb={2} color="fg.muted">
												エラー詳細:
											</Heading>
											<Text color="fg.error" mb={2}>
												{this.state.error.toString()}
											</Text>
											{this.state.error.stack && (
												<Box>
													<Heading size="sm" mb={2} color="fg.muted">
														スタックトレース:
													</Heading>
													<Code
														display="block"
														whiteSpace="pre-wrap"
														fontSize="xs"
														p={2}
														bg="gray.100"
														borderRadius="md"
													>
														{this.state.error.stack}
													</Code>
												</Box>
											)}
											{this.state.errorInfo?.componentStack && (
												<Box mt={4}>
													<Heading size="sm" mb={2} color="fg.muted">
														コンポーネントスタック:
													</Heading>
													<Code
														display="block"
														whiteSpace="pre-wrap"
														fontSize="xs"
														p={2}
														bg="gray.100"
														borderRadius="md"
													>
														{this.state.errorInfo.componentStack}
													</Code>
												</Box>
											)}
										</Box>
									</Collapsible.Content>
								</Collapsible.Root>
							)}
						</VStack>
					</Box>
				);
			}

			return this.props.children;
		}
	}

	// React.memoでラップして返す
	const MemoizedErrorBoundary = React.memo(CustomErrorBoundary);
	MemoizedErrorBoundary.displayName = `${featureName}ErrorBoundary`;

	return MemoizedErrorBoundary as unknown as React.MemoExoticComponent<
		React.FC<ErrorBoundaryProps>
	>;
}

/**
 * 汎用的なErrorBoundary
 *
 * 一般的なエラーハンドリングに使用するデフォルト設定のErrorBoundary。
 * 特別なカスタマイズが不要な場合に使用します。
 *
 * @example
 * <GenericErrorBoundary>
 *   <MyComponent />
 * </GenericErrorBoundary>
 */
export const GenericErrorBoundary = createFeatureErrorBoundary({
	featureName: "Generic",
	messages: {
		title: "予期しないエラー",
		description: "アプリケーションで問題が発生しました。",
	},
});

/**
 * ネットワークエラー用ErrorBoundary
 *
 * ネットワーク関連のエラーに特化したErrorBoundary。
 * オレンジ色の配色と自動リカバリー機能を持ち、
 * 10秒後に自動的に再試行を行います。
 *
 * @example
 * <NetworkErrorBoundary>
 *   <ApiDependentComponent />
 * </NetworkErrorBoundary>
 */
export const NetworkErrorBoundary = createFeatureErrorBoundary({
	featureName: "Network",
	colorScheme: {
		background: "orange.50",
		border: "orange.200",
		iconColor: "orange.500",
		textColor: "orange.900",
		buttonColor: "orange",
	},
	messages: {
		title: "ネットワークエラー",
		description:
			"接続に問題が発生しました。インターネット接続を確認してください。",
		retryButtonText: "再接続",
	},
	options: {
		enableAutoRecovery: true,
		autoRecoveryTimeout: 10000,
	},
});

/**
 * 権限エラー用ErrorBoundary
 *
 * 認証・認可関連のエラーに特化したErrorBoundary。
 * 紫色の配色を使用し、ログイン画面への遷移ボタンを提供します。
 *
 * @example
 * <PermissionErrorBoundary>
 *   <ProtectedComponent />
 * </PermissionErrorBoundary>
 */
export const PermissionErrorBoundary = createFeatureErrorBoundary({
	featureName: "Permission",
	colorScheme: {
		background: "purple.50",
		border: "purple.200",
		iconColor: "purple.500",
		textColor: "purple.900",
		buttonColor: "purple",
	},
	messages: {
		title: "権限エラー",
		description: "この操作を実行する権限がありません。",
		retryButtonText: "ホームに戻る",
	},
	customActions: [
		{
			label: "ログイン",
			onClick: () => {
				window.location.href = "/login";
			},
		},
	],
});
