"use client";

import { useAuth, useUser } from "@clerk/nextjs";
import { ConvexReactClient } from "convex/react";
import { ConvexProviderWithClerk } from "convex/react-clerk";
import type { ReactNode } from "react";
import { useEffect } from "react";
import { setSentryUser } from "@/shared/lib/sentry-utils";
import { ThemeSyncProvider } from "./ThemeSyncProvider";

/** Convexクライアントのインスタンス（環境変数から接続URLを取得） */
const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

/**
 * ConvexClientProviderコンポーネント
 *
 * Convexクライアントのコンテキストを提供し、Clerk認証と統合します。
 * このコンポーネントは、アプリケーション全体でConvexの機能（リアルタイムデータ同期、
 * クエリ、ミューテーション）を利用可能にします。
 *
 * 主な機能：
 * - ConvexとClerkの認証統合
 * - ユーザー情報のSentryへの自動送信（エラートラッキング用）
 * - テーマ同期機能の提供（ThemeSyncProvider経由）
 *
 * @param {Object} props - コンポーネントのプロパティ
 * @param {ReactNode} props.children - ラップされる子コンポーネント
 * @returns {JSX.Element} ConvexとClerkで認証されたプロバイダーでラップされた子コンポーネント
 *
 * @example
 * ```tsx
 * // app/layout.tsx
 * import ConvexClientProvider from '@/shared/components/providers/ConvexClientProvider'
 *
 * export default function RootLayout({ children }: { children: React.ReactNode }) {
 *   return (
 *     <html lang="ja">
 *       <body>
 *         <ConvexClientProvider>
 *           {children}
 *         </ConvexClientProvider>
 *       </body>
 *     </html>
 *   )
 * }
 * ```
 *
 * @remarks
 * - 環境変数 `NEXT_PUBLIC_CONVEX_URL` が必須です
 * - ユーザー情報は自動的にSentryに送信され、エラー発生時のデバッグに活用されます
 * - ユーザーがログアウトした場合、Sentryのユーザー情報もクリアされます
 *
 * @see {@link ConvexProviderWithClerk} - ConvexとClerkの統合プロバイダー
 * @see {@link ThemeSyncProvider} - テーマ同期機能のプロバイダー
 */
export default function ConvexClientProvider({
	children,
}: {
	children: ReactNode;
}) {
	const { user } = useUser();

	// Sentryにユーザー情報を設定
	useEffect(() => {
		if (user) {
			setSentryUser({
				id: user.id,
				email: user.primaryEmailAddress?.emailAddress,
			});
		} else {
			setSentryUser(null);
		}
	}, [user]);

	return (
		<ConvexProviderWithClerk client={convex} useAuth={useAuth}>
			<ThemeSyncProvider>{children}</ThemeSyncProvider>
		</ConvexProviderWithClerk>
	);
}
