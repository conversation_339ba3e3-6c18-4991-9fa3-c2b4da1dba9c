"use client";

import type { ReactNode } from "react";
import { useThemeSync } from "@/features/settings/hooks/useThemeSync";

/**
 * テーマ同期プロバイダーのプロパティ
 */
interface ThemeSyncProviderProps {
	/** ラップする子コンポーネント */
	children: ReactNode;
}

/**
 * テーマ同期を行うプロバイダーコンポーネント
 * ConvexProviderの内側で使用することで、useQueryを正しく使用できる
 */
export function ThemeSyncProvider({ children }: ThemeSyncProviderProps) {
	// テーマ同期を初期化
	useThemeSync();

	// 子コンポーネントをそのまま返す
	return <>{children}</>;
}
