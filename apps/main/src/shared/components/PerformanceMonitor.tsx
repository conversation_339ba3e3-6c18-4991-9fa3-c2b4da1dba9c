"use client";

import { usePageTransitionMetrics } from "@/shared/hooks";
import { WebVitalsReporter } from "./WebVitalsReporter";

/**
 * パフォーマンス監視を統合するコンポーネント
 *
 * アプリケーション全体のパフォーマンスを監視するためのコンポーネント。
 * ページ遷移メトリクスとWeb Vitalsの両方を有効化し、
 * Sentryへのレポートと開発環境でのコンソール出力を行います。
 *
 * 含まれる機能:
 * - ページ遷移時間の計測（usePageTransitionMetrics）
 * - Core Web Vitals（LCP、FID、CLS）の計測
 * - パフォーマンスデータのSentryへの送信
 * - 開発環境でのデバッグ用コンソール出力
 *
 * @returns パフォーマンス監視コンポーネント（WebVitalsReporterを含む）
 * @example
 * // アプリケーションのルートレイアウトで使用
 * export default function RootLayout({ children }: { children: React.ReactNode }) {
 *   return (
 *     <html>
 *       <body>
 *         <PerformanceMonitor />
 *         {children}
 *       </body>
 *     </html>
 *   );
 * }
 */
export function PerformanceMonitor() {
	// ページ遷移メトリクスの有効化
	usePageTransitionMetrics();

	// Web Vitalsレポーターを含める
	return <WebVitalsReporter />;
}
