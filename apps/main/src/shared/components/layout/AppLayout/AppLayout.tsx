"use client";

import { Box, Flex, Separator } from "@chakra-ui/react";
import type { FC, ReactNode } from "react";
import { useEffect, useState } from "react";
import { AppHeader } from "../AppHeader";
import { Sidenav } from "../Sidenav";
import { useSafeBreakpointValue } from "../Sidenav/hooks/useSafeBreakpointValue";
import { useSidenav } from "../Sidenav/hooks/useSidenav";
import { SidenavContainer } from "../Sidenav/SidenavContainer";
import { SidenavErrorBoundary } from "../Sidenav/SidenavErrorBoundary";
import { SidenavItems } from "../Sidenav/SidenavItems";
import type { NavItem } from "../Sidenav/types";

/**
 * Props for AppLayout component
 */
export interface AppLayoutProps {
	children: ReactNode;
	topNavItems?: NavItem[];
	bottomNavItems?: NavItem[];
}

/**
 * Sidenavコンテキストにアクセス可能な内部レイアウトコンポーネント
 *
 * サイドバーの状態に基づいてメインコンテンツのレイアウトを調整する
 *
 * @param props - コンポーネントのプロパティ
 * @param props.children - 子要素
 * @param props.bottomNavItems - 下部に表示するナビゲーションアイテム
 * @returns レイアウト済みのコンテンツ
 *
 * @internal
 */
const AppLayoutInner: FC<{
	children: ReactNode;
	bottomNavItems?: NavItem[];
}> = ({ children, bottomNavItems = [] }) => {
	const { isOpen, variant } = useSidenav();
	const isMobile = useSafeBreakpointValue({ base: true, md: false }, false);
	const [mounted, setMounted] = useState(false);

	useEffect(() => {
		setMounted(true);
	}, []);

	// サイドバーの幅を計算
	const sidebarWidth =
		mounted && !isMobile && variant !== "over"
			? isOpen
				? "200px"
				: "60px"
			: "0";

	return (
		<>
			{/* ヘッダー（Sidenavの外に配置） */}
			<AppHeader pl={sidebarWidth} />

			{/* サイドバー */}
			<SidenavContainer>
				<Flex direction="column" height="100%">
					<Box flex="1" overflow="auto">
						<SidenavItems />
					</Box>
					{bottomNavItems.length > 0 && (
						<Box flexShrink={0}>
							<Separator />
							<SidenavItems items={bottomNavItems} />
						</Box>
					)}
				</Flex>
			</SidenavContainer>

			{/* メインコンテンツ */}
			<Box
				as="main"
				pt="64px" // ヘッダーの高さ分のパディング
				pl={sidebarWidth}
				bg="bg.muted"
				transition="padding-left 0.3s ease-in-out"
				css={{
					minHeight: "100vh",
					"@supports (height: 100dvh)": {
						minHeight: "100dvh",
					},
				}}
			>
				{children}
			</Box>
		</>
	);
};

/**
 * アプリケーションのメインレイアウトコンポーネント
 *
 * ヘッダー、サイドバー、メインコンテンツを統合した包括的なレイアウトを提供します。
 * レスポンシブデザインに対応し、モバイルとデスクトップで異なる表示を行います。
 *
 * @param props - コンポーネントのプロパティ
 * @param props.children - メインコンテンツとして表示する子要素
 * @param props.topNavItems - サイドバー上部に表示するナビゲーションアイテム
 * @param props.bottomNavItems - サイドバー下部に表示するナビゲーションアイテム
 * @returns レイアウト済みのアプリケーション
 *
 * @example
 * ```tsx
 * // 基本的な使用例
 * <AppLayout>
 *   <Dashboard />
 * </AppLayout>
 * ```
 *
 * @example
 * ```tsx
 * // カスタムナビゲーションアイテムを含む使用例
 * const topItems = [
 *   { id: '1', label: 'Dashboard', href: '/dashboard' },
 *   { id: '2', label: 'Settings', href: '/settings' }
 * ];
 * const bottomItems = [
 *   { id: '3', label: 'Help', href: '/help' }
 * ];
 *
 * <AppLayout topNavItems={topItems} bottomNavItems={bottomItems}>
 *   <MainContent />
 * </AppLayout>
 * ```
 */
export const AppLayout: FC<AppLayoutProps> = ({
	children,
	topNavItems = [],
	bottomNavItems = [],
}) => {
	return (
		<SidenavErrorBoundary>
			<Sidenav defaultNavItems={topNavItems} contentMargin={false}>
				<AppLayoutInner bottomNavItems={bottomNavItems}>
					{children}
				</AppLayoutInner>
			</Sidenav>
		</SidenavErrorBoundary>
	);
};
