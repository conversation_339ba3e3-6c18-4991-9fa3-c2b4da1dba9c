"use client";

import { Skeleton } from "@chakra-ui/react";
import dynamic from "next/dynamic";

// UserButtonを動的インポート（SSRを無効化）
const UserButtonComponent = dynamic(
	() =>
		import("@clerk/nextjs").then((mod) => ({
			default: mod.UserButton,
		})),
	{
		ssr: false,
		loading: () => <Skeleton height="32px" width="32px" borderRadius="full" />,
	},
);

/**
 * 動的にロードされるユーザーボタンコンポーネント
 *
 * ClerkのUserButtonをSSRを無効化して動的にインポートし、
 * ハイドレーションエラーを防ぎます。
 * ロード中はスケルトンを表示してUXを向上させます。
 *
 * @returns ユーザーボタンまたはロード中のスケルトン
 *
 * @example
 * ```tsx
 * // ヘッダー内での使用例
 * <Box>
 *   <DynamicUserButton />
 * </Box>
 * ```
 */
export const DynamicUserButton = () => {
	return (
		<UserButtonComponent
			appearance={{
				elements: {
					avatarBox: "h-8 w-8",
				},
			}}
		/>
	);
};
