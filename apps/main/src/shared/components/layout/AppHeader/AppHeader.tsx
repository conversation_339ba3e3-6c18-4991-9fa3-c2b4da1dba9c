"use client";

import { Box, Flex, Icon, IconButton, Skeleton } from "@chakra-ui/react";
import { UserButton } from "@clerk/nextjs";
import { Menu } from "lucide-react";
import type { FC } from "react";
import { useEffect, useState } from "react";
import { useSidenav } from "../Sidenav/hooks/useSidenav";

interface AppHeaderProps {
	/** Padding left to be applied to the header */
	pl?: string | number | Record<string, string | number>;
}

/**
 * アプリケーションヘッダーコンポーネント
 *
 * 固定ヘッダーとして画面上部に表示され、サイドバーのトグルボタンと
 * ユーザーアカウントメニューを提供します。
 *
 * @param props - コンポーネントのプロパティ
 * @param props.pl - ヘッダーに適用する左側のパディング（サイドバーとの連動用）
 * @returns ヘッダーコンポーネント
 *
 * @example
 * ```tsx
 * // 基本的な使用例
 * <AppHeader />
 * ```
 *
 * @example
 * ```tsx
 * // サイドバーの幅に合わせた使用例
 * <AppHeader pl={sidebarWidth} />
 * ```
 */
export const AppHeader: FC<AppHeaderProps> = ({ pl }) => {
	const { toggleSidebar } = useSidenav();
	const [mounted, setMounted] = useState(false);

	useEffect(() => {
		setMounted(true);
	}, []);

	// サイドバートグルボタンの表示条件
	// 常に表示して、ユーザーがいつでもサイドバーを開閉できるようにする
	const showToggleButton = true;

	return (
		<Box
			as="header"
			height="64px"
			bg="bg"
			borderBottomWidth="1px"
			borderColor="border"
			position="fixed"
			top={0}
			left={0}
			right={0}
			zIndex={1000}
			pl={pl}
			transition="padding-left 0.3s ease-in-out"
		>
			<Flex
				height="100%"
				alignItems="center"
				justifyContent="space-between"
				px={{ base: 3, md: 4 }}
			>
				{/* 左側：サイドバートグルボタン */}
				<Box minWidth="40px">
					{showToggleButton && (
						<IconButton
							onClick={toggleSidebar}
							aria-label="メニューを開く"
							variant="ghost"
							size="md"
							cursor="pointer"
							touchAction="manipulation"
							userSelect="none"
							WebkitTapHighlightColor="transparent"
							WebkitTouchCallout="none"
						>
							<Icon>
								<Menu />
							</Icon>
						</IconButton>
					)}
				</Box>

				{/* 右側：Clerkユーザーボタン */}
				<Box minHeight="32px" minWidth="32px">
					{mounted ? (
						<Box suppressHydrationWarning>
							<UserButton
								appearance={{
									elements: {
										avatarBox: "h-8 w-8",
									},
								}}
							/>
						</Box>
					) : (
						<Skeleton height="32px" width="32px" borderRadius="full" />
					)}
				</Box>
			</Flex>
		</Box>
	);
};
