"use client";

import {
	createContext,
	type FC,
	type ReactNode,
	useCallback,
	useEffect,
	useState,
} from "react";
import type { NavItem, SidenavContextType, SidenavVariant } from "../types";

/**
 * Sidebar navigation context
 */
export const SidenavContext = createContext<SidenavContextType | undefined>(
	undefined,
);

/**
 * Props for SidenavProvider component
 */
export interface SidenavProviderProps {
	children: ReactNode;
	defaultIsOpen?: boolean;
	defaultVariant?: SidenavVariant;
	defaultNavItems?: NavItem[];
}

/**
 * Sidebar navigation context provider
 * Manages sidebar state including open/closed state, variant, and navigation items
 *
 * Features:
 * - 開閉状態の管理
 * - 表示バリアントの制御（semi/over）
 * - ナビゲーションアイテムの管理
 * - ローカルストレージでの状態永続化
 */
export const SidenavProvider: FC<SidenavProviderProps> = ({
	children,
	defaultIsOpen = true,
	defaultVariant = "semi",
	defaultNavItems = [],
}) => {
	// SSRセーフな初期状態（サーバーとクライアントで同じ値を使用）
	const [isOpen, setIsOpenState] = useState(defaultIsOpen);
	const [isHydrated, setIsHydrated] = useState(false);

	const [variant, setVariant] = useState<SidenavVariant>(defaultVariant);
	const [navItems, setNavItems] = useState<NavItem[]>(defaultNavItems);

	// クライアントサイドでのみlocalStorageから初期値を読み込む
	useEffect(() => {
		try {
			const saved = localStorage.getItem("sidenav-open");
			if (saved !== null) {
				setIsOpenState(saved === "true");
			}
		} catch (error) {
			console.error("Failed to load sidenav state from localStorage:", error);
		} finally {
			setIsHydrated(true);
		}
	}, []);

	// 開閉状態をローカルストレージに保存
	const setIsOpen = useCallback(
		(value: boolean | ((prev: boolean) => boolean)) => {
			setIsOpenState((prev) => {
				const newValue = typeof value === "function" ? value(prev) : value;
				// Hydrationが完了してからlocalStorageに保存
				if (isHydrated && typeof window !== "undefined") {
					try {
						localStorage.setItem("sidenav-open", String(newValue));
					} catch (error) {
						console.error(
							"Failed to save sidenav state to localStorage:",
							error,
						);
					}
				}
				return newValue;
			});
		},
		[isHydrated],
	);

	const toggleSidebar = useCallback(() => {
		setIsOpen((prev) => !prev);
	}, [setIsOpen]);

	const toggleNavItem = useCallback((itemId: string) => {
		setNavItems((prevItems) => {
			const updateItems = (items: NavItem[]): NavItem[] => {
				return items.map((item) => {
					if (item.id === itemId) {
						return { ...item, isExpanded: !item.isExpanded };
					}
					if (item.children) {
						return { ...item, children: updateItems(item.children) };
					}
					return item;
				});
			};
			return updateItems(prevItems);
		});
	}, []);

	// アクティブなナビゲーションアイテムを設定
	const setActiveNavItem = useCallback((itemId: string) => {
		setNavItems((prevItems) => {
			const updateItems = (items: NavItem[]): NavItem[] => {
				return items.map((item) => {
					const isActive = item.id === itemId;
					if (item.children) {
						const updatedChildren = updateItems(item.children);
						const hasActiveChild = updatedChildren.some(
							(child) => child.isActive,
						);
						return {
							...item,
							isActive: isActive || hasActiveChild,
							children: updatedChildren,
						};
					}
					return { ...item, isActive };
				});
			};
			return updateItems(prevItems);
		});
	}, []);

	// ESCキーでサイドバーを閉じる（モバイル時）
	useEffect(() => {
		const handleEscape = (e: KeyboardEvent) => {
			if (e.key === "Escape" && variant === "over" && isOpen) {
				setIsOpen(false);
			}
		};

		window.addEventListener("keydown", handleEscape);
		return () => window.removeEventListener("keydown", handleEscape);
	}, [variant, isOpen, setIsOpen]);

	const value: SidenavContextType = {
		isOpen,
		toggleSidebar,
		setIsOpen,
		variant,
		setVariant,
		navItems,
		setNavItems,
		toggleNavItem,
		setActiveNavItem,
	};

	return (
		<SidenavContext.Provider value={value}>{children}</SidenavContext.Provider>
	);
};
