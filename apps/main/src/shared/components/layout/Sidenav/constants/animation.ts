/**
 * Sidenav アニメーション設定
 * 一貫性のあるアニメーション体験を提供するための共通設定
 */

export const ANIMATION_CONFIG = {
	/** トランジションの継続時間（ミリ秒） */
	duration: {
		fast: 200,
		normal: 300,
		slow: 500,
	},

	/** イージング関数 */
	easing: {
		smooth: "ease",
		easeOut: "ease-out",
		easeInOut: "ease-in-out",
		spring: "cubic-bezier(0.68, -0.55, 0.265, 1.55)",
	},

	/** デフォルトのトランジション設定 */
	defaultTransition: "all 0.3s ease",

	/** 個別要素のトランジション */
	transitions: {
		width: "width 0.3s ease",
		transform: "transform 0.3s ease",
		opacity: "opacity 0.3s ease",
		margin: "margin-left 0.3s ease",
		all: "all 0.3s ease",
	},

	/** アニメーション無効化のメディアクエリ */
	reducedMotion: "@media (prefers-reduced-motion: reduce)",
} as const;

/**
 * アニメーションの待機時間を取得
 * @param type - アニメーションのタイプ
 * @returns 待機時間（ミリ秒）
 */
export const getAnimationDelay = (
	type: keyof typeof ANIMATION_CONFIG.duration = "normal",
) => {
	return ANIMATION_CONFIG.duration[type];
};

/**
 * トランジション文字列を生成
 * @param properties - トランジションを適用するプロパティ
 * @param duration - 継続時間
 * @param easing - イージング関数
 * @returns トランジション文字列
 */
export const createTransition = (
	properties: string | string[],
	duration: number = ANIMATION_CONFIG.duration.normal,
	easing: string = ANIMATION_CONFIG.easing.smooth,
): string => {
	const props = Array.isArray(properties) ? properties : [properties];
	return props.map((prop) => `${prop} ${duration}ms ${easing}`).join(", ");
};
