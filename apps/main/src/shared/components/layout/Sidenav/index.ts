/**
 * Sidebar Navigation Component
 *
 * This module provides a flexible sidebar navigation system with context-based state management.
 * Supports opening/closing, responsive behavior, and customizable navigation items.
 *
 * @example
 * ```tsx
 * import { Sidenav, SidenavContainer, SidenavItems } from '@/shared/components/layout/Sidenav';
 *
 * // Use the component
 * <Sidenav defaultIsOpen={true} defaultVariant="semi">
 *   <SidenavContainer>
 *     <SidenavItems />
 *   </SidenavContainer>
 *   <Box>Main content</Box>
 * </Sidenav>
 * ```
 */

export type { SidenavProviderProps } from "./context/sidenav.context";
// Context and Provider
export { SidenavContext, SidenavProvider } from "./context/sidenav.context";
// Hooks
export { useSidenav } from "./hooks/useSidenav";
export type { SidenavProps } from "./Sidenav";
// Components
export { Sidenav } from "./Sidenav";
export type { SidenavContainerProps } from "./SidenavContainer";
export { SidenavContainer } from "./SidenavContainer";
export { SidenavErrorBoundary } from "./SidenavErrorBoundary";
export { SidenavItems } from "./SidenavItems";
// Types
export type { NavItem, SidenavContextType, SidenavVariant } from "./types";
