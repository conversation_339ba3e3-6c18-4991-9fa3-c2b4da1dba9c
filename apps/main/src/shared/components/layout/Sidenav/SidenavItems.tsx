"use client";

import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON>,
	<PERSON>S<PERSON>ck,
	<PERSON><PERSON>,
	<PERSON>,
	Stack,
	Text,
} from "@chakra-ui/react";
import { ChevronDown, ChevronRight, ExternalLink } from "lucide-react";
import NextLink from "next/link";
import type { FC } from "react";
import { Tooltip } from "../../../../components/ui/tooltip";
import { useSidenav } from "./hooks/useSidenav";
import type { NavItem } from "./types";

/**
 * Props for SidenavItems component
 */
export interface SidenavItemsProps {
	items?: NavItem[];
	depth?: number;
	onItemClick?: (item: NavItem) => void;
}

/**
 * 個別のナビゲーションアイテムコンポーネント
 *
 * 単一のナビゲーションアイテムをレンダリングし、
 * ネスト、外部リンク、ツールチップなどの機能を提供します。
 *
 * @param props - コンポーネントのプロパティ
 * @param props.item - ナビゲーションアイテムのデータ
 * @param props.depth - ネストの深さ（インデント計算用）
 * @param props.isCollapsed - サイドバーが縮小されているかどうか
 * @param props.onItemClick - アイテムクリック時のコールバック
 * @returns ナビゲーションアイテムコンポーネント
 *
 * @internal
 */
const SidenavItem: FC<{
	item: NavItem;
	depth: number;
	isCollapsed: boolean;
	onItemClick?: (item: NavItem) => void;
}> = ({ item, depth, isCollapsed, onItemClick }) => {
	const { toggleNavItem } = useSidenav();
	const hasChildren = item.children && item.children.length > 0;
	const paddingLeft = depth * 4 + 4;

	const handleClick = () => {
		if (hasChildren) {
			toggleNavItem(item.id);
		}
		onItemClick?.(item);
	};

	const buttonContent = (
		<Button
			variant="ghost"
			width="100%"
			justifyContent={isCollapsed ? "center" : "flex-start"}
			alignItems="center"
			height="auto"
			py={2}
			px={isCollapsed ? 2 : paddingLeft}
			onClick={handleClick}
			disabled={item.isDisabled}
			position="relative"
			_hover={{
				bg: "gray.100",
			}}
			_active={{
				bg: "gray.200",
			}}
		>
			<HStack
				gap={isCollapsed ? 0 : 3}
				width="100%"
				align="center"
				justify={isCollapsed ? "center" : "flex-start"}
			>
				{hasChildren && (
					<Icon
						as={item.isExpanded ? ChevronDown : ChevronRight}
						boxSize={4}
						flexShrink={0}
					/>
				)}
				{item.icon && !hasChildren && (
					<Box width={4} height={4} flexShrink={0}>
						{typeof item.icon === "string" ? (
							<Text fontSize="md">{item.icon}</Text>
						) : (
							item.icon
						)}
					</Box>
				)}
				{!isCollapsed && (
					<>
						<Text
							fontSize="sm"
							fontWeight="medium"
							flex="1"
							textAlign="left"
							lineClamp={1}
						>
							{item.label}
						</Text>
						{item.badge && (
							<Badge colorScheme="blue" fontSize="xs" flexShrink={0}>
								{item.badge}
							</Badge>
						)}
						{item.isExternal && (
							<Icon
								as={ExternalLink}
								boxSize={3}
								color="fg.subtle"
								flexShrink={0}
							/>
						)}
					</>
				)}
			</HStack>
		</Button>
	);

	// 縮小モード時はツールチップで包む
	const itemContent =
		isCollapsed && !hasChildren ? (
			<Tooltip
				content={item.label}
				showArrow
				positioning={{ placement: "right" }}
			>
				{buttonContent}
			</Tooltip>
		) : (
			buttonContent
		);

	// 外部リンクまたは内部リンクの処理
	if (item.href && !hasChildren) {
		if (item.isExternal) {
			return (
				<Link
					href={item.href}
					target="_blank"
					width="100%"
					_hover={{ textDecoration: "none" }}
				>
					{itemContent}
				</Link>
			);
		}

		return (
			<Link asChild width="100%" _hover={{ textDecoration: "none" }}>
				<NextLink href={item.href}>{itemContent}</NextLink>
			</Link>
		);
	}

	// 子要素を持つ項目またはアクションのみの項目
	return itemContent;
};

/**
 * ナビゲーションアイテムリストコンポーネント
 *
 * ナビゲーションアイテムのリストをレンダリングし、
 * ネスト構造とインタラクティブな機能をサポートします。
 *
 * @param props - コンポーネントのプロパティ
 * @param props.items - 表示するナビゲーションアイテムの配列（指定しない場合はコンテキストから取得）
 * @param props.depth - 現在のネストの深さ（デフォルト: 0）
 * @param props.onItemClick - アイテムクリック時のコールバック関数
 * @returns ナビゲーションアイテムリスト
 *
 * @example
 * ```tsx
 * // 基本的な使用例（コンテキストからアイテムを取得）
 * <SidenavItems />
 * ```
 *
 * @example
 * ```tsx
 * // カスタムアイテムを指定する場合
 * const customItems = [
 *   { id: '1', label: 'Home', href: '/', icon: <HomeIcon /> },
 *   {
 *     id: '2',
 *     label: 'Settings',
 *     children: [
 *       { id: '2-1', label: 'Profile', href: '/settings/profile' },
 *       { id: '2-2', label: 'Security', href: '/settings/security' }
 *     ]
 *   }
 * ];
 *
 * <SidenavItems
 *   items={customItems}
 *   onItemClick={(item) => console.log('Clicked:', item.label)}
 * />
 * ```
 */
export const SidenavItems: FC<SidenavItemsProps> = ({
	items,
	depth = 0,
	onItemClick,
}) => {
	const { navItems, isOpen } = useSidenav();
	const isCollapsed = !isOpen;
	const itemsToRender = items || navItems;

	return (
		<Stack gap={1} p={2}>
			{itemsToRender.map((item: NavItem) => (
				<Box key={item.id}>
					<SidenavItem
						item={item}
						depth={depth}
						isCollapsed={isCollapsed}
						onItemClick={onItemClick}
					/>
					{item.children && item.children.length > 0 && item.isExpanded && (
						<SidenavItems
							items={item.children}
							depth={depth + 1}
							onItemClick={onItemClick}
						/>
					)}
				</Box>
			))}
		</Stack>
	);
};
