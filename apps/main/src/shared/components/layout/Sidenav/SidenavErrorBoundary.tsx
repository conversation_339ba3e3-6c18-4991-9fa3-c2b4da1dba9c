"use client";

import { Box, Center, Text, VStack } from "@chakra-ui/react";
import * as Sentry from "@sentry/nextjs";
import type { ErrorInfo, ReactNode } from "react";
import { Component } from "react";

interface Props {
	children: ReactNode;
	fallback?: ReactNode;
}

interface State {
	hasError: boolean;
	error?: Error;
}

/**
 * Sidenav用のエラーバウンダリ
 * SSR関連のエラーをキャッチして適切にハンドリング
 */
export class SidenavErrorBoundary extends Component<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError(error: Error): State {
		// エラー発生時の状態を更新
		return { hasError: true, error };
	}

	componentDidCatch(error: Error, errorInfo: ErrorInfo) {
		// エラーログをコンソールに出力
		console.error("Sidenav Error:", error, errorInfo);

		// SSR関連のエラーかどうかを判定
		const isSSRError =
			error.message?.includes("window is not defined") ||
			error.message?.includes("matchMedia") ||
			error.message?.includes("localStorage") ||
			error.message?.includes("sessionStorage");

		if (isSSRError) {
			console.warn("SSR-related error detected in Sidenav. Using fallback UI.");
		}

		// Sentryにエラーを報告
		Sentry.captureException(error, {
			contexts: {
				react: {
					componentStack: errorInfo.componentStack,
				},
			},
			tags: {
				component: "Sidenav",
				errorBoundary: true,
				isSSRError: isSSRError,
			},
			level: isSSRError ? "warning" : "error",
		});
	}

	render() {
		if (this.state.hasError) {
			// カスタムフォールバックが提供されている場合はそれを使用
			if (this.props.fallback) {
				return <>{this.props.fallback}</>;
			}

			// デフォルトのエラー表示
			return (
				<Box
					position="fixed"
					left={0}
					top={0}
					bottom="var(--safe-area-bottom)"
					width="60px"
					bg="gray.100"
					borderRight="1px solid"
					borderColor="gray.200"
				>
					<Center height="100%">
						<VStack gap={2}>
							<Text fontSize="xs" color="fg.subtle">
								サイドバー
							</Text>
							<Text fontSize="xs" color="fg.subtle">
								エラー
							</Text>
						</VStack>
					</Center>
				</Box>
			);
		}

		return this.props.children;
	}
}
