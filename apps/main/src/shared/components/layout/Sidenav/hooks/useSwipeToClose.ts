"use client";

import { useCallback, useEffect, useRef } from "react";

/**
 * Hook for handling swipe gestures to close the sidenav
 * @param isOpen - Whether the sidenav is open
 * @param onClose - Function to close the sidenav
 * @param threshold - Minimum swipe distance to trigger close (default: 100px)
 */
export const useSwipeToClose = (
	isOpen: boolean,
	onClose: () => void,
	threshold = 100,
) => {
	const touchStartX = useRef<number | null>(null);
	const touchEndX = useRef<number | null>(null);

	const handleTouchStart = useCallback((e: Event) => {
		const touchEvent = e as TouchEvent;
		touchStartX.current = touchEvent.touches[0].clientX;
		touchEndX.current = null;
	}, []);

	const handleTouchMove = useCallback((e: Event) => {
		const touchEvent = e as TouchEvent;
		touchEndX.current = touchEvent.touches[0].clientX;
	}, []);

	const handleTouchEnd = useCallback(() => {
		if (!touchStartX.current || !touchEndX.current) return;

		const distance = touchStartX.current - touchEndX.current;
		const isLeftSwipe = distance > threshold;

		if (isLeftSwipe && isOpen) {
			onClose();
		}

		// Reset
		touchStartX.current = null;
		touchEndX.current = null;
	}, [isOpen, onClose, threshold]);

	useEffect(() => {
		if (!isOpen) return;

		const element = document.querySelector("nav");
		if (!element) return;

		element.addEventListener("touchstart", handleTouchStart);
		element.addEventListener("touchmove", handleTouchMove);
		element.addEventListener("touchend", handleTouchEnd);

		return () => {
			element.removeEventListener("touchstart", handleTouchStart);
			element.removeEventListener("touchmove", handleTouchMove);
			element.removeEventListener("touchend", handleTouchEnd);
		};
	}, [isOpen, handleTouchStart, handleTouchMove, handleTouchEnd]);

	return null;
};
