"use client";

import { useEffect, useRef } from "react";
import { CleanupManager, isBrowser } from "@/shared/utils/client-utils";

/**
 * Hook for trapping focus within an element
 * @param isActive - Whether the focus trap is active
 * @param containerRef - Ref to the container element
 */
export const useFocusTrap = (
	isActive: boolean,
	containerRef?: React.RefObject<HTMLElement | null>,
) => {
	const previousFocusRef = useRef<HTMLElement | null>(null);

	useEffect(() => {
		if (!isActive || !isBrowser()) return;

		const cleanupManager = new CleanupManager();

		// Store the previously focused element
		previousFocusRef.current = document.activeElement as HTMLElement;

		const container =
			containerRef?.current || (document.querySelector("nav") as HTMLElement);
		if (!container) return;

		// Get all focusable elements
		const focusableElements = container.querySelectorAll<HTMLElement>(
			'a[href], button:not([disabled]), textarea:not([disabled]), input:not([disabled]), select:not([disabled]), [tabindex]:not([tabindex="-1"])',
		);

		if (focusableElements.length === 0) return;

		const firstElement = focusableElements[0];
		const lastElement = focusableElements[focusableElements.length - 1];

		// Focus the first element
		firstElement.focus();

		const handleKeyDown = (e: KeyboardEvent) => {
			if (e.key !== "Tab") return;

			// If shift + tab on first element, focus last
			if (e.shiftKey && document.activeElement === firstElement) {
				e.preventDefault();
				lastElement.focus();
			}
			// If tab on last element, focus first
			else if (!e.shiftKey && document.activeElement === lastElement) {
				e.preventDefault();
				firstElement.focus();
			}
		};

		container.addEventListener("keydown", handleKeyDown);
		cleanupManager.add(() =>
			container.removeEventListener("keydown", handleKeyDown),
		);

		// Restore focus on cleanup
		cleanupManager.add(() => {
			if (previousFocusRef.current?.focus) {
				previousFocusRef.current.focus();
			}
		});

		return () => cleanupManager.cleanup();
	}, [isActive, containerRef]);

	return null;
};
