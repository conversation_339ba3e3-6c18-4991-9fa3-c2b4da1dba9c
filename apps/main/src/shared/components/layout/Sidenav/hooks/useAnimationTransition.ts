"use client";

import { ANIMATION_CONFIG } from "../constants";
import { useReducedMotion } from "./useReducedMotion";

/**
 * アニメーショントランジションを管理するフック
 * prefers-reduced-motionを考慮して適切なトランジションを返す
 *
 * @param transition - 使用するトランジション
 * @returns reduced motion時は "none"、それ以外は指定されたトランジション
 */
export const useAnimationTransition = (
	transition: keyof typeof ANIMATION_CONFIG.transitions | string = "all",
) => {
	const prefersReducedMotion = useReducedMotion();

	if (prefersReducedMotion) {
		return "none";
	}

	// トランジションキーが定義されている場合はそれを使用
	if (transition in ANIMATION_CONFIG.transitions) {
		return ANIMATION_CONFIG.transitions[
			transition as keyof typeof ANIMATION_CONFIG.transitions
		];
	}

	// カスタムトランジション文字列の場合はそのまま返す
	return transition;
};
