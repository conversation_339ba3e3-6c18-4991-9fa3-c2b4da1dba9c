import { act, renderHook } from "@testing-library/react";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { useSafeBreakpointValue } from "../useSafeBreakpointValue";

describe("useSafeBreakpointValue", () => {
	// window.matchMediaのモック
	const mockMatchMedia = vi.fn();
	let originalMatchMedia: typeof window.matchMedia | undefined;

	beforeEach(() => {
		// window.matchMediaをモック
		mockMatchMedia.mockImplementation((query: string) => ({
			matches: false,
			media: query,
			onchange: null,
			addListener: vi.fn(),
			removeListener: vi.fn(),
			addEventListener: vi.fn(),
			removeEventListener: vi.fn(),
			dispatchEvent: vi.fn(),
		}));

		// windowが存在する場合のみ設定
		if (typeof window !== "undefined") {
			originalMatchMedia = window.matchMedia;
			window.matchMedia = mockMatchMedia;
		}
	});

	afterEach(() => {
		// モックをリセット
		vi.clearAllMocks();

		// windowが存在する場合のみ復元
		if (typeof window !== "undefined" && originalMatchMedia) {
			window.matchMedia = originalMatchMedia;
		}
	});

	describe("SSR環境", () => {
		it("初回レンダリング時はデフォルト値を返す", () => {
			// windowのinnerWidthを小さく設定（モバイルサイズ）
			Object.defineProperty(window, "innerWidth", {
				writable: true,
				configurable: true,
				value: 400,
			});

			const { result } = renderHook(() =>
				useSafeBreakpointValue({ base: "mobile", md: "desktop" }, "mobile"),
			);

			// 初回レンダリング（SSR相当）ではデフォルト値が返される
			// useEffectが実行されても、400pxはbaseブレークポイントなので"mobile"
			expect(result.current).toBe("mobile");
		});

		it("window.matchMediaが存在しない場合、デフォルト値を返す", () => {
			// matchMediaを一時的に削除
			const originalMatchMedia = window.matchMedia;
			// @ts-ignore
			window.matchMedia = undefined;

			const { result } = renderHook(() =>
				useSafeBreakpointValue({ base: true, md: false }, true),
			);

			// useEffectが実行されても、matchMediaがないのでデフォルト値のまま
			act(() => {
				// 次のティックで値が更新される
			});

			expect(result.current).toBe(true);

			// matchMediaを復元
			window.matchMedia = originalMatchMedia;
		});
	});

	describe("クライアント環境", () => {
		it("オブジェクト形式の値を正しく処理する", async () => {
			// 768px以上の画面幅をシミュレート
			Object.defineProperty(window, "innerWidth", {
				writable: true,
				configurable: true,
				value: 800,
			});

			const { result } = renderHook(() =>
				useSafeBreakpointValue({ base: "mobile", md: "desktop" }, "mobile"),
			);

			// useEffectの実行を待つ
			await act(async () => {
				// renderHookはuseEffectを自動的に実行するため、
				// 結果はすぐに更新される
			});

			// mdブレークポイント（768px）以上なので"desktop"が返される
			expect(result.current).toBe("desktop");
		});

		it("配列形式の値を正しく処理する", async () => {
			Object.defineProperty(window, "innerWidth", {
				writable: true,
				configurable: true,
				value: 500,
			});

			const { result } = renderHook(() =>
				useSafeBreakpointValue(["mobile", "tablet", "desktop"], "mobile"),
			);

			// useEffectの実行を待つ
			await act(async () => {
				// renderHookはuseEffectを自動的に実行する
			});

			// 500pxはsmブレークポイント（480px）以上なので、2番目の値
			expect(result.current).toBe("tablet");
		});

		it("リサイズイベントに反応する", async () => {
			Object.defineProperty(window, "innerWidth", {
				writable: true,
				configurable: true,
				value: 500,
			});

			const { result } = renderHook(() =>
				useSafeBreakpointValue({ base: "mobile", md: "desktop" }, "mobile"),
			);

			// 初期状態
			expect(result.current).toBe("mobile");

			// 画面サイズを変更
			act(() => {
				Object.defineProperty(window, "innerWidth", {
					writable: true,
					configurable: true,
					value: 800,
				});
				window.dispatchEvent(new Event("resize"));
			});

			// デバウンス処理のため、少し待つ
			await act(async () => {
				await new Promise((resolve) => setTimeout(resolve, 150));
			});

			expect(result.current).toBe("desktop");
		});

		it("コンポーネントのアンマウント時にイベントリスナーをクリーンアップする", () => {
			const removeEventListenerSpy = vi.spyOn(window, "removeEventListener");

			const { unmount } = renderHook(() =>
				useSafeBreakpointValue({ base: "mobile", md: "desktop" }, "mobile"),
			);

			unmount();

			expect(removeEventListenerSpy).toHaveBeenCalledWith(
				"resize",
				expect.any(Function),
			);
		});
	});

	describe("エッジケース", () => {
		it("値が存在しないブレークポイントの場合、デフォルト値を返す", () => {
			Object.defineProperty(window, "innerWidth", {
				writable: true,
				configurable: true,
				value: 1600,
			});

			const { result } = renderHook(() =>
				useSafeBreakpointValue({ base: "mobile" }, "fallback"),
			);

			act(() => {
				// 次のティックで値が更新される
			});

			// 1600pxは2xlブレークポイントだが、値が定義されていないので"mobile"を返す
			expect(result.current).toBe("mobile");
		});

		it("空のオブジェクトの場合、デフォルト値を返す", () => {
			const { result } = renderHook(() =>
				useSafeBreakpointValue({}, "default"),
			);

			act(() => {
				// 次のティックで値が更新される
			});

			expect(result.current).toBe("default");
		});

		it("空の配列の場合、デフォルト値を返す", () => {
			const { result } = renderHook(() =>
				useSafeBreakpointValue([], "default"),
			);

			act(() => {
				// 次のティックで値が更新される
			});

			expect(result.current).toBe("default");
		});
	});
});
