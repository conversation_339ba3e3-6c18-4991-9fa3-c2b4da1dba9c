"use client";

import { useEffect, useState } from "react";
import { debounce, isBrowser } from "@/shared/utils/client-utils";

// Chakra UIのデフォルトブレークポイント
const BREAKPOINTS = {
	base: 0,
	sm: 480,
	md: 768,
	lg: 992,
	xl: 1280,
	"2xl": 1536,
} as const;

type BreakpointKey = keyof typeof BREAKPOINTS;

/**
 * SSR-safe version of useBreakpointValue
 * Returns a default value during SSR and the actual breakpoint value after hydration
 *
 * This implementation avoids the "window is not defined" error by:
 * 1. Not calling any Chakra UI hooks that access window during SSR
 * 2. Using useEffect to ensure window access only happens on client
 * 3. Implementing custom breakpoint detection logic
 * 4. Providing consistent default values for SSR/CSR synchronization
 *
 * @param values - Breakpoint values object or array
 * @param defaultValue - Default value to use during SSR (should match expected mobile behavior)
 * @returns The appropriate value for the current breakpoint
 */
export function useSafeBreakpointValue<T = any>(
	values: Record<string, T> | T[],
	defaultValue: T,
): T {
	// 常にデフォルト値から開始（SSR対応）
	const [currentValue, setCurrentValue] = useState<T>(defaultValue);

	useEffect(() => {
		// クライアントサイドでのみ実行
		if (!isBrowser() || !window.matchMedia) {
			return;
		}

		// ブレークポイントの値を取得する関数
		const getBreakpointValue = (): T => {
			const windowWidth = window.innerWidth;

			// 配列形式の場合
			if (Array.isArray(values)) {
				const breakpointKeys = Object.keys(BREAKPOINTS) as BreakpointKey[];
				for (let i = breakpointKeys.length - 1; i >= 0; i--) {
					const breakpoint = breakpointKeys[i];
					const breakpointWidth = BREAKPOINTS[breakpoint];
					if (windowWidth >= breakpointWidth && values[i] !== undefined) {
						return values[i];
					}
				}
				return values[0] ?? defaultValue;
			}

			// オブジェクト形式の場合
			const breakpointKeys = Object.keys(BREAKPOINTS) as BreakpointKey[];
			for (let i = breakpointKeys.length - 1; i >= 0; i--) {
				const breakpoint = breakpointKeys[i];
				const breakpointWidth = BREAKPOINTS[breakpoint];
				if (windowWidth >= breakpointWidth && breakpoint in values) {
					return values[breakpoint];
				}
			}

			// baseブレークポイントのチェック
			if ("base" in values) {
				return values.base;
			}

			return defaultValue;
		};

		// 初期値を設定
		setCurrentValue(getBreakpointValue());

		// リサイズイベントのハンドラー
		const handleResize = () => {
			setCurrentValue(getBreakpointValue());
		};

		// デバウンス処理
		const debouncedHandleResize = debounce(handleResize, 100);

		// リサイズイベントのリスナーを追加
		window.addEventListener("resize", debouncedHandleResize);

		// クリーンアップ
		return () => {
			window.removeEventListener("resize", debouncedHandleResize);
		};
	}, [values, defaultValue]);

	return currentValue;
}
