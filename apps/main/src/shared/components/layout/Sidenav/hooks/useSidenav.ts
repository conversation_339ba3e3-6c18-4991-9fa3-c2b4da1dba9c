import { useContext } from "react";
import { SidenavContext } from "../context/sidenav.context";
import type { SidenavContextType } from "../types";

/**
 * Custom hook to access sidebar navigation context
 *
 * @throws {Error} If used outside of SidenavProvider
 * @returns {SidenavContextType} Sidebar navigation context value
 *
 * @example
 * ```tsx
 * const { isOpen, toggleSidebar, navItems } = useSidenav();
 *
 * return (
 *   <Button onClick={toggleSidebar}>
 *     {isOpen ? "Close" : "Open"} Sidebar
 *   </Button>
 * );
 * ```
 */
export const useSidenav = (): SidenavContextType => {
	const context = useContext(SidenavContext);

	if (!context) {
		throw new Error("useSidenav must be used within a SidenavProvider");
	}

	return context;
};

/**
 * Optional version of useSidenav that returns undefined instead of throwing
 * Useful for components that may be rendered outside of the provider
 *
 * @returns {SidenavContextType | undefined} Sidebar navigation context value or undefined
 */
export const useSidenavOptional = (): SidenavContextType | undefined => {
	return useContext(SidenavContext);
};
