import type { ReactNode } from "react";

/**
 * Navigation item interface for sidebar menu items
 */
export interface NavItem {
	id: string;
	label: string;
	href?: string;
	icon?: ReactNode;
	children?: NavItem[];
	isActive?: boolean;
	isExpanded?: boolean;
	badge?: string | number;
	onClick?: () => void;
	isExternal?: boolean;
	isDisabled?: boolean;
}

/**
 * Sidebar navigation variant types
 */
export type SidenavVariant = "semi" | "over";

/**
 * Sidebar navigation context type definition
 */
export interface SidenavContextType {
	/** Current open/closed state of the sidebar */
	isOpen: boolean;
	/** Toggle the sidebar open/closed state */
	toggleSidebar: () => void;
	/** Set the sidebar open state */
	setIsOpen: (isOpen: boolean | ((prev: boolean) => boolean)) => void;
	/** Current sidebar variant */
	variant: SidenavVariant;
	/** Set the sidebar variant */
	setVariant: (variant: SidenavVariant) => void;
	/** Navigation items */
	navItems: NavItem[];
	/** Set navigation items */
	setNavItems: (items: NavItem[]) => void;
	/** Expand/collapse a navigation item by ID */
	toggleNavItem: (itemId: string) => void;
	/** Set active navigation item by ID */
	setActiveNavItem?: (itemId: string) => void;
}
