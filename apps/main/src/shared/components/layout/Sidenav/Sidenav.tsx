"use client";

import { Box } from "@chakra-ui/react";
import type { FC } from "react";
import { useEffect } from "react";
import { ANIMATION_CONFIG } from "./constants";
import { SidenavProvider } from "./context/sidenav.context";
import { useSafeBreakpointValue } from "./hooks/useSafeBreakpointValue";
import { useSidenav } from "./hooks/useSidenav";
import type { NavItem, SidenavVariant } from "./types";

/**
 * Props for Sidenav component
 */
export interface SidenavProps {
	children: React.ReactNode;
	defaultIsOpen?: boolean;
	defaultVariant?: SidenavVariant;
	defaultNavItems?: NavItem[];
	onToggle?: (isOpen: boolean) => void;
	contentMargin?: boolean;
}

/**
 * Sidenavコンテキストを使用する内部コンポーネント
 *
 * サイドバーの状態に基づいてコンテンツのマージンを動的に調整し、
 * レスポンシブな動作を実現します。
 *
 * @param props - コンポーネントのプロパティ
 * @param props.children - 子要素
 * @param props.onToggle - サイドバーの開閉時に呼ばれるコールバック
 * @param props.contentMargin - コンテンツマージンを自動調整するかどうか
 * @returns マージン調整済みのコンテンツラッパー
 *
 * @internal
 */
const SidenavInner: FC<{
	children: React.ReactNode;
	onToggle?: (isOpen: boolean) => void;
	contentMargin?: boolean;
}> = ({ children, onToggle, contentMargin = true }) => {
	const { isOpen, setVariant, variant } = useSidenav();
	const isMobile = useSafeBreakpointValue({ base: true, md: false }, false);

	// モバイル/デスクトップに応じてバリアントを自動設定
	useEffect(() => {
		if (isMobile) {
			setVariant("over");
		} else {
			setVariant("semi");
		}
	}, [isMobile, setVariant]);

	// 開閉状態の変更を親コンポーネントに通知
	useEffect(() => {
		onToggle?.(isOpen);
	}, [isOpen, onToggle]);

	// サイドバーの幅を計算
	const sidenavWidth = variant === "over" ? 0 : isOpen ? "200px" : "60px";

	return (
		<Box
			position="relative"
			css={{
				minHeight: "100vh",
				"@supports (height: 100dvh)": {
					minHeight: "100dvh",
				},
			}}
			width="100%"
			marginLeft={contentMargin ? sidenavWidth : 0}
			transition={ANIMATION_CONFIG.transitions.margin}
		>
			{children}
		</Box>
	);
};

/**
 * サイドナビゲーションのメインコンポーネント
 *
 * サイドバーナビゲーションの状態管理とレスポンシブな動作を提供します。
 * コンテキストを通じて子コンポーネントに状態を共有し、
 * モバイルとデスクトップで異なる表示モードを自動的に切り替えます。
 *
 * @param props - コンポーネントのプロパティ
 * @param props.children - 子要素（通常はSidenavContainerとメインコンテンツ）
 * @param props.defaultIsOpen - サイドバーの初期開閉状態
 * @param props.defaultVariant - サイドバーの初期表示モード（'semi' | 'over'）
 * @param props.defaultNavItems - 初期ナビゲーションアイテム
 * @param props.onToggle - サイドバー開閉時のコールバック関数
 * @param props.contentMargin - コンテンツマージンの自動調整を有効にするか
 * @returns サイドナビゲーションコンポーネント
 *
 * @example
 * ```tsx
 * // 基本的な使用例
 * <Sidenav>
 *   <SidenavContainer>
 *     <SidenavItems />
 *   </SidenavContainer>
 *   <MainContent />
 * </Sidenav>
 * ```
 *
 * @example
 * ```tsx
 * // フル機能の使用例
 * const navItems = [
 *   { id: '1', label: 'Dashboard', href: '/dashboard' },
 *   { id: '2', label: 'Settings', href: '/settings' }
 * ];
 *
 * <Sidenav
 *   defaultIsOpen={true}
 *   defaultVariant="semi"
 *   defaultNavItems={navItems}
 *   onToggle={(isOpen) => console.log('Sidebar is', isOpen ? 'open' : 'closed')}
 *   contentMargin={true}
 * >
 *   <SidenavContainer>
 *     <SidenavItems />
 *   </SidenavContainer>
 *   <Box>Main content</Box>
 * </Sidenav>
 * ```
 */
export const Sidenav: FC<SidenavProps> = ({
	children,
	defaultIsOpen = true,
	defaultVariant = "semi",
	defaultNavItems = [],
	onToggle,
	contentMargin = true,
}) => {
	return (
		<SidenavProvider
			defaultIsOpen={defaultIsOpen}
			defaultVariant={defaultVariant}
			defaultNavItems={defaultNavItems}
		>
			<SidenavInner onToggle={onToggle} contentMargin={contentMargin}>
				{children}
			</SidenavInner>
		</SidenavProvider>
	);
};
