"use client";

import { Box, Flex } from "@chakra-ui/react";
import type { FC, ReactNode } from "react";
import { useRef } from "react";
import { ANIMATION_CONFIG } from "./constants";
import { useFocusTrap } from "./hooks/useFocusTrap";
import { useSafeBreakpointValue } from "./hooks/useSafeBreakpointValue";
import { useSidenav } from "./hooks/useSidenav";
import { useSwipeToClose } from "./hooks/useSwipeToClose";

/**
 * Props for SidenavContainer component
 */
export interface SidenavContainerProps {
	children: ReactNode;
	header?: ReactNode;
	footer?: ReactNode;
	width?: {
		expanded: string | number;
		collapsed: string | number;
	};
	zIndex?: number;
}

/**
 * サイドバーナビゲーションのコンテナコンポーネント
 *
 * デスクトップとモバイルで異なる表示方法を提供し、
 * レスポンシブな動作とアクセシビリティ機能を実装します。
 *
 * @param props - コンポーネントのプロパティ
 * @param props.children - サイドバー内に表示するメインコンテンツ
 * @param props.header - サイドバーのヘッダー領域に表示するコンテンツ
 * @param props.footer - サイドバーのフッター領域に表示するコンテンツ
 * @param props.width - サイドバーの幅設定（展開時・縮小時）
 * @param props.width.expanded - 展開時の幅
 * @param props.width.collapsed - 縮小時の幅
 * @param props.zIndex - サイドバーのz-indexスタイル値
 * @returns サイドバーコンテナコンポーネント
 *
 * @example
 * ```tsx
 * // 基本的な使用例
 * <SidenavContainer>
 *   <SidenavItems />
 * </SidenavContainer>
 * ```
 *
 * @example
 * ```tsx
 * // ヘッダー・フッター付きの使用例
 * <SidenavContainer
 *   header={<Logo />}
 *   footer={<UserInfo />}
 *   width={{ expanded: "250px", collapsed: "70px" }}
 * >
 *   <SidenavItems />
 * </SidenavContainer>
 * ```
 */
export const SidenavContainer: FC<SidenavContainerProps> = ({
	children,
	header,
	footer,
	width = {
		expanded: "200px",
		collapsed: "60px",
	},
	zIndex = 1100,
}) => {
	const { isOpen, toggleSidebar, variant } = useSidenav();
	const isMobile = useSafeBreakpointValue({ base: true, md: false }, false);
	const containerRef = useRef<HTMLDivElement>(null);

	// モバイル時のタッチ操作とフォーカストラップ
	useSwipeToClose(isOpen && (isMobile || variant === "over"), toggleSidebar);
	useFocusTrap(isOpen && (isMobile || variant === "over"), containerRef);

	// モバイルまたはオーバーレイモードの場合
	if (isMobile || variant === "over") {
		return (
			<>
				{/* オーバーレイ背景 */}
				{isOpen && (
					<Box
						position="fixed"
						top={0}
						left={0}
						right={0}
						bottom="var(--safe-area-bottom)"
						bg="blackAlpha.600"
						zIndex={zIndex - 1}
						onClick={toggleSidebar}
						aria-label="サイドバーを閉じる"
						transition={ANIMATION_CONFIG.transitions.opacity}
						opacity={isOpen ? 1 : 0}
					/>
				)}

				{/* サイドバー本体 */}
				<Box
					ref={containerRef}
					as="nav"
					position="fixed"
					left={0}
					top={0}
					css={{
						height: "100vh",
						"@supports (height: 100dvh)": {
							height: "100dvh",
						},
					}}
					width={width.expanded}
					bg="white"
					_dark={{ bg: "gray.800" }}
					boxShadow="2xl"
					transform={isOpen ? "translateX(0)" : "translateX(-100%)"}
					transition={ANIMATION_CONFIG.transitions.transform}
					zIndex={zIndex}
					overflow="hidden"
					aria-label="メインナビゲーション"
					aria-hidden={!isOpen}
					aria-modal={isOpen}
				>
					<Flex direction="column" height="100%">
						{header && (
							<Box
								borderBottomWidth="1px"
								borderBottomColor="gray.200"
								_dark={{ borderBottomColor: "gray.700" }}
								p={4}
							>
								{header}
							</Box>
						)}
						<Box
							flex="1"
							overflowY="auto"
							overflowX="hidden"
							css={{
								"&::-webkit-scrollbar": {
									width: "4px",
								},
								"&::-webkit-scrollbar-track": {
									background: "transparent",
								},
								"&::-webkit-scrollbar-thumb": {
									background: "var(--chakra-colors-gray-300)",
									borderRadius: "2px",
								},
								"&::-webkit-scrollbar-thumb:hover": {
									background: "var(--chakra-colors-gray-400)",
								},
							}}
						>
							{children}
						</Box>
						{footer && (
							<Box
								borderTopWidth="1px"
								borderTopColor="gray.200"
								_dark={{ borderTopColor: "gray.700" }}
								p={4}
							>
								{footer}
							</Box>
						)}
					</Flex>
				</Box>
			</>
		);
	}

	// デスクトップ用の固定サイドバー表示
	return (
		<Box
			as="nav"
			position="fixed"
			left={0}
			top={0}
			css={{
				height: "100vh",
				"@supports (height: 100dvh)": {
					height: "100dvh",
				},
			}}
			width={isOpen ? width.expanded : width.collapsed}
			bg="white"
			borderRightWidth="1px"
			borderRightColor="gray.200"
			_dark={{
				bg: "gray.800",
				borderRightColor: "gray.700",
			}}
			transition={ANIMATION_CONFIG.transitions.width}
			zIndex={zIndex}
			overflow="hidden"
			aria-label="メインナビゲーション"
			aria-expanded={isOpen}
		>
			<Flex direction="column" height="100%">
				{header && (
					<Box
						borderBottomWidth="1px"
						borderBottomColor="gray.200"
						_dark={{ borderBottomColor: "gray.700" }}
						p={4}
					>
						{header}
					</Box>
				)}
				<Box
					flex="1"
					overflowY="auto"
					overflowX="hidden"
					css={{
						"&::-webkit-scrollbar": {
							width: "4px",
						},
						"&::-webkit-scrollbar-track": {
							background: "transparent",
						},
						"&::-webkit-scrollbar-thumb": {
							background: "var(--chakra-colors-gray-300)",
							borderRadius: "2px",
						},
						"&::-webkit-scrollbar-thumb:hover": {
							background: "var(--chakra-colors-gray-400)",
						},
					}}
				>
					{children}
				</Box>
				{footer && (
					<Box
						borderTopWidth="1px"
						borderTopColor="gray.200"
						_dark={{ borderTopColor: "gray.700" }}
						p={4}
					>
						{footer}
					</Box>
				)}
			</Flex>
		</Box>
	);
};
