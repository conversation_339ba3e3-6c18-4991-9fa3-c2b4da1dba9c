"use client";

import { Text } from "@chakra-ui/react";
import type { CalendarCellHolidayLabelProps } from "./types";

/**
 * カレンダーセルに祝日名を表示するためのラベルコンポーネント
 *
 * @note このコンポーネントは<span>要素（インライン要素）を返します。
 * 中央揃えが必要な場合は、親要素のブロックレベル要素にtext-centerクラスを適用してください。
 *
 * @example
 * ```tsx
 * // 通常の祝日
 * <CalendarCellHolidayLabel holidayName="春分の日" textColor="amber.600" />
 *
 * // 翌日が祝日の場合
 * <CalendarCellHolidayLabel holidayName="勤労感謝の日" textColor="red.600" />
 *
 * // 背景色付き
 * <CalendarCellHolidayLabel
 *   holidayName="元日"
 *   textColor="red.700"
 *   backgroundColor="red.50"
 * />
 * ```
 */
export const CalendarCellHolidayLabel = ({
	holidayName,
	textColor,
	backgroundColor,
}: CalendarCellHolidayLabelProps) => {
	const hasBackground = !!backgroundColor;

	return (
		<Text
			as="span"
			fontSize="xs"
			color={textColor}
			backgroundColor={backgroundColor}
			mt={0.5}
			display={hasBackground ? "inline-block" : "inline"}
			px={hasBackground ? 2 : undefined}
			py={hasBackground ? 0.5 : undefined}
			borderRadius={hasBackground ? "sm" : undefined}
		>
			{holidayName}
		</Text>
	);
};
