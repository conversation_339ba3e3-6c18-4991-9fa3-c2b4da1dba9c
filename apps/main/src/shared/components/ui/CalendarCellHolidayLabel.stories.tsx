import { Box } from "@chakra-ui/react";
import type { Meta, StoryObj } from "@storybook/react";
import { useColorModeValue } from "../../../components/ui/color-mode";
import { CalendarCellHolidayLabel } from "./CalendarCellHolidayLabel";

const meta: Meta<typeof CalendarCellHolidayLabel> = {
	title: "Shared/UI/CalendarCellHolidayLabel",
	component: CalendarCellHolidayLabel,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		holidayName: {
			control: { type: "text" },
			description: "表示する祝日名",
		},
		textColor: {
			control: { type: "text" },
			description: "祝日名の文字色（Chakra UIのカラートークン）",
		},
		backgroundColor: {
			control: { type: "text" },
			description: "背景色（Chakra UIのカラートークン）",
		},
	},
	decorators: [
		(Story) => (
			<Box p={8} bg="gray.50" borderRadius="md" minW="300px" textAlign="center">
				<Story />
			</Box>
		),
	],
} satisfies Meta<typeof CalendarCellHolidayLabel>;

export default meta;
type Story = StoryObj<typeof meta>;

// 通常の祝日
export const Default: Story = {
	args: {
		holidayName: "春分の日",
		textColor: "amber.600",
	},
};

// 翌日が祝日の場合（赤色表示）
export const NextDayHoliday: Story = {
	args: {
		holidayName: "勤労感謝の日",
		textColor: "red.600",
	},
};

// 背景色付き（デフォルト）
export const WithBackground: Story = {
	args: {
		holidayName: "元日",
		textColor: "red.700",
		backgroundColor: "red.50",
	},
};

// カスタムカラー（青系）
export const CustomColorBlue: Story = {
	args: {
		holidayName: "海の日",
		textColor: "blue.600",
		backgroundColor: "blue.100",
	},
};

// カスタムカラー（緑系）
export const CustomColorGreen: Story = {
	args: {
		holidayName: "みどりの日",
		textColor: "green.600",
		backgroundColor: "green.100",
	},
};

// ダークモード対応（手動設定）
export const DarkMode: Story = {
	args: {
		holidayName: "文化の日",
		textColor: "gray.100",
		backgroundColor: "gray.800",
	},
	decorators: [
		(Story) => (
			<Box
				p={8}
				bg="gray.900"
				borderRadius="md"
				minW="300px"
				textAlign="center"
			>
				<Story />
			</Box>
		),
	],
};

// カラーモード対応（自動切り替え）
const AdaptiveWrapper = ({ children }: { children: React.ReactNode }) => {
	const bgColor = useColorModeValue("gray.50", "gray.900");
	const borderColor = useColorModeValue("gray.200", "gray.700");

	return (
		<Box
			p={8}
			bg={bgColor}
			borderRadius="md"
			borderWidth="1px"
			borderColor={borderColor}
			minW="300px"
			textAlign="center"
		>
			{children}
		</Box>
	);
};

// カラーモード対応コンポーネント
const AdaptiveHolidayLabel = () => {
	const textColor = useColorModeValue("red.600", "red.400");
	const backgroundColor = useColorModeValue("red.50", "red.900/20");

	return (
		<CalendarCellHolidayLabel
			holidayName="勤労感謝の日"
			textColor={textColor}
			backgroundColor={backgroundColor}
		/>
	);
};

export const AdaptiveColorMode: Story = {
	render: () => (
		<AdaptiveWrapper>
			<AdaptiveHolidayLabel />
		</AdaptiveWrapper>
	),
};

// 長い祝日名
export const LongHolidayName: Story = {
	args: {
		holidayName: "天皇誕生日（振替休日）",
		textColor: "purple.600",
		backgroundColor: "purple.50",
	},
};

// 背景色なし（シンプル）
export const NoBackground: Story = {
	args: {
		holidayName: "建国記念の日",
		textColor: "orange.600",
	},
};

// 複数の祝日ラベル（レイアウト確認用）
export const MultipleLabels: Story = {
	args: {
		holidayName: "デフォルト",
		textColor: "gray.600",
	},
	render: () => (
		<Box display="flex" flexDirection="column" gap={2}>
			<CalendarCellHolidayLabel holidayName="元日" textColor="red.600" />
			<CalendarCellHolidayLabel
				holidayName="成人の日"
				textColor="pink.600"
				backgroundColor="pink.50"
			/>
			<CalendarCellHolidayLabel
				holidayName="建国記念の日"
				textColor="purple.600"
				backgroundColor="purple.50"
			/>
		</Box>
	),
};
