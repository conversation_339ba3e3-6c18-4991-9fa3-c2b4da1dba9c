"use client";

import { Center, Spinner, Text, VStack } from "@chakra-ui/react";

const spinnerSizes = {
	sm: "sm",
	md: "md",
	lg: "lg",
	xl: "xl",
} as const;

/**
 * LoadingSpinnerコンポーネントのプロパティ
 */
interface LoadingSpinnerProps {
	/**
	 * ローディング中に表示するメッセージ
	 * @default "読み込み中..."
	 */
	message?: string;
	/**
	 * 廃止予定: messageを使用してください
	 * @deprecated
	 */
	label?: string;
	/**
	 * スピナーのサイズ
	 * @default "xl"
	 */
	size?: "sm" | "md" | "lg" | "xl";
	/**
	 * 画面全体を使用するかどうか
	 * @default true
	 */
	fullScreen?: boolean;
}

/**
 * ローディングスピナーコンポーネント
 *
 * ページやデータの読み込み中に表示するローディングインジケーター。
 * 画面中央にスピナーとメッセージを表示します。
 * dvhサポートにより、モバイル環境でも適切な高さを保持します。
 *
 * @param props - LoadingSpinnerコンポーネントのプロパティ
 * @param props.message - ローディング中に表示するメッセージ
 * @returns ローディングスピナーコンポーネント
 * @example
 * // デフォルトメッセージで使用
 * <LoadingSpinner />
 *
 * @example
 * // カスタムメッセージで使用
 * <LoadingSpinner message="データを取得しています..." />
 */
export function LoadingSpinner({
	message = "読み込み中...",
	label,
	size = "xl",
	fullScreen = true,
}: LoadingSpinnerProps) {
	// labelが指定されている場合は優先（後方互換性のため）
	const displayMessage = label ?? message;

	const spinnerContent = (
		<VStack gap={size === "sm" ? 2 : 4}>
			<Spinner
				size={spinnerSizes[size]}
				borderWidth={size === "sm" || size === "md" ? "4px" : undefined}
				animationDuration={size === "sm" || size === "md" ? "0.65s" : undefined}
			/>
			{displayMessage && (
				<Text color="fg.muted" fontSize="sm">
					{displayMessage}
				</Text>
			)}
		</VStack>
	);

	if (!fullScreen) {
		return spinnerContent;
	}

	return (
		<Center
			w="100%"
			css={{
				height: "100vh",
				"@supports (height: 100dvh)": {
					height: "100dvh",
				},
			}}
		>
			{spinnerContent}
		</Center>
	);
}
