"use client";

import { useReportWebVitals } from "next/web-vitals";
import { useEffect } from "react";

/**
 * Web Vitalsメトリクスのデータ構造
 */
interface WebVitalsMetric {
	/** メトリクス名（FCP, LCP, FID, CLS, TTFB, INPなど） */
	name: string;
	/** メトリクスの値（ミリ秒またはスコア） */
	value: number;
	/** メトリクスのラベル */
	label: "web-vital" | "custom";
	/** メトリクスの一意識別子 */
	id: string;
	/** メトリクスの詳細情報（オプショナル） */
	attribution?: any;
	/** メトリクスの評価 */
	rating?: "good" | "needs-improvement" | "poor";
}

/**
 * ローカルストレージに保存されるWeb Vitalsメトリクス
 */
interface StoredWebVitalsMetric extends WebVitalsMetric {
	/** メトリクスを記録したタイムスタンプ */
	timestamp: number;
}

/**
 * Windowオブジェクトの拡張
 *
 * 開発環境でのWeb Vitalsデバッグ用ユーティリティ関数を追加
 */
declare global {
	interface Window {
		/** 保存されたWeb Vitalsメトリクスを取得 */
		getWebVitalsMetrics?: () => Record<string, StoredWebVitalsMetric[]>;
		/** Web Vitalsメトリクスをクリア */
		clearWebVitalsMetrics?: () => void;
		/** Web Vitalsのサマリーをコンソールに表示 */
		showWebVitalsSummary?: () => void;
	}
}

/**
 * Web Vitalsの計測と報告を行うコンポーネント
 *
 * Next.jsのreportWebVitals機能を使用してCore Web Vitalsを計測し、
 * 開発環境では色分けされたコンソール出力とローカルストレージへの保存を行います。
 *
 * 計測対象のメトリクス:
 * - FCP (First Contentful Paint): 最初のコンテンツが表示されるまでの時間
 * - LCP (Largest Contentful Paint): 最大のコンテンツが表示されるまでの時間
 * - FID (First Input Delay): 最初のユーザー入力までの遅延
 * - CLS (Cumulative Layout Shift): レイアウトのズレの累積値
 * - TTFB (Time to First Byte): サーバーから最初のバイトを受信するまでの時間
 * - INP (Interaction to Next Paint): ユーザー操作から次のペイントまでの時間
 *
 * @returns null（表示要素を持たないコンポーネント）
 * @example
 * // app/layout.tsxで使用
 * <WebVitalsReporter />
 */
export function WebVitalsReporter() {
	useReportWebVitals((metric: WebVitalsMetric) => {
		// 開発環境でのみログ出力
		if (process.env.NODE_ENV !== "development") {
			return;
		}

		/**
		 * メトリクスの値に基づいて色を決定
		 *
		 * Web Vitalsの推奨値に基づいて、
		 * 緑（良好）、黄色（改善が必要）、赤（不良）の色を返します。
		 *
		 * @param name - メトリクス名
		 * @param value - メトリクスの値
		 * @returns HEX形式の色コード
		 */
		const getMetricColor = (name: string, value: number): string => {
			switch (name) {
				case "FCP": // First Contentful Paint
					return value < 1800
						? "#10B981"
						: value < 3000
							? "#F59E0B"
							: "#EF4444";
				case "LCP": // Largest Contentful Paint
					return value < 2500
						? "#10B981"
						: value < 4000
							? "#F59E0B"
							: "#EF4444";
				case "FID": // First Input Delay
					return value < 100 ? "#10B981" : value < 300 ? "#F59E0B" : "#EF4444";
				case "CLS": // Cumulative Layout Shift
					return value < 0.1 ? "#10B981" : value < 0.25 ? "#F59E0B" : "#EF4444";
				case "TTFB": // Time to First Byte
					return value < 800 ? "#10B981" : value < 1800 ? "#F59E0B" : "#EF4444";
				case "INP": // Interaction to Next Paint
					return value < 200 ? "#10B981" : value < 500 ? "#F59E0B" : "#EF4444";
				default:
					return "#6B7280";
			}
		};

		const color = getMetricColor(metric.name, metric.value);
		const emoji =
			metric.rating === "good"
				? "✅"
				: metric.rating === "needs-improvement"
					? "⚠️"
					: "❌";

		console.group(
			`%c${emoji} Web Vitals: ${metric.name}`,
			`color: ${color}; font-weight: bold;`,
		);
		console.log(
			`Value: ${metric.value.toFixed(2)}${metric.name === "CLS" ? "" : "ms"}`,
		);
		console.log(`ID: ${metric.id}`);

		if (metric.rating) {
			console.log(`Rating: ${metric.rating}`);
		}

		if (metric.attribution) {
			console.log("Attribution:", metric.attribution);
		}

		console.groupEnd();

		// ローカルストレージに保存（分析用）
		try {
			const storedMetrics = localStorage.getItem("webVitalsMetrics");
			const metricsMap: Record<string, StoredWebVitalsMetric[]> = storedMetrics
				? JSON.parse(storedMetrics)
				: {};

			if (!metricsMap[metric.name]) {
				metricsMap[metric.name] = [];
			}

			metricsMap[metric.name].push({
				...metric,
				timestamp: Date.now(),
			});

			// 各メトリクスタイプごとに最新の20件のみ保持
			Object.keys(metricsMap).forEach((key) => {
				if (metricsMap[key].length > 20) {
					metricsMap[key] = metricsMap[key].slice(-20);
				}
			});

			localStorage.setItem("webVitalsMetrics", JSON.stringify(metricsMap));
		} catch (_error) {
			// ストレージエラーは無視
		}
	});

	// 開発環境でのデバッグ用ユーティリティ
	useEffect(() => {
		if (
			process.env.NODE_ENV === "development" &&
			typeof window !== "undefined"
		) {
			// Web Vitalsデータ取得関数
			window.getWebVitalsMetrics = () => {
				try {
					const stored = localStorage.getItem("webVitalsMetrics");
					return stored ? JSON.parse(stored) : {};
				} catch {
					return {};
				}
			};

			// Web Vitalsデータクリア関数
			window.clearWebVitalsMetrics = () => {
				localStorage.removeItem("webVitalsMetrics");
				console.log("Web Vitals metrics cleared");
			};

			// Web Vitalsサマリー表示関数
			window.showWebVitalsSummary = () => {
				const metrics = window.getWebVitalsMetrics!();
				console.group(
					"%c📊 Web Vitals Summary",
					"color: #3182CE; font-weight: bold;",
				);

				Object.keys(metrics).forEach((metricName) => {
					const values = metrics[metricName].map((m) => m.value);
					const avg = values.reduce((a, b) => a + b, 0) / values.length;
					const min = Math.min(...values);
					const max = Math.max(...values);

					console.group(`${metricName}`);
					console.log(`Average: ${avg.toFixed(2)}`);
					console.log(`Min: ${min.toFixed(2)}`);
					console.log(`Max: ${max.toFixed(2)}`);
					console.log(`Sample Count: ${values.length}`);
					console.groupEnd();
				});

				console.groupEnd();
			};
		}
	}, []);

	return null;
}
