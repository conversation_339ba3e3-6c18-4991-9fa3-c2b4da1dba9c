"use client";

import { Chart, useChart } from "@chakra-ui/charts";
import { Card, Center, Skeleton, Text } from "@chakra-ui/react";
import type { TooltipProps } from "recharts";
import {
	Area,
	CartesianGrid,
	ComposedChart,
	Line,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	YAxis,
} from "recharts";

/**
 * レビュー指標チャートコンポーネントのプロパティ
 */
export interface ReviewMetricsChartProps {
	/** チャートに表示するデータの配列 */
	data?: {
		/** 日付（ISO形式またはYYYY-MM-DD形式） */
		date: string;
		/** その期間のレビュー件数 */
		reviewCount: number;
		/** その期間の平均スコア（0-10） */
		averageScore: number;
	}[];
	/** チャートコンテナに適用するCSSクラス名 */
	className?: string;
	/** チャートのタイトル */
	title?: string;
	/** チャートの説明文 */
	description?: string;
	/** ローディング状態かどうか */
	isLoading?: boolean;
}

// チャート設定
const chartConfig = {
	height: 400,
	margins: { top: 20, right: 30, bottom: 60, left: 60 },
};

/**
 * チャートツールチップのペイロードアイテムの型定義
 */
interface ChartPayloadItem {
	/** 表示色 */
	color?: string;
	/** データ項目名 */
	name?: string;
	/** データ値 */
	value?: number;
	/** データキー */
	dataKey?: string;
}

/**
 * カスタムツールチップコンポーネント
 *
 * チャート上にマウスホバーまたはタッチした際に表示される詳細情報を表示します。
 * 日付、レビュー件数、平均スコアを見やすい形式で表示します。
 *
 * @param props - Rechartsのツールチッププロパティ
 * @returns ツールチップコンポーネント
 */
const CustomTooltip = ({
	active,
	payload,
	label,
}: TooltipProps<number, string> & {
	payload?: ChartPayloadItem[];
	label?: string;
}) => {
	if (active && payload && payload.length) {
		return (
			<Card.Root
				size="sm"
				shadow="lg"
				bg="white"
				_dark={{ bg: "gray.800" }}
				p={3}
			>
				<Card.Body>
					<p
						style={{ fontSize: "14px", fontWeight: "600", marginBottom: "8px" }}
					>
						{label}
					</p>
					{payload.map((entry) => (
						<p
							key={entry.dataKey}
							style={{
								color: entry.color,
								fontSize: "12px",
								marginBottom: "4px",
							}}
						>
							{entry.name}:{" "}
							{entry.name === "レビュー件数"
								? `${entry.value}件`
								: entry.value?.toFixed(1)}
						</p>
					))}
				</Card.Body>
			</Card.Root>
		);
	}
	return null;
};

/**
 * レビュー指標チャートコンポーネント
 *
 * レビュー件数と平均スコアの推移を複合チャートで表示します。
 * レビュー件数はエリアチャート、平均スコアは折れ線グラフで表現されます。
 *
 * @param props - ReviewMetricsChartProps
 * @returns チャートコンポーネント
 *
 * @example
 * ```tsx
 * const data = [
 *   { date: "2024-01", reviewCount: 10, averageScore: 8.5 },
 *   { date: "2024-02", reviewCount: 15, averageScore: 8.7 },
 * ];
 *
 * <ReviewMetricsChart
 *   data={data}
 *   title="月別レビュー推移"
 *   description="レビュー件数と平均評価の推移"
 * />
 * ```
 */
export function ReviewMetricsChart({
	data,
	className,
	title = "レビュー推移",
	description,
	isLoading = false,
}: ReviewMetricsChartProps) {
	// Chakra UI Chartフックの設定
	const chart = useChart({
		data: data || [],
		series: [
			{ name: "reviewCount", label: "レビュー件数", color: "chart.0" },
			{ name: "averageScore", label: "平均スコア", color: "chart.1" },
		],
	});
	return (
		<Card.Root className={className}>
			<Card.Header>
				<Card.Title>
					{isLoading ? <Skeleton height="24px" width="120px" /> : title}
				</Card.Title>
				{description && !isLoading && (
					<Card.Description>{description}</Card.Description>
				)}
			</Card.Header>
			<Card.Body>
				{isLoading ? (
					<Skeleton height={`${chartConfig.height}px`} />
				) : !data || data.length === 0 ? (
					<Center height={`${chartConfig.height}px`}>
						<Text color="fg.muted" fontSize="lg">
							表示するデータがありません
						</Text>
					</Center>
				) : (
					<Chart.Root chart={chart}>
						<ResponsiveContainer width="100%" height={chartConfig.height}>
							<ComposedChart data={chart.data} margin={chartConfig.margins}>
								<CartesianGrid
									strokeDasharray="3 3"
									stroke="var(--chakra-colors-gray-200)"
									vertical={false}
								/>
								<XAxis
									dataKey="date"
									tickLine={false}
									axisLine={false}
									tickMargin={8}
									tickFormatter={chart.formatDate({
										year: "numeric",
										month: "2-digit",
									})}
									tick={{ fontSize: 12 }}
								/>
								<YAxis
									yAxisId="count"
									tickLine={false}
									axisLine={false}
									tickFormatter={(value: number) =>
										`${chart.formatNumber()(value)}件`
									}
									tick={{ fontSize: 12 }}
									label={{
										value: "レビュー件数",
										position: "insideBottomLeft",
										offset: 10,
										angle: -90,
										style: { fontSize: 12 },
									}}
								/>
								<YAxis
									yAxisId="score"
									orientation="right"
									tickLine={false}
									axisLine={false}
									tickFormatter={(value: number) =>
										chart.formatNumber({
											minimumFractionDigits: 1,
											maximumFractionDigits: 1,
										})(value)
									}
									domain={[0, 10]}
									tick={{ fontSize: 12 }}
									label={{
										value: "評価平均（累積）",
										position: "insideBottomRight",
										offset: 10,
										angle: 90,
										style: { fontSize: 12 },
									}}
								/>
								<Tooltip content={<CustomTooltip />} />
								<Area
									yAxisId="count"
									dataKey={chart.key("reviewCount")}
									type="step"
									stroke={chart.color("chart.0")}
									fill={chart.color("chart.0")}
									fillOpacity={0.3}
									strokeWidth={2}
									dot={false}
									name="レビュー件数"
								/>
								<Line
									yAxisId="score"
									dataKey={chart.key("averageScore")}
									type="linear"
									stroke={chart.color("chart.1")}
									strokeWidth={2}
									dot={{
										fill: chart.color("chart.1"),
										r: 3,
									}}
									activeDot={{
										r: 5,
										fill: chart.color("chart.1"),
									}}
									name="平均スコア"
								/>
							</ComposedChart>
						</ResponsiveContainer>
					</Chart.Root>
				)}
			</Card.Body>
		</Card.Root>
	);
}
