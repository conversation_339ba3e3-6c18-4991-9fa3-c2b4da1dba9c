import type { Meta, StoryObj } from "@storybook/react";
import seedrandom from "seedrandom";
import { ReviewMetricsChart } from "./ReviewMetricsChart";

const meta = {
	title: "Shared/Charts/ReviewMetricsChart",
	component: ReviewMetricsChart,
	parameters: {
		layout: "padded",
	},
	tags: ["autodocs"],
	argTypes: {
		isLoading: {
			control: { type: "boolean" },
			description: "ローディング状態を表示するかどうか",
		},
		title: {
			control: { type: "text" },
			description: "チャートのタイトル",
		},
		description: {
			control: { type: "text" },
			description: "チャートの説明文",
		},
		className: {
			control: { type: "text" },
			description: "追加のCSSクラス",
		},
	},
} satisfies Meta<typeof ReviewMetricsChart>;

export default meta;
type Story = StoryObj<typeof meta>;

// サンプルデータ生成
type ReviewMetricsData = {
	date: string;
	reviewCount: number;
	averageScore: number;
};

const generateSampleData = (months: number): Array<ReviewMetricsData> => {
	const data: Array<ReviewMetricsData> = [];
	const today = new Date();
	let cumulativeScore = 0;
	let cumulativeReviewCount = 0;
	const rng = seedrandom("review-metrics-seed");

	for (let i = months - 1; i >= 0; i--) {
		const date = new Date(today.getFullYear(), today.getMonth() - i, 1);
		const monthReviews = Math.floor(rng() * 50) + 10;
		const previousReviewCount = cumulativeReviewCount;
		cumulativeReviewCount += monthReviews;

		// 評価スコアを生成（6.0〜9.5の範囲）
		const monthScore = 6.0 + rng() * 3.5;
		cumulativeScore =
			previousReviewCount === 0
				? monthScore
				: (cumulativeScore * previousReviewCount + monthScore * monthReviews) /
					cumulativeReviewCount;

		data.push({
			date: date.toISOString().split("T")[0],
			reviewCount: cumulativeReviewCount,
			averageScore: Number(cumulativeScore.toFixed(2)),
		});
	}
	return data;
};

// デフォルト表示（12ヶ月分のデータ）
export const Default: Story = {
	args: {
		data: generateSampleData(12),
		title: "レビュー推移",
		description: "過去12ヶ月のレビュー件数と評価平均の推移",
		isLoading: false,
	},
};

// ローディング状態
export const Loading: Story = {
	args: {
		isLoading: true,
		title: "レビュー推移",
	},
};

// 少ないデータ（3ヶ月分）
export const FewDataPoints: Story = {
	args: {
		data: generateSampleData(3),
		title: "レビュー推移（3ヶ月）",
		description: "過去3ヶ月のレビュー件数と評価平均の推移",
		isLoading: false,
	},
};

// 多いデータ（24ヶ月分）
export const ManyDataPoints: Story = {
	args: {
		data: generateSampleData(24),
		title: "レビュー推移（2年間）",
		description: "過去2年間のレビュー件数と評価平均の推移",
		isLoading: false,
	},
};

// データなし
export const NoData: Story = {
	args: {
		data: [],
		title: "レビュー推移",
		description: "データがありません",
		isLoading: false,
	},
};

// 安定したレビュー数と評価
export const StableMetrics: Story = {
	args: {
		data: Array.from({ length: 12 }, (_, i) => {
			const date = new Date();
			date.setMonth(date.getMonth() - (11 - i));
			// 累積レビュー数（毎月安定して25件前後のレビューが追加）
			const cumulativeReviews =
				(i + 1) * 25 + Math.floor(Math.random() * 10) - 5;
			return {
				date: date.toISOString().split("T")[0],
				reviewCount: cumulativeReviews,
				averageScore: 8.0 + Math.random() * 0.5 - 0.25,
			};
		}),
		title: "安定したレビュー推移",
		description: "レビュー数と評価が安定している施設",
		isLoading: false,
	},
};

// 上昇トレンド
export const UpwardTrend: Story = {
	args: {
		data: (() => {
			const data: Array<ReviewMetricsData> = [];
			let cumulativeReviews = 0;

			for (let i = 0; i < 12; i++) {
				const date = new Date();
				date.setMonth(date.getMonth() - (11 - i));
				// 月を追うごとに増加ペースが上がる（10件→60件）
				const monthlyReviews = 10 + i * 5;
				cumulativeReviews += monthlyReviews;

				data.push({
					date: date.toISOString().split("T")[0],
					reviewCount: cumulativeReviews,
					averageScore: 6.0 + i * 0.3,
				});
			}

			return data;
		})(),
		title: "改善傾向のレビュー",
		description: "サービス改善により評価が向上している施設",
		isLoading: false,
	},
};

// 下降トレンド
export const DownwardTrend: Story = {
	args: {
		data: (() => {
			const data: Array<ReviewMetricsData> = [];
			let cumulativeReviews = 0;

			for (let i = 0; i < 12; i++) {
				const date = new Date();
				date.setMonth(date.getMonth() - (11 - i));
				// 月を追うごとに増加ペースが下がる（60件→10件）
				const monthlyReviews = Math.max(10, 60 - i * 4);
				cumulativeReviews += monthlyReviews;

				data.push({
					date: date.toISOString().split("T")[0],
					reviewCount: cumulativeReviews,
					averageScore: 9.0 - i * 0.2,
				});
			}

			return data;
		})(),
		title: "要注意のレビュー推移",
		description: "評価が下降傾向にある施設",
		isLoading: false,
	},
};

// 季節変動
export const SeasonalVariation: Story = {
	args: {
		data: (() => {
			const data: Array<ReviewMetricsData> = [];
			let cumulativeReviews = 0;
			const rng = seedrandom("seasonal-variation-seed");

			for (let i = 0; i < 24; i++) {
				const date = new Date();
				date.setMonth(date.getMonth() - (23 - i));
				// 夏季（6-8月）にピーク
				const month = date.getMonth();
				const seasonalFactor = month >= 5 && month <= 7 ? 2.0 : 1.0;

				// 当月のレビュー数を計算
				const monthlyReviews = Math.floor((20 + rng() * 10) * seasonalFactor);
				cumulativeReviews += monthlyReviews;

				data.push({
					date: date.toISOString().split("T")[0],
					reviewCount: cumulativeReviews,
					averageScore: 7.5 + (seasonalFactor - 1) * 0.5 + rng() * 0.5,
				});
			}

			return data;
		})(),
		title: "季節変動のあるレビュー",
		description: "夏季に人気が高まるリゾート施設",
		isLoading: false,
	},
};

// モバイル表示（375px - iPhone SE）
export const Mobile: Story = {
	args: {
		data: generateSampleData(6),
		title: "レビュー推移",
		description: "過去6ヶ月のレビュー",
		isLoading: false,
	},
	globals: {
		viewport: {
			value: "mobile1",
		},
	},
};

// タブレット表示（768px - iPad）
export const Tablet: Story = {
	args: {
		data: generateSampleData(12),
		title: "レビュー推移",
		description: "過去12ヶ月のレビュー件数と評価平均の推移",
		isLoading: false,
	},
	globals: {
		viewport: {
			value: "tablet",
		},
	},
};

// カスタムタイトルなし
export const NoTitle: Story = {
	args: {
		data: generateSampleData(12),
		title: undefined,
		description: "タイトルなしのチャート",
		isLoading: false,
	},
};
