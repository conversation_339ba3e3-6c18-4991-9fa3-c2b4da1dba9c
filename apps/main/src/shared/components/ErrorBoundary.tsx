"use client";

import {
	Box,
	Button,
	Code,
	Collapsible,
	Flex,
	Heading,
	Icon,
	Text,
	useDisclosure,
	VStack,
} from "@chakra-ui/react";
import * as Sentry from "@sentry/nextjs";
import { AlertTriangle } from "lucide-react";
import { Component, type ReactNode } from "react";

interface Props {
	children: ReactNode;
	fallback?: ReactNode;
	errorType?: string;
	onError?: (error: Error, errorInfo: { componentStack: string }) => void;
}

interface State {
	hasError: boolean;
	error: Error | null;
}

export class ErrorBoundary extends Component<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = { hasError: false, error: null };
	}

	static getDerivedStateFromError(error: Error): State {
		return { hasError: true, error };
	}

	componentDidCatch(error: Error, errorInfo: { componentStack: string }) {
		const { errorType = "unknown", onError } = this.props;

		// Sentryにエラーを送信
		Sentry.withScope((scope) => {
			scope.setTag("errorBoundary", true);
			scope.setTag("errorType", errorType);
			scope.setContext("componentStack", {
				componentStack: errorInfo.componentStack,
			});
			scope.setLevel("error");
			Sentry.captureException(error);
		});

		// カスタムエラーハンドラーを実行
		if (onError) {
			onError(error, errorInfo);
		}

		// 開発環境でのデバッグ用
		if (process.env.NODE_ENV === "development") {
			console.error("ErrorBoundary caught an error:", error, errorInfo);
		}
	}

	handleReset = () => {
		this.setState({ hasError: false, error: null });
	};

	render() {
		if (this.state.hasError) {
			if (this.props.fallback) {
				return this.props.fallback;
			}

			return (
				<VStack
					gap={4}
					p={8}
					bg="bg.error.subtle"
					borderWidth={1}
					borderColor="border.error"
					borderRadius="lg"
					align="center"
				>
					<Icon boxSize={12} color="fg.error">
						<AlertTriangle />
					</Icon>
					<Heading as="h2" size="md" color="fg">
						エラーが発生しました
					</Heading>
					<Text fontSize="sm" color="fg.muted" textAlign="center">
						申し訳ございません。予期しないエラーが発生しました。
						<br />
						問題が続く場合は、サポートまでお問い合わせください。
					</Text>
					<Flex gap={3}>
						<Button onClick={this.handleReset} colorScheme="blue" size="md">
							再試行
						</Button>
						<Button
							onClick={() => window.location.reload()}
							colorScheme="gray"
							variant="solid"
							size="md"
						>
							ページを再読み込み
						</Button>
					</Flex>
					{process.env.NODE_ENV === "development" && this.state.error && (
						<Box w="full" maxW="2xl" mt={4}>
							<ErrorDetails error={this.state.error} />
						</Box>
					)}
				</VStack>
			);
		}

		return this.props.children;
	}
}

// Re-export components from the ErrorBoundary folder
export {
	createFeatureErrorBoundary,
	GenericErrorBoundary,
	NetworkErrorBoundary,
	PermissionErrorBoundary,
} from "./ErrorBoundary/factory";
export type {
	ErrorBoundaryConfig,
	ErrorBoundaryProps as FeatureErrorBoundaryProps,
	ErrorBoundaryState as FeatureErrorBoundaryState,
} from "./ErrorBoundary/types";

// Error details component for development mode
function ErrorDetails({ error }: { error: Error }) {
	const { open, onToggle } = useDisclosure();

	return (
		<Box>
			<Button
				onClick={onToggle}
				variant="ghost"
				size="sm"
				color="fg.subtle"
				fontWeight="normal"
			>
				エラー詳細（開発環境のみ）
			</Button>
			<Collapsible.Root open={open}>
				<Collapsible.Content>
					<Code
						mt={2}
						p={4}
						bg="gray.100"
						borderRadius="md"
						fontSize="xs"
						overflow="auto"
						display="block"
						whiteSpace="pre"
					>
						{error.stack}
					</Code>
				</Collapsible.Content>
			</Collapsible.Root>
		</Box>
	);
}
