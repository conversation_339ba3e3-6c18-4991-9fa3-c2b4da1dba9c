"use client";

import { Box } from "@chakra-ui/react";
import { createListCollection } from "@chakra-ui/react/collection";
import { Select } from "@chakra-ui/react/select";
import { useState } from "react";
import { useSafeBreakpointValue } from "@/shared/components/layout/Sidenav/hooks/useSafeBreakpointValue";
import { ScrollableTabs, type ScrollableTabsProps } from "./ScrollableTabs";

/**
 * レスポンシブ対応スクロール可能タブコンポーネントのプロパティ
 */
export interface ResponsiveScrollableTabsProps extends ScrollableTabsProps {
	/** モバイル表示でセレクトボックスに切り替えるタブ数の閾値 */
	mobileThreshold?: number;
	/** セレクトボックスのプレースホルダーテキスト */
	selectPlaceholder?: string;
}

/**
 * レスポンシブ対応スクロール可能タブコンポーネント
 *
 * モバイルデバイスでタブ数が多い場合、自動的にセレクトボックスに切り替わります。
 * デスクトップまたはタブ数が少ない場合は通常のタブ表示になります。
 *
 * @param props - ResponsiveScrollableTabsProps
 * @returns レスポンシブタブコンポーネント
 *
 * @example
 * ```tsx
 * const tabs = [
 *   { value: "tab1", label: "タブ1", content: <div>コンテンツ1</div> },
 *   { value: "tab2", label: "タブ2", content: <div>コンテンツ2</div> },
 *   // ... 8個以上のタブ
 * ];
 *
 * // モバイルで8個以上のタブがある場合、セレクトボックスに切り替わる
 * <ResponsiveScrollableTabs tabs={tabs} mobileThreshold={7} />
 * ```
 */
export function ResponsiveScrollableTabs({
	tabs,
	mobileThreshold = 7,
	selectPlaceholder = "タブを選択",
	...scrollableTabsProps
}: ResponsiveScrollableTabsProps) {
	const isMobile = useSafeBreakpointValue({ base: true, md: false }, true);
	const [selectedValue, setSelectedValue] = useState(
		scrollableTabsProps.defaultValue || tabs[0]?.value || "",
	);

	// モバイルかつタブ数が閾値を超える場合はSelectコンポーネントを使用
	if (isMobile && tabs.length > mobileThreshold) {
		const selectedTab = tabs.find((tab) => tab.value === selectedValue);
		const collection = createListCollection({
			items: tabs.map((tab) => ({ value: tab.value, label: tab.label })),
		});

		return (
			<>
				<Select.Root
					collection={collection}
					value={[selectedValue]}
					onValueChange={(details) => {
						const newValue = details.value[0];
						if (newValue) {
							setSelectedValue(newValue);
						}
					}}
					mb={4}
				>
					<Select.Control>
						<Select.Trigger>
							<Select.ValueText placeholder={selectPlaceholder} />
							<Select.Indicator />
						</Select.Trigger>
					</Select.Control>
					<Select.Positioner>
						<Select.Content>
							{tabs.map((tab) => (
								<Select.Item
									key={tab.value}
									item={{ value: tab.value, label: tab.label }}
								>
									<Select.ItemText>{tab.label}</Select.ItemText>
								</Select.Item>
							))}
						</Select.Content>
					</Select.Positioner>
				</Select.Root>
				{selectedTab && <Box>{selectedTab.content}</Box>}
			</>
		);
	}

	// デスクトップまたはタブ数が少ない場合は通常のタブ表示
	return (
		<ScrollableTabs
			tabs={tabs}
			{...scrollableTabsProps}
			defaultValue={selectedValue || scrollableTabsProps.defaultValue}
		/>
	);
}
