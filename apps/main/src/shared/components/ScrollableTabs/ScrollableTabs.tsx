"use client";

import {
	Box,
	Icon,
	IconButton,
	Tabs,
	type TabsRootProps,
} from "@chakra-ui/react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import type { ReactNode } from "react";
import { useRef, useState } from "react";

/**
 * スクロール可能なタブのデータ構造
 */
export interface ScrollableTab {
	/** タブの一意識別子 */
	value: string;
	/** タブに表示するラベルテキスト */
	label: string;
	/** タブに表示するアイコン（オプション） */
	icon?: ReactNode;
	/** タブがアクティブな時に表示するコンテンツ */
	content: ReactNode;
}

/**
 * スクロール可能なタブコンポーネントのプロパティ
 */
export interface ScrollableTabsProps extends Omit<TabsRootProps, "children"> {
	/** 表示するタブの配列 */
	tabs: ScrollableTab[];
	/** スクロールボタンを表示するかどうか */
	showScrollButtons?: boolean;
	/** スクロールボタンクリック時のスクロール量（px） */
	scrollAmount?: number;
	/** スナップスクロールを有効にするかどうか */
	enableSnapScroll?: boolean;
}

/**
 * 水平スクロール可能なタブコンポーネント
 *
 * 多数のタブを限られた幅で表示する際に使用します。
 * タッチデバイスではスワイプ操作、デスクトップでは矢印ボタンでスクロール可能です。
 *
 * @param props - ScrollableTabsProps
 * @returns タブコンポーネント
 *
 * @example
 * ```tsx
 * const tabs = [
 *   { value: "tab1", label: "タブ1", content: <div>コンテンツ1</div> },
 *   { value: "tab2", label: "タブ2", content: <div>コンテンツ2</div> },
 * ];
 *
 * <ScrollableTabs tabs={tabs} showScrollButtons={true} />
 * ```
 */
export function ScrollableTabs({
	tabs,
	showScrollButtons = true,
	scrollAmount = 200,
	enableSnapScroll = true,
	...tabsRootProps
}: ScrollableTabsProps) {
	const scrollRef = useRef<HTMLDivElement>(null);
	const [touchStart, setTouchStart] = useState<number | null>(null);
	const [touchEnd, setTouchEnd] = useState<number | null>(null);
	const [isDragging, setIsDragging] = useState(false);

	// タッチスワイプの最小距離
	const minSwipeDistance = 50;

	/**
	 * 指定された方向にタブをスクロールする
	 *
	 * @param direction - スクロール方向 (-1: 左, 1: 右)
	 */
	const scrollBy = (direction: -1 | 1) => {
		scrollRef.current?.scrollBy({
			left: direction * scrollAmount,
			behavior: "smooth",
		});
	};

	/**
	 * タッチ開始イベントハンドラー
	 *
	 * スワイプジェスチャーの開始位置を記録します。
	 * ネイティブスクロールが動作中の場合は新しいタッチを無視します。
	 *
	 * @param e - タッチイベント
	 */
	const handleTouchStart = (e: React.TouchEvent) => {
		// ネイティブスクロールが既に動作中の場合は、新しいタッチを無視
		const element = scrollRef.current;
		if (!element || element.scrollLeft !== Math.round(element.scrollLeft)) {
			return;
		}

		setTouchEnd(null);
		setTouchStart(e.targetTouches[0].clientX);
		setIsDragging(false); // まだドラッグ開始ではない
	};

	/**
	 * タッチ移動イベントハンドラー
	 *
	 * タッチの移動距離を追跡し、一定距離を超えたらドラッグ状態に移行します。
	 *
	 * @param e - タッチイベント
	 */
	const handleTouchMove = (e: React.TouchEvent) => {
		if (touchStart === null) return;

		const currentTouch = e.targetTouches[0].clientX;
		setTouchEnd(currentTouch);

		// 一定の距離を移動したらドラッグ開始
		const distance = Math.abs(touchStart - currentTouch);
		if (distance > 10 && !isDragging) {
			setIsDragging(true);
		}
	};

	/**
	 * タッチ終了イベントハンドラー
	 *
	 * スワイプの方向と距離を判定し、条件を満たす場合は前後のタブへスクロールします。
	 * スワイプ距離が閾値未満の場合は何もしません。
	 */
	const handleTouchEnd = () => {
		if (!touchStart || !touchEnd) {
			setTouchStart(null);
			setTouchEnd(null);
			setIsDragging(false);
			return;
		}

		const distance = touchStart - touchEnd;
		const isLeftSwipe = distance > minSwipeDistance;
		const isRightSwipe = distance < -minSwipeDistance;

		// スワイプ方向に応じてスクロール
		if (isLeftSwipe || isRightSwipe) {
			const element = scrollRef.current;
			if (!element) return;

			// タブごとにスクロールする量を計算
			const tabWidth = element.scrollWidth / tabs.length;
			const currentScroll = element.scrollLeft;
			const currentIndex = Math.round(currentScroll / tabWidth);

			if (isLeftSwipe && currentIndex < tabs.length - 1) {
				// 次のタブへ
				element.scrollTo({
					left: (currentIndex + 1) * tabWidth,
					behavior: "smooth",
				});
			} else if (isRightSwipe && currentIndex > 0) {
				// 前のタブへ
				element.scrollTo({
					left: (currentIndex - 1) * tabWidth,
					behavior: "smooth",
				});
			}
		}

		// 状態をリセット
		setTouchStart(null);
		setTouchEnd(null);
		setIsDragging(false);
	};

	return (
		<Tabs.Root {...tabsRootProps}>
			<Box position="relative">
				{/* スクロールコンテナ */}
				<Box
					ref={scrollRef}
					overflowX="auto"
					overflowY="hidden"
					tabIndex={0}
					whiteSpace="nowrap"
					position="relative"
					_focusVisible={{ outline: "none" }}
					scrollSnapType={enableSnapScroll ? "x mandatory" : undefined}
					onTouchStart={handleTouchStart}
					onTouchMove={handleTouchMove}
					onTouchEnd={handleTouchEnd}
					css={{
						touchAction: isDragging ? "none" : "pan-x",
						overscrollBehaviorX: "contain",
						willChange: "scroll-position",
						WebkitTransform: "translateZ(0)",
						WebkitOverflowScrolling: "touch",
						userSelect: "none",
						"&::-webkit-scrollbar": {
							height: "6px",
						},
						"&::-webkit-scrollbar-track": {
							background: "transparent",
						},
						"&::-webkit-scrollbar-thumb": {
							background: "gray.300",
							borderRadius: "3px",
						},
						"&::-webkit-scrollbar-thumb:hover": {
							background: "gray.400",
						},
					}}
				>
					<Tabs.List minW="max-content" px={showScrollButtons ? 10 : 0}>
						{tabs.map((tab) => (
							<Tabs.Trigger
								key={tab.value}
								value={tab.value}
								scrollSnapAlign={enableSnapScroll ? "start" : undefined}
								onFocus={(e) => {
									// iOS Safari検出
									const isIOSSafari =
										typeof window !== "undefined" &&
										/iPad|iPhone|iPod/.test(navigator.userAgent) &&
										!("MSStream" in window);

									e.currentTarget.scrollIntoView({
										inline: "nearest",
										behavior: isIOSSafari ? "auto" : "smooth",
										block: "nearest",
									});
								}}
							>
								{tab.icon && <Icon>{tab.icon}</Icon>}
								{tab.label}
							</Tabs.Trigger>
						))}
						<Tabs.Indicator />
					</Tabs.List>
				</Box>

				{/* スクロールボタン */}
				{showScrollButtons && (
					<>
						<IconButton
							aria-label="前のタブへスクロール"
							onClick={() => scrollBy(-1)}
							position="absolute"
							left={0}
							top="50%"
							transform="translateY(-50%)"
							size="sm"
							variant="outline"
							zIndex={1}
							shadow="md"
							_hover={{
								shadow: "lg",
							}}
						>
							<Icon>
								<ChevronLeft />
							</Icon>
						</IconButton>
						<IconButton
							aria-label="次のタブへスクロール"
							onClick={() => scrollBy(1)}
							position="absolute"
							right={0}
							top="50%"
							transform="translateY(-50%)"
							size="sm"
							variant="outline"
							zIndex={1}
							shadow="md"
							_hover={{
								shadow: "lg",
							}}
						>
							<Icon>
								<ChevronRight />
							</Icon>
						</IconButton>
					</>
				)}
			</Box>

			{/* タブコンテンツ */}
			<Tabs.ContentGroup>
				{tabs.map((tab) => (
					<Tabs.Content key={tab.value} value={tab.value}>
						{tab.content}
					</Tabs.Content>
				))}
			</Tabs.ContentGroup>
		</Tabs.Root>
	);
}
