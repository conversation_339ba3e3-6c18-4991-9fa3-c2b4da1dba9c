import type { Meta, StoryObj } from "@storybook/react";
import { ErrorNotFound, NotFound, SimpleNotFound } from "./index";

const meta = {
	title: "Shared/Components/NotFound",
	component: NotFound,
	parameters: {
		layout: "centered",
		docs: {
			description: {
				component:
					"404エラーページやコンテンツが見つからない場合に表示するコンポーネント",
			},
		},
	},
	tags: ["autodocs"],
	argTypes: {
		onHomeClick: {
			description: "ホームボタンクリック時のコールバック",
		},
		onBackClick: {
			description: "戻るボタンクリック時のコールバック",
		},
		customMessages: {
			description: "カスタムメッセージ設定",
		},
		additionalActions: {
			description: "追加のアクションボタン",
		},
	},
} satisfies Meta<typeof NotFound>;

export default meta;
type Story = StoryObj<typeof meta>;

// デフォルト（基本的な404ページ）
export const Default: Story = {
	args: {},
};

// イベントハンドラー付き
export const WithHandlers: Story = {
	args: {
		onHomeClick: () => console.log("Home clicked"),
		onBackClick: () => console.log("Back clicked"),
	},
};

// カスタムメッセージ
export const CustomMessages: Story = {
	args: {
		customMessages: {
			title: "お探しのページは見つかりませんでした",
			description: "URLをご確認いただくか、ホームページからお探しください。",
		},
	},
};

// 追加アクション付き
export const WithAdditionalActions: Story = {
	args: {
		onHomeClick: () => console.log("Home clicked"),
		onBackClick: () => console.log("Back clicked"),
		additionalActions: [
			{
				label: "ヘルプセンター",
				onClick: () => console.log("Help clicked"),
			},
			{
				label: "お問い合わせ",
				onClick: () => console.log("Contact clicked"),
			},
			{
				label: "サイトマップ",
				onClick: () => console.log("Sitemap clicked"),
			},
		],
	},
};

// 追加アクションのバリアント付き
export const AdditionalActionsWithVariant: Story = {
	args: {
		additionalActions: [
			{
				label: "ヘルプ",
				onClick: () => console.log("Help clicked"),
				variant: "outline",
			},
			{
				label: "サポート",
				onClick: () => console.log("Support clicked"),
				variant: "ghost",
			},
		],
	},
};

// 最小構成（ボタンなし）
export const NoButtons: Story = {
	args: {
		// onHomeClickとonBackClickを設定しないことでボタンが表示されない
	},
};

// メンテナンス中のメッセージ
export const Maintenance: Story = {
	args: {
		customMessages: {
			title: "メンテナンス中",
			description:
				"システムメンテナンスのため、一時的にサービスをご利用いただけません。ご不便をおかけして申し訳ございません。",
		},
		onHomeClick: () => console.log("Home clicked"),
		additionalActions: [
			{
				label: "メンテナンス情報",
				onClick: () => console.log("Maintenance info clicked"),
				variant: "outline",
			},
		],
	},
};

// アクセス拒否メッセージ
export const Forbidden: Story = {
	args: {
		customMessages: {
			title: "アクセスが拒否されました",
			description:
				"このページを閲覧する権限がありません。管理者にお問い合わせください。",
		},
		onHomeClick: () => console.log("Home clicked"),
	},
};

// シンプル版のNotFoundコンポーネント
export const SimpleVersion: Story = {
	render: (args) => <SimpleNotFound {...args} />,
	args: {
		onHomeClick: () => console.log("Home clicked"),
		onBackClick: () => console.log("Back clicked"),
	},
};

// エラー版のNotFoundコンポーネント
export const ErrorVersion: Story = {
	render: (args) => <ErrorNotFound {...args} />,
	args: {
		customMessages: {
			title: "エラーが発生しました",
			description: "申し訳ございません。予期しないエラーが発生しました。",
		},
		onHomeClick: () => console.log("Home clicked"),
		onBackClick: () => console.log("Back clicked"),
	},
};

// モバイル表示
export const Mobile: Story = {
	args: {
		onHomeClick: () => console.log("Home clicked"),
		onBackClick: () => console.log("Back clicked"),
	},
	parameters: {
		viewport: {
			defaultViewport: "mobile1",
		},
	},
};

// タブレット表示
export const Tablet: Story = {
	args: {
		onHomeClick: () => console.log("Home clicked"),
		onBackClick: () => console.log("Back clicked"),
	},
	parameters: {
		viewport: {
			defaultViewport: "tablet",
		},
	},
};

// ダークモード（Chakra UIのカラーモードで自動対応）
export const DarkMode: Story = {
	args: {
		onHomeClick: () => console.log("Home clicked"),
		onBackClick: () => console.log("Back clicked"),
	},
	parameters: {
		backgrounds: {
			default: "dark",
		},
	},
};
