"use client";

import {
	<PERSON>,
	But<PERSON>,
	Heading,
	Icon,
	Stack,
	Text,
	VStack,
} from "@chakra-ui/react";
import { FileQuestion, Home, MoveLeft } from "lucide-react";
import React from "react";
import type { NotFoundConfig, NotFoundProps } from "./types";

/**
 * NotFoundコンポーネントのファクトリ関数
 *
 * カスタマイズされた404ページコンポーネントを生成します。
 * 配色、メッセージ、サイズ、レイアウトを柔軟にカスタマイズできます。
 *
 * @param config - NotFoundコンポーネントの設定オブジェクト
 * @param config.componentName - コンポーネントの表示名
 * @param config.colorScheme - カラースキームの設定
 * @param config.messages - 表示メッセージのカスタマイズ
 * @param config.sizes - アイコンやテキストのサイズ設定
 * @param config.layout - レイアウト関連の設定
 * @returns メモ化されたNotFoundコンポーネント
 * @example
 * const MyNotFound = createNotFoundComponent({
 *   componentName: "MyNotFound",
 *   messages: {
 *     title: "お探しのページは見つかりません",
 *   },
 *   colorScheme: {
 *     background: "blue.50",
 *   },
 * });
 */
export function createNotFoundComponent(config: NotFoundConfig = {}) {
	const {
		componentName = "NotFound",
		colorScheme = {},
		messages = {},
		sizes = {},
		layout = {},
	} = config;

	// デフォルト値を設定
	const defaultColorScheme = {
		background: "gray.50",
		textColor: "gray.800",
		subTextColor: "gray.600",
		iconColor: "gray.400",
		buttonColorScheme: "blue",
		...colorScheme,
	};

	const defaultMessages = {
		title: "ページが見つかりません",
		description: "お探しのページは存在しないか、移動した可能性があります。",
		homeButtonText: "ホームへ戻る",
		backButtonText: "前のページへ戻る",
		...messages,
	};

	const defaultSizes = {
		iconSize: 120,
		titleSize: { base: "2xl", md: "4xl" },
		descriptionSize: { base: "md", md: "lg" },
		buttonSize: "md" as const,
		...sizes,
	};

	const defaultLayout = {
		maxWidth: "container.sm",
		padding: { base: 4, md: 8 },
		spacing: 8,
		...layout,
	};

	/**
	 * NotFoundコンポーネントの実装
	 *
	 * @param props - NotFoundコンポーネントのプロパティ
	 * @param props.onHomeClick - ホームボタンクリック時のハンドラ
	 * @param props.onBackClick - 戻るボタンクリック時のハンドラ
	 * @param props.customMessages - メッセージのカスタマイズ
	 * @param props.additionalActions - 追加のアクションボタン
	 * @returns NotFoundコンポーネント
	 */
	const NotFoundComponent: React.FC<NotFoundProps> = ({
		onHomeClick,
		onBackClick,
		customMessages = {},
		additionalActions = [],
	}) => {
		// メッセージをマージ
		const finalMessages = {
			...defaultMessages,
			...customMessages,
		};

		return (
			<Box
				bg={defaultColorScheme.background}
				display="flex"
				alignItems="center"
				justifyContent="center"
				px={defaultLayout.padding}
				css={{
					minHeight: "100vh",
					"@supports (height: 100dvh)": {
						minHeight: "100dvh",
					},
				}}
			>
				<VStack
					maxW={defaultLayout.maxWidth}
					w="full"
					gap={defaultLayout.spacing}
					textAlign="center"
				>
					{/* アイコン */}
					<Icon
						color={defaultColorScheme.iconColor}
						boxSize={defaultSizes.iconSize}
					>
						<FileQuestion />
					</Icon>

					{/* タイトル */}
					<Heading
						as="h1"
						fontSize={defaultSizes.titleSize}
						color={defaultColorScheme.textColor}
						fontWeight="bold"
					>
						{finalMessages.title}
					</Heading>

					{/* 説明文 */}
					<Text
						fontSize={defaultSizes.descriptionSize}
						color={defaultColorScheme.subTextColor}
						maxW="md"
					>
						{finalMessages.description}
					</Text>

					{/* アクションボタン */}
					<Stack
						direction={{ base: "column", sm: "row" }}
						gap={4}
						mt={6}
						w={{ base: "full", sm: "auto" }}
					>
						{/* 戻るボタン */}
						{onBackClick && (
							<Button
								variant="outline"
								colorScheme={defaultColorScheme.buttonColorScheme}
								size={defaultSizes.buttonSize}
								onClick={onBackClick}
								w={{ base: "full", sm: "auto" }}
							>
								<Icon boxSize="5">
									<MoveLeft />
								</Icon>
								{finalMessages.backButtonText}
							</Button>
						)}

						{/* ホームボタン */}
						{onHomeClick && (
							<Button
								colorScheme={defaultColorScheme.buttonColorScheme}
								size={defaultSizes.buttonSize}
								onClick={onHomeClick}
								w={{ base: "full", sm: "auto" }}
							>
								<Icon boxSize="5">
									<Home />
								</Icon>
								{finalMessages.homeButtonText}
							</Button>
						)}

						{/* 追加のアクションボタン */}
						{additionalActions.map((action, index) => (
							<Button
								key={`${action.label}-${index}`}
								variant={action.variant || "ghost"}
								colorScheme={defaultColorScheme.buttonColorScheme}
								size={defaultSizes.buttonSize}
								onClick={action.onClick}
								w={{ base: "full", sm: "auto" }}
							>
								{action.label}
							</Button>
						))}
					</Stack>
				</VStack>
			</Box>
		);
	};

	NotFoundComponent.displayName = componentName;

	return React.memo(NotFoundComponent);
}

/**
 * デフォルト設定のNotFoundコンポーネント
 *
 * 標準的な404ページとして使用できるデフォルト設定のコンポーネント。
 * グレー系の落ち着いた配色を使用します。
 *
 * @example
 * <NotFound
 *   onHomeClick={() => router.push('/')}
 *   onBackClick={() => router.back()}
 * />
 */
export const NotFound = createNotFoundComponent({
	componentName: "NotFound",
});

/**
 * 軽量版NotFoundコンポーネント
 *
 * シンプルでミニマルなデザインの404ページ。
 * 白背景にグレー系のテキスト、小さめのアイコンとボタンを使用します。
 *
 * @example
 * <SimpleNotFound
 *   onHomeClick={() => window.location.href = '/'}
 * />
 */
export const SimpleNotFound = createNotFoundComponent({
	componentName: "SimpleNotFound",
	colorScheme: {
		background: "white",
		textColor: "gray.700",
		subTextColor: "gray.500",
		iconColor: "gray.300",
		buttonColorScheme: "gray",
	},
	sizes: {
		iconSize: 80,
		titleSize: { base: "xl", md: "2xl" },
		descriptionSize: { base: "sm", md: "md" },
		buttonSize: "sm",
	},
	layout: {
		spacing: 6,
	},
});

/**
 * エラー風NotFoundコンポーネント
 *
 * エラーページとしての404ページ。
 * 赤系の配色を使用し、エラー発生を強調したデザイン。
 * サーバーエラーやAPIエラー時に使用します。
 *
 * @example
 * <ErrorNotFound
 *   onHomeClick={() => router.push('/')}
 *   customMessages={{
 *     description: "APIエラーが発生しました",
 *   }}
 * />
 */
export const ErrorNotFound = createNotFoundComponent({
	componentName: "ErrorNotFound",
	colorScheme: {
		background: "red.50",
		textColor: "red.800",
		subTextColor: "red.600",
		iconColor: "red.400",
		buttonColorScheme: "red",
	},
	messages: {
		title: "404 エラー",
		description: "申し訳ございません。ページの読み込みに失敗しました。",
	},
});
