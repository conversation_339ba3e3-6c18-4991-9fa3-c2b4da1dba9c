/**
 * NotFoundコンポーネントの設定
 */
export interface NotFoundConfig {
	/** コンポーネント名 */
	componentName?: string;
	/** カラースキーム設定 */
	colorScheme?: {
		/** 背景色 */
		background?: string;
		/** テキスト色 */
		textColor?: string;
		/** サブテキスト色 */
		subTextColor?: string;
		/** アイコン色 */
		iconColor?: string;
		/** ボタンカラースキーム */
		buttonColorScheme?: string;
	};
	/** メッセージ設定 */
	messages?: {
		/** タイトル */
		title?: string;
		/** 説明文 */
		description?: string;
		/** ホームボタンのテキスト */
		homeButtonText?: string;
		/** 戻るボタンのテキスト */
		backButtonText?: string;
	};
	/** サイズ設定 */
	sizes?: {
		/** アイコンサイズ */
		iconSize?: number;
		/** タイトルサイズ */
		titleSize?: string | { base?: string; md?: string };
		/** 説明文サイズ */
		descriptionSize?: string | { base?: string; md?: string };
		/** ボタンサイズ */
		buttonSize?: "sm" | "md" | "lg" | "xl" | "2xl" | "2xs" | "xs";
	};
	/** レイアウト設定 */
	layout?: {
		/** 最大幅 */
		maxWidth?: string;
		/** パディング */
		padding?: number | { base?: number; md?: number };
		/** 要素間のスペース */
		spacing?: number;
	};
}

/**
 * NotFoundコンポーネントのProps
 */
export interface NotFoundProps {
	/** ホームボタンクリック時のハンドラ */
	onHomeClick?: () => void;
	/** 戻るボタンクリック時のハンドラ */
	onBackClick?: () => void;
	/** カスタムメッセージ（個別にオーバーライド可能） */
	customMessages?: {
		title?: string;
		description?: string;
	};
	/** 追加のアクションボタン */
	additionalActions?: Array<{
		label: string;
		onClick: () => void;
		variant?: "solid" | "outline" | "ghost" | "subtle" | "surface" | "plain";
	}>;
}
