import type { NavItem } from "../components/layout/Sidenav/types";

/**
 * 静的ナビゲーションデータの型定義
 * iconフィールドをiconNameに置き換えた型
 */
export interface NavItemData extends Omit<NavItem, "icon"> {
	iconName?: string;
	children?: NavItemData[];
}

/**
 * ナビゲーションアイテムの静的データ定義
 * アイコンは文字列として定義し、実際のコンポーネントは使用時に解決する
 */
export const navigationData: NavItemData[] = [
	{
		id: "dashboard",
		label: "ダッシュボード",
		href: "/app",
		iconName: "CalendarRange",
	},
	{
		id: "review-analysis",
		label: "レビュー分析",
		href: "/app/reviews",
		iconName: "MessageSquareHeart",
	},
];

/**
 * 下部に表示するナビゲーションアイテム
 */
export const bottomNavigationData: NavItemData[] = [
	{
		id: "manual",
		label: "マニュアル",
		href: process.env.NEXT_PUBLIC_MANUAL_URL || "https://docs.example.com",
		iconName: "Book",
		isExternal: true,
	},
	{
		id: "settings",
		label: "設定",
		href: "/app/settings",
		iconName: "Settings",
	},
];

/**
 * アプリケーション名
 */
export const APP_NAME = "Kadou Delta";

/**
 * アプリケーションの説明
 */
export const APP_DESCRIPTION = "Next.js + Convex + Clerk";
