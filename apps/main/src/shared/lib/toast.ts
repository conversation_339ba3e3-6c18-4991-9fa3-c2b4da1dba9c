"use client";

import { createToaster } from "@chakra-ui/react";

/**
 * アプリケーション全体で使用する統一されたトースター
 * - 表示位置: 画面右下（bottom-end）
 * - ページアイドル時に一時停止
 */
export const toaster: ReturnType<typeof createToaster> = createToaster({
	placement: "bottom-end",
	pauseOnPageIdle: true,
});

/**
 * 成功トーストを表示
 */
export function toastSuccess(
	title: string,
	description?: string,
	options?: { duration?: number; id?: string },
) {
	return toaster.success({
		title,
		description,
		duration: options?.duration,
		id: options?.id,
	});
}

/**
 * エラートーストを表示
 */
export function toastError(
	title: string,
	description?: string,
	options?: { duration?: number; id?: string },
) {
	return toaster.error({
		title,
		description,
		duration: options?.duration,
		id: options?.id,
	});
}

/**
 * 警告トーストを表示
 */
export function toastWarning(
	title: string,
	description?: string,
	options?: { duration?: number; id?: string },
) {
	return toaster.warning({
		title,
		description,
		duration: options?.duration,
		id: options?.id,
	});
}

/**
 * 情報トーストを表示
 */
export function toastInfo(
	title: string,
	description?: string,
	options?: { duration?: number; id?: string },
) {
	return toaster.info({
		title,
		description,
		duration: options?.duration,
		id: options?.id,
	});
}

/**
 * ローディングトーストを表示
 */
export function toastLoading(
	title: string,
	description?: string,
	options?: { duration?: number; id?: string },
) {
	return toaster.loading({
		title,
		description,
		duration: options?.duration,
		id: options?.id,
	});
}

/**
 * エラーハンドリングと連携したトースト表示
 * - エラーメッセージを自動的に解析して表示
 * - エラーの詳細情報を description に含める
 */
export function toastErrorFromException(
	error: unknown,
	defaultTitle = "エラーが発生しました",
	options?: { duration?: number; id?: string },
) {
	let title = defaultTitle;
	let description: string | undefined;

	if (error instanceof Error) {
		// エラーメッセージをタイトルに使用
		title = error.message || defaultTitle;

		// エラーの詳細情報を description に設定
		if (error.cause) {
			// cause がある場合は、その情報を含める
			if (error.cause instanceof Error) {
				description = error.cause.message;
			} else if (typeof error.cause === "string") {
				description = error.cause;
			} else {
				description = `詳細: ${String(error.cause)}`;
			}
		} else if (error.stack && process.env.NODE_ENV === "development") {
			// 開発環境でのみスタックトレースの一部を表示
			const stackLines = error.stack.split("\n");
			// 最初の2行（エラーメッセージとエラー発生箇所）を取得
			description = stackLines.slice(1, 2).join("\n").trim();
		}
	} else if (typeof error === "string") {
		title = error;
	} else if (error && typeof error === "object" && "message" in error) {
		// Error オブジェクトではないが、message プロパティを持つオブジェクト
		title = String(error.message);
		if ("details" in error) {
			description = String(error.details);
		}
	}

	return toaster.error({
		title,
		description,
		duration: options?.duration,
		id: options?.id,
	});
}
