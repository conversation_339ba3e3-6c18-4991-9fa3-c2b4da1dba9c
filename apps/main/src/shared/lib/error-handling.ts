import * as Sentry from "@sentry/nextjs";
import type { ErrorInfo } from "react";

/**
 * エラーコンテキストを含むカスタムエラークラス
 */
export class ErrorWithContext extends Error {
	constructor(
		message: string,
		public context: Record<string, unknown>,
		public cause?: unknown,
	) {
		super(message);
		this.name = "ErrorWithContext";
		Object.setPrototypeOf(this, ErrorWithContext.prototype);
	}
}

/**
 * エラーをハンドリングしてログとSentryに送信
 */
export function handleError(
	error: unknown,
	context?: Record<string, unknown>,
): void {
	console.error("Error:", error);

	if (process.env.NODE_ENV === "development" && context) {
		console.error("Error context:", context);
	}

	Sentry.withScope((scope) => {
		if (context) {
			scope.setContext("error", context);
		}
		Sentry.captureException(error);
	});
}

/**
 * ネットワークエラーかどうかを判定
 */
export function isNetworkError(error: unknown): boolean {
	if (!(error instanceof Error)) {
		return false;
	}

	const networkErrorPatterns = [
		"network",
		"fetch",
		"ERR_INTERNET_DISCONNECTED",
		"ERR_NETWORK",
		"ENOTFOUND",
		"ECONNREFUSED",
		"ETIMEDOUT",
	];

	const message = error.message.toLowerCase();
	return networkErrorPatterns.some((pattern) =>
		message.includes(pattern.toLowerCase()),
	);
}

/**
 * バリデーションエラーかどうかを判定
 */
export function isValidationError(error: unknown): boolean {
	if (!(error instanceof Error)) {
		return false;
	}

	const validationErrorPatterns = [
		"validation",
		"invalid",
		"required",
		"must be",
		"should be",
	];

	const message = error.message.toLowerCase();
	const isValidationPattern = validationErrorPatterns.some((pattern) =>
		message.includes(pattern.toLowerCase()),
	);

	return isValidationPattern || error.name === "ValidationError";
}

/**
 * エラーからメッセージを取得
 */
export function getErrorMessage(error: unknown): string {
	if (error instanceof Error) {
		return error.message;
	}

	if (typeof error === "string") {
		return error;
	}

	if (
		error &&
		typeof error === "object" &&
		"message" in error &&
		typeof error.message === "string"
	) {
		return error.message;
	}

	if (
		error &&
		typeof error === "object" &&
		"error" in error &&
		typeof error.error === "string"
	) {
		return error.error;
	}

	// ネストしたエラーオブジェクトのチェック
	if (
		error &&
		typeof error === "object" &&
		"response" in error &&
		error.response &&
		typeof error.response === "object" &&
		"data" in error.response &&
		error.response.data &&
		typeof error.response.data === "object" &&
		"message" in error.response.data &&
		typeof error.response.data.message === "string"
	) {
		return error.response.data.message;
	}

	return "不明なエラーが発生しました";
}

/**
 * コンテキスト付きエラーを作成
 */
export function createErrorWithContext(
	message: string | Error,
	context: Record<string, unknown>,
): ErrorWithContext {
	if (message instanceof Error) {
		return new ErrorWithContext(message.message, context, message);
	}
	return new ErrorWithContext(message, context);
}

/**
 * フィーチャー固有のErrorBoundary用エラーハンドラー
 * @param featureName - フィーチャー名（コンテキスト用）
 * @param error - エラーオブジェクト
 * @param errorInfo - Reactのエラー情報（コンポーネントスタック含む）
 */
export function handleFeatureError(
	featureName: string,
	error: Error,
	errorInfo: ErrorInfo,
): void {
	console.error(`${featureName} Error:`, error, errorInfo);

	Sentry.withScope((scope) => {
		scope.setTag("feature", featureName.toLowerCase());
		scope.setContext("errorBoundary", {
			componentStack: errorInfo.componentStack,
			feature: featureName,
		});
		Sentry.captureException(error);
	});
}

/**
 * エラーリカバリーのログ記録（モニタリング用）
 * @param featureName - リカバリーしたフィーチャー名
 */
export function logErrorRecovery(featureName: string): void {
	console.log(`${featureName} error boundary recovered`);

	Sentry.addBreadcrumb({
		message: `${featureName} error boundary recovered`,
		level: "info",
		category: "error-boundary",
		data: {
			feature: featureName,
		},
	});
}
