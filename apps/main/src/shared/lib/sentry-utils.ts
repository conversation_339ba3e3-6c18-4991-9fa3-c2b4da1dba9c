"use client";

import * as Sen<PERSON> from "@sentry/nextjs";

/**
 * ClerkユーザーをSentryユーザーとして設定します
 *
 * クライアントサイド専用のユーティリティ関数です。
 * サーバーサイドでは何も実行されません。
 *
 * @param {Object|null} user - Clerkユーザーオブジェクトまたはnull
 * @param {string} user.id - ユーザーID（必須）
 * @param {string} [user.email] - ユーザーのメールアドレス（オプション）
 *
 * @example
 * // ユーザー情報を設定
 * setSentryUser({ id: "user_123", email: "<EMAIL>" });
 *
 * @example
 * // ユーザー情報をクリア（ログアウト時など）
 * setSentryUser(null);
 */
export const setSentryUser = (user: { id: string; email?: string } | null) => {
	if (typeof window === "undefined") {
		// サーバーサイドでは何もしない
		return;
	}

	if (user) {
		Sentry.setUser({
			id: user.id,
			email: user.email,
		});
	} else {
		Sentry.setUser(null);
	}
};
