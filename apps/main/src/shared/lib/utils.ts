import { type ClassValue, clsx } from "clsx";

/**
 * クラス名を結合するユーティリティ関数
 * clsxを使用して条件付きクラス名や複数のクラス名を効率的に結合します
 * @param {...ClassValue[]} inputs - 結合するクラス名の配列（文字列、配列、オブジェクト、条件式など）
 * @returns {string} 結合されたクラス名文字列
 * @example
 * cn("base-class", isActive && "active", { "hover:bg-gray-100": isHoverable })
 * // => "base-class active hover:bg-gray-100" (条件がtrueの場合)
 */
export function cn(...inputs: ClassValue[]) {
	return clsx(inputs);
}

/**
 * 日付を日本語ロケールでフォーマットする関数
 * 年月日を「2024年1月27日」形式で表示します
 * @param {Date | string | number} date - フォーマットする日付（Date型、文字列、またはタイムスタンプ）
 * @returns {string} フォーマットされた日付文字列（例：2024年1月27日）
 * @example
 * formatDate(new Date()) // => "2024年1月27日"
 * formatDate("2024-01-27") // => "2024年1月27日"
 * formatDate(1706329200000) // => "2024年1月27日"
 */
export function formatDate(date: Date | string | number): string {
	const d = new Date(date);
	return new Intl.DateTimeFormat("ja-JP", {
		year: "numeric",
		month: "long",
		day: "numeric",
	}).format(d);
}

/**
 * 数値を日本語ロケールでフォーマットする関数
 * 3桁ごとにカンマ区切りで表示します
 * @param {number} num - フォーマットする数値
 * @returns {string} フォーマットされた数値文字列（例：1,234,567）
 * @example
 * formatNumber(1234567) // => "1,234,567"
 * formatNumber(1000.5) // => "1,000.5"
 */
export function formatNumber(num: number): string {
	return new Intl.NumberFormat("ja-JP").format(num);
}
