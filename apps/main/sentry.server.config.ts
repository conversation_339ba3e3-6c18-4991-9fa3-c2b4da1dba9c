import * as Sentry from "@sentry/nextjs";

// 共通のSentry DSN
const SENTRY_DSN =
	"https://<EMAIL>/4509557822652416";

Sentry.init({
	dsn: SENTRY_DSN,
	debug: process.env.NODE_ENV !== "production",
	environment: process.env.NODE_ENV || "development",
	tracesSampleRate: process.env.NODE_ENV === "production" ? 0.1 : 1.0,
	// 実験的機能
	_experiments: {
		// ログの有効化
		enableLogs: true,
	},
	// サーバー固有の設定
	integrations: [
		// HTTPリクエストの追跡
		Sentry.httpIntegration(),
		// 未処理のPromiseリジェクションのキャプチャ
		Sentry.onUncaughtExceptionIntegration(),
		Sentry.onUnhandledRejectionIntegration(),
	],
	// 追加のタグとコンテキスト
	initialScope: {
		tags: {
			component: "server",
			runtime: "nodejs",
		},
	},
});

// Next.jsのリクエストエラーをキャプチャするためのエクスポート
export const onRequestError = Sentry.captureRequestError;
