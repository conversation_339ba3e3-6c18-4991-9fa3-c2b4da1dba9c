/**
 * Beds24 Sync Mutations
 *
 * このファイルはBeds24同期に関する統合ミューテーションを提供します。
 * V8環境で実行されるため、"use node"ディレクティブはありません。
 * 複数のクエリ/ミューテーションを単一トランザクションで実行することで
 * パフォーマンスを最適化します。
 */

import { v } from "convex/values";
import type { Id } from "./_generated/dataModel";
import { internalMutation } from "./_generated/server";
import { createLogger } from "./lib/logging";
import {
	softDeleteInactiveProperties,
	updateSyncHistoryStatus,
	upsertBeds24Properties,
} from "./model/beds24";
import {
	beds24APIPropertyValidator,
	QueueJobType,
	SyncHistoryStatus,
} from "./types/beds24";

/**
 * 同期処理の準備を一括で行う
 *
 * アクセストークンの確認/更新と同期履歴の記録開始を単一トランザクションで実行します。
 * パフォーマンス最適化のため、複数のクエリ/ミューテーションをまとめて処理します。
 *
 * @param userId - 同期を実行するユーザーのID
 * @param jobType - 実行するジョブタイプ（施設同期、レビュー同期、予約同期など）
 * @returns 成功時はhistoryID、アクセストークン、トークン期限を返す。失敗時はエラーメッセージを返す
 * @example
 * // 施設同期の準備
 * const result = await ctx.runMutation(
 *   internal.beds24SyncMutations.prepareSyncData,
 *   { userId: "user123", jobType: QueueJobType.SYNC_PROPERTIES }
 * );
 * if (result.success) {
 *   console.log("History ID:", result.historyId);
 * }
 */
export const prepareSyncData = internalMutation({
	args: {
		userId: v.string(),
		jobType: v.union(
			v.literal(QueueJobType.SYNC_PROPERTIES),
			v.literal(QueueJobType.SYNC_REVIEWS),
			v.literal(QueueJobType.SCRAPE_BOOKING_COM_SLUG),
			v.literal(QueueJobType.SYNC_BOOKINGS),
		),
	},
	returns: v.union(
		v.object({
			success: v.literal(true),
			historyId: v.id("beds24SyncHistory"),
			accessToken: v.string(),
			tokenExpiresAt: v.number(),
			needsTokenRefresh: v.boolean(),
		}),
		v.object({
			success: v.literal(false),
			error: v.string(),
		}),
	),
	handler: async (
		ctx,
		args,
	): Promise<
		| {
				success: true;
				historyId: Id<"beds24SyncHistory">;
				accessToken: string;
				tokenExpiresAt: number;
				needsTokenRefresh: boolean;
		  }
		| {
				success: false;
				error: string;
		  }
	> => {
		const logger = createLogger("prepareSyncData", ctx);
		logger.setArgs(args);

		try {
			// 1. アクセストークンを確認
			const tokenInfo = await ctx.db
				.query("beds24AccessTokens")
				.withIndex("by_userId", (q) => q.eq("userId", args.userId))
				.first();

			// トークンの有効性を確認（1分のマージン）
			const needsTokenRefresh =
				!tokenInfo || tokenInfo.expiresAt < Date.now() + 60000;

			// 2. 同期履歴の記録を開始
			const now = Date.now();
			const historyId = await ctx.db.insert("beds24SyncHistory", {
				userId: args.userId,
				jobType: args.jobType,
				status: SyncHistoryStatus.PROCESSING,
				startedAt: now,
				duration: 0,
				totalItems: 0,
				successCount: 0,
				failedCount: 0,
				errors: [],
				createdAt: now,
				updatedAt: now,
			});

			if (!tokenInfo) {
				// トークンが存在しない場合はエラー
				return {
					success: false,
					error: "アクセストークンが見つかりません",
				};
			}

			return {
				success: true,
				historyId,
				accessToken: tokenInfo?.accessToken || "",
				tokenExpiresAt: tokenInfo?.expiresAt || 0,
				needsTokenRefresh,
			};
		} catch (error) {
			logger.error("同期準備中にエラーが発生しました", { error });
			return {
				success: false,
				error: error instanceof Error ? error.message : "不明なエラー",
			};
		}
	},
});

/**
 * 同期処理の完了を一括で記録する
 *
 * 施設データの更新、削除済み施設のマーク、同期履歴の完了記録を単一トランザクションで実行します。
 * パフォーマンス最適化のため、複数のミューテーションをまとめて処理します。
 *
 * @param userId - 同期を実行したユーザーのID
 * @param historyId - 同期履歴のID
 * @param properties - 同期された施設データの配列
 * @param success - 同期処理の成功/失敗フラグ
 * @param error - エラーメッセージ（失敗時のみ）
 * @returns 作成・更新・削除されたレコード数とエラー情報
 * @example
 * // 同期処理の完了を記録
 * const result = await ctx.runMutation(
 *   internal.beds24SyncMutations.completeSyncData,
 *   {
 *     userId: "user123",
 *     historyId: "history456",
 *     properties: syncedProperties,
 *     success: true
 *   }
 * );
 * console.log(`作成: ${result.createdCount}, 更新: ${result.updatedCount}`);
 */
export const completeSyncData = internalMutation({
	args: {
		userId: v.string(),
		historyId: v.id("beds24SyncHistory"),
		properties: v.array(beds24APIPropertyValidator),
		success: v.boolean(),
		error: v.optional(v.string()),
	},
	returns: v.object({
		createdCount: v.number(),
		updatedCount: v.number(),
		deletedCount: v.number(),
		errors: v.array(v.string()),
	}),
	handler: async (
		ctx,
		args,
	): Promise<{
		createdCount: number;
		updatedCount: number;
		deletedCount: number;
		errors: string[];
	}> => {
		const logger = createLogger("completeSyncData", ctx);
		logger.setArgs({
			userId: args.userId,
			historyId: args.historyId,
			propertyCount: args.properties.length,
		});

		const now = Date.now();

		try {
			const result = {
				createdCount: 0,
				updatedCount: 0,
				deletedCount: 0,
				errors: [] as string[],
			};

			if (args.success && args.properties.length > 0) {
				// 1. 施設データのupsert
				const updateResult = await upsertBeds24Properties(
					ctx,
					args.userId,
					args.properties,
					now,
				);
				result.createdCount = updateResult.created;
				result.updatedCount = updateResult.updated;
				result.errors = updateResult.errors;

				// 2. 非アクティブ施設のソフトデリート
				const activePropertyIds = new Set(
					args.properties.map((p) => p.id.toString()),
				);
				result.deletedCount = await softDeleteInactiveProperties(
					ctx,
					args.userId,
					activePropertyIds,
					now,
				);
			}

			// 3. 同期履歴の更新
			await updateSyncHistoryStatus(
				ctx,
				args.historyId,
				{
					success: args.success,
					propertiesCount: args.properties.length,
					createdCount: result.createdCount,
					updatedCount: result.updatedCount,
					deletedCount: result.deletedCount,
					errors: result.errors,
					error: args.error,
				},
				now,
			);

			logger.info("同期処理が完了しました", {
				createdCount: result.createdCount,
				updatedCount: result.updatedCount,
				deletedCount: result.deletedCount,
				errorCount: result.errors.length,
			});

			return result;
		} catch (error) {
			logger.error("同期完了処理中にエラーが発生しました", { error });

			// エラーの場合も履歴を更新
			await updateSyncHistoryStatus(
				ctx,
				args.historyId,
				{
					success: false,
					propertiesCount: args.properties.length,
					createdCount: 0,
					updatedCount: 0,
					deletedCount: 0,
					errors: [],
					error: error instanceof Error ? error.message : String(error),
				},
				now,
			);

			throw error;
		}
	},
});
