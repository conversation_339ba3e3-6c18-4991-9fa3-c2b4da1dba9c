import { v } from "convex/values";
import { internalMutation } from "./_generated/server";
import { createLogger } from "./lib/logging";

/**
 * OTAマスタの初期データを投入する
 * @returns 投入結果（全て新規投入の場合はcreated、全てスキップの場合はskipped、一部投入の場合はpartial）
 */
export const seedOtaMaster = internalMutation({
	args: {},
	returns: v.object({
		status: v.union(
			v.literal("created"),
			v.literal("skipped"),
			v.literal("partial"),
		),
		count: v.number(),
		details: v.optional(
			v.object({
				createdCount: v.number(),
				skippedCount: v.number(),
				totalCount: v.number(),
			}),
		),
	}),
	handler: async (ctx) => {
		const logger = createLogger("otaMaster.seedOtaMaster", ctx);
		logger.info("OTAマスタの初期データ投入を開始");

		// 初期データの定義
		const now = Date.now();
		const initialOtaData = [
			{
				fullName: "Agoda",
				shortName: "agoda",
				createdAt: now,
				updatedAt: now,
			},
			{
				fullName: "Airbnb",
				shortName: "airbnb",
				createdAt: now,
				updatedAt: now,
			},
			{
				fullName: "Booking.com",
				shortName: "booking",
				createdAt: now,
				updatedAt: now,
			},
			{
				fullName: "Expedia",
				shortName: "expedia",
				createdAt: now,
				updatedAt: now,
			},
			{
				fullName: "Trivago",
				shortName: "trivago",
				createdAt: now,
				updatedAt: now,
			},
		];

		let createdCount = 0;
		let skippedCount = 0;

		// 各OTAデータの投入
		const timer = logger.startTimer("データ投入処理");
		for (const otaData of initialOtaData) {
			// 既存データの確認
			const existingOta = await ctx.db
				.query("otaMaster")
				.withIndex("by_shortName", (q) => q.eq("shortName", otaData.shortName))
				.unique();

			if (existingOta) {
				logger.info("OTAマスタデータは既に存在します", {
					shortName: otaData.shortName,
					fullName: otaData.fullName,
				});
				skippedCount++;
				continue;
			}

			// データの投入
			const otaId = await ctx.db.insert("otaMaster", otaData);
			logger.info("OTAマスタデータを投入しました", {
				id: otaId,
				shortName: otaData.shortName,
				fullName: otaData.fullName,
			});
			createdCount++;
		}
		timer();

		// ステータスの決定
		let status: "created" | "skipped" | "partial";
		if (createdCount === initialOtaData.length) {
			status = "created";
		} else if (createdCount === 0) {
			status = "skipped";
		} else {
			status = "partial";
		}

		// 最終結果のログ出力
		logger.info("OTAマスタの初期データ投入が完了しました", {
			status,
			createdCount,
			skippedCount,
			totalCount: initialOtaData.length,
		});

		return {
			status,
			count: createdCount,
			details: {
				createdCount,
				skippedCount,
				totalCount: initialOtaData.length,
			},
		};
	},
});
