/**
 * @module init
 * @description アプリケーション初期化モジュール
 *
 * このモジュールは、アプリケーションの起動時に必要なマスタデータの投入や
 * 初期設定を行う責任を持ちます。開発環境の起動時（npm run dev）や
 * 本番環境へのデプロイ後に自動的に実行されます。
 */

import { v } from "convex/values";
import { internal } from "./_generated/api";
import { internalMutation } from "./_generated/server";
import { createLogger } from "./lib/logging";

/**
 * 初期化処理の結果を表す型
 * @typedef {Object} InitResult
 * @property {string} name - 初期化対象の名前（例：OTAマスタ）
 * @property {string} status - 初期化の状態（例：completed, failed）
 * @property {number} [count] - 処理したレコード数（オプション）
 */
type InitResult = {
	name: string;
	status: string;
	count?: number;
};

/**
 * アプリケーションの初期化処理
 * 開発環境起動時（npm run dev）や本番デプロイ後に自動実行される
 *
 * @returns {Promise<{success: boolean, results: InitResult[]}>} 初期化の結果
 * @returns {boolean} returns.success - 初期化が成功したかどうか
 * @returns {InitResult[]} returns.results - 各初期化処理の詳細結果の配列
 */
const init = internalMutation({
	args: {},
	returns: v.object({
		success: v.boolean(),
		results: v.array(
			v.object({
				name: v.string(),
				status: v.string(),
				count: v.optional(v.number()),
			}),
		),
	}),
	handler: async (ctx) => {
		const logger = createLogger("init", ctx);
		logger.info("アプリケーション初期化を開始");

		const results: InitResult[] = [];

		try {
			// OTAマスタの初期データ投入
			logger.info("OTAマスタの初期化を開始");
			const otaResult: { status: string; count: number } =
				await ctx.runMutation(internal.otaMasterSeed.seedOtaMaster, {});
			results.push({
				name: "OTAマスタ",
				status: otaResult.status,
				count: otaResult.count,
			});
			logger.info("OTAマスタの初期化完了", otaResult);

			// 今後、他のマスタデータの初期化処理をここに追加
			// 例:
			// const anotherResult = await ctx.runMutation(internal.features.anotherMaster.seed.seedData, {});
			// results.push({
			//   name: "AnotherMaster",
			//   status: anotherResult.status,
			//   count: anotherResult.count,
			// });

			logger.info("アプリケーション初期化が完了しました", { results });
			return {
				success: true,
				results,
			};
		} catch (error) {
			logger.error("アプリケーション初期化中にエラーが発生しました", {
				error: error instanceof Error ? error.message : "Unknown error",
				results,
			});
			return {
				success: false,
				results,
			};
		}
	},
});

export default init;
