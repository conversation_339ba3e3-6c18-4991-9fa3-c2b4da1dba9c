"use node";

import { v } from "convex/values";
import { internal } from "./_generated/api";
import { internalAction } from "./_generated/server";
import { createConvexError, ERROR_CODES } from "./lib/errors";
import { createLogger } from "./lib/logging";
import { createReviewParser } from "./lib/reviewParser";
import { createBookingReviewUrl } from "./lib/reviewUrlBuilder";
import { createScrapingAntClient } from "./lib/scrapingAnt";
import type { ScrapingAntApiError } from "./types/reviews";

/**
 * OTAからレビューデータを取得して同期する内部アクション
 *
 * @param taskId - syncQueueテーブルのタスクID
 * @returns なし（void）
 */
export const syncReviewsFromOTA = internalAction({
	args: {
		taskId: v.id("syncQueue"),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		const logger = createLogger("syncReviewsFromOTA", ctx);
		logger.info("レビュー同期開始", {
			taskId: args.taskId,
			timestamp: new Date().toISOString(),
		});

		// デバッグ：受け取った引数の詳細
		logger.info("[DEBUG] syncReviewsFromOTA 引数詳細", {
			taskId: args.taskId,
			taskIdType: typeof args.taskId,
			contextInfo: {
				functionType: "internalAction",
			},
		});

		try {
			// タスク詳細の取得
			logger.info("[DEBUG] getReviewSyncTask呼び出し前", {
				taskId: args.taskId,
			});

			const taskData = await ctx.runQuery(
				internal.otaReviewsInternal.getReviewSyncTask,
				{
					taskId: args.taskId,
				},
			);

			logger.info("[DEBUG] getReviewSyncTask呼び出し後", {
				taskId: args.taskId,
				taskDataExists: !!taskData,
				taskDataIsNull: taskData === null,
				taskDataType: typeof taskData,
			});

			if (!taskData) {
				// タスクが見つからない場合、追加の診断情報を取得
				logger.error("[DEBUG] タスクが見つかりません - 追加診断開始", {
					taskId: args.taskId,
				});

				// ジョブの詳細情報を取得
				try {
					const jobDetails = await ctx.runQuery(
						internal.beds24Queue.getJobDetails,
						{ jobId: args.taskId },
					);

					logger.error("[DEBUG] ジョブ詳細診断結果", {
						taskId: args.taskId,
						jobExists: !!jobDetails,
						jobType: jobDetails?.job.jobType,
						jobStatus: jobDetails?.job.status,
						diagnostics: jobDetails?.diagnostics,
					});
				} catch (diagError) {
					logger.error("[DEBUG] ジョブ詳細診断エラー", {
						taskId: args.taskId,
						error:
							diagError instanceof Error
								? diagError.message
								: String(diagError),
					});
				}

				throw createConvexError(ERROR_CODES.NOT_FOUND, "Task not found", {
					taskId: args.taskId,
					timestamp: new Date().toISOString(),
				});
			}

			const { metadata } = taskData;
			const { url: metadataUrl, otaType, pageNumber } = metadata;

			// URL作成ユーティリティを使用してURLを処理
			// 既存URLがある場合は更新（ページネーション対応）
			const url = metadataUrl;

			logger.info("レビュー取得開始", {
				url,
				otaType,
				pageNumber,
			});

			// ScrapingAntクライアントを使用したスクレイピング
			const scrapingAnt = createScrapingAntClient();
			let htmlContent: string;

			try {
				const response = await scrapingAnt.scrapeUrl(url, {
					browser: true,
					proxy_country: "JP",
					wait_for_selector: ".review-item",
				});
				htmlContent = response.content;
			} catch (error) {
				// ScrapingAntApiErrorの判別
				if (error && typeof error === "object" && "statusCode" in error) {
					const apiError = error as ScrapingAntApiError;
					if (apiError.statusCode === 429) {
						throw createConvexError(
							ERROR_CODES.RATE_LIMIT_EXCEEDED,
							"ScrapingAnt rate limit exceeded",
						);
					}
				}
				throw createConvexError(
					ERROR_CODES.SCRAPING_FAILED,
					error instanceof Error ? error.message : String(error),
				);
			}

			// デバッグ用: HTML構造をログに出力（開発環境のみ）
			const DEBUG_SCRAPING = true; // デバッグ時はtrueに設定
			if (DEBUG_SCRAPING) {
				try {
					// Cheerioを使ってHTML構造を分析
					const cheerio = await import("cheerio");
					const $ = cheerio.default
						? cheerio.default.load(htmlContent)
						: cheerio.load(htmlContent);

					// 最初のレビュー要素のHTML構造を取得
					const firstReviewElement = $("li.review_item").first();
					if (firstReviewElement.length > 0) {
						// レビュー要素内の主要な要素を探す
						const debugInfo = {
							reviewItemFound: true,
							scoreElement:
								firstReviewElement.find("span.review-score-badge").length > 0,
							scoreText: firstReviewElement
								.find("span.review-score-badge")
								.text()
								.trim(),
							// 様々なセレクターを試す
							reviewBodySelectors: {
								"p.review_neg > span[itemprop='reviewBody']":
									firstReviewElement.find(
										"p.review_neg > span[itemprop='reviewBody']",
									).length,
								"p.review_pos > span[itemprop='reviewBody']":
									firstReviewElement.find(
										"p.review_pos > span[itemprop='reviewBody']",
									).length,
								"p.review_neg": firstReviewElement.find("p.review_neg").length,
								"p.review_pos": firstReviewElement.find("p.review_pos").length,
								"span[itemprop='reviewBody']": firstReviewElement.find(
									"span[itemprop='reviewBody']",
								).length,
								".review_neg": firstReviewElement.find(".review_neg").length,
								".review_pos": firstReviewElement.find(".review_pos").length,
							},
							// 実際のHTML構造の一部を確認
							reviewNegHTML: firstReviewElement
								.find("p.review_neg")
								.html()
								?.substring(0, 200),
							reviewPosHTML: firstReviewElement
								.find("p.review_pos")
								.html()
								?.substring(0, 200),
							// すべてのpタグのクラス名を取得
							allParagraphClasses: firstReviewElement
								.find("p")
								.map((_: number, el: any) => $(el).attr("class"))
								.get(),
						};

						logger.info("レビュー要素のHTML構造分析", debugInfo);
					} else {
						logger.warn("レビュー要素が見つかりませんでした", {
							htmlLength: htmlContent.length,
							hasReviewList: $("ul.review_list").length > 0,
							hasAnyReviewItem: $(".review_item").length > 0,
							bodyPreview: htmlContent.substring(0, 500),
						});
					}
				} catch (debugError) {
					logger.warn("HTML構造の分析に失敗しました", {
						error:
							debugError instanceof Error
								? debugError.message
								: String(debugError),
					});
				}
			}

			// HTMLパース処理
			logger.info("HTMLパース開始");
			const reviewParser = createReviewParser(logger);
			const parseResult = reviewParser.parseHtml(htmlContent, otaType, url);
			const { reviews, nextPageUrl } = parseResult;

			logger.info("レビュー抽出完了", {
				reviewCount: reviews.length,
				hasNextPage: !!nextPageUrl,
			});

			// 次ページURLの処理
			let processedNextPageUrl: string | undefined;
			if (nextPageUrl) {
				// URL作成ユーティリティを使用して次ページURLを更新
				// ページネーション対応（pageパラメータをold_pageに移動、rows=25に統一）
				processedNextPageUrl = createBookingReviewUrl({
					property: {
						id: metadata.propertyId || "unknown",
						name: "unknown", // プロパティ名は不要（既存URL更新のため）
					},
					existingUrl: nextPageUrl,
				});
			}

			// processReviewBatchの呼び出し
			const results = await ctx.runMutation(
				internal.otaReviewsInternal.processReviewBatch,
				{
					taskId: args.taskId,
					reviews,
					hasNextPage: !!nextPageUrl,
					nextPageUrl: processedNextPageUrl,
				},
			);

			logger.info("レビュー同期完了", {
				taskId: args.taskId,
				processedCount: results.processedCount,
				createdCount: results.createdCount,
				updatedCount: results.updatedCount,
			});

			return null;
		} catch (error) {
			logger.error("レビュー同期エラー", {
				taskId: args.taskId,
				error: error instanceof Error ? error.message : String(error),
			});

			// タスクのステータスを更新
			await ctx.runMutation(internal.beds24Queue.updateJobStatus, {
				jobId: args.taskId,
				status: "failed",
				error: error instanceof Error ? error.message : String(error),
			});

			throw error;
		}
	},
});
