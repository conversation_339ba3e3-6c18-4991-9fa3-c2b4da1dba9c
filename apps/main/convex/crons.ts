/**
 * Cron Job 設定ファイル
 *
 * Convexのcron機能を使用して定期的なタスクを実行します。
 * 全てのcronジョブはUTC時間で設定されています。
 *
 * @module crons
 */

import { cronJobs } from "convex/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import { internalAction } from "./_generated/server";
import { createLogger } from "./lib/logging";
import { SyncStatus } from "./types/beds24";

// ========== Cronから呼び出される内部アクション ==========

/**
 * 古いジョブをクリーンアップする内部アクション
 *
 * 指定した日数より古い完了済みおよび失敗したジョブを削除します。
 * デフォルトでは7日以上前のジョブが対象です。
 *
 * @returns null
 */
const cleanupOldJobs = internalAction({
	args: {},
	returns: v.null(),
	handler: async (ctx) => {
		const logger = createLogger("cleanupOldJobs", ctx);
		logger.info("古いジョブのクリーンアップを開始");

		// 7日以上前のCOMPLETEDとFAILEDジョブを削除
		const result = await ctx.runMutation(internal.beds24Queue.cleanupOldJobs, {
			olderThanDays: 7,
			statuses: [SyncStatus.COMPLETED, SyncStatus.FAILED],
		});

		logger.info("古いジョブのクリーンアップが完了", {
			deletedCount: result.deletedCount,
		});

		return null;
	},
});

// ========== Cron Jobsの設定 ==========

/**
 * Cron Job設定インスタンス
 *
 * Convexのcron機能を使用して定期的なタスクを登録します
 */
const crons = cronJobs();

/**
 * 毎日の施設同期ジョブ投入
 *
 * 実行時刻: 毎日午前2時（JST）/ UTC 17:00（前日）
 * 機能: Beds24トークンを持つ全ユーザーの施設データを同期
 */
crons.cron(
	"Daily sync job enqueueing",
	"0 17 * * *", // UTC時間で前日の17時
	internal.beds24CronActions.enqueueDailySync,
);

/**
 * 毎日のレビュー同期ジョブ投入
 *
 * 実行時刻: 毎日午前3時（JST）/ UTC 18:00（前日）
 * 機能: 全施設のBooking.comレビューを同期
 * 特徴: スラッグ未取得の施設は先にスラッグ取得ジョブを実行
 */
crons.cron(
	"Daily review sync job enqueueing",
	"0 18 * * *", // UTC時間で前日の18時
	internal.beds24CronActions.enqueueDailyReviewSync,
);

/**
 * キュー処理の定期実行
 *
 * 実行間隔: 5分毎
 * 機能: 同期キュー内の待機中ジョブを順次処理
 * 処理内容:
 *   - 施設同期
 *   - レビュー同期
 *   - 予約同期
 *   - スラッグ取得
 */
crons.interval(
	"Process sync queue",
	{ minutes: 5 },
	internal.beds24QueueProcessor.processQueue,
);

/**
 * 毎日の予約同期ジョブ投入
 *
 * 実行時刻: 毎日午前4時（JST）/ UTC 19:00（前日）
 * 機能: 全ユーザーの全施設の予約データを同期
 * 特徴: 初回同期として現在日時以降の予約を取得
 */
crons.cron(
	"Daily booking sync job enqueueing",
	"0 19 * * *", // UTC時間で前日の19時
	internal.beds24CronActions.enqueueDailyBookingSync,
);

/**
 * 古いジョブのクリーンアップ
 *
 * 実行時刻: 毎日午前4時（JST）/ UTC 19:00（前日）
 * 機能: 7日以上前の完了・失敗ジョブを削除
 * 目的: データベースのサイズ管理とパフォーマンス維持
 */
crons.cron(
	"Cleanup old jobs",
	"0 19 * * *", // UTC時間で前日の19時
	internal.crons.cleanupOldJobs,
);

/**
 * Cron設定のエクスポート
 *
 * Convexはこのデフォルトエクスポートを使用して
 * cronジョブを自動的に登録します
 */
export default crons;

/**
 * 古いジョブのクリーンアップアクション
 *
 * 内部で定義されたアクションを他のファイルから
 * 参照できるようにエクスポート
 */
export { cleanupOldJobs };
