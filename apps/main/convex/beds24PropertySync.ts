"use node";

/**
 * Beds24 Property Sync
 *
 * このファイルはBeds24の施設データ同期アクションを提供します。
 * Node.js環境で実行され、外部API呼び出しを含みます。
 */

import { v } from "convex/values";
import { internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import { internalAction } from "./_generated/server";
import { createConvexError, ERROR_CODES } from "./lib/errors";
import { createLogger } from "./lib/logging";
// fetchPropertiesはbeds24ApiActionsに移動しました
import { QueueJobType } from "./types/beds24";

/**
 * アクセストークン情報の型定義
 *
 * Beds24のアクセストークンとその関連情報を含む
 * nullの場合はトークンが存在しないことを示す
 *
 * @typedef {Object} TokenInfoObject
 * @property {string} userId - トークンを所有するユーザーのID
 * @property {string} accessToken - Beds24 APIアクセストークン
 * @property {number} expiresAt - トークンの有効期限（UNIXタイムスタンプ）
 * @property {number} createdAt - トークンの作成日時（UNIXタイムスタンプ）
 * @property {number} updatedAt - トークンの最終更新日時（UNIXタイムスタンプ）
 * @property {number} lastRefreshedAt - トークンの最終リフレッシュ日時（UNIXタイムスタンプ）
 */
type TokenInfo = {
	userId: string;
	accessToken: string;
	expiresAt: number;
	createdAt: number;
	updatedAt: number;
	lastRefreshedAt: number;
} | null;

/**
 * 特定ユーザーの施設を同期する内部アクション
 *
 * 指定されたユーザーのBeds24アカウントから全施設データを取得し、
 * ローカルデータベースと同期します。
 *
 * 処理フロー：
 * 1. アクセストークンの取得と検証
 * 2. 必要に応じてトークンのリフレッシュ
 * 3. Beds24 APIから施設データを取得
 * 4. 重複同期チェック（1時間以内）
 * 5. データベースへの保存（作成/更新/削除）
 *
 * @param userId - 同期対象のユーザーID
 * @param jobId - 同期キューのジョブID（オプション）
 * @returns 同期結果（成功/失敗、処理件数、エラー情報）
 * @throws {ConvexError} トークンが無効な場合
 */
export const syncUserProperties = internalAction({
	args: {
		userId: v.string(),
		jobId: v.optional(v.id("syncQueue")),
	},
	returns: v.object({
		success: v.boolean(),
		totalProperties: v.number(),
		createdCount: v.number(),
		updatedCount: v.number(),
		deletedCount: v.number(),
		errors: v.array(
			v.object({
				propertyId: v.optional(v.number()),
				error: v.string(),
			}),
		),
	}),
	handler: async (
		ctx,
		args,
	): Promise<{
		success: boolean;
		totalProperties: number;
		createdCount: number;
		updatedCount: number;
		deletedCount: number;
		errors: Array<{
			propertyId?: number;
			error: string;
		}>;
	}> => {
		const logger = createLogger("syncUserProperties", ctx);
		logger.setArgs({ userId: args.userId, jobId: args.jobId });
		logger.info("ユーザーの施設同期を開始");

		// 統合ミューテーションで準備処理を実行
		const prepareResult:
			| {
					success: true;
					historyId: Id<"beds24SyncHistory">;
					accessToken: string;
					tokenExpiresAt: number;
					needsTokenRefresh: boolean;
			  }
			| {
					success: false;
					error: string;
			  } = await ctx.runMutation(
			internal.beds24SyncMutations.prepareSyncData,
			{
				userId: args.userId,
				jobType: QueueJobType.SYNC_PROPERTIES,
			},
		);

		if (!prepareResult.success) {
			throw createConvexError(ERROR_CODES.INVALID_TOKEN, prepareResult.error);
		}

		const { historyId, accessToken, needsTokenRefresh } = prepareResult;

		try {
			// トークンの更新が必要な場合
			let currentAccessToken = accessToken;
			if (needsTokenRefresh) {
				logger.info("アクセストークンを更新します");
				await ctx.runAction(internal.beds24Tokens.refreshAccessToken, {
					userId: args.userId,
				});

				// 更新後のトークンを取得
				const tokenInfo: TokenInfo = await ctx.runQuery(
					internal.beds24Tokens.getAccessToken,
					{
						userId: args.userId,
					},
				);

				if (!tokenInfo) {
					throw createConvexError(
						ERROR_CODES.INVALID_TOKEN,
						"アクセストークンの取得に失敗しました",
					);
				}
				currentAccessToken = tokenInfo.accessToken;
			}

			// Beds24 APIから施設データを取得
			logger.info("Beds24 APIから施設データを取得します");
			const properties = await ctx.runAction(
				internal.beds24ApiActions.fetchProperties,
				{ accessToken: currentAccessToken },
			);

			logger.info(`取得した施設数: ${properties.length}`);

			// フィルタリング処理（1時間以内の重複同期チェック）
			const oneHourAgo = Date.now() - 60 * 60 * 1000;
			const filteredProperties = await ctx.runQuery(
				internal.beds24SyncQueries.filterPropertiesForSync,
				{
					properties,
					oneHourAgo,
				},
			);

			// 統合ミューテーションで完了処理を実行
			const result: {
				createdCount: number;
				updatedCount: number;
				deletedCount: number;
				errors: string[];
			} = await ctx.runMutation(internal.beds24SyncMutations.completeSyncData, {
				userId: args.userId,
				historyId,
				properties: filteredProperties,
				success: true,
			});

			const totalResult = {
				success: true,
				totalProperties: properties.length,
				createdCount: result.createdCount,
				updatedCount: result.updatedCount,
				deletedCount: result.deletedCount,
				errors: result.errors.map((error) => ({ error })),
			};

			logger.info("施設同期が完了しました", totalResult);
			return totalResult;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			logger.error("施設同期中にエラーが発生しました", { error: errorMessage });

			// エラーの場合も統合ミューテーションで処理
			await ctx.runMutation(internal.beds24SyncMutations.completeSyncData, {
				userId: args.userId,
				historyId,
				properties: [],
				success: false,
				error: errorMessage,
			});

			throw error;
		}
	},
});
