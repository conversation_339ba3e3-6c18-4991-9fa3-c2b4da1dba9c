"use node";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import { internalAction } from "./_generated/server";
import {
	type ScrapeResult,
	scrapeBookingComSlug as scrapeSlugFromBooking,
} from "./lib/bookingComSlugScraper";
import { createConvexError, ERROR_CODES } from "./lib/errors";
import { createLogger } from "./lib/logging";
import { createBookingReviewUrl } from "./lib/reviewUrlBuilder";
import {
	createScrapingAntClient,
	type ScrapingAntWrapper,
} from "./lib/scrapingAnt";
import {
	type BookingComSlugSyncMetadata,
	JobPriority,
	QueueJobType,
	SyncHistoryStatus,
	SyncStatus,
} from "./types/beds24";

/**
 * Booking.comから施設のスラッグを取得し、レビュー同期タスクを自動登録する
 *
 * 処理フロー：
 * 1. タスクとプロパティ情報を取得
 * 2. ScrapingAntを使用してBooking.comから施設を検索
 * 3. 検索結果からスラッグを抽出
 * 4. DBにスラッグを保存
 * 5. レビュー同期タスクを自動登録（同日中のレビュー取得を実現）
 */
export const scrapeBookingComSlug = internalAction({
	args: {
		taskId: v.id("syncQueue"),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		const logger = createLogger("scrapeBookingComSlug", ctx);
		logger.info("Booking.comスラッグ取得開始", { taskId: args.taskId });

		let historyId: Id<"beds24SyncHistory"> | undefined;

		try {
			// 1. タスク情報を取得
			const taskData = await ctx.runQuery(
				internal.bookingComSlugQueries.getSlugSyncTask,
				{
					taskId: args.taskId,
				},
			);

			if (!taskData) {
				throw createConvexError(
					ERROR_CODES.NOT_FOUND,
					`タスクが見つかりません: ${args.taskId}`,
				);
			}

			const { task } = taskData;

			// 同期履歴を記録
			historyId = await ctx.runMutation(
				internal.beds24SyncHistory.recordSyncStart,
				{
					userId: task.userId,
					jobType: "scrape_booking_com_slug",
					metadata: { taskId: args.taskId },
				},
			);

			logger.info("同期履歴を作成しました", { historyId, userId: task.userId });

			if (
				task.status !== SyncStatus.PENDING &&
				task.status !== SyncStatus.PROCESSING
			) {
				logger.warn("タスクは既に処理済みです", {
					taskId: args.taskId,
					status: task.status,
				});
				return null;
			}

			// メタデータを解析
			const parsedMetadata =
				typeof task.metadata === "string"
					? JSON.parse(task.metadata)
					: task.metadata;

			// 必須フィールドの存在確認
			const validationErrors = [];
			if (!parsedMetadata.propertyId) {
				validationErrors.push("propertyIdが存在しません");
			}
			if (!parsedMetadata.propertyName) {
				validationErrors.push("propertyNameが存在しません");
			}
			if (!parsedMetadata.beds24PropertyKey) {
				validationErrors.push("beds24PropertyKeyが存在しません");
			}
			if (!parsedMetadata.userId) {
				validationErrors.push("userIdが存在しません");
			}

			if (validationErrors.length > 0) {
				const errorMessage = `メタデータの検証に失敗しました: ${validationErrors.join(", ")}`;
				logger.error(errorMessage, {
					taskId: args.taskId,
					validationErrors,
					metadata: parsedMetadata,
				});
				throw createConvexError(ERROR_CODES.VALIDATION_ERROR, errorMessage, {
					code: "INVALID_METADATA",
				});
			}

			// 検証済みのメタデータを型アサーション
			const metadata = parsedMetadata as BookingComSlugSyncMetadata;
			logger.info("タスクメタデータ", metadata);

			// 施設名の前処理分析
			logger.debug("施設名前処理の分析", {
				propertyName: metadata.propertyName,
				nameAnalysis: {
					hasStarEmoji: metadata.propertyName.includes("⭐"),
					starPosition: metadata.propertyName.indexOf("⭐"),
					cleanedName: metadata.propertyName.replace(/⭐️?/g, "").trim(),
					nameAfterFirstSpace: metadata.propertyName
						.split(" ")
						.slice(1)
						.join(" "),
					hasJapanese: /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(
						metadata.propertyName,
					),
					hasAlphabet: /[a-zA-Z]/.test(metadata.propertyName),
				},
				timestamp: new Date().toISOString(),
			});

			// 2. ScrapingAntクライアントを初期化
			logger.info("ScrapingAntクライアント初期化前", {
				memoryUsage: process.memoryUsage(),
				uptime: process.uptime(),
				timestamp: new Date().toISOString(),
			});

			let scrapingAnt: ScrapingAntWrapper;
			try {
				scrapingAnt = createScrapingAntClient(logger);
				logger.info("ScrapingAntクライアント作成成功", {
					hasClient: !!scrapingAnt,
					timestamp: new Date().toISOString(),
				});
			} catch (initError: any) {
				logger.error("ScrapingAntクライアント初期化失敗", {
					errorType: initError.constructor.name,
					errorMessage: initError.message,
					errorStack: initError.stack,
					timestamp: new Date().toISOString(),
				});
				throw initError;
			}

			// 3. Booking.comから施設を検索してスラッグを取得
			const scrapeStartTime = Date.now();
			logger.info("スクレイピング開始", {
				propertyName: metadata.propertyName,
				startTime: new Date(scrapeStartTime).toISOString(),
				environment: "Convex Action (Node.js)",
				processInfo: {
					memoryUsage: process.memoryUsage(),
					uptime: process.uptime(),
				},
			});

			let result: ScrapeResult;
			try {
				logger.info("scrapeSlugFromBooking呼び出し直前", {
					propertyName: metadata.propertyName,
					timestamp: new Date().toISOString(),
				});

				result = await scrapeSlugFromBooking(
					metadata.propertyName,
					scrapingAnt,
					logger,
				);

				logger.info("scrapeSlugFromBooking呼び出し完了", {
					success: result?.success || false,
					timestamp: new Date().toISOString(),
				});
			} catch (scrapeError: any) {
				logger.error("scrapeSlugFromBooking内でエラー発生", {
					errorType: scrapeError.constructor.name,
					errorMessage: scrapeError.message,
					errorCode: scrapeError.code,
					errorStack: scrapeError.stack,
					isConnectionError: scrapeError.message?.includes("Connection"),
					isTimeoutError: scrapeError.message?.includes("timeout"),
					timestamp: new Date().toISOString(),
				});
				throw scrapeError;
			}

			const scrapeEndTime = Date.now();
			const scrapeDuration = scrapeEndTime - scrapeStartTime;
			logger.info("スクレイピング完了", {
				duration: scrapeDuration,
				durationSeconds: scrapeDuration / 1000,
				success: result?.success || false,
				endTime: new Date(scrapeEndTime).toISOString(),
			});

			if (!result || !result.success) {
				throw createConvexError(
					ERROR_CODES.NOT_FOUND,
					`Booking.comで施設が見つかりません: ${metadata.propertyName}`,
				);
			}

			const slug = result.slug;
			logger.info("スラッグ取得成功", {
				slug,
				propertyUrl: result.propertyUrl,
			});

			// 4. DBにスラッグを保存
			logger.info("updatePropertySlug呼び出し開始", {
				propertyId: metadata.propertyId,
				bookingComFacilitySlug: slug,
				propertyName: metadata.propertyName,
			});

			let updateResult: {
				success: boolean;
				propertyId: Id<"beds24Properties">;
				updatedFields: {
					bookingComFacilitySlug: string;
					bookingComLastScrapedAt: number;
				};
			};
			try {
				updateResult = await ctx.runMutation(
					internal.beds24Properties.updatePropertySlug,
					{
						propertyId: metadata.propertyId,
						bookingComFacilitySlug: slug,
					},
				);
				logger.info("updatePropertySlug呼び出し成功", {
					updateResult,
					propertyId: metadata.propertyId,
					slug,
				});
			} catch (updateError: any) {
				logger.error("updatePropertySlugエラー", {
					error:
						updateError instanceof Error
							? updateError.message
							: String(updateError),
					errorType: updateError?.constructor?.name,
					errorStack: updateError?.stack,
					propertyId: metadata.propertyId,
					slug,
				});

				// スラッグの保存に失敗した場合、タスクを失敗にする
				await ctx.runMutation(internal.beds24Queue.updateJobStatus, {
					jobId: args.taskId,
					status: "failed",
					error: `スラッグの保存に失敗: ${updateError instanceof Error ? updateError.message : String(updateError)}`,
				});

				throw updateError;
			}

			logger.info("スラッグ保存完了", {
				updateResult,
				propertyId: metadata.propertyId,
				propertyName: metadata.propertyName,
				slug,
			});

			// 5. タスクを完了にする（メイン処理が成功したので早めに完了扱いにする）
			logger.info("タスクステータス更新開始", { taskId: args.taskId });
			await ctx.runMutation(internal.beds24Queue.updateJobStatus, {
				jobId: args.taskId,
				status: "completed",
				error: undefined,
			});
			logger.info("タスクステータス更新完了", { taskId: args.taskId });

			// 同期履歴を成功として記録
			if (historyId) {
				await ctx.runMutation(internal.beds24SyncHistory.recordSyncComplete, {
					historyId,
					status: SyncHistoryStatus.SUCCESS,
					totalItems: 1,
					successCount: 1,
					failedCount: 0,
					metadata: {
						taskId: args.taskId,
						propertyId: metadata.propertyId,
						propertyName: metadata.propertyName,
						slug,
					},
				});
				logger.info("同期履歴を成功として記録しました", { historyId });
			}

			// 6. レビュー同期タスクを自動登録（失敗してもメインタスクは成功扱い）
			let reviewTaskId: string | undefined;
			try {
				// OTAマスタから"booking.com"のレコードを取得
				const otaMaster = await ctx.runQuery(
					internal.otaMaster.getByShortName,
					{
						shortName: "booking",
					},
				);

				if (!otaMaster) {
					logger.warn("OTAマスタレコードが見つかりません", {
						shortName: "booking",
					});
					throw new Error("OTAマスタレコードが見つかりません");
				}

				const reviewUrl = createBookingReviewUrl({
					property: {
						id: metadata.propertyId,
						name: metadata.propertyName,
						booking_com_facility_name: slug,
					},
				});

				logger.info("レビュー同期タスク登録開始", { reviewUrl });
				reviewTaskId = await ctx.runMutation(internal.beds24Queue.enqueueJob, {
					userId: metadata.userId,
					jobType: QueueJobType.SYNC_REVIEWS,
					priority: JobPriority.LOW,
					metadata: {
						url: reviewUrl,
						otaType: "booking.com",
						pageNumber: 1,
						maxPages: 10,
						propertyId: metadata.propertyId,
						otaId: otaMaster._id,
					},
				});

				logger.info("レビュー同期タスク登録完了", {
					reviewTaskId,
					reviewUrl,
				});
			} catch (reviewError) {
				// レビュータスク登録が失敗してもメイン処理は成功扱い
				logger.warn("レビュー同期タスク登録失敗（メイン処理は成功）", {
					error:
						reviewError instanceof Error
							? reviewError.message
							: String(reviewError),
					taskId: args.taskId,
				});
			}

			logger.info("Booking.comスラッグ取得完了", {
				taskId: args.taskId,
				slug,
				reviewTaskId,
			});

			logger.info("アクション正常終了", { taskId: args.taskId });
			return null;
		} catch (error: any) {
			// エラーの詳細情報を収集
			const errorDetails = {
				taskId: args.taskId,
				errorType: error?.constructor?.name || "UnknownError",
				errorMessage: error?.message || String(error),
				errorCode: error?.code,
				errorStack: error?.stack,
				isConnectionLost: error?.message?.includes("Connection lost"),
				isNetworkError: error?.message?.includes("NETWORK_ERROR"),
				isConvexError: error?.message?.includes("ConvexError"),
				timestamp: new Date().toISOString(),
				processInfo: {
					memoryUsage: process.memoryUsage(),
					uptime: process.uptime(),
				},
			};

			logger.error("Booking.comスラッグ取得エラー（詳細）", errorDetails);

			// エラーの原因を分析
			if (
				error?.message?.includes("Connection lost while action was in flight")
			) {
				logger.error("Convexアクション接続エラー", {
					description: "アクション実行中に接続が切断されました",
					possibleCauses: [
						"Convexアクションのタイムアウト",
						"ネットワークの不安定性",
						"ScrapingAntへの接続問題",
					],
					taskId: args.taskId,
				});
			}

			// タスクを失敗にする
			try {
				await ctx.runMutation(internal.beds24Queue.updateJobStatus, {
					jobId: args.taskId,
					status: "failed",
					error: error instanceof Error ? error.message : String(error),
				});
			} catch (updateError) {
				logger.error("タスクステータス更新エラー", {
					originalError: errorDetails.errorMessage,
					updateError:
						updateError instanceof Error
							? updateError.message
							: String(updateError),
				});
			}

			// 同期履歴を失敗として記録
			if (historyId) {
				try {
					await ctx.runMutation(internal.beds24SyncHistory.recordSyncComplete, {
						historyId,
						status: SyncHistoryStatus.FAILED,
						totalItems: 1,
						successCount: 0,
						failedCount: 1,
						metadata: {
							taskId: args.taskId,
							error: errorDetails.errorMessage,
							errorType: errorDetails.errorType,
						},
					});
				} catch (historyError) {
					logger.error("同期履歴の記録に失敗", {
						historyError:
							historyError instanceof Error
								? historyError.message
								: String(historyError),
					});
				}
			}

			// レート制限エラーの場合は特別な処理
			if (
				error instanceof Error &&
				error.message.includes(ERROR_CODES.RATE_LIMIT_EXCEEDED)
			) {
				throw error; // リトライ対象
			}

			// その他のエラーは再スロー
			throw error;
		}
	},
});
