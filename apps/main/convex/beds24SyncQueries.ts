/**
 * Beds24 Sync Queries
 *
 * このファイルはBeds24同期に関する内部クエリを提供します。
 * V8環境で実行されるため、"use node"ディレクティブはありません。
 */

import { v } from "convex/values";
import { internalQuery } from "./_generated/server";
import { createLogger } from "./lib/logging";
import { beds24APIPropertyValidator } from "./types/beds24";

/**
 * 同期が必要な施設をフィルタリングする
 *
 * 1時間以内に同期済みの施設は除外し、同期が必要な施設のみを返します。
 * パフォーマンス最適化のため、施設数に応じてバッチ処理または並列処理を行います。
 *
 * @param properties - フィルタリング対象の施設リスト
 * @param oneHourAgo - 1時間前のタイムスタンプ（ミリ秒）
 * @returns 同期が必要な施設のリスト
 * @example
 * // 1時間以内に同期されていない施設を取得
 * const oneHourAgo = Date.now() - 60 * 60 * 1000;
 * const propertiesNeedingSync = await ctx.runQuery(
 *   internal.beds24SyncQueries.filterPropertiesForSync,
 *   { properties: allProperties, oneHourAgo }
 * );
 */
export const filterPropertiesForSync = internalQuery({
	args: {
		properties: v.array(beds24APIPropertyValidator),
		oneHourAgo: v.number(),
	},
	returns: v.array(beds24APIPropertyValidator),
	handler: async (ctx, args) => {
		const logger = createLogger("filterPropertiesForSync", ctx);

		// 施設IDのセットを作成
		const propertyIds = args.properties.map((p) => p.id.toString());

		// バッチで既存施設を取得（最大1000件まで）
		// 注意: Convexは現在バッチクエリをサポートしていないため、
		// 施設数が多い場合は複数のクエリに分割する必要がある
		const existingPropertiesMap = new Map<string, { lastSyncedAt: number }>();

		// 施設数が少ない場合（< 100）は個別にクエリ
		if (propertyIds.length < 100) {
			await Promise.all(
				propertyIds.map(async (propId) => {
					const existing = await ctx.db
						.query("beds24Properties")
						.withIndex("by_beds24PropertyId", (q) =>
							q.eq("beds24PropertyId", propId),
						)
						.unique();

					if (existing) {
						existingPropertiesMap.set(propId, {
							lastSyncedAt: existing.lastSyncedAt,
						});
					}
				}),
			);
		} else {
			// 施設数が多い場合は順次処理（並列度を制限）
			const batchSize = 10;
			for (let i = 0; i < propertyIds.length; i += batchSize) {
				const batch = propertyIds.slice(i, i + batchSize);
				await Promise.all(
					batch.map(async (propId) => {
						const existing = await ctx.db
							.query("beds24Properties")
							.withIndex("by_beds24PropertyId", (q) =>
								q.eq("beds24PropertyId", propId),
							)
							.unique();

						if (existing) {
							existingPropertiesMap.set(propId, {
								lastSyncedAt: existing.lastSyncedAt,
							});
						}
					}),
				);
			}
		}

		// フィルタリング処理
		const filteredProperties = args.properties.filter((prop) => {
			const propId = prop.id.toString();
			const existing = existingPropertiesMap.get(propId);

			if (!existing || existing.lastSyncedAt <= args.oneHourAgo) {
				return true;
			} else {
				return false;
			}
		});

		logger.info(
			`Filtered properties: ${filteredProperties.length} out of ${args.properties.length} will be synced`,
		);

		return filteredProperties;
	},
});

/**
 * Beds24のrefreshTokenを持つユーザーのIDリストを取得する
 *
 * has_beds24_tokenインデックスを使用して効率的にBeds24トークンを持つユーザーを検索します。
 *
 * @returns ユーザーIDを含むオブジェクトの配列
 * @example
 * // Beds24トークンを持つユーザーのIDリストを取得
 * const usersWithToken = await ctx.runQuery(
 *   internal.beds24SyncQueries.getUserIdsWithBeds24Token,
 *   {}
 * );
 * // 結果: [{ userId: "user123" }, { userId: "user456" }]
 *
 * @note この関数はユーザーIDのみを返します。トークンの詳細情報が必要な場合は
 * beds24BookingQueries.getUsersWithBeds24TokenDetails を使用してください。
 */
export const getUserIdsWithBeds24Token = internalQuery({
	args: {},
	returns: v.array(
		v.object({
			userId: v.string(),
		}),
	),
	handler: async (ctx) => {
		const _logger = createLogger("getUserIdsWithBeds24Token", ctx);

		// has_beds24_tokenインデックスを使用して効率的に検索
		const users = await ctx.db
			.query("userSettings")
			.withIndex("by_has_beds24_token", (q) => q.eq("has_beds24_token", true))
			.collect();

		const usersWithToken = users.map((user) => ({
			userId: user.userId,
		}));

		return usersWithToken;
	},
});
