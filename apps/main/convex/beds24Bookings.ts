/**
 * Beds24 Bookings Functions (V8 Environment)
 *
 * このファイルは予約に関するクエリとミューテーション関数を提供します。
 * 公開および内部関数を含み、予約データの取得、作成、更新、削除を管理します。
 *
 * 注意: "use node"を使用しないV8環境で動作します
 */

import { paginationOptsValidator } from "convex/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import type { Doc } from "./_generated/dataModel";
import { internalMutation, mutation, query } from "./_generated/server";
import { getAuthenticatedUserId } from "./lib/auth";
import { createConvexError, ERROR_CODES } from "./lib/errors";
import { createLogger } from "./lib/logging";
import {
	calculateBookingStatsOptimized,
	checkPropertyAccess,
	enrichBookingsWithDetails,
	getBookingsByPropertyWithAuth,
	getUserAccessiblePropertyIds,
	validateBookingData,
} from "./model/beds24Bookings";
import {
	type BatchResult,
	type BookingResult,
	batchResultValidator,
	bookingDataValidator,
	bookingFilterOptionsValidator,
	bookingResultValidator,
	bookingStatsValidator,
	bookingSummaryValidator,
	bookingWithDetailsValidator,
	deleteResultValidator,
	paginatedBookingsValidator,
} from "./types/beds24Bookings";

/**
 * 施設別の予約一覧を取得（ページネーション対応）
 *
 * 指定された施設の予約一覧をページネーション対応で取得します。
 * ユーザーのアクセス権限を確認してからデータを返します。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {Id<"beds24Properties">} args.propertyId - 施設ID
 * @param {PaginationOpts} args.paginationOpts - ページネーションオプション
 * @returns {Promise<PaginatedBookings>} ページネーションされた予約一覧
 * @throws {ConvexError} 認証エラーまたはアクセス権限エラー
 *
 * @example
 * const bookings = await getBookingsByProperty({
 *   propertyId: "prop123",
 *   paginationOpts: { numItems: 20, cursor: null }
 * });
 */
export const getBookingsByProperty = query({
	args: {
		propertyId: v.id("beds24Properties"),
		paginationOpts: paginationOptsValidator,
	},
	returns: paginatedBookingsValidator,
	handler: async (ctx, args) => {
		const logger = createLogger("getBookingsByProperty", ctx);
		logger.debug("施設別予約一覧を取得", { propertyId: args.propertyId });

		// 認証チェック
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw createConvexError(ERROR_CODES.UNAUTHORIZED, "Unauthenticated");
		}
		const userId = getAuthenticatedUserId(identity);

		// Model関数を使用して予約を取得
		const result = await getBookingsByPropertyWithAuth(
			ctx,
			userId,
			args.propertyId,
			args.paginationOpts,
		);

		logger.info("施設別予約一覧を取得しました", {
			propertyId: args.propertyId,
			count: result.page.length,
			isDone: result.isDone,
		});

		return result;
	},
});

/**
 * ユーザー別の予約一覧を取得
 *
 * 認証されたユーザーがアクセス可能な全施設の予約を取得します。
 * フィルタリングやソート機能を提供します。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {BookingFilterOptions} [args.filters] - フィルタリングオプション
 * @param {number} [args.limit] - 取得する最大件数（デフォルト: 100）
 * @returns {Promise<BookingSummary[]>} 予約サマリーの配列
 * @throws {ConvexError} 認証エラー
 *
 * @example
 * // 全予約を取得
 * const allBookings = await getBookingsByUser({});
 *
 * // フィルタリングして取得
 * const filteredBookings = await getBookingsByUser({
 *   filters: {
 *     status: "new",
 *     dateFrom: "2024-01-01",
 *     dateTo: "2024-12-31"
 *   },
 *   limit: 50
 * });
 */
export const getBookingsByUser = query({
	args: {
		filters: v.optional(bookingFilterOptionsValidator),
		limit: v.optional(v.number()),
	},
	returns: v.array(bookingSummaryValidator),
	handler: async (ctx, args) => {
		const logger = createLogger("getBookingsByUser", ctx);

		// 認証チェック
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw createConvexError(ERROR_CODES.UNAUTHORIZED, "Unauthenticated");
		}
		const userId = getAuthenticatedUserId(identity);

		const limit = args.limit || 100;
		logger.debug("ユーザー別予約一覧を取得", { userId, limit });

		// ユーザーがアクセス可能な施設を取得
		const accessiblePropertyIds = await getUserAccessiblePropertyIds(
			ctx,
			userId,
		);

		if (accessiblePropertyIds.size === 0) {
			logger.info("アクセス可能な施設がありません", { userId });
			return [];
		}

		// 各施設から予約を取得
		const allBookings = [];
		for (const propertyId of accessiblePropertyIds) {
			// フィルタの適用
			if (args.filters?.propertyId && args.filters.propertyId !== propertyId) {
				continue;
			}

			const bookings = await ctx.db
				.query("beds24Bookings")
				.withIndex("by_property", (q) => q.eq("propertyId", propertyId))
				.filter((q) => q.eq(q.field("isDeleted"), false))
				.take(20); // 各施設から最大20件

			// フィルタリング
			const filtered = bookings.filter((booking) => {
				if (args.filters?.status && booking.status !== args.filters.status) {
					return false;
				}
				if (args.filters?.dateFrom && booking.checkIn < args.filters.dateFrom) {
					return false;
				}
				if (args.filters?.dateTo && booking.checkIn > args.filters.dateTo) {
					return false;
				}
				if (
					args.filters?.channel &&
					booking.data?.channel !== args.filters.channel &&
					booking.data?.referer !== args.filters.channel
				) {
					return false;
				}
				return true;
			});

			// 施設情報を追加してサマリー形式に変換
			const property = await ctx.db.get(propertyId);
			if (property) {
				const summaries = filtered.map((booking) => ({
					_id: booking._id,
					_creationTime: booking._creationTime,
					bookingId: booking.bookingId,
					propertyId: booking.propertyId,
					checkIn: booking.checkIn,
					checkOut: booking.checkOut,
					status: booking.status,
					totalPrice: booking.totalPrice,
					currency: booking.currency,
					propertyName: property.name,
				}));
				allBookings.push(...summaries);
			}
		}

		// チェックイン日でソート（新しい順）
		allBookings.sort((a, b) => b.checkIn.localeCompare(a.checkIn));

		// 制限を適用
		const result = allBookings.slice(0, limit);

		logger.info("ユーザー別予約一覧を取得しました", {
			userId,
			totalCount: result.length,
		});

		return result;
	},
});

/**
 * 予約統計情報を取得
 *
 * ユーザーの予約に関する統計情報を計算して返します。
 * 総予約数、総売上、ステータス別分布などを含みます。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {BookingFilterOptions} [args.filters] - フィルタリングオプション
 * @returns {Promise<BookingStats>} 予約統計情報
 * @throws {ConvexError} 認証エラー
 *
 * @example
 * const stats = await getBookingStats({
 *   filters: {
 *     propertyId: "prop123",
 *     dateFrom: "2024-01-01",
 *     dateTo: "2024-12-31"
 *   }
 * });
 * console.log(`総予約数: ${stats.totalBookings}`);
 * console.log(`総売上: ${stats.totalRevenue}`);
 */
export const getBookingStats = query({
	args: {
		filters: v.optional(bookingFilterOptionsValidator),
	},
	returns: bookingStatsValidator,
	handler: async (ctx, args) => {
		const logger = createLogger("getBookingStats", ctx);

		// 認証チェック
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw createConvexError(ERROR_CODES.UNAUTHORIZED, "Unauthenticated");
		}
		const userId = getAuthenticatedUserId(identity);

		logger.debug("予約統計情報を取得", { userId, filters: args.filters });

		// Model関数を使用して統計を計算
		const stats = await calculateBookingStatsOptimized(
			ctx,
			userId,
			args.filters || {},
		);

		logger.info("予約統計情報を取得しました", {
			userId,
			totalBookings: stats.totalBookings,
			totalRevenue: stats.totalRevenue,
		});

		return stats;
	},
});

/**
 * 最近の予約を取得
 *
 * 指定された件数の最新予約を取得します。
 * 特定の施設またはユーザーの全施設から取得可能です。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {number} [args.limit] - 取得する最大件数（デフォルト: 10）
 * @param {Id<"beds24Properties">} [args.propertyId] - 特定の施設に限定する場合の施設ID
 * @returns {Promise<BookingWithDetails[]>} 最近の予約リスト（詳細情報付き）
 * @throws {ConvexError} 認証エラーまたはアクセス権限エラー
 *
 * @example
 * // 全施設の最新10件を取得
 * const recentBookings = await getRecentBookings({});
 *
 * // 特定施設の最新5件を取得
 * const propertyBookings = await getRecentBookings({
 *   propertyId: "prop123",
 *   limit: 5
 * });
 */
export const getRecentBookings = query({
	args: {
		limit: v.optional(v.number()),
		propertyId: v.optional(v.id("beds24Properties")),
	},
	returns: v.array(bookingWithDetailsValidator),
	handler: async (ctx, args) => {
		const logger = createLogger("getRecentBookings", ctx);

		// 認証チェック
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw createConvexError(ERROR_CODES.UNAUTHORIZED, "Unauthenticated");
		}
		const userId = getAuthenticatedUserId(identity);

		const limit = args.limit || 10;
		logger.debug("最近の予約を取得", {
			userId,
			limit,
			propertyId: args.propertyId,
		});

		let bookings: Doc<"beds24Bookings">[];

		if (args.propertyId) {
			// 特定の施設の予約
			const accessCheck = await checkPropertyAccess(
				ctx,
				userId,
				args.propertyId,
			);
			if (!accessCheck.hasAccess) {
				throw createConvexError(
					ERROR_CODES.UNAUTHORIZED,
					"Access denied to property",
				);
			}

			bookings = await ctx.db
				.query("beds24Bookings")
				.withIndex("by_property", (q) => q.eq("propertyId", args.propertyId!))
				.filter((q) => q.eq(q.field("isDeleted"), false))
				.order("desc")
				.take(limit);
		} else {
			// 全施設の予約
			const accessiblePropertyIds = await getUserAccessiblePropertyIds(
				ctx,
				userId,
			);

			const bookingPromises = Array.from(accessiblePropertyIds).map(
				(propertyId) =>
					ctx.db
						.query("beds24Bookings")
						.withIndex("by_property", (q) => q.eq("propertyId", propertyId))
						.filter((q) => q.eq(q.field("isDeleted"), false))
						.order("desc")
						.take(5), // 各施設から最大5件
			);

			const bookingArrays = await Promise.all(bookingPromises);
			bookings = bookingArrays.flat();

			// 作成日時でソート（新しい順）
			bookings.sort((a, b) => b._creationTime - a._creationTime);
			bookings = bookings.slice(0, limit);
		}

		// 詳細情報を追加
		const bookingsWithDetails = await enrichBookingsWithDetails(ctx, bookings);

		logger.info("最近の予約を取得しました", {
			userId,
			count: bookingsWithDetails.length,
		});

		return bookingsWithDetails;
	},
});

/**
 * バッチ処理での予約データ保存（内部用）
 *
 * 複数の予約データをバッチで処理し、データベースに保存します。
 * 各予約は個別に処理され、エラーが発生しても他の予約の処理は継続されます。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {BookingData[]} args.bookings - 保存する予約データの配列
 * @returns {Promise<BatchResult>} バッチ処理結果
 * @returns {number} returns.successful - 成功した件数
 * @returns {number} returns.failed - 失敗した件数
 * @returns {number} returns.created - 新規作成された件数
 * @returns {number} returns.updated - 更新された件数
 * @returns {number} returns.skipped - スキップされた件数
 * @returns {boolean} returns.allExisting - 全て既存データかどうか
 * @returns {Array} returns.errors - エラー情報の配列
 * @internal
 */
export const processBookingBatch = internalMutation({
	args: {
		bookings: v.array(bookingDataValidator),
	},
	returns: batchResultValidator,
	handler: async (ctx, args) => {
		const logger = createLogger("processBookingBatch", ctx);
		logger.info("予約バッチ処理を開始", { count: args.bookings.length });

		let successful = 0;
		let failed = 0;
		let created = 0;
		let updated = 0;
		let skipped = 0;
		const errors: { bookingId: string; error: string }[] = [];

		for (const bookingData of args.bookings) {
			try {
				// バリデーション
				const validation = validateBookingData(bookingData);
				if (!validation.isValid) {
					throw new Error(validation.errors.join(", "));
				}

				// 内部アップサート関数を呼び出し
				const result: BookingResult = await ctx.runMutation(
					internal.beds24Bookings._upsertBooking,
					{
						booking: bookingData,
					},
				);

				if (result.success) {
					successful++;
					// アクションごとにカウントを更新
					switch (result.action) {
						case "created":
							created++;
							break;
						case "updated":
							updated++;
							break;
						case "skipped":
							skipped++;
							break;
					}
				} else {
					failed++;
					errors.push({
						bookingId: bookingData.bookingId,
						error: result.error || "Unknown error",
					});
				}
			} catch (error) {
				failed++;
				errors.push({
					bookingId: bookingData.bookingId,
					error: error instanceof Error ? error.message : String(error),
				});

				logger.error("予約の保存に失敗", {
					bookingId: bookingData.bookingId,
					error: error instanceof Error ? error.message : String(error),
				});
			}
		}

		// すべて既存データかどうかを判定（新規作成が0の場合）
		const allExisting = created === 0;

		logger.info("予約バッチ処理が完了", {
			total: args.bookings.length,
			successful,
			failed,
			created,
			updated,
			skipped,
			allExisting,
		});

		return {
			successful,
			failed,
			created,
			updated,
			skipped,
			allExisting,
			errors,
		};
	},
});

/**
 * 内部用予約アップサート
 *
 * 予約データを作成または更新します。
 * modifiedDateを比較して、新しいデータのみ更新します。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {BookingData} args.booking - 保存する予約データ
 * @returns {Promise<BookingResult>} アップサート結果
 * @returns {boolean} returns.success - 成功フラグ
 * @returns {string} returns.bookingId - 予約ID
 * @returns {"created"|"updated"|"skipped"} returns.action - 実行されたアクション
 * @returns {string} [returns.error] - エラーメッセージ（失敗時）
 * @internal
 */
export const _upsertBooking = internalMutation({
	args: {
		booking: bookingDataValidator,
	},
	returns: bookingResultValidator,
	handler: async (ctx, args) => {
		const logger = createLogger("_upsertBooking", ctx);
		const { booking } = args;

		try {
			// 既存の予約を確認
			const existing = await ctx.db
				.query("beds24Bookings")
				.withIndex("by_bookingId", (q) => q.eq("bookingId", booking.bookingId))
				.first();

			if (existing) {
				// 更新
				const shouldUpdate = existing.modifiedDate < booking.modifiedDate;

				if (shouldUpdate) {
					await ctx.db.patch(existing._id, {
						...booking,
						updatedAt: Date.now(),
					});

					logger.debug("予約を更新しました", {
						bookingId: booking.bookingId,
						action: "updated" as const,
					});

					return {
						success: true,
						bookingId: booking.bookingId,
						action: "updated" as const,
					};
				} else {
					logger.debug("予約は最新です", {
						bookingId: booking.bookingId,
						action: "skipped" as const,
					});

					return {
						success: true,
						bookingId: booking.bookingId,
						action: "skipped" as const,
					};
				}
			} else {
				// 新規作成
				await ctx.db.insert("beds24Bookings", booking);

				logger.debug("予約を作成しました", {
					bookingId: booking.bookingId,
					action: "created" as const,
				});

				return {
					success: true,
					bookingId: booking.bookingId,
					action: "created" as const,
				};
			}
		} catch (error) {
			logger.error("予約のアップサートに失敗", {
				bookingId: booking.bookingId,
				error: error instanceof Error ? error.message : String(error),
			});

			return {
				success: false,
				bookingId: booking.bookingId,
				action: "skipped" as const,
				error: error instanceof Error ? error.message : String(error),
			};
		}
	},
});

/**
 * 単一予約のアップサート
 *
 * クライアントから呼び出される公開ミューテーション関数です。 * 認証チェックとアクセス権限確認を行ってから予約を保存します。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {BookingData} args.booking - 保存する予約データ
 * @returns {Promise<BookingResult>} アップサート結果
 * @throws {ConvexError} 認証エラー、アクセス権限エラー、ユーザーID不一致エラー
 *
 * @example
 * const result = await upsertBooking({
 *   booking: {
 *     bookingId: "123456",
 *     propertyId: "prop123",
 *     userId: "user123",
 *     checkIn: "2024-01-01",
 *     checkOut: "2024-01-05",
 *     // ...その他の予約データ
 *   }
 * });
 */
export const upsertBooking = mutation({
	args: {
		booking: bookingDataValidator,
	},
	returns: bookingResultValidator,
	handler: async (ctx, args) => {
		const logger = createLogger("upsertBooking", ctx);

		// 認証チェック
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw createConvexError(ERROR_CODES.UNAUTHORIZED, "Unauthenticated");
		}
		const userId = getAuthenticatedUserId(identity);

		// アクセス権限チェック
		const accessCheck = await checkPropertyAccess(
			ctx,
			userId,
			args.booking.propertyId,
		);
		if (!accessCheck.hasAccess) {
			throw createConvexError(
				ERROR_CODES.UNAUTHORIZED,
				"Access denied to property",
			);
		}

		// ユーザーIDが一致することを確認
		if (args.booking.userId !== userId) {
			throw createConvexError(
				ERROR_CODES.UNAUTHORIZED,
				"Cannot create booking for another user",
			);
		}

		logger.debug("予約をアップサート", {
			bookingId: args.booking.bookingId,
			userId,
		});

		// 内部アップサート関数を呼び出し
		const result: BookingResult = await ctx.runMutation(
			internal.beds24Bookings._upsertBooking,
			{
				booking: args.booking,
			},
		);

		logger.info("予約のアップサートが完了", {
			bookingId: args.booking.bookingId,
			result: result.action,
		});

		return result;
	},
});

/**
 * 複数予約の一括アップサート
 *
 * 複数の予約データを一括で保存します。
 * 全ての予約に対してアクセス権限を確認してから処理を行います。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {BookingData[]} args.bookings - 保存する予約データの配列
 * @returns {Promise<BatchResult>} バッチ処理結果
 * @throws {ConvexError} 認証エラー、アクセス権限エラー、ユーザーID不一致エラー
 *
 * @example
 * const result = await batchUpsertBookings({
 *   bookings: [
 *     { bookingId: "123", propertyId: "prop1", ... },
 *     { bookingId: "456", propertyId: "prop2", ... }
 *   ]
 * });
 * console.log(`${result.successful}件の予約を処理しました`);
 */
export const batchUpsertBookings = mutation({
	args: {
		bookings: v.array(bookingDataValidator),
	},
	returns: batchResultValidator,
	handler: async (ctx, args) => {
		const logger = createLogger("batchUpsertBookings", ctx);

		// 認証チェック
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw createConvexError(ERROR_CODES.UNAUTHORIZED, "Unauthenticated");
		}
		const userId = getAuthenticatedUserId(identity);

		logger.debug("予約の一括アップサートを開始", {
			userId,
			count: args.bookings.length,
		});

		// 全ての予約に対してアクセス権限をチェック
		const propertyIds = new Set(args.bookings.map((b) => b.propertyId));
		for (const propertyId of propertyIds) {
			const accessCheck = await checkPropertyAccess(ctx, userId, propertyId);
			if (!accessCheck.hasAccess) {
				throw createConvexError(
					ERROR_CODES.UNAUTHORIZED,
					`Access denied to property: ${propertyId}`,
				);
			}
		}

		// 全ての予約のユーザーIDをチェック
		for (const booking of args.bookings) {
			if (booking.userId !== userId) {
				throw createConvexError(
					ERROR_CODES.UNAUTHORIZED,
					"Cannot create booking for another user",
				);
			}
		}

		// バッチ処理を実行
		const result: BatchResult = await ctx.runMutation(
			internal.beds24Bookings.processBookingBatch,
			{
				bookings: args.bookings,
			},
		);

		logger.info("予約の一括アップサートが完了", {
			userId,
			total: args.bookings.length,
			successful: result.successful,
			failed: result.failed,
		});

		return result;
	},
});

/**
 * 非アクティブ予約の論理削除
 *
 * 施設の予約のうち、アクティブな予約IDリストに含まれない予約を論理削除します。
 * 予約同期時に使用され、APIから削除された予約をクリーンアップします。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {Id<"beds24Properties">} args.propertyId - 施設ID
 * @param {string[]} args.activeBookingIds - アクティブな予約IDのリスト
 * @returns {Promise<DeleteResult>} 削除結果
 * @returns {number} returns.deletedCount - 論理削除された件数
 * @returns {string[]} returns.errors - エラーメッセージの配列
 * @throws {ConvexError} 認証エラー、アクセス権限エラー
 *
 * @example
 * const result = await softDeleteInactiveBookings({
 *   propertyId: "prop123",
 *   activeBookingIds: ["booking1", "booking2", "booking3"]
 * });
 * console.log(`${result.deletedCount}件の予約を論理削除しました`);
 */
export const softDeleteInactiveBookings = mutation({
	args: {
		propertyId: v.id("beds24Properties"),
		activeBookingIds: v.array(v.string()),
	},
	returns: deleteResultValidator,
	handler: async (ctx, args) => {
		const logger = createLogger("softDeleteInactiveBookings", ctx);

		// 認証チェック
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw createConvexError(ERROR_CODES.UNAUTHORIZED, "Unauthenticated");
		}
		const userId = getAuthenticatedUserId(identity);

		// アクセス権限チェック
		const accessCheck = await checkPropertyAccess(ctx, userId, args.propertyId);
		if (!accessCheck.hasAccess) {
			throw createConvexError(
				ERROR_CODES.UNAUTHORIZED,
				"Access denied to property",
			);
		}

		logger.debug("非アクティブ予約を論理削除", {
			propertyId: args.propertyId,
			activeCount: args.activeBookingIds.length,
		});

		const activeBookingIdSet = new Set(args.activeBookingIds);
		let deletedCount = 0;
		const errors: string[] = [];

		// 施設の全予約を取得
		const bookings = await ctx.db
			.query("beds24Bookings")
			.withIndex("by_property", (q) => q.eq("propertyId", args.propertyId))
			.filter((q) => q.eq(q.field("isDeleted"), false))
			.collect();

		for (const booking of bookings) {
			if (!activeBookingIdSet.has(booking.bookingId)) {
				try {
					// 論理削除
					await ctx.db.patch(booking._id, {
						isDeleted: true,
						deletedAt: Date.now(),
						updatedAt: Date.now(),
					});
					deletedCount++;

					logger.debug("予約を論理削除", {
						bookingId: booking.bookingId,
					});
				} catch (error) {
					const errorMessage = `Failed to delete booking ${booking.bookingId}: ${
						error instanceof Error ? error.message : String(error)
					}`;
					errors.push(errorMessage);
					logger.error("予約の削除に失敗", {
						bookingId: booking.bookingId,
						error: error instanceof Error ? error.message : String(error),
					});
				}
			}
		}

		logger.info("非アクティブ予約の論理削除が完了", {
			propertyId: args.propertyId,
			totalBookings: bookings.length,
			activeBookings: args.activeBookingIds.length,
			deletedCount,
			errorCount: errors.length,
		});

		return {
			deletedCount,
			errors,
		};
	},
});
