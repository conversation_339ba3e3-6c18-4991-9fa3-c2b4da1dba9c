import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthenticatedUserId } from "./lib/auth";
import * as Beds24Model from "./model/beds24";
import type { PropertySummary, SyncStatusInfo } from "./types/beds24";

/**
 * ユーザーのBeds24施設一覧を取得する
 *
 * @param includeDeleted - 削除済み施設も含むかどうか（デフォルト: false）
 */
export const getProperties = query({
	args: {
		includeDeleted: v.optional(v.boolean()),
	},
	returns: v.array(
		v.object({
			_id: v.id("beds24Properties"),
			_creationTime: v.number(),
			beds24PropertyId: v.string(),
			beds24PropertyKey: v.string(),
			name: v.string(),
			isDeleted: v.boolean(),
			deletedAt: v.optional(v.number()),
			lastSyncedAt: v.number(),
			address: v.optional(v.string()),
			city: v.optional(v.string()),
			country: v.optional(v.string()),
			currency: v.optional(v.string()),
			timeZone: v.optional(v.string()),
		}),
	),
	handler: async (ctx, args): Promise<PropertySummary[]> => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("認証されていません");
		}

		const userId = getAuthenticatedUserId(identity);
		return await Beds24Model.getPropertiesByUserId(
			ctx,
			userId,
			args.includeDeleted,
		);
	},
});

/**
 * 最新の同期状態を取得する
 *
 * @param jobType - ジョブタイプ（オプション）
 */
export const getSyncStatus = query({
	args: {
		jobType: v.optional(
			v.union(
				v.literal("sync_properties"),
				v.literal("sync_reviews"),
				v.literal("scrape_booking_com_slug"),
				v.literal("sync_bookings"),
			),
		),
	},
	returns: v.union(
		v.object({
			isProcessing: v.boolean(),
			lastSync: v.optional(
				v.object({
					_id: v.id("beds24SyncHistory"),
					status: v.string(),
					startedAt: v.number(),
					completedAt: v.number(),
					duration: v.number(),
					totalItems: v.number(),
					successCount: v.number(),
					failedCount: v.number(),
				}),
			),
			pendingJobs: v.number(),
		}),
		v.null(),
	),
	handler: async (ctx, args): Promise<SyncStatusInfo | null> => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("認証されていません");
		}

		const userId = getAuthenticatedUserId(identity);
		return await Beds24Model.getSyncStatusForUser(ctx, userId, args.jobType);
	},
});

/**
 * 手動で施設同期を開始する
 */
export const triggerManualSync = mutation({
	args: {},
	returns: v.object({
		success: v.boolean(),
		message: v.string(),
		jobId: v.optional(v.id("syncQueue")),
	}),
	handler: async (ctx, _args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("認証されていません");
		}

		const userId = getAuthenticatedUserId(identity);
		return await Beds24Model.triggerManualSyncForUser(ctx, userId);
	},
});

/**
 * Beds24連携設定を更新する
 *
 * @param apiKey - Beds24 APIキー
 */
export const updateBeds24Settings = mutation({
	args: {
		apiKey: v.string(),
	},
	returns: v.object({
		success: v.boolean(),
		message: v.string(),
	}),
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("認証されていません");
		}

		const userId = getAuthenticatedUserId(identity);
		return await Beds24Model.updateBeds24ApiKey(ctx, userId, args.apiKey);
	},
});

/**
 * Beds24連携状態を確認する
 */
export const getBeds24ConnectionStatus = query({
	args: {},
	returns: v.object({
		isConnected: v.boolean(),
		hasRefreshToken: v.boolean(),
		hasAccessToken: v.boolean(),
		lastTokenRefresh: v.optional(v.number()),
		tokenExpiresAt: v.optional(v.number()),
	}),
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("認証されていません");
		}

		const userId = getAuthenticatedUserId(identity);
		return await Beds24Model.getConnectionStatus(ctx, userId);
	},
});

/**
 * 同期履歴を取得する（公開API）
 *
 * @param limit - 取得件数（デフォルト: 10）
 * @param jobType - ジョブタイプでフィルタリング（オプション）
 */
export { getSyncHistory } from "./beds24SyncHistory";

/**
 * 特定の施設の詳細情報を取得する
 *
 * @param beds24PropertyId - Beds24施設ID
 */
export const getPropertyDetails = query({
	args: {
		beds24PropertyId: v.string(),
	},
	returns: v.union(
		v.object({
			_id: v.id("beds24Properties"),
			_creationTime: v.number(),
			beds24PropertyId: v.string(),
			beds24PropertyKey: v.string(),
			data: v.any(), // 完全な施設データ
			lastSyncedAt: v.number(),
			isDeleted: v.boolean(),
			deletedAt: v.optional(v.number()),
		}),
		v.null(),
	),
	handler: async (ctx, args) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("認証されていません");
		}

		const userId = getAuthenticatedUserId(identity);
		return await Beds24Model.getPropertyDetailsForUser(
			ctx,
			userId,
			args.beds24PropertyId,
		);
	},
});
