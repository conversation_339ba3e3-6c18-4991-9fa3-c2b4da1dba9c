import type { ActionCtx, MutationCtx, QueryCtx } from "../_generated/server";

export type LogLevel = "DEBUG" | "INFO" | "WARN" | "ERROR";

export interface LogContext {
	functionName: string;
	userId?: string;
	args?: Record<string, any>;
	[key: string]: any;
}

/**
 * 環境に応じたログレベルを取得
 */
function getLogLevel(): LogLevel {
	// 開発環境では全てのログを出力
	// 本番環境ではWARN以上のみ（パフォーマンス改善）
	const env = process.env.NODE_ENV || "development";
	return env === "production" ? "WARN" : "INFO";
}

/**
 * ログレベルの優先度を数値で取得
 */
function getLogLevelPriority(level: LogLevel): number {
	const priorities: Record<LogLevel, number> = {
		DEBUG: 0,
		INFO: 1,
		WARN: 2,
		ERROR: 3,
	};
	return priorities[level];
}

/**
 * 構造化されたログを出力するヘルパークラス
 */
export class ConvexLogger {
	private context: LogContext;
	private minLevel: LogLevel;

	constructor(functionName: string, ctx?: QueryCtx | MutationCtx | ActionCtx) {
		this.context = {
			functionName,
			// @ts-ignore - ctxの型によってはauthが存在しない
			userId: ctx?.auth?.getUserIdentity?.()?.subject,
		};
		this.minLevel = getLogLevel();
	}

	/**
	 * コンテキスト情報を追加
	 */
	addContext(context: Partial<LogContext>): void {
		this.context = { ...this.context, ...context };
	}

	/**
	 * 引数情報を追加（センシティブな情報はマスク）
	 */
	setArgs(args: Record<string, any>): void {
		this.context.args = this.maskSensitiveData(args);
	}

	/**
	 * デバッグレベルのログ
	 */
	debug(message: string, data?: any): void {
		this.log("DEBUG", message, data);
	}

	/**
	 * 情報レベルのログ
	 */
	info(message: string, data?: any): void {
		this.log("INFO", message, data);
	}

	/**
	 * 警告レベルのログ
	 */
	warn(message: string, data?: any): void {
		this.log("WARN", message, data);
	}

	/**
	 * エラーレベルのログ
	 */
	error(message: string, error?: Error | any, data?: any): void {
		const errorData = {
			...data,
			error:
				error instanceof Error
					? {
							name: error.name,
							message: error.message,
							stack: error.stack,
						}
					: error,
		};
		this.log("ERROR", message, errorData);
	}

	/**
	 * パフォーマンス計測用のタイマー開始
	 */
	startTimer(label: string): () => void {
		const start = Date.now();
		return () => {
			const duration = Date.now() - start;
			this.info(`${label} completed`, { duration_ms: duration });
		};
	}

	/**
	 * 遡り処理専用のログ（再帰的日付範囲同期用）
	 */
	backtrack(
		message: string,
		data: {
			dateRange: { from: string; to: string };
			recursionDepth: number;
			totalProcessed?: number;
			newItems?: number;
			existingItems?: number;
			[key: string]: any;
		},
	): void {
		const enrichedData = {
			...data,
			tag: "BACKTRACK",
			progressIndicator: `[深度: ${data.recursionDepth}] ${data.dateRange.from} ~ ${data.dateRange.to}`,
			newVsExistingRatio:
				data.newItems && data.existingItems
					? `${Math.round((data.newItems / (data.newItems + data.existingItems)) * 100)}% 新規`
					: undefined,
		};
		this.log("INFO", `[遡り処理] ${message}`, enrichedData);
	}

	/**
	 * 同期進捗状況の可視化ログ
	 */
	syncProgress(
		phase: "start" | "processing" | "complete" | "error",
		data: {
			jobType: string;
			dateRange?: { from: string; to: string | null };
			progress?: {
				current: number;
				total: number;
				percentage?: number;
			};
			stats?: {
				created: number;
				updated: number;
				skipped: number;
				failed: number;
			};
			recursionInfo?: {
				isBacktracking: boolean;
				depth: number;
				originalStartDate?: string;
			};
			[key: string]: any;
		},
	): void {
		const phaseEmoji = {
			start: "🚀",
			processing: "⏳",
			complete: "✅",
			error: "❌",
		};

		let progressBar = "";
		if (data.progress) {
			const percentage =
				data.progress.percentage ||
				Math.round((data.progress.current / data.progress.total) * 100);
			const filled = Math.round(percentage / 10);
			const empty = 10 - filled;
			progressBar = `[${"█".repeat(filled)}${"░".repeat(empty)}] ${percentage}%`;
		}

		const message = `${phaseEmoji[phase]} 同期${phase === "start" ? "開始" : phase === "processing" ? "処理中" : phase === "complete" ? "完了" : "エラー"}`;

		const enrichedData = {
			...data,
			phase,
			progressBar: progressBar || undefined,
			summary: data.stats
				? `新規: ${data.stats.created}, 更新: ${data.stats.updated}, スキップ: ${data.stats.skipped}, 失敗: ${data.stats.failed}`
				: undefined,
		};

		this.log(phase === "error" ? "ERROR" : "INFO", message, enrichedData);
	}

	/**
	 * ログ出力の実装
	 */
	private log(level: LogLevel, message: string, data?: any): void {
		if (getLogLevelPriority(level) < getLogLevelPriority(this.minLevel)) {
			return;
		}

		const logEntry = {
			timestamp: new Date().toISOString(),
			level,
			message,
			context: this.context,
			data: data ? this.maskSensitiveData(data) : undefined,
		};

		// Convexではconsoleメソッドでログ出力
		switch (level) {
			case "DEBUG":
				console.log(JSON.stringify(logEntry));
				break;
			case "INFO":
				console.info(JSON.stringify(logEntry));
				break;
			case "WARN":
				console.warn(JSON.stringify(logEntry));
				break;
			case "ERROR":
				console.error(JSON.stringify(logEntry));
				break;
		}
	}

	/**
	 * センシティブなデータをマスク
	 */
	private maskSensitiveData(data: any): any {
		if (typeof data !== "object" || data === null) {
			return data;
		}

		const sensitiveKeys = [
			"password",
			"token",
			"refreshToken",
			"apiKey",
			"secret",
			"credential",
			"authorization",
		];

		const masked = Array.isArray(data) ? [...data] : { ...data };

		for (const key in masked) {
			if (
				sensitiveKeys.some((sensitive) =>
					key.toLowerCase().includes(sensitive.toLowerCase()),
				)
			) {
				masked[key] = "[MASKED]";
			} else if (typeof masked[key] === "object" && masked[key] !== null) {
				masked[key] = this.maskSensitiveData(masked[key]);
			}
		}

		return masked;
	}
}

/**
 * ロガーインスタンスを作成するヘルパー関数
 */
export function createLogger(
	functionName: string,
	ctx?: QueryCtx | MutationCtx | ActionCtx,
): ConvexLogger {
	return new ConvexLogger(functionName, ctx);
}
