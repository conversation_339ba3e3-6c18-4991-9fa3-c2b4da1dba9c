/**
 * 予約データパーサー
 *
 * Beds24 APIから取得した予約データを構造化し、
 * データベースに保存する形式に変換します。
 *
 * タイムゾーンの扱い:
 * - bookingTime: UTCで返される（APIドキュメント明記）
 * - その他の日時フィールド: JSTで返される（プロパティのタイムゾーン）
 * - 全ての宿は日本国内のため、タイムゾーンはJST固定
 *
 * 注意: このファイルはV8環境で動作します（"use node"なし）
 *
 * @module bookingParser
 * @see {@link BookingData} パース結果の型定義
 * @see {@link Beds24ApiWrapper} APIクライアント
 * @example
 * // 基本的な使用例
 * const parser = createBookingParser(logger);
 * const result = parser.parseBookingData(apiResponse, propertyId, userId);
 * if (result.success) {
 *   console.log(`予約ID: ${result.booking.bookingId}`);
 * }
 */

import { DateTime } from "luxon";
import type { Id } from "../_generated/dataModel";
import type {
	AmountInfo,
	BookingData,
	BookingStatus,
	GuestInfo,
	ParsedBookingResult,
} from "../types/beds24Bookings";
import { createConvexError, ERROR_CODES } from "./errors";
import { ConvexLogger } from "./logging";

/**
 * BookingParserのインターフェース
 */
export interface BookingParserInterface {
	parseBookingData(
		apiResponse: any,
		propertyId: Id<"beds24Properties">,
		userId: string,
	): ParsedBookingResult;
	normalizeBookingStatus(status: string): BookingStatus;
}

/**
 * パーサーオプション
 */
export interface BookingParserOptions {
	logger?: ConvexLogger;
}

/**
 * 予約データパーサーの実装
 *
 * Beds24 APIレスポンスを構造化されたBookingDataオブジェクトに変換します。
 * 日付処理、金額正規化、ステータス変換などの機能を提供します。
 *
 * @class BookingDataParser
 * @implements {BookingParserInterface}
 */
export class BookingDataParser implements BookingParserInterface {
	private logger: ConvexLogger;

	/**
	 * BookingDataParserのコンストラクタ
	 *
	 * @param options - パーサーオプション
	 * @param options.logger - ロギング用のConvexLoggerインスタンス（オプション）
	 */
	constructor(options?: BookingParserOptions) {
		this.logger = options?.logger || new ConvexLogger("BookingDataParser");
	}

	/**
	 * Beds24 APIレスポンスを構造化データに変換
	 *
	 * 生のAPIレスポンスから必要な情報を抽出し、型安全な構造化データに変換します。
	 * ゲスト情報、日付、金額、ステータスなどを解析し、データベース保存用の形式に整形します。
	 *
	 * @param apiResponse - Beds24 APIからの生レスポンス
	 * @param propertyId - 予約が属するプロパティのID
	 * @param userId - 予約を同期するユーザーのID
	 * @returns パース結果（成功時: 構造化された予約データ、失敗時: エラー情報）
	 * @example
	 * const result = parseBookingData(
	 *   { id: "12345", arrival: "2024-12-25", departure: "2024-12-27", ... },
	 *   propertyId,
	 *   "user123"
	 * );
	 * if (result.success) {
	 *   console.log(`チェックイン: ${result.booking.checkIn}`);
	 * }
	 */
	parseBookingData(
		apiResponse: any,
		propertyId: Id<"beds24Properties">,
		userId: string,
	): ParsedBookingResult {
		try {
			if (!apiResponse || typeof apiResponse !== "object") {
				throw createConvexError(
					ERROR_CODES.VALIDATION_ERROR,
					"Invalid API response format",
				);
			}

			// 必須フィールドの検証
			if (!apiResponse.id) {
				throw createConvexError(
					ERROR_CODES.VALIDATION_ERROR,
					"Booking ID is required",
				);
			}

			const bookingId = String(apiResponse.id);
			const now = Date.now();

			// ゲスト情報の解析
			const guestInfo = this.parseGuestInfo(apiResponse);

			// 日付情報の解析
			const { checkIn, checkOut, bookingDate } =
				this.parseDateInfo(apiResponse);

			// ステータスの正規化
			const status = this.normalizeBookingStatus(apiResponse.status);

			// 金額情報の解析
			const amountInfo = this.parseAmountInfo(apiResponse);
			const totalPrice = this.normalizePrice(
				apiResponse.price,
				"totalPrice",
				true,
			) as number;
			const currency = apiResponse.currency || "JPY";

			// 修正日時の解析
			const modifiedDate = apiResponse.modified
				? this.parseModifiedDate(apiResponse.modified)
				: now;

			// 構造化された予約データ
			const bookingData: BookingData = {
				bookingId,
				propertyId,
				userId,
				guestInfo,
				checkIn,
				checkOut,
				bookingDate,
				status,
				totalPrice,
				currency,
				amountInfo,
				// 部屋情報
				roomId: apiResponse.roomId ? String(apiResponse.roomId) : undefined,
				roomName: apiResponse.roomName || undefined,
				adults: this.parseNumber(apiResponse.numAdult),
				children: this.parseNumber(apiResponse.numChild),
				// チャネル情報
				channel: apiResponse.referer || undefined,
				channelId: apiResponse.refererId
					? String(apiResponse.refererId)
					: undefined,
				// メモ
				notes: apiResponse.notes || undefined,
				internalNotes: apiResponse.internalNotes || undefined,
				// APIレスポンス全体を保存
				data: apiResponse,
				// メタデータ
				modifiedDate,
				lastSyncedAt: now,
				createdAt: now,
				updatedAt: now,
				isDeleted: false,
			};

			this.logger.debug("Successfully parsed booking data", {
				bookingId,
				checkIn,
				checkOut,
				bookingDate,
				status,
				timezone: "Property timezone: JST, bookingTime: UTC",
			});

			return {
				success: true,
				booking: bookingData,
				rawData: apiResponse,
			};
		} catch (error) {
			this.logger.error("Failed to parse booking data", {
				error: error instanceof Error ? error.message : String(error),
				apiResponse,
			});

			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: "Unknown error during parsing",
				rawData: apiResponse,
			};
		}
	}

	/**
	 * Beds24のステータスコードを内部ステータスに変換
	 *
	 * Beds24 APIが返すステータスコード（0-3）を、
	 * アプリケーション内部で使用するBookingStatus型に変換します。
	 *
	 * @param status - Beds24のステータスコード（0: cancelled, 1: confirmed, 2: new, 3: black）
	 * @returns 内部ステータス（"confirmed" | "cancelled"）
	 * @example
	 * normalizeBookingStatus("1") // => "confirmed"
	 * normalizeBookingStatus(0) // => "cancelled"
	 * normalizeBookingStatus("2") // => "confirmed" (新規予約も確定として扱う)
	 */
	normalizeBookingStatus(status: string | number): BookingStatus {
		const statusStr = String(status);

		switch (statusStr) {
			case "0": // cancelled
				return "cancelled";
			case "1": // confirmed
				return "confirmed";
			case "2": // new/request
				return "confirmed"; // 新規予約も確定として扱う
			case "3": // black
				return "cancelled"; // ブラックリストはキャンセル扱い
			default:
				this.logger.warn("Unknown booking status", { status: statusStr });
				return "confirmed"; // デフォルトは確定
		}
	}

	/**
	 * ゲスト情報を解析
	 */
	private parseGuestInfo(data: any): GuestInfo | undefined {
		const hasGuestInfo =
			data.guestEmail ||
			data.guestPhone ||
			data.guestCountry ||
			data.guestAddress ||
			data.guestCity ||
			data.guestPostcode;

		if (!hasGuestInfo) {
			return undefined;
		}

		return {
			firstName: data.guestFirstName || undefined,
			lastName: data.guestName || undefined,
			email: data.guestEmail || undefined,
			phone: data.guestPhone || undefined,
			country: data.guestCountry || undefined,
			address: data.guestAddress || undefined,
			city: data.guestCity || undefined,
			postCode: data.guestPostcode || undefined,
		};
	}

	/**
	 * 金額情報を解析
	 */
	private parseAmountInfo(data: any): AmountInfo | undefined {
		const hasAmountInfo =
			data.price ||
			data.roomPrice ||
			data.extras ||
			data.tax ||
			data.commission ||
			data.deposit ||
			data.paid ||
			data.balance;

		if (!hasAmountInfo) {
			return undefined;
		}

		return {
			totalPrice: this.normalizePrice(data.price, "totalPrice", true) as number,
			currency: data.currency || "JPY",
			roomPrice: data.roomPrice
				? this.normalizePrice(data.roomPrice, "roomPrice", false)
				: undefined,
			extras: data.extras
				? this.normalizePrice(data.extras, "extras", false)
				: undefined,
			taxes: data.tax
				? this.normalizePrice(data.tax, "taxes", false)
				: undefined,
			commission: data.commission
				? this.normalizePrice(data.commission, "commission", false)
				: undefined,
			deposit: data.deposit
				? this.normalizePrice(data.deposit, "deposit", false)
				: undefined,
			paid: data.paid
				? this.normalizePrice(data.paid, "paid", false)
				: undefined,
			balance: data.balance
				? this.normalizePrice(data.balance, "balance", false)
				: undefined,
		};
	}

	/**
	 * 日付情報を解析
	 */
	private parseDateInfo(data: any): {
		checkIn: string;
		checkOut: string;
		bookingDate?: string;
	} {
		// チェックイン・チェックアウト日の解析（必須）
		const checkIn = this.formatDateString(data.arrival);
		const checkOut = this.formatDateString(data.departure);

		// 予約日時の解析（オプション）
		const bookingDate = data.bookingTime
			? this.parseBookingDateTime(data.bookingTime)
			: undefined;

		return {
			checkIn,
			checkOut,
			bookingDate,
		};
	}

	/**
	 * 日付文字列をフォーマット（YYYY-MM-DD形式を保持）
	 */
	private formatDateString(dateStr: any): string {
		if (!dateStr) {
			throw createConvexError(
				ERROR_CODES.VALIDATION_ERROR,
				"Date string is required",
			);
		}

		const date = String(dateStr);

		// YYYY-MM-DD形式の検証
		if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
			throw createConvexError(
				ERROR_CODES.VALIDATION_ERROR,
				`Invalid date format: ${date}`,
			);
		}

		return date;
	}

	/**
	 * 予約日時を解析（ISO形式に変換）
	 * bookingTimeはISO 8601形式（UTC）で返される
	 */
	private parseBookingDateTime(dateTimeStr: any): string | undefined {
		if (!dateTimeStr) {
			return undefined;
		}

		try {
			const dateTime = String(dateTimeStr);

			// ISO 8601形式としてパース
			const dt = DateTime.fromISO(dateTime);

			// 有効な日付かチェック
			if (!dt.isValid) {
				this.logger.warn("Invalid booking datetime", {
					dateTimeStr,
					format: "ISO 8601",
					expectedFormat: "YYYY-MM-DDTHH:mm:ssZ",
					reason: (dt as any).invalidExplanation || "Unknown reason",
				});
				return undefined;
			}

			// ISO8601形式で返す（すでにISO形式だが、Luxonで正規化）
			return dt.toISO();
		} catch (error) {
			this.logger.warn("Failed to parse booking datetime", {
				dateTimeStr,
				error: error instanceof Error ? error.message : String(error),
			});
			return undefined;
		}
	}

	/**
	 * 修正日時を解析（タイムスタンプに変換）
	 * modifiedTimeは宿のタイムゾーン（JST）で返される
	 */
	private parseModifiedDate(modifiedStr: any): number {
		if (!modifiedStr) {
			return Date.now();
		}

		try {
			// YYYY-MM-DD HH:MM:SS形式を想定（JST）
			const dateTime = String(modifiedStr);

			// LuxonでJSTとしてパース
			const dt = DateTime.fromFormat(dateTime, "yyyy-MM-dd HH:mm:ss", {
				zone: "Asia/Tokyo",
			});

			// 有効な日付かチェック
			if (!dt.isValid) {
				this.logger.warn("Invalid modified date", {
					modifiedStr,
					format: "yyyy-MM-dd HH:mm:ss",
					timezone: "Asia/Tokyo",
				});
				return Date.now();
			}

			// タイムスタンプ（ミリ秒）で返す（自動的にUTCに変換される）
			return dt.toMillis();
		} catch (error) {
			this.logger.warn("Failed to parse modified date", {
				modifiedStr,
				error: error instanceof Error ? error.message : String(error),
			});
			return Date.now();
		}
	}

	/**
	 * 金額を正規化（文字列から数値に変換）
	 * @param price - 変換する価格データ
	 * @param fieldName - フィールド名（エラーメッセージ用）
	 * @param isRequired - 必須フィールドかどうか
	 */
	private normalizePrice(
		price: any,
		fieldName: string = "price",
		isRequired: boolean = false,
	): number | undefined {
		if (typeof price === "number") {
			return price;
		}

		if (typeof price === "string") {
			// カンマを除去して数値に変換
			const normalized = price.replace(/,/g, "");
			const parsed = parseFloat(normalized);

			if (Number.isNaN(parsed)) {
				this.logger.error(`Invalid ${fieldName} format`, { price, fieldName });

				if (isRequired) {
					throw createConvexError(
						ERROR_CODES.VALIDATION_ERROR,
						`Invalid ${fieldName} format: ${price}`,
					);
				}

				return undefined;
			}

			return parsed;
		}

		// null, undefined, その他の型の場合
		if (isRequired) {
			throw createConvexError(
				ERROR_CODES.VALIDATION_ERROR,
				`${fieldName} is required but not provided`,
			);
		}

		return undefined;
	}

	/**
	 * 数値を解析
	 */
	private parseNumber(value: any): number | undefined {
		if (value === undefined || value === null || value === "") {
			return undefined;
		}

		const num = typeof value === "number" ? value : parseInt(String(value), 10);

		if (Number.isNaN(num)) {
			return undefined;
		}

		return num;
	}
}

/**
 * BookingParserのファクトリー関数
 *
 * BookingParserInterfaceを実装したパーサーインスタンスを生成します。
 *
 * @param logger - ロギング用のConvexLoggerインスタンス（オプション）
 * @returns BookingParserInterfaceを実装したインスタンス
 * @example
 * // カスタムロガー付きで作成
 * const parser = createBookingParser(new ConvexLogger("MyParser"));
 *
 * // デフォルトロガーで作成
 * const parser = createBookingParser();
 */
export function createBookingParser(
	logger?: ConvexLogger,
): BookingParserInterface {
	return new BookingDataParser({ logger });
}

/**
 * デフォルトのBookingParser
 */
export const defaultBookingParser = new BookingDataParser();
