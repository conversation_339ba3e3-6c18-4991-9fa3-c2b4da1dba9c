/**
 * 日付計算ユーティリティ
 * Beds24 API用の日付処理を行うヘルパー関数群
 */

/**
 * ISO日付文字列をDateオブジェクトに変換する
 * @param dateString - ISO形式の日付文字列 (YYYY-MM-DD または YYYY-MM-DDTHH:mm:ssZ)
 * @returns Dateオブジェクト
 * @throws {Error} 無効な日付文字列の場合
 */
export function parseISODate(dateString: string): Date {
	const date = new Date(dateString);
	if (Number.isNaN(date.getTime())) {
		throw new Error("Invalid date string");
	}
	return date;
}

/**
 * DateオブジェクトをBeds24 API用のYYYY-MM-DD形式に変換する
 * @param date - 変換するDateオブジェクト
 * @returns YYYY-MM-DD形式の日付文字列
 */
export function formatDateForBeds24(date: Date): string {
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, "0");
	const day = String(date.getDate()).padStart(2, "0");
	return `${year}-${month}-${day}`;
}

/**
 * 月単位で日付を加算/減算する
 * @param date - 基準となる日付
 * @param months - 加算する月数（負の値で減算）
 * @returns 新しいDateオブジェクト（元のオブジェクトは変更されない）
 */
export function addMonths(date: Date, months: number): Date {
	const result = new Date(date);
	const currentMonth = result.getMonth();
	const currentDate = result.getDate();

	// 月を設定
	result.setMonth(currentMonth + months);

	// 月末日の調整
	// 例: 1月31日 + 1ヶ月 = 2月28日（または29日）
	if (result.getDate() !== currentDate) {
		// 月が変わってしまった場合は、前月の最終日に設定
		result.setDate(0);
	}

	return result;
}

/**
 * 現在の日付範囲から2ヶ月前の期間を計算する
 * @param currentFrom - 現在の開始日（ISO形式）
 * @param currentTo - 現在の終了日（ISO形式）、nullの場合は現在日時を使用
 * @returns 2ヶ月前の期間 { from, to }
 */
export function calculatePreviousDateRange(
	currentFrom: string,
	currentTo: string | null,
): { from: string; to: string } {
	const fromDate = parseISODate(currentFrom);
	const toDate = currentTo ? parseISODate(currentTo) : new Date();

	const previousFrom = addMonths(fromDate, -2);
	const previousTo = addMonths(toDate, -2);

	return {
		from: formatDateForBeds24(previousFrom),
		to: formatDateForBeds24(previousTo),
	};
}
