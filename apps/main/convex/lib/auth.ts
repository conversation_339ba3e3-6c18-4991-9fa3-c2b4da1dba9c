import type { UserIdentity } from "convex/server";
import type { MutationCtx, QueryCtx } from "../_generated/server";
import { createConvexError, ERROR_CODES } from "./errors";
import { type ConvexLogger, createLogger } from "./logging";

/**
 * 認証をチェックし、ユーザーIDを返す
 * @param ctx - Convexコンテキスト
 * @returns ユーザーID
 * @throws 認証されていない場合はエラー
 */
export async function requireAuth(ctx: {
	auth: QueryCtx["auth"];
}): Promise<string> {
	const identity = await ctx.auth.getUserIdentity();
	if (!identity) {
		throw new Error("認証されていません");
	}
	return identity.tokenIdentifier;
}

/**
 * 認証されたユーザーのIDを取得する
 * @param identity - ユーザーアイデンティティ
 * @returns ユーザーID（issuer|subject形式）
 */
export function getAuthenticatedUserId(identity: UserIdentity): string {
	// tokenIdentifier全体を返す（issuer|subject形式）
	// 例: "https://your-clerk-domain.clerk.accounts.dev|user_xxxxxxxxxxxxxxxxxxxxx"
	return identity.tokenIdentifier;
}

/**
 * 認証されたユーザーを要求し、ユーザーIDとロガーを返す
 * @param ctx - Convexコンテキスト（QueryCtx | MutationCtx）
 * @param functionName - 呼び出し元の関数名
 * @returns ユーザーIDとロガーのオブジェクト
 * @throws 認証されていない場合はConvexError
 */
export async function requireAuthenticatedUser<
	TCtx extends QueryCtx | MutationCtx,
>(
	ctx: TCtx,
	functionName: string,
): Promise<{ userId: string; logger: ConvexLogger }> {
	const logger = createLogger(functionName, ctx);

	const identity = await ctx.auth.getUserIdentity();
	if (!identity) {
		logger.error("認証エラー: ユーザーが認証されていません");
		throw createConvexError(
			ERROR_CODES.UNAUTHORIZED,
			"認証が必要です",
			undefined,
			{ functionName },
		);
	}

	const userId = getAuthenticatedUserId(identity);
	logger.addContext({ userId });

	return { userId, logger };
}
