/**
 * Booking.com施設スラッグスクレイパー
 *
 * Booking.comの検索結果から施設URLを抽出し、スラッグを取得するためのユーティリティ。
 * 施設名による検索、HTMLパース、URL解析の機能を提供します。
 *
 * @module bookingComSlugScraper
 * @see {@link ScrapingAntWrapper} スクレイピング実行用のラッパー
 * @example
 * // 基本的な使用例
 * const result = await scrapeBookingComSlug("東京ホテル", scrapingAnt, logger);
 * if (result.success) {
 *   console.log(result.slug); // "tokyo-hotel"
 * }
 */

import type { ScrapingAntResponse } from "../types/reviews";
import type { ConvexLogger } from "./logging";
import type { ScrapingAntWrapper } from "./scrapingAnt";

// =====================================
// 定数定義
// =====================================

/** Booking.com検索結果のベースURL */
const BOOKING_COM_SEARCH_BASE_URL =
	"https://www.booking.com/searchresults.ja.html";

/** 検索時のデフォルトパラメータ */
const DEFAULT_SEARCH_PARAMS = {
	group_adults: "2",
	no_rooms: "1",
	group_children: "0",
} as const;

/** 施設リンクを識別するCSSクラス */
const HOTEL_LINK_CSS_CLASS = "js-sr-hotel-link";

/** Booking.comの施設URLパス */
const BOOKING_COM_HOTEL_PATH = "booking.com/hotel/";

/** エラーメッセージ */
const ERROR_MESSAGES = {
	PROPERTY_NOT_FOUND: "施設が見つかりませんでした",
	SLUG_EXTRACTION_FAILED: "施設URLからスラッグを抽出できませんでした",
} as const;

/** ログメッセージ */
const LOG_MESSAGES = {
	SCRAPING_START: "Booking.com施設スラッグ取得開始",
	SEARCH_URL_GENERATED: "検索URL生成",
	PROPERTY_URL_NOT_FOUND: "施設URLが見つかりませんでした",
	PROPERTY_URL_EXTRACTED: "施設URL抽出成功",
	SLUG_EXTRACTION_SUCCESS: "施設スラッグ取得成功",
	SCRAPING_ERROR: "施設スラッグ取得エラー",
} as const;

// =====================================
// 公開関数
// =====================================

/**
 * 施設名からBooking.com検索URLを生成する
 *
 * 施設名をダブルクォートで囲んで完全一致検索を行うためのURLを生成します。
 *
 * @param propertyName - 施設名
 * @returns 検索URL
 * @example
 * generateSearchUrl("東京ホテル")
 * // => "https://www.booking.com/searchresults.ja.html?ss=%22%E6%9D%B1%E4%BA%AC%E3%83%9B%E3%83%86%E3%83%AB%22&..."
 */
export function generateSearchUrl(propertyName: string): string {
	// 施設名をダブルクォートで囲んで完全一致検索を促す
	const params = new URLSearchParams({
		ss: `"${propertyName}"`, // URLSearchParamsが自動的にエンコードする
		...DEFAULT_SEARCH_PARAMS,
	});

	return `${BOOKING_COM_SEARCH_BASE_URL}?${params.toString()}`;
}

/**
 * 検索結果HTMLから施設URLを抽出する
 *
 * Booking.comの検索結果ページのHTMLから、最初の施設のURLを抽出します。
 * js-sr-hotel-linkクラスを持つリンクを探し、Booking.comの施設URLのみを返します。
 *
 * @param html - 検索結果のHTML
 * @returns 施設URL（クエリパラメータを除く）またはnull
 * @throws {Error} cheerioのインポートに失敗した場合
 * @example
 * const url = await extractPropertyUrl('<a href="https://www.booking.com/hotel/jp/tokyo.ja.html?aid=123" class="js-sr-hotel-link">...</a>')
 * // => "https://www.booking.com/hotel/jp/tokyo.ja.html"
 */
export async function extractPropertyUrl(html: string): Promise<string | null> {
	const cheerio = await import("cheerio");
	const $ = cheerio.default ? cheerio.default.load(html) : cheerio.load(html);

	// js-sr-hotel-linkクラスを持つリンクを探す
	const hotelLink = $(`.${HOTEL_LINK_CSS_CLASS}`).first();
	if (hotelLink.length > 0) {
		const href = hotelLink.attr("href");
		if (href?.includes(BOOKING_COM_HOTEL_PATH)) {
			// クエリパラメータを除去
			return href.split("?")[0];
		}
	}

	// フォールバック: Booking.comの施設リンクを直接検索
	const allLinks = $("a[href*='booking.com/hotel/']");
	for (let i = 0; i < allLinks.length; i++) {
		const href = $(allLinks[i]).attr("href");
		if (href?.includes(BOOKING_COM_HOTEL_PATH)) {
			return href.split("?")[0];
		}
	}

	return null;
}

/**
 * 施設URLからスラッグを抽出する
 *
 * Booking.comの施設URL形式から、施設を識別するスラッグ部分を抽出します。
 * URLパターン: /hotel/{country}/{slug}.{lang}.html
 *
 * @param url - 施設のURL
 * @returns スラッグまたはnull
 * @example
 * extractSlugFromUrl("https://www.booking.com/hotel/jp/tokyo-grand.ja.html")
 * // => "tokyo-grand"
 */
export function extractSlugFromUrl(url: string): string | null {
	// URLパターン: /hotel/{2文字以上の国コード}/{スラッグ}.{言語コード}.html
	// または: /hotel/{2文字以上の国コード}/{スラッグ}.html (言語コードなし)
	const slugPattern = /\/hotel\/[a-z]{2,}\/([^.]+)(?:\.[a-z-]+)?\.html/;
	const match = url.match(slugPattern);

	if (match?.[1]) {
		return match[1];
	}

	return null;
}

/**
 * スクレイピング結果の型定義
 *
 * @typedef {Object} ScrapeResult
 * @property {boolean} success - スクレイピングの成功/失敗を示すフラグ
 * @property {string} [slug] - 成功時: 施設のスラッグ（例: "tokyo-hotel"）
 * @property {string} [propertyUrl] - 成功時: 施設の完全なURL
 * @property {string} [error] - 失敗時: エラーメッセージ
 */
export type ScrapeResult =
	| { success: true; slug: string; propertyUrl: string }
	| { success: false; error: string };

/**
 * Booking.comから施設のスラッグをスクレイピングする
 *
 * 以下の手順で処理を実行します：
 * 1. 施設名から検索URLを生成
 * 2. ScrapingAntを使用して検索結果ページを取得
 * 3. HTMLから施設URLを抽出
 * 4. URLからスラッグを抽出
 *
 * @param propertyName - 施設名
 * @param scrapingAnt - ScrapingAntクライアント
 * @param logger - ロガー
 * @returns スクレイピング結果（成功時: slug, propertyUrl / 失敗時: error）
 * @throws {Error} ScrapingAntのAPI呼び出しに失敗した場合
 * @throws {Error} ネットワークエラーが発生した場合
 * @example
 * const result = await scrapeBookingComSlug("東京ホテル", scrapingAnt, logger);
 * if (result.success) {
 *   console.log(result.slug); // "tokyo-hotel"
 *   console.log(result.propertyUrl); // "https://www.booking.com/hotel/jp/tokyo-hotel.ja.html"
 * } else {
 *   console.error(result.error); // "施設が見つかりませんでした"
 * }
 */
export async function scrapeBookingComSlug(
	propertyName: string,
	scrapingAnt: ScrapingAntWrapper,
	logger: ConvexLogger,
): Promise<ScrapeResult> {
	logger.info(LOG_MESSAGES.SCRAPING_START, { propertyName });

	// 施設名の検証ログ
	logger.info("施設名の分析", {
		originalName: propertyName,
		hasEmoji: /[\u{1F000}-\u{1F9FF}|\u{2B50}]/u.test(propertyName), // ⭐️ (U+2B50) を含む
		hasSpecialChars: /[^\w\s\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(
			propertyName,
		),
		encodedName: encodeURIComponent(propertyName),
		nameLength: propertyName.length,
		starChar: propertyName.includes("⭐"),
		starCharCode: propertyName.charCodeAt(0), // ⭐の文字コード確認
		firstChar: propertyName[0],
	});

	try {
		// 検索URLを生成
		const searchUrl = generateSearchUrl(propertyName);
		logger.debug(LOG_MESSAGES.SEARCH_URL_GENERATED, { searchUrl });

		// スクレイピング実行
		// wait_for_selectorで指定したCSSクラスが表示されるまで待機
		logger.info("ScrapingAnt呼び出し準備", {
			searchUrl,
			selector: `.${HOTEL_LINK_CSS_CLASS}`,
			options: {
				browser: true,
				wait_for_selector: `.${HOTEL_LINK_CSS_CLASS}`,
			},
			timestamp: new Date().toISOString(),
		});

		let response: ScrapingAntResponse;
		try {
			response = await scrapingAnt.scrapeUrl(searchUrl, {
				browser: true,
				wait_for_selector: `.${HOTEL_LINK_CSS_CLASS}`,
			});

			logger.info("ScrapingAntレスポンス受信", {
				hasContent: !!response.content,
				contentLength: response.content?.length || 0,
				statusCode: response.statusCode,
				timestamp: new Date().toISOString(),
			});
		} catch (scrapeError: any) {
			logger.error("ScrapingAnt呼び出しエラー", {
				errorType: scrapeError.constructor.name,
				errorMessage: scrapeError.message,
				errorCode: scrapeError.code,
				searchUrl,
				propertyName,
				timestamp: new Date().toISOString(),
			});
			throw scrapeError;
		}

		// 施設URLを抽出
		const propertyUrl = await extractPropertyUrl(response.content);
		if (!propertyUrl) {
			// セレクタ検証ログ
			logger.warn("セレクタ検証", {
				hasTargetClass: response.content.includes("js-sr-hotel-link"),
				alternativeSelectors: {
					hasDataTestId: response.content.includes(
						'data-testid="property-card"',
					),
					hasHotelCard: response.content.includes("sr_property_block"),
					hasPropertyLink: response.content.includes("property_link"),
					hasHotelNameLink: response.content.includes("hotel_name_link"),
				},
				contentLength: response.content.length,
				contentSnippet: response.content.substring(0, 1000),
				searchKeywordFound: response.content.includes(
					propertyName.substring(0, 10),
				),
			});

			logger.warn(LOG_MESSAGES.PROPERTY_URL_NOT_FOUND, { propertyName });
			return {
				success: false,
				error: ERROR_MESSAGES.PROPERTY_NOT_FOUND,
			};
		}

		logger.debug(LOG_MESSAGES.PROPERTY_URL_EXTRACTED, { propertyUrl });

		// スラッグを抽出
		const slug = extractSlugFromUrl(propertyUrl);
		if (!slug) {
			logger.error(ERROR_MESSAGES.SLUG_EXTRACTION_FAILED, { propertyUrl });
			return {
				success: false,
				error: ERROR_MESSAGES.SLUG_EXTRACTION_FAILED,
			};
		}

		logger.info(LOG_MESSAGES.SLUG_EXTRACTION_SUCCESS, {
			propertyName,
			propertyUrl,
			slug,
		});

		return { success: true, slug, propertyUrl };
	} catch (error: any) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logger.error(LOG_MESSAGES.SCRAPING_ERROR, {
			propertyName,
			error: errorMessage,
			errorType: error?.constructor?.name,
			errorCode: error?.code,
			errorStack: error?.stack,
			isConvexError: error?.message?.includes("ConvexError"),
			isNetworkError: error?.message?.includes("NETWORK_ERROR"),
			timestamp: new Date().toISOString(),
		});
		return { success: false, error: errorMessage };
	}
}
