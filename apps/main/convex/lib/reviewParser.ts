"use node";

/**
 * レビューHTMLパーサー
 *
 * OTAサイトからスクレイピングしたHTMLからレビューデータを抽出します。
 * 現在はBooking.comのみをサポートしています。
 *
 * @module reviewParser
 * @see {@link ParsedReview} パース結果の型定義
 * @see {@link ReviewParser} パーサーインターフェース
 * @example
 * // 基本的な使用例
 * const parser = createReviewParser(logger);
 * const result = parser.parseHtml(html, "booking.com", url);
 * console.log(`${result.reviews.length}件のレビューを抽出`);
 */

import * as cheerio from "cheerio";
import { DateTime } from "luxon";
import type {
	OTAType,
	ParsedReview,
	ParseResult,
	ReviewParser,
} from "../types/reviews";
import { createConvexError, ERROR_CODES } from "./errors";
import { ConvexLogger } from "./logging";

/**
 * レビューパーサーの実装
 *
 * OTAサイトのHTMLからレビューデータを抽出するパーサーの実装クラス。
 * CheerioによるHTMLパース、日付処理、スコア正規化機能を提供します。
 *
 * @class ReviewParserImpl
 * @implements {ReviewParser}
 */
export class ReviewParserImpl implements ReviewParser {
	private logger: ConvexLogger;

	/**
	 * ReviewParserImplのコンストラクタ
	 *
	 * @param logger - ロギング用のConvexLoggerインスタンス（オプション）
	 */
	constructor(logger?: ConvexLogger) {
		this.logger = logger || new ConvexLogger("ReviewParser");
	}

	/**
	 * HTMLからレビューをパース
	 *
	 * スクレイピングしたHTMLコンテンツからレビューデータを抽出します。
	 * OTAタイプに応じて適切なパーサーを選択し、構造化されたレビューデータを返します。
	 *
	 * @param html - パース対象のHTMLコンテンツ
	 * @param otaType - OTAサイトの種類（現在は"booking.com"のみサポート）
	 * @param url - スクレイピング元のURL（ログ出力用）
	 * @returns パース結果（レビュー配列、次ページURL、総レビュー数を含む）
	 * @throws {ConvexError} HTMLが空の場合（VALIDATION_ERROR）
	 * @throws {ConvexError} 未実装のOTAタイプの場合（NOT_FOUND）
	 * @throws {ConvexError} 不明なOTAタイプの場合（INVALID_REQUEST）
	 * @throws {ConvexError} HTMLパースに失敗した場合（INTERNAL_ERROR）
	 * @example
	 * const result = parser.parseHtml(htmlContent, "booking.com", "https://www.booking.com/reviews/...");
	 * console.log(`抽出されたレビュー: ${result.reviews.length}件`);
	 * if (result.nextPageUrl) {
	 *   console.log(`次ページ: ${result.nextPageUrl}`);
	 * }
	 */
	parseHtml(html: string, otaType: OTAType, url: string): ParseResult {
		if (!html || html.trim().length === 0) {
			throw createConvexError(
				ERROR_CODES.VALIDATION_ERROR,
				"Empty HTML content provided",
				{ url, otaType },
			);
		}

		this.logger.info("Starting HTML parsing", {
			otaType,
			url,
			htmlLength: html.length,
		});

		try {
			// HTMLをパース
			const $ = cheerio.load(html);

			// OTAタイプに応じてパーサーを選択
			switch (otaType) {
				case "booking.com":
					return this.parseBookingReviews($, url);
				case "expedia":
				case "hotels.com":
				case "agoda":
					throw createConvexError(
						ERROR_CODES.NOT_FOUND,
						`Parser for ${otaType} is not implemented yet`,
						{ otaType },
					);
				default:
					throw createConvexError(
						ERROR_CODES.INVALID_REQUEST,
						`Unknown OTA type: ${otaType}`,
						{ otaType },
					);
			}
		} catch (error) {
			if (error instanceof Error && error.message.includes("ConvexError")) {
				throw error;
			}

			this.logger.error("HTML parsing failed", error, { url, otaType });
			throw createConvexError(
				ERROR_CODES.INTERNAL_ERROR,
				"Failed to parse HTML content",
				{
					url,
					otaType,
					error: error instanceof Error ? error.message : String(error),
				},
			);
		}
	}

	/**
	 * レビューのハッシュを生成（重複チェック用）
	 *
	 * レビューの一意性を保証するためのSHA-256ハッシュを生成します。
	 * OTA ID、プロパティID、レビュー日付、コンテンツを組み合わせてハッシュ化します。
	 *
	 * @param review - ハッシュ生成対象のレビューデータ
	 * @param propertyId - プロパティのID
	 * @param otaId - OTAサイトのID
	 * @returns SHA-256ハッシュ値（16進数文字列）
	 * @throws {ConvexError} Crypto APIが利用できない場合（INTERNAL_ERROR）
	 * @example
	 * const hash = await generateHash(
	 *   { reviewDate: 1234567890, content: "素晴らしいホテル", ... },
	 *   "property123",
	 *   "booking.com"
	 * );
	 * // => "a1b2c3d4e5f6..."
	 */
	async generateHash(
		review: ParsedReview,
		propertyId: string,
		otaId: string,
	): Promise<string> {
		// Convex環境ではcryptoモジュールが使用できるか確認が必要
		// 簡易的なハッシュ生成（実際の実装では crypto.subtle.digest を使用）
		const data = `${otaId}-${propertyId}-${review.reviewDate}-${review.content}`;

		// Node.js環境のcrypto APIを使用
		const encoder = new TextEncoder();
		const dataBuffer = encoder.encode(data);

		try {
			// Web Crypto APIを使用（Convex Action環境で利用可能）
			const hashBuffer = await crypto.subtle.digest("SHA-256", dataBuffer);
			const hashArray = Array.from(new Uint8Array(hashBuffer));
			const hashHex = hashArray
				.map((b) => b.toString(16).padStart(2, "0"))
				.join("");

			return hashHex;
		} catch (error) {
			// Crypto APIが利用できない場合はエラーとする
			this.logger.error("crypto.subtle not available for hash generation", {
				error: String(error),
			});

			throw createConvexError(
				ERROR_CODES.INTERNAL_ERROR,
				"Crypto API is required for hash generation",
				{ environment: "convex_action", error: String(error) },
			);
		}
	}

	/**
	 * Booking.comのレビューをパース
	 *
	 * Booking.com固有のHTML構造からレビューデータを抽出します。
	 * レビュー要素の検索、個別レビューの抽出、次ページリンクの処理を行います。
	 *
	 * @param $ - CheerioのAPIインスタンス（jQuery風のDOM操作用）
	 * @param url - パース元のURL（次ページURL生成用）
	 * @returns パース結果（レビュー配列、次ページURL、総レビュー数）
	 * @private
	 */
	private parseBookingReviews($: cheerio.CheerioAPI, url: string): ParseResult {
		const reviews: ParsedReview[] = [];

		// レビュー要素を取得
		const reviewElements = $("li.review_item");

		if (reviewElements.length === 0) {
			this.logger.warn("No review elements found", { url });
			return { reviews: [], nextPageUrl: undefined };
		}

		this.logger.info("Found review elements", {
			count: reviewElements.length,
			url,
		});

		// 各レビューを抽出
		reviewElements.each((_: number, element: cheerio.Element) => {
			try {
				const review = this.extractBookingReview($, element);
				if (review) {
					reviews.push(review);
				}
			} catch (error) {
				this.logger.warn("Failed to extract review from element", {
					error: error instanceof Error ? error.message : String(error),
				});
			}
		});

		// 次ページのリンクを検索
		const nextPageLink = $("#review_next_page_link");
		let nextPageUrl: string | undefined;

		if (nextPageLink.length > 0) {
			const href = nextPageLink.attr("href");
			if (href) {
				try {
					const baseUrl = new URL(url);
					const nextUrl = new URL(href, baseUrl.toString());
					// rowsパラメータを25に設定（パフォーマンス最適化）
					nextUrl.searchParams.set("rows", "25");
					nextPageUrl = nextUrl.toString();

					this.logger.info("Found next page link", { nextPageUrl });
				} catch (error) {
					this.logger.warn("Failed to parse next page URL", {
						href,
						error: String(error),
					});
				}
			}
		}

		// 総レビュー数を取得（可能な場合）
		let totalReviews: number | undefined;
		const totalReviewsElement = $(
			".review_list_score_container .review_list_score_count",
		).first();
		const totalReviewsText = totalReviewsElement.text();
		if (totalReviewsText) {
			const match = totalReviewsText.match(/\d+/);
			if (match) {
				totalReviews = parseInt(match[0], 10);
			}
		}

		return {
			reviews,
			nextPageUrl,
			totalReviews,
		};
	}

	/**
	 * Booking.comの個別レビュー要素から情報を抽出
	 *
	 * 単一のレビュー要素から、スコア、タイトル、内容（肯定的/否定的）、
	 * レビュアー情報、日付などの詳細情報を抽出します。
	 *
	 * @param $ - CheerioのAPIインスタンス
	 * @param element - 抽出対象のレビュー要素（li.review_item）
	 * @returns 抽出されたレビューデータ、または抽出に失敗した場合null
	 * @private
	 */
	private extractBookingReview(
		$: cheerio.CheerioAPI,
		element: cheerio.Element,
	): ParsedReview | null {
		// スコアを取得
		const scoreElement = $(element).find("span.review-score-badge");
		const scoreText = scoreElement.text().trim();
		if (!scoreText) {
			this.logger.warn("Score element not found", {
				scoreElementLength: scoreElement.length,
				selector: "span.review-score-badge",
			});
			return null;
		}
		const score = parseFloat(scoreText);
		if (Number.isNaN(score)) {
			this.logger.warn("Invalid score value", { scoreText });
			return null;
		}

		// タイトルを取得
		const titleElement = $(element).find(
			"div.review_item_header_content > span",
		);
		const title = titleElement.text().trim() || undefined;

		// レビュー内容を取得（構造化）
		const negativeElement = $(element).find(
			"p.review_neg > span[itemprop='reviewBody']",
		);
		const positiveElement = $(element).find(
			"p.review_pos > span[itemprop='reviewBody']",
		);

		const negative = negativeElement.text().trim() || undefined;
		const positive = positiveElement.text().trim() || undefined;

		// レビュー内容を結合
		let content = "";
		const contentStructured: ParsedReview["contentStructured"] = {};

		if (positive) {
			content += positive;
			contentStructured.positive = positive;
		}
		if (negative) {
			if (content) content += "\n";
			content += negative;
			contentStructured.negative = negative;
		}

		// レビュアー名を取得
		const reviewerNameElement = $(element).find("p.reviewer_name > span");
		const reviewerName = reviewerNameElement.text().trim() || "Anonymous";

		// レビュアーの国を取得
		const reviewerCountryElement = $(element).find(
			"div.review_item_reviewer > span.reviewer_country span[itemprop='name']",
		);
		const reviewerCountry = reviewerCountryElement.text().trim() || undefined;

		// レビュー日付を取得
		const reviewDateElement = $(element).find("p.review_item_date");
		const reviewDateText = reviewDateElement.text().trim();

		let reviewDate: number;
		if (!reviewDateText) {
			// 日付要素が見つからない場合の詳細ログ
			const elementHTML = $(element).html();
			const truncatedHTML = elementHTML
				? elementHTML.substring(0, 1000)
				: "HTMLが取得できませんでした";

			this.logger.error("レビュー日付要素が見つかりません", {
				selector: "p.review_item_date",
				reviewerName,
				reviewScore: score,
				elementClasses: $(element).attr("class"),
				dateElementExists: reviewDateElement.length > 0,
				dateElementText: reviewDateElement.text(),
				dateElementHTML: reviewDateElement.html(),
				parentHTML: truncatedHTML,
				allParagraphs: $(element)
					.find("p")
					.map((_: number, p: cheerio.Element) => ({
						class: $(p).attr("class"),
						text: $(p).text().substring(0, 50),
					}))
					.get(),
			});

			// 日付が見つからない場合はこのレビューをスキップ
			return null;
		}

		// 日付解析を試みる
		try {
			reviewDate = this.parseDateString(reviewDateText);
			this.logger.info("レビュー日付の解析に成功", {
				originalText: reviewDateText,
				parsedDate: new Date(reviewDate).toISOString(),
				reviewerName,
				reviewScore: score,
			});
		} catch (error) {
			// 日付解析エラーの詳細ログ
			this.logger.error("レビュー日付の解析に失敗", {
				reviewDateText,
				reviewerName,
				reviewScore: score,
				error: error instanceof Error ? error.message : String(error),
				dateElementHTML: reviewDateElement.html(),
				dateElementOuterHTML: reviewDateElement.prop("outerHTML"),
			});

			// 日付解析に失敗した場合もこのレビューをスキップ
			return null;
		}

		// レビューIDを生成（ユニークな識別子）
		// Booking.comはレビューIDを提供しないため、内容から生成
		const reviewId = this.generateReviewId($, element);

		return {
			reviewId,
			score: this.normalizeScore(score, "booking.com"),
			title,
			content,
			contentStructured:
				Object.keys(contentStructured).length > 0
					? contentStructured
					: undefined,
			reviewerName,
			reviewerCountry,
			reviewDate,
		};
	}

	/**
	 * レビューIDを生成
	 */
	private generateReviewId(
		$: cheerio.CheerioAPI,
		element: cheerio.Element,
	): string {
		// data-review-url属性があればそこから抽出
		const reviewUrl = $(element).attr("data-review-url");
		if (reviewUrl) {
			const match = reviewUrl.match(/review_hash=([^&]+)/);
			if (match) {
				return match[1];
			}
		}

		// なければ要素の位置とタイムスタンプから生成
		const parent = $(element).parent();
		const index = parent.children().index(element);
		return `booking_${DateTime.now().toMillis()}_${index}`;
	}

	/**
	 * スコアを0-10スケールに正規化
	 */
	private normalizeScore(score: number, otaType: OTAType): number {
		switch (otaType) {
			case "booking.com":
				// Booking.comは既に0-10スケール
				return Math.min(Math.max(score, 0), 10);
			case "expedia":
			case "hotels.com":
				// これらは通常1-5スケールなので2倍する
				return Math.min(Math.max(score * 2, 0), 10);
			case "agoda":
				// Agodaは0-10スケール
				return Math.min(Math.max(score, 0), 10);
			default:
				return score;
		}
	}

	/**
	 * 日本語形式の日付文字列をパース
	 *
	 * 複数の日付形式に対応したパーサー。日本語形式（YYYY年MM月DD日）を優先し、
	 * ISO形式、英語形式など様々な形式を試行します。
	 *
	 * @param dateString - パース対象の日付文字列
	 * @returns 日付のミリ秒表現（Unixタイムスタンプ）
	 * @throws {ConvexError} どの形式でもパースできなかった場合（VALIDATION_ERROR）
	 * @example
	 * parseDateString("2024年11月15日") // => 1731628800000
	 * parseDateString("November 15, 2024") // => 1731628800000
	 * parseDateString("2024-11-15") // => 1731628800000
	 * @private
	 */
	private parseDateString(dateString: string): number {
		this.logger.info("日付文字列の解析を開始", {
			dateString,
			length: dateString.length,
			trimmed: dateString.trim(),
		});

		// "2024年11月15日" 形式をパース
		const dateMatch = dateString.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/);
		if (dateMatch) {
			const year = parseInt(dateMatch[1], 10);
			const month = parseInt(dateMatch[2], 10) - 1; // JavaScriptの月は0ベース
			const day = parseInt(dateMatch[3], 10);

			// 日本時間として扱う
			const date = DateTime.local(year, month + 1, day, 0, 0, 0, 0, {
				zone: "Asia/Tokyo",
			});

			this.logger.info("日本語形式の日付解析に成功", {
				dateString,
				year,
				month: month + 1,
				day,
				result: date.toISO(),
			});

			return date.toMillis();
		}

		// 英語形式も試す "November 15, 2024"
		try {
			// ISO形式を試す
			const dateISO = DateTime.fromISO(dateString);
			if (dateISO.isValid) {
				this.logger.info("ISO形式の日付解析に成功", {
					dateString,
					result: dateISO.toISO(),
				});
				return dateISO.toMillis();
			}

			// 一般的な英語形式を試す
			const dateFromFormat = DateTime.fromFormat(dateString, "LLLL d, yyyy", {
				locale: "en",
			});
			if (dateFromFormat.isValid) {
				this.logger.info("英語形式の日付解析に成功", {
					dateString,
					format: "LLLL d, yyyy",
					result: dateFromFormat.toISO(),
				});
				return dateFromFormat.toMillis();
			}

			// その他の形式を試す
			const alternativeFormats = [
				"MMM d, yyyy", // "Nov 15, 2024"
				"d LLLL yyyy", // "15 November 2024"
				"d MMM yyyy", // "15 Nov 2024"
				"yyyy-MM-dd", // "2024-11-15"
				"dd/MM/yyyy", // "15/11/2024"
				"MM/dd/yyyy", // "11/15/2024"
			];

			for (const format of alternativeFormats) {
				const dateAlt = DateTime.fromFormat(dateString, format, {
					locale: "en",
				});
				if (dateAlt.isValid) {
					this.logger.info("代替形式での日付解析に成功", {
						dateString,
						format,
						result: dateAlt.toISO(),
					});
					return dateAlt.toMillis();
				}
			}
		} catch (error) {
			this.logger.error("日付解析中にエラーが発生", {
				dateString,
				error: error instanceof Error ? error.message : String(error),
			});
		}

		// すべての形式で解析に失敗
		this.logger.error("日付解析に失敗しました", {
			dateString,
			triedPatterns: [
				"YYYY年MM月DD日",
				"ISO format",
				"LLLL d, yyyy",
				"MMM d, yyyy",
				"d LLLL yyyy",
				"d MMM yyyy",
				"yyyy-MM-dd",
				"dd/MM/yyyy",
				"MM/dd/yyyy",
			],
			originalInput: dateString,
			trimmedInput: dateString.trim(),
		});

		throw createConvexError(
			ERROR_CODES.VALIDATION_ERROR,
			`Failed to parse date string: ${dateString}`,
			{ dateString },
		);
	}
}

/**
 * レビューパーサーのシングルトンインスタンスを作成
 *
 * ReviewParserインターフェースを実装したパーサーインスタンスを生成します。
 *
 * @param logger - ロギング用のConvexLoggerインスタンス（オプション）
 * @returns ReviewParserインターフェースを実装したインスタンス
 * @example
 * // カスタムロガー付きで作成
 * const parser = createReviewParser(new ConvexLogger("MyParser"));
 *
 * // デフォルトロガーで作成
 * const parser = createReviewParser();
 */
export function createReviewParser(logger?: ConvexLogger): ReviewParser {
	return new ReviewParserImpl(logger);
}

/**
 * デフォルトのレビューパーサー
 */
export const defaultReviewParser = new ReviewParserImpl();
