import { ConvexError as ConvexErrorBase, v } from "convex/values";

// Re-export ConvexError for easier imports
export const ConvexError = ConvexErrorBase;

/**
 * アプリケーション全体で使用するエラーコード
 */
export const ERROR_CODES = {
	// 認証関連
	UNAUTHORIZED: "UNAUTHORIZED",
	INVALID_TOKEN: "INVALID_TOKEN",
	TOKEN_EXPIRED: "TOKEN_EXPIRED",

	// データ関連
	NOT_FOUND: "NOT_FOUND",
	ALREADY_EXISTS: "ALREADY_EXISTS",
	VALIDATION_ERROR: "VALIDATION_ERROR",
	INVALID_REQUEST: "INVALID_REQUEST",

	// 外部API関連
	API_ERROR: "API_ERROR",
	NETWORK_ERROR: "NETWORK_ERROR",
	RATE_LIMIT_EXCEEDED: "RATE_LIMIT_EXCEEDED",

	// システム関連
	INTERNAL_ERROR: "INTERNAL_ERROR",
	SERVICE_UNAVAILABLE: "SERVICE_UNAVAILABLE",
	NOT_IMPLEMENTED: "NOT_IMPLEMENTED",

	// レビュー同期関連
	REVIEW_SYNC_FAILED: "REVIEW_SYNC_FAILED",
	REVIEW_BATCH_FAILED: "REVIEW_BATCH_FAILED",
	INVALID_OTA_TYPE: "INVALID_OTA_TYPE",
	SCRAPING_FAILED: "SCRAPING_FAILED",
} as const;

export type ErrorCode = (typeof ERROR_CODES)[keyof typeof ERROR_CODES];

/**
 * エラーの詳細情報を含む構造化されたエラーデータ
 */
export type ErrorData = {
	code: ErrorCode;
	message: string;
	details?: Record<string, any>;
	timestamp: number;
	context?: {
		userId?: string;
		functionName?: string;
		args?: Record<string, any>;
		[key: string]: any;
	};
};

/**
 * Convexエラーを作成するヘルパー関数
 */
export function createConvexError(
	code: ErrorCode,
	message: string,
	details?: Record<string, any>,
	context?: ErrorData["context"],
) {
	const errorData: ErrorData = {
		code,
		message,
		details,
		timestamp: Date.now(),
		context,
	};

	return new ConvexErrorBase(errorData);
}

/**
 * エラーデータのバリデーター（クライアント側での型安全性のため）
 */
export const errorDataValidator = v.object({
	code: v.string(),
	message: v.string(),
	details: v.optional(v.record(v.string(), v.any())),
	timestamp: v.number(),
	context: v.optional(
		v.object({
			userId: v.optional(v.string()),
			functionName: v.optional(v.string()),
			args: v.optional(v.record(v.string(), v.any())),
		}),
	),
});
