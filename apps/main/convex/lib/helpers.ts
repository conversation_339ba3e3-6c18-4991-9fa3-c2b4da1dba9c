/**
 * Convex共通ヘルパー関数
 *
 * このファイルは、Convex関数（query, mutation, action）で使用される
 * 共通のヘルパー関数を提供します。
 */

import type { MutationCtx } from "../_generated/server";
import { requireAuthenticatedUser } from "./auth";
import type { ConvexLogger } from "./logging";

/**
 * 認証が必要なMutation用のラッパー関数
 *
 * 認証チェック、ロギング、エラーハンドリングを統一的に処理します。
 *
 * @param ctx - MutationContext
 * @param functionName - 関数名（ログ出力用）
 * @param args - Mutationの引数
 * @param handler - 実際の処理を行う関数
 * @returns handlerの返り値
 * @throws 認証エラーまたはhandler内で発生したエラー
 *
 * @example
 * ```typescript
 * export const myMutation = mutation({
 *   args: { foo: v.string() },
 *   returns: v.id("table"),
 *   handler: async (ctx, args) =>
 *     withAuthenticatedMutation(
 *       ctx,
 *       "myMutation",
 *       args,
 *       async (ctx, userId, logger) => {
 *         logger.info("処理を開始");
 *         // ビジネスロジック
 *         return await ctx.db.insert("table", { userId, foo: args.foo });
 *       }
 *     ),
 * });
 * ```
 */
export async function withAuthenticatedMutation<
	TArgs extends Record<string, any>,
	TReturn,
>(
	ctx: MutationCtx,
	functionName: string,
	args: TArgs,
	handler: (
		ctx: MutationCtx,
		userId: string,
		logger: ConvexLogger,
	) => Promise<TReturn>,
): Promise<TReturn> {
	// 認証チェックとロガー初期化
	const { userId, logger } = await requireAuthenticatedUser(ctx, functionName);

	// 引数をログコンテキストに追加
	logger.setArgs(args);
	logger.info(`${functionName}を開始`);

	try {
		// ハンドラーを実行
		const result = await handler(ctx, userId, logger);
		logger.debug(`${functionName}が正常に完了`);
		return result;
	} catch (error) {
		// エラーログを出力してから再スロー
		logger.error(`${functionName}でエラーが発生`, error);
		throw error;
	}
}
