/**
 * レビューURLビルダー
 *
 * OTAサイト（現在はBooking.com）のレビューページURLを生成・更新するユーティリティ。
 * 既存URLのパラメータ更新と新規URL作成の両方をサポートします。
 *
 * @module reviewUrlBuilder
 * @example
 * // 新規URL作成
 * const url = createBookingReviewUrl({
 *   property: { id: "123", name: "東京ホテル", booking_com_facility_name: "tokyo-hotel" }
 * });
 *
 * // 既存URL更新
 * const updatedUrl = createBookingReviewUrl({
 *   property: { id: "123", name: "東京ホテル" },
 *   existingUrl: "https://www.booking.com/reviews/..."
 * });
 */

import { v } from "convex/values";
import { createConvexError, ERROR_CODES } from "./errors";

// 定数定義
const BOOKING_BASE_URL = "https://www.booking.com/reviews/jp/hotel";
const DEFAULT_ROWS_PER_PAGE = "25";
const DEFAULT_REVIEW_PARAMS = {
	r_lang: "all",
	customer_type: "total",
	order: "completed_desc",
	rows: DEFAULT_ROWS_PER_PAGE,
};

// バリデーター定義
export const reviewUrlBuilderValidator = {
	property: v.object({
		id: v.string(),
		name: v.string(),
		booking_com_facility_name: v.optional(v.string()),
	}),
	existingUrl: v.optional(v.string()),
};

export interface ReviewUrlBuilderInput {
	property: {
		id: string;
		name: string;
		booking_com_facility_name?: string;
	};
	existingUrl?: string;
}

/**
 * Booking.comのレビューページURLを作成または更新する
 * @param input - プロパティ情報と既存URL（オプション）
 * @returns 処理済みのレビューページURL
 * @throws {ConvexError} booking_com_facility_nameが存在しない場合
 */
export function createBookingReviewUrl(input: ReviewUrlBuilderInput): string {
	const { property, existingUrl } = input;

	if (existingUrl) {
		return updateExistingUrl(existingUrl, property);
	}

	return createNewUrl(property);
}

/**
 * 既存URLのパラメータを更新する
 *
 * 既存のレビューページURLのパラメータを標準化し、ページング情報を更新します。
 * - rowsパラメータを標準値（25）に統一
 * - orderパラメータを完了日降順（completed_desc）に固定
 * - ページ番号が存在する場合はold_pageパラメータを設定
 *
 * @param existingUrl - 既存のレビューページURL
 * @param _property - プロパティ情報（現在はログ出力用、将来の拡張用）
 * @returns 標準化されたパラメータを持つ更新後のURL
 * @example
 * // ページ2のURLを更新
 * const url = updateExistingUrl(
 *   "https://www.booking.com/reviews/jp/hotel/tokyo.ja.html?page=2&rows=10",
 *   { id: "123", name: "東京ホテル" }
 * );
 * // => "https://www.booking.com/reviews/jp/hotel/tokyo.ja.html?page=2&rows=25&order=completed_desc&old_page=1"
 */
function updateExistingUrl(
	existingUrl: string,
	_property: { id: string; name: string },
): string {
	const urlObj = new URL(existingUrl);

	// pageパラメータが存在する場合、old_pageを設定
	const pageParam = urlObj.searchParams.get("page");
	if (pageParam) {
		const oldPage = Number.parseInt(pageParam, 10) - 1;
		urlObj.searchParams.set("old_page", oldPage.toString());
	}

	// rowsパラメータを標準値に統一
	urlObj.searchParams.set("rows", DEFAULT_ROWS_PER_PAGE);

	// orderパラメータを強制的にcompleted_descに設定
	urlObj.searchParams.set("order", "completed_desc");

	const processedUrl = urlObj.toString();

	// パラメータの変更をログに記録
	if (processedUrl !== existingUrl) {
		logUrlModification(existingUrl, processedUrl, pageParam);
	}

	return processedUrl;
}

/**
 * 新規URLを作成する
 *
 * Booking.comのレビューページURLを新規作成します。
 * デフォルトパラメータ（言語: all、顧客タイプ: total、並び順: 完了日降順）を設定します。
 *
 * @param property - プロパティ情報
 * @param property.id - プロパティID（エラー時のログ用）
 * @param property.name - プロパティ名（エラー時のログ用）
 * @param property.booking_com_facility_name - Booking.comの施設スラッグ（必須）
 * @returns 標準パラメータを含む新規レビューページURL
 * @throws {ConvexError} booking_com_facility_nameが存在しない場合（VALIDATION_ERROR）
 * @example
 * const url = createNewUrl({
 *   id: "123",
 *   name: "東京ホテル",
 *   booking_com_facility_name: "tokyo-hotel"
 * });
 * // => "https://www.booking.com/reviews/jp/hotel/tokyo-hotel.ja.html?r_lang=all&customer_type=total&order=completed_desc&rows=25"
 */
function createNewUrl(property: {
	id: string;
	name: string;
	booking_com_facility_name?: string;
}): string {
	// booking_com_facility_nameの検証
	if (!property.booking_com_facility_name) {
		console.warn("booking_com_facility_name is missing", {
			property_id: property.id,
			name: property.name,
		});
		throw createConvexError(
			ERROR_CODES.VALIDATION_ERROR,
			"Booking.com facility name is not set for this property",
			{ property_id: property.id },
		);
	}

	// URLパラメータを構築
	const params = new URLSearchParams(DEFAULT_REVIEW_PARAMS);

	// 新規URLを構築
	return `${BOOKING_BASE_URL}/${encodeURIComponent(property.booking_com_facility_name)}.ja.html?${params.toString()}`;
}

/**
 * URL変更をログに記録する
 *
 * URLパラメータの変更内容を詳細にログ出力します。
 * デバッグやトラブルシューティングに使用されます。
 *
 * @param originalUrl - 変更前の元のURL
 * @param modifiedUrl - パラメータ変更後のURL
 * @param pageParam - pageパラメータの値（ページング使用時のみ存在）
 * @example
 * logUrlModification(
 *   "https://www.booking.com/reviews/jp/hotel/tokyo.ja.html?page=2",
 *   "https://www.booking.com/reviews/jp/hotel/tokyo.ja.html?page=2&rows=25&order=completed_desc&old_page=1",
 *   "2"
 * );
 * // ログ出力: 変更内容（rows: 25, order: completed_desc, page: 2, old_page: 1）
 */
function logUrlModification(
	originalUrl: string,
	modifiedUrl: string,
	pageParam: string | null,
): void {
	console.info("Modified URL parameters", {
		original_url: originalUrl,
		modified_url: modifiedUrl,
		modifications: {
			rows: DEFAULT_ROWS_PER_PAGE,
			order: "completed_desc",
			...(pageParam && {
				page: pageParam,
				old_page: (Number.parseInt(pageParam, 10) - 1).toString(),
			}),
		},
	});
}

// 将来の拡張用インターフェース（Expedia等）
export interface OtaReviewUrlBuilder {
	createReviewUrl(input: ReviewUrlBuilderInput): string;
}

// Booking.com実装（将来的にクラス化可能）
export const bookingComUrlBuilder: OtaReviewUrlBuilder = {
	createReviewUrl: createBookingReviewUrl,
};
