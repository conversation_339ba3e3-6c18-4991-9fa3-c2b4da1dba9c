"use node";

/**
 * ScrapingAntクライアントラッパー
 *
 * Convex Action内でScrapingAnt APIを使用するためのラッパークラス。
 * エラーハンドリングの強化、ロギング、型安全性を提供します。
 *
 * @module scrapingAnt
 * @see {@link https://scrapingant.com/} ScrapingAnt公式サイト
 * @example
 * // 基本的な使用例
 * const scrapingAnt = createScrapingAntClient(logger);
 * const response = await scrapingAnt.scrapeUrl("https://www.booking.com/hotel/jp/tokyo.html");
 * console.log(response.content); // HTMLコンテンツ
 */

import { DateTime } from "luxon";
import type {
	ScrapingAntOptions,
	ScrapingAntResponse,
	ScrapingAntScrapeParameters,
} from "../types/reviews";
import { createConvexError, ERROR_CODES } from "./errors";
import { ConvexLogger } from "./logging";

// @ts-ignore - JavaScript ライブラリのため型定義なし
let ScrapingAntClientLib: any;
try {
	ScrapingAntClientLib = require("@scrapingant/scrapingant-client");
} catch {
	// テスト環境でモックを使用できるようにする
	ScrapingAntClientLib = null;
}

/**
 * ScrapingAntで許可されているドメイン
 * 現在はbooking.comのみをサポート
 */
const ALLOWED_DOMAIN = "booking.com";

/**
 * ScrapingAntラッパークラス
 *
 * Convex環境でScrapingAnt APIを安全に使用するためのラッパー。
 * 自動リトライ、エラーハンドリング、ロギング機能を提供。
 *
 * @class ScrapingAntWrapper
 * @property {any} client - ScrapingAntClientのインスタンス
 * @property {ConvexLogger} logger - ロギング用のインスタンス
 */
export class ScrapingAntWrapper {
	private client: any; // ScrapingAntClientの実体
	private logger: ConvexLogger;

	/**
	 * ScrapingAntWrapperのコンストラクタ
	 *
	 * 環境変数からAPIキーを取得し、ScrapingAntクライアントを初期化します。
	 * APIキーが設定されていない場合はエラーをスローします。
	 *
	 * @param logger - ロギング用のConvexLoggerインスタンス（オプション）
	 * @param testClient - テスト用のモッククライアント（オプション）
	 * @throws {ConvexError} SCRAPING_ANT_API_KEYが環境変数に設定されていない場合
	 * @throws {ConvexError} ScrapingAntクライアントの初期化に失敗した場合
	 */
	constructor(logger?: ConvexLogger, testClient?: any) {
		// Convex環境変数からAPIキーを取得
		const apiKey = process.env.SCRAPING_ANT_API_KEY;
		if (!apiKey) {
			throw createConvexError(
				ERROR_CODES.INTERNAL_ERROR,
				"SCRAPING_ANT_API_KEY is not set in Convex environment variables",
				{
					suggestion:
						'Run: npx convex env set SCRAPING_ANT_API_KEY "your-api-key"',
				},
			);
		}

		// ロガーの初期化
		this.logger = logger || new ConvexLogger("ScrapingAntWrapper");

		// テスト用クライアントが提供されている場合はそれを使用
		if (testClient) {
			this.client = testClient;
			this.logger.info("Using test client");
			return;
		}

		// ScrapingAntクライアントの初期化
		const options: ScrapingAntOptions = {
			apiKey,
			maxRetries: 5, // デフォルトの8回から減らして制御しやすくする
			minDelayBetweenRetriesMillis: 1000, // 1秒のリトライ間隔
			timeoutSecs: 120, // 120秒のタイムアウト（Booking.comの重いページに対応）
		};

		try {
			if (!ScrapingAntClientLib) {
				throw new Error("ScrapingAnt client library not found");
			}

			this.logger.info("ScrapingAntクライアント初期化開始", {
				hasLibrary: !!ScrapingAntClientLib,
				libraryType: ScrapingAntClientLib?.name || "unknown",
				apiKeyLength: apiKey.length,
				options,
			});

			this.client = new ScrapingAntClientLib(options);

			this.logger.info("ScrapingAnt client initialized successfully", {
				maxRetries: options.maxRetries,
				timeoutSecs: options.timeoutSecs,
				clientType: this.client?.constructor?.name,
				hasScrapMethod: typeof this.client?.scrape === "function",
			});
		} catch (error: any) {
			this.logger.error("Failed to initialize ScrapingAnt client", {
				error: String(error),
				errorType: error?.constructor?.name,
				errorMessage: error?.message,
				errorStack: error?.stack,
			});
			throw createConvexError(
				ERROR_CODES.INTERNAL_ERROR,
				"Failed to initialize ScrapingAnt client",
				{ error: String(error) },
			);
		}
	}

	/**
	 * URLをスクレイピング
	 *
	 * 指定されたURLのコンテンツを取得します。
	 * URLの検証、ドメインチェック、エラーハンドリングを含む包括的なスクレイピング処理を実行します。
	 *
	 * @param url - スクレイピング対象のURL（booking.comドメインのみ許可）
	 * @param options - スクレイピングオプション（ブラウザ使用、待機セレクタなど）
	 * @returns スクレイピング結果（HTMLコンテンツ、ステータスコードを含む）
	 * @throws {ConvexError} URLが無効な場合（VALIDATION_ERROR）
	 * @throws {ConvexError} ドメインが許可されていない場合（VALIDATION_ERROR）
	 * @throws {ConvexError} レート制限に達した場合（RATE_LIMIT_EXCEEDED）
	 * @throws {ConvexError} ネットワークエラーが発生した場合（NETWORK_ERROR）
	 * @throws {ConvexError} ScrapingAntサービスが利用できない場合（SERVICE_UNAVAILABLE）
	 * @example
	 * const response = await scrapeUrl("https://www.booking.com/hotel/jp/tokyo.html", {
	 *   browser: true,
	 *   wait_for_selector: ".hotel-name"
	 * });
	 * console.log(response.statusCode); // 200
	 * console.log(response.content); // HTMLコンテンツ
	 */
	async scrapeUrl(
		url: string,
		options?: ScrapingAntScrapeParameters,
	): Promise<ScrapingAntResponse> {
		// URLの検証
		if (!url || !this.isValidUrl(url)) {
			throw createConvexError(
				ERROR_CODES.VALIDATION_ERROR,
				"Invalid URL provided",
				{ url },
			);
		}

		// セキュリティチェック：信頼できるドメインのみ許可
		if (!this.isAllowedDomain(url)) {
			throw createConvexError(
				ERROR_CODES.VALIDATION_ERROR,
				"URL domain is not allowed for scraping",
				{ url, allowedDomains: this.getAllowedDomains() },
			);
		}

		this.logger.info("Starting scraping", {
			url,
			options: this.sanitizeOptions(options),
			configuredTimeout: this.client?.options?.timeoutSecs || 120,
			timestamp: DateTime.now().toISO(),
		});

		const startTime = DateTime.now().toMillis();

		try {
			// ScrapingAnt APIを呼び出し
			this.logger.info("ScrapingAnt API呼び出し開始", {
				url,
				hasClient: !!this.client,
				clientType: this.client?.constructor?.name,
				timestamp: DateTime.now().toISO(),
			});

			const response = await this.client.scrape(url, options);

			const duration = DateTime.now().toMillis() - startTime;
			this.logger.info("Scraping completed successfully", {
				url,
				duration,
				contentLength: response.content?.length || 0,
				statusCode: response.statusCode,
			});

			// レスポンスの検証
			if (!response.content) {
				throw createConvexError(
					ERROR_CODES.API_ERROR,
					"Empty response content from ScrapingAnt",
					{ url },
				);
			}

			return response;
		} catch (error: any) {
			const duration = DateTime.now().toMillis() - startTime;

			// ScrapingAntApiErrorの処理
			if (error.statusCode !== undefined) {
				this.logger.error("ScrapingAnt API error", {
					url,
					duration,
					statusCode: error.statusCode,
					message: error.message,
					httpMethod: error.httpMethod,
				});

				// ステータスコードに基づいてエラーを分類
				if (error.statusCode === 429) {
					throw createConvexError(
						ERROR_CODES.RATE_LIMIT_EXCEEDED,
						"ScrapingAnt rate limit exceeded",
						{
							url,
							statusCode: error.statusCode,
							message: error.message,
						},
					);
				}

				if (error.statusCode >= 400 && error.statusCode < 500) {
					throw createConvexError(
						ERROR_CODES.INVALID_REQUEST,
						`ScrapingAnt request failed: ${error.message}`,
						{
							url,
							statusCode: error.statusCode,
							originalError: error.message,
						},
					);
				}

				// 5xx エラーはサービス不可として扱う
				if (error.statusCode >= 500) {
					throw createConvexError(
						ERROR_CODES.SERVICE_UNAVAILABLE,
						"ScrapingAnt service is temporarily unavailable",
						{
							url,
							statusCode: error.statusCode,
							message: error.message,
						},
					);
				}
			}

			// ネットワークエラーやその他のエラー
			this.logger.error("Unexpected scraping error", {
				url,
				duration,
				error: String(error),
				errorType: error.constructor.name,
				errorMessage: error.message,
				errorCode: error.code,
				hasResponse: !!error.response,
				responseStatus: error.response?.statusCode,
			});

			// タイムアウトエラーの詳細ログ
			if (error.message?.includes("timeout")) {
				this.logger.error("タイムアウト詳細", {
					url,
					configuredTimeout: 120000, // 120秒
					actualDuration: duration,
					errorMessage: error.message,
					errorStack: error.stack,
					isTimeoutExceeded: duration > 120000,
				});
			}

			// 接続エラーの詳細ログ
			if (
				error.message &&
				(error.message.includes("Connection") ||
					error.message.includes("ECONNREFUSED"))
			) {
				this.logger.error("接続エラー詳細", {
					url,
					errorMessage: error.message,
					errorCode: error.code,
					errorType: error.constructor.name,
					syscall: error.syscall,
					address: error.address,
					port: error.port,
					timestamp: DateTime.now().toISO(),
				});
			}

			throw createConvexError(
				ERROR_CODES.NETWORK_ERROR,
				"Failed to scrape URL",
				{
					url,
					error: String(error),
					duration,
					errorType: error.constructor.name,
				},
			);
		}
	}

	/**
	 * 複数のURLを並行してスクレイピング（バッチ処理）
	 *
	 * 複数のURLを効率的にスクレイピングするためのバッチ処理メソッド。
	 * 同時実行数を制御しながら、全URLのスクレイピングを実行します。
	 * 個々のURLのエラーは結果に含まれ、他のURLの処理には影響しません。
	 *
	 * @param urls - スクレイピング対象のURLリスト
	 * @param options - 全URLに適用される共通のスクレイピングオプション
	 * @param concurrency - 同時実行数（デフォルト: 3、推奨: 1-5）
	 * @returns スクレイピング結果の配列（各URLの成功/失敗情報を含む）
	 * @example
	 * const urls = [
	 *   "https://www.booking.com/hotel/jp/tokyo.html",
	 *   "https://www.booking.com/hotel/jp/osaka.html"
	 * ];
	 * const results = await scrapeUrlsBatch(urls, { browser: true }, 2);
	 *
	 * results.forEach(({ url, result, error }) => {
	 *   if (result) {
	 *     console.log(`${url}: 成功 - ${result.content.length}文字`);
	 *   } else {
	 *     console.error(`${url}: 失敗 - ${error.message}`);
	 *   }
	 * });
	 */
	async scrapeUrlsBatch(
		urls: string[],
		options?: ScrapingAntScrapeParameters,
		concurrency = 3,
	): Promise<
		Array<{ url: string; result?: ScrapingAntResponse; error?: Error }>
	> {
		this.logger.info("Starting batch scraping", {
			urlCount: urls.length,
			concurrency,
		});

		const results: Array<{
			url: string;
			result?: ScrapingAntResponse;
			error?: Error;
		}> = [];

		// URLをチャンクに分割
		const chunks: string[][] = [];
		for (let i = 0; i < urls.length; i += concurrency) {
			chunks.push(urls.slice(i, i + concurrency));
		}

		// チャンクごとに並行処理
		for (const chunk of chunks) {
			const chunkResults = await Promise.allSettled(
				chunk.map((url) => this.scrapeUrl(url, options)),
			);

			chunkResults.forEach((result, index) => {
				const url = chunk[index];
				if (result.status === "fulfilled") {
					results.push({ url, result: result.value });
				} else {
					results.push({ url, error: result.reason });
				}
			});
		}

		const successCount = results.filter((r) => r.result).length;
		const errorCount = results.filter((r) => r.error).length;

		this.logger.info("Batch scraping completed", {
			totalUrls: urls.length,
			successCount,
			errorCount,
		});

		return results;
	}

	/**
	 * URLの検証
	 *
	 * URLが正しい形式（http/httpsプロトコル）であることを確認します。
	 *
	 * @param url - 検証対象のURL
	 * @returns URLが有効な場合true
	 * @private
	 */
	private isValidUrl(url: string): boolean {
		try {
			const parsedUrl = new URL(url);
			return ["http:", "https:"].includes(parsedUrl.protocol);
		} catch {
			return false;
		}
	}

	/**
	 * 許可されたドメインかチェック
	 *
	 * URLが許可されたドメイン（現在はbooking.comのみ）に属するか確認します。
	 *
	 * @param url - チェック対象のURL
	 * @returns 許可されたドメインの場合true
	 * @private
	 */
	private isAllowedDomain(url: string): boolean {
		try {
			const parsedUrl = new URL(url);
			return parsedUrl.hostname.endsWith(ALLOWED_DOMAIN);
		} catch {
			return false;
		}
	}

	/**
	 * 許可されたドメインのリスト
	 *
	 * スクレイピングが許可されているドメインのリストを返します。
	 *
	 * @returns 許可されたドメインの配列
	 * @private
	 */
	private getAllowedDomains(): string[] {
		return [ALLOWED_DOMAIN];
	}

	/**
	 * オプションのサニタイズ（ログ用）
	 *
	 * ログ出力用にスクレイピングオプションからセンシティブな情報をマスクします。
	 * cookies、認証ヘッダーなどの機密情報は[REDACTED]に置き換えられます。
	 *
	 * @param options - サニタイズ対象のオプション
	 * @returns センシティブな情報がマスクされたオプション
	 * @private
	 */
	private sanitizeOptions(
		options?: ScrapingAntScrapeParameters,
	): Record<string, any> | undefined {
		if (!options) return undefined;

		const sanitized = { ...options };
		// センシティブな情報をマスク
		if (sanitized.cookies) {
			sanitized.cookies = "[REDACTED]";
		}
		if (sanitized.headers) {
			sanitized.headers = Object.fromEntries(
				Object.entries(sanitized.headers).map(([key, value]) => [
					key,
					key.toLowerCase().includes("auth") ||
					key.toLowerCase().includes("token")
						? "[REDACTED]"
						: value,
				]),
			);
		}
		return sanitized;
	}
}

/**
 * ScrapingAntラッパーのシングルトンインスタンスを作成
 *
 * @param logger ロガーインスタンス（オプション）
 * @param testClient テスト用クライアント（オプション）
 * @returns ScrapingAntラッパーのインスタンス
 */
export function createScrapingAntClient(
	logger?: ConvexLogger,
	testClient?: any,
): ScrapingAntWrapper {
	return new ScrapingAntWrapper(logger, testClient);
}
