/**
 * Convex認証設定
 *
 * Clerk認証プロバイダーを使用してConvexアプリケーションの認証を設定します。
 * この設定はConvexのauthConfig機能と連携して、セキュアな認証フローを提供します。
 *
 * @example
 * // convex/auth.ts内で使用
 * import authConfig from "./auth.config";
 *
 * export const { auth } = convexAuth(authConfig);
 */
const authConfig = {
	/**
	 * 認証プロバイダーの設定配列
	 * 現在はClerkプロバイダーのみをサポート
	 */
	providers: [
		{
			/**
			 * ClerkのフロントエンドAPIドメイン
			 * カスタムドメインやプロキシ設定を使用する場合に指定
			 * @see https://clerk.com/docs/deployments/proxy
			 */
			domain: process.env.NEXT_PUBLIC_CLERK_FRONTEND_API_URL,
			/**
			 * ConvexアプリケーションID
			 * Convex内部で認証プロバイダーを識別するための固定値
			 */
			applicationID: "convex",
		},
	],
};

export default authConfig;
