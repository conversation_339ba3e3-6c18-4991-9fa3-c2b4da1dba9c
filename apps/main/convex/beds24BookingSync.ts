"use node";

import { v } from "convex/values";
import { internal } from "./_generated/api";
import { internalAction } from "./_generated/server";
// Beds24ApiWrapperはbeds24ApiActionsに移動しました
import { createBookingParser } from "./lib/bookingParser";
import {
	calculatePreviousDateRange,
	formatDateForBeds24,
} from "./lib/dateUtils";
import { createConvexError, ERROR_CODES } from "./lib/errors";
import { createLogger } from "./lib/logging";
import type { BookingSyncMetadata } from "./types/beds24";

/**
 * Beds24から予約データを取得して同期する内部アクション
 *
 * レビュー同期（otaReviewsSync.ts）と同じパターンで実装
 *
 * @param taskId - syncQueueテーブルのタスクID
 * @returns なし（void）
 */
export const syncBookingsFromBeds24 = internalAction({
	args: {
		taskId: v.id("syncQueue"),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		const logger = createLogger("syncBookingsFromBeds24", ctx);
		logger.info("予約同期開始", {
			taskId: args.taskId,
			timestamp: new Date().toISOString(),
		});

		// エラーハンドリング用に変数を外部スコープで宣言
		let taskData: any = null;
		let metadata: BookingSyncMetadata | null = null;
		let dateFrom: string | undefined;
		let dateTo: string | undefined;

		try {
			// タスク詳細の取得
			logger.info("[DEBUG] getBookingSyncTask呼び出し前", {
				taskId: args.taskId,
			});

			taskData = await ctx.runQuery(
				internal.beds24BookingQueries.getBookingSyncTask,
				{
					taskId: args.taskId,
				},
			);

			logger.info("[DEBUG] getBookingSyncTask呼び出し後", {
				taskId: args.taskId,
				taskDataExists: !!taskData,
				taskDataIsNull: taskData === null,
			});

			if (!taskData) {
				throw createConvexError(ERROR_CODES.NOT_FOUND, "Task not found", {
					taskId: args.taskId,
					timestamp: new Date().toISOString(),
				});
			}

			// jobTypeの確認
			if (taskData.jobType !== "sync_bookings") {
				throw createConvexError(
					ERROR_CODES.INVALID_REQUEST,
					"Invalid job type",
					{
						taskId: args.taskId,
						jobType: taskData.jobType,
						expected: "sync_bookings",
					},
				);
			}

			metadata = taskData.metadata as BookingSyncMetadata;
			const { userId, propertyId, propertyName, pageNumber } = metadata;

			// 初回同期ロジック：日付範囲がない場合は現在日時以降を設定
			dateFrom = metadata.dateFrom;
			dateTo = metadata.dateTo;
			if (!dateFrom) {
				dateFrom = formatDateForBeds24(new Date());
				logger.info("初回同期：現在日時以降の予約を取得します", { dateFrom });
			}

			// 同期進捗ログの開始
			logger.syncProgress("start", {
				jobType: "sync_bookings",
				dateRange: { from: dateFrom || "", to: dateTo || "" },
				recursionInfo: {
					isBacktracking: metadata.isRecursiveBacktrack || false,
					depth: metadata.recursionDepth || 0,
					originalStartDate: metadata.originalStartDate,
				},
				propertyId,
				propertyName,
				pageNumber,
			});

			// ユーザーのアクセストークンを取得
			const accessToken = await ctx.runQuery(
				internal.beds24Tokens.getAccessToken,
				{ userId },
			);

			if (!accessToken) {
				throw createConvexError(
					ERROR_CODES.UNAUTHORIZED,
					"Access token not found",
					{
						userId,
						taskId: args.taskId,
					},
				);
			}

			// トークンの有効期限を確認し、必要に応じてリフレッシュ
			const now = Date.now();
			if (accessToken.expiresAt <= now) {
				logger.info(
					"アクセストークンの有効期限が切れています。リフレッシュを開始します。",
					{
						userId,
						expiresAt: new Date(accessToken.expiresAt).toISOString(),
					},
				);

				// トークンのリフレッシュ
				await ctx.runAction(internal.beds24Tokens.refreshAccessToken, {
					userId,
				});

				// 新しいトークンを再取得
				const newAccessToken = await ctx.runQuery(
					internal.beds24Tokens.getAccessToken,
					{ userId },
				);

				if (!newAccessToken) {
					throw createConvexError(
						ERROR_CODES.INTERNAL_ERROR,
						"Failed to get refreshed token",
						{ userId },
					);
				}

				accessToken.accessToken = newAccessToken.accessToken;
			}

			// 同期履歴の記録開始
			const syncHistoryId = await ctx.runMutation(
				internal.beds24SyncHistory.recordSyncStart,
				{
					userId,
					jobType: "sync_bookings",
					metadata: {
						propertyId,
						propertyName,
						pageNumber,
						dateFrom,
						dateTo,
					},
				},
			);

			logger.info("同期履歴を作成しました", { syncHistoryId });

			// 予約データの取得
			logger.info("Beds24 APIから予約データを取得開始", {
				propertyId,
				pageNumber,
				dateFrom,
				dateTo,
			});

			let bookings: any[] = [];
			let hasMore = false;
			let dateRangeHints: any = null;

			try {
				const response = await ctx.runAction(
					internal.beds24ApiActions.fetchBookings,
					{
						accessToken: accessToken.accessToken,
						filters: {
							propId: propertyId.toString(),
							dateFrom,
							dateTo,
						},
						pagination: {
							page: pageNumber,
							limit: 100,
						},
					},
				);

				bookings = response.data || [];
				hasMore = response.pageCount ? pageNumber < response.pageCount : false;
				dateRangeHints = response.dateRangeHints;

				logger.info("予約データを取得しました", {
					count: bookings.length,
					hasMore,
					pageNumber,
					dateRangeHints,
				});
			} catch (error) {
				logger.error("Beds24 API呼び出しエラー", {
					error: error instanceof Error ? error.message : String(error),
					propertyId,
					pageNumber,
				});

				// 同期履歴を失敗として更新
				await ctx.runMutation(internal.beds24SyncHistory.recordSyncComplete, {
					historyId: syncHistoryId,
					status: "failed",
					totalItems: 0,
					successCount: 0,
					failedCount: 0,
					metadata: {
						error: error instanceof Error ? error.message : String(error),
					},
				});

				throw error;
			}

			// 予約データの処理
			let processResult = {
				successful: 0,
				failed: 0,
				created: 0,
				updated: 0,
				skipped: 0,
				allExisting: false,
			};

			if (bookings.length > 0) {
				const parser = createBookingParser();
				const parsedBookings = [];

				// まず施設IDを取得
				const property = await ctx.runQuery(
					internal.beds24Properties.getPropertyByBeds24Id,
					{ beds24PropertyId: propertyId.toString() },
				);

				if (!property) {
					throw createConvexError(ERROR_CODES.NOT_FOUND, "Property not found", {
						propertyId,
					});
				}

				const dbPropertyId = property._id;

				for (const booking of bookings) {
					try {
						const parsed = parser.parseBookingData(
							booking,
							dbPropertyId,
							userId,
						);
						if (parsed.success && parsed.booking) {
							parsedBookings.push(parsed.booking);
						}
					} catch (parseError) {
						logger.error("予約データのパースエラー", {
							bookingId: booking.id,
							error:
								parseError instanceof Error
									? parseError.message
									: String(parseError),
						});
					}
				}

				// バッチ処理での保存
				if (parsedBookings.length > 0) {
					processResult = await ctx.runMutation(
						internal.beds24Bookings.processBookingBatch,
						{
							bookings: parsedBookings,
						},
					);

					logger.info("予約データを保存しました", {
						successful: processResult.successful,
						failed: processResult.failed,
						created: processResult.created,
						updated: processResult.updated,
						skipped: processResult.skipped,
						allExisting: processResult.allExisting,
					});

					// 処理中の進捗ログ
					logger.syncProgress("processing", {
						jobType: "sync_bookings",
						dateRange: { from: dateFrom || "", to: dateTo || "" },
						progress: {
							current: pageNumber,
							total: hasMore ? pageNumber + 1 : pageNumber, // 概算
						},
						stats: {
							created: processResult.created,
							updated: processResult.updated,
							skipped: processResult.skipped,
							failed: processResult.failed,
						},
						recursionInfo: {
							isBacktracking: metadata.isRecursiveBacktrack || false,
							depth: metadata.recursionDepth || 0,
						},
					});
				}
			} else {
				// 予約が0件の場合
				logger.info("取得した予約が0件でした", {
					dateFrom,
					dateTo,
					pageNumber,
				});
			}

			// 停止条件の判定
			const shouldStop =
				bookings.length === 0 || // APIから予約データが0件
				(processResult.created === 0 && processResult.updated === 0) || // 新規・更新が0件
				processResult.allExisting; // すべて既存データ

			// ページネーション処理の改善
			if (hasMore && !shouldStop) {
				// 新規予約がある場合のみ次ページへ
				logger.info("次ページのタスクを作成します", {
					nextPage: pageNumber + 1,
					created: processResult.created,
					hasMore,
				});

				await ctx.runMutation(internal.beds24Queue.enqueueJob, {
					userId,
					jobType: "sync_bookings",
					priority: 5,
					metadata: {
						...metadata,
						pageNumber: pageNumber + 1,
						dateFrom,
						dateTo,
						hasMoreInDateRange: true,
					},
				});
			} else if (!hasMore || shouldStop) {
				// 現在の日付範囲の処理が完了
				logger.info("現在の日付範囲の処理が完了しました", {
					dateFrom,
					dateTo,
					pageNumber,
					totalBookings: bookings.length,
					created: processResult.created,
					shouldStop,
				});

				// 再帰的遡りロジック：新規予約がない場合、2ヶ月前の期間を計算
				if (shouldStop && !metadata.isRecursiveBacktrack) {
					// 初回の遡り処理の場合
					const previousRange = calculatePreviousDateRange(
						dateFrom,
						dateTo || formatDateForBeds24(new Date()),
					);

					logger.backtrack("初回遡り処理を開始します", {
						dateRange: previousRange,
						recursionDepth: 1,
						totalProcessed: bookings.length,
						newItems: processResult.created,
						existingItems: processResult.skipped,
					});

					await ctx.runMutation(internal.beds24Queue.enqueueJob, {
						userId,
						jobType: "sync_bookings",
						priority: 10, // 低優先度
						metadata: {
							propertyId,
							propertyName,
							userId,
							pageNumber: 1,
							dateFrom: previousRange.from,
							dateTo: previousRange.to,
							isRecursiveBacktrack: true,
							recursionDepth: 1,
							originalStartDate: metadata.originalStartDate || dateFrom,
							hasMoreInDateRange: false,
						},
					});
				} else if (shouldStop && metadata.isRecursiveBacktrack) {
					// 再帰的遡り中の場合
					const currentDepth = metadata.recursionDepth || 1;
					const maxDepth = 12; // 最大12回（2年分）まで遡る

					if (currentDepth < maxDepth) {
						const previousRange = calculatePreviousDateRange(
							dateFrom,
							dateTo || formatDateForBeds24(new Date()),
						);

						logger.backtrack("継続遡り処理", {
							dateRange: previousRange,
							recursionDepth: currentDepth + 1,
							totalProcessed: bookings.length,
							newItems: processResult.created,
							existingItems: processResult.skipped,
							maxDepth,
						});

						await ctx.runMutation(internal.beds24Queue.enqueueJob, {
							userId,
							jobType: "sync_bookings",
							priority: 10, // 低優先度
							metadata: {
								propertyId,
								propertyName,
								userId,
								pageNumber: 1,
								dateFrom: previousRange.from,
								dateTo: previousRange.to,
								isRecursiveBacktrack: true,
								recursionDepth: currentDepth + 1,
								originalStartDate: metadata.originalStartDate,
								hasMoreInDateRange: false,
							},
						});
					} else {
						logger.info("最大遡り回数に達しました。同期を完了します", {
							maxDepth,
							originalStartDate: metadata.originalStartDate,
						});
					}
				}
			}

			// タスクのステータスを完了に更新
			await ctx.runMutation(internal.beds24Queue.updateJobStatus, {
				jobId: args.taskId,
				status: "completed",
			});

			// 次の処理予定を計算
			let nextProcessing = null;
			if (hasMore && !shouldStop) {
				// 次ページがある場合
				nextProcessing = {
					type: "next_page",
					pageNumber: pageNumber + 1,
					dateRange: { from: dateFrom || "", to: dateTo || "" },
				};
			} else if (!hasMore || shouldStop) {
				// 遡り処理が予定されている場合
				if (
					shouldStop &&
					(!metadata.isRecursiveBacktrack ||
						(metadata.recursionDepth || 0) < 12)
				) {
					const previousRange = calculatePreviousDateRange(
						dateFrom,
						dateTo || formatDateForBeds24(new Date()),
					);
					nextProcessing = {
						type: "backtrack",
						dateRange: previousRange,
						recursionDepth: (metadata.recursionDepth || 0) + 1,
					};
				}
			}

			// 同期履歴を成功として更新
			await ctx.runMutation(internal.beds24SyncHistory.recordSyncComplete, {
				historyId: syncHistoryId,
				status: "success",
				totalItems: bookings.length,
				successCount: processResult.successful,
				failedCount: processResult.failed,
				metadata: {
					created: processResult.created,
					updated: processResult.updated,
					skipped: processResult.skipped,
					dateRange: { from: dateFrom || "", to: dateTo || "" },
					isRecursiveBacktrack: metadata.isRecursiveBacktrack,
					recursionDepth: metadata.recursionDepth,
					nextProcessing,
				},
			});

			// 同期進捗ログの完了
			logger.syncProgress("complete", {
				jobType: "sync_bookings",
				dateRange: { from: dateFrom || "", to: dateTo || "" },
				stats: {
					created: processResult.created,
					updated: processResult.updated,
					skipped: processResult.skipped,
					failed: processResult.failed,
				},
				recursionInfo: {
					isBacktracking: metadata.isRecursiveBacktrack || false,
					depth: metadata.recursionDepth || 0,
					originalStartDate: metadata.originalStartDate,
				},
				nextProcessing,
			});

			return null;
		} catch (error) {
			const errorDetails = {
				taskId: args.taskId,
				error: error instanceof Error ? error.message : String(error),
				stack: error instanceof Error ? error.stack : undefined,
				name: error instanceof Error ? error.name : undefined,
				fullError: JSON.stringify(error, null, 2),
			};
			logger.error("予約同期エラー", errorDetails);

			// 同期進捗ログのエラー
			logger.syncProgress("error", {
				jobType: "sync_bookings",
				dateRange: { from: dateFrom || "", to: dateTo || "" },
				recursionInfo: {
					isBacktracking: metadata?.isRecursiveBacktrack || false,
					depth: metadata?.recursionDepth || 0,
					originalStartDate: metadata?.originalStartDate,
				},
				error: error instanceof Error ? error.message : String(error),
			});

			// タスクのステータスを失敗に更新
			await ctx.runMutation(internal.beds24Queue.updateJobStatus, {
				jobId: args.taskId,
				status: "failed",
				error: error instanceof Error ? error.message : String(error),
			});

			// エラー連続時の停止条件
			// タスクのattempts数を確認（3回以上失敗したら停止）
			if (taskData && taskData.attempts >= 3) {
				logger.error("エラーが連続して発生したため、同期を停止します", {
					taskId: args.taskId,
					attempts: taskData.attempts,
					maxAttempts: taskData.maxAttempts,
				});
				// 再帰的遡りも停止（新しいタスクを作成しない）
			}

			throw error;
		}
	},
});
