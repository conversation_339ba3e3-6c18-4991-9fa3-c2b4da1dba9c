/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as beds24 from "../beds24.js";
import type * as beds24ApiActions from "../beds24ApiActions.js";
import type * as beds24BookingQueries from "../beds24BookingQueries.js";
import type * as beds24BookingSync from "../beds24BookingSync.js";
import type * as beds24Bookings from "../beds24Bookings.js";
import type * as beds24CronActions from "../beds24CronActions.js";
import type * as beds24Properties from "../beds24Properties.js";
import type * as beds24PropertySync from "../beds24PropertySync.js";
import type * as beds24Queue from "../beds24Queue.js";
import type * as beds24QueueMutations from "../beds24QueueMutations.js";
import type * as beds24QueueProcessor from "../beds24QueueProcessor.js";
import type * as beds24SyncHistory from "../beds24SyncHistory.js";
import type * as beds24SyncMutations from "../beds24SyncMutations.js";
import type * as beds24SyncQueries from "../beds24SyncQueries.js";
import type * as beds24Tokens from "../beds24Tokens.js";
import type * as bookingComSlugQueries from "../bookingComSlugQueries.js";
import type * as bookingComSlugSync from "../bookingComSlugSync.js";
import type * as checkDuplicates from "../checkDuplicates.js";
import type * as crons from "../crons.js";
import type * as init from "../init.js";
import type * as lib_auth from "../lib/auth.js";
import type * as lib_bookingComSlugScraper from "../lib/bookingComSlugScraper.js";
import type * as lib_bookingParser from "../lib/bookingParser.js";
import type * as lib_dateUtils from "../lib/dateUtils.js";
import type * as lib_errors from "../lib/errors.js";
import type * as lib_helpers from "../lib/helpers.js";
import type * as lib_logging from "../lib/logging.js";
import type * as lib_reviewParser from "../lib/reviewParser.js";
import type * as lib_reviewUrlBuilder from "../lib/reviewUrlBuilder.js";
import type * as lib_scrapingAnt from "../lib/scrapingAnt.js";
import type * as model_beds24 from "../model/beds24.js";
import type * as model_beds24Api from "../model/beds24Api.js";
import type * as model_beds24Bookings from "../model/beds24Bookings.js";
import type * as model_otaReviews from "../model/otaReviews.js";
import type * as model_userSettings from "../model/userSettings.js";
import type * as otaMaster from "../otaMaster.js";
import type * as otaMasterSeed from "../otaMasterSeed.js";
import type * as otaReviews from "../otaReviews.js";
import type * as otaReviewsInternal from "../otaReviewsInternal.js";
import type * as otaReviewsMutations from "../otaReviewsMutations.js";
import type * as otaReviewsQueries from "../otaReviewsQueries.js";
import type * as otaReviewsSync from "../otaReviewsSync.js";
import type * as types_beds24 from "../types/beds24.js";
import type * as types_beds24Bookings from "../types/beds24Bookings.js";
import type * as types_otaReviews from "../types/otaReviews.js";
import type * as types_reviews from "../types/reviews.js";
import type * as types_userSettings from "../types/userSettings.js";
import type * as userSettings from "../userSettings.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  beds24: typeof beds24;
  beds24ApiActions: typeof beds24ApiActions;
  beds24BookingQueries: typeof beds24BookingQueries;
  beds24BookingSync: typeof beds24BookingSync;
  beds24Bookings: typeof beds24Bookings;
  beds24CronActions: typeof beds24CronActions;
  beds24Properties: typeof beds24Properties;
  beds24PropertySync: typeof beds24PropertySync;
  beds24Queue: typeof beds24Queue;
  beds24QueueMutations: typeof beds24QueueMutations;
  beds24QueueProcessor: typeof beds24QueueProcessor;
  beds24SyncHistory: typeof beds24SyncHistory;
  beds24SyncMutations: typeof beds24SyncMutations;
  beds24SyncQueries: typeof beds24SyncQueries;
  beds24Tokens: typeof beds24Tokens;
  bookingComSlugQueries: typeof bookingComSlugQueries;
  bookingComSlugSync: typeof bookingComSlugSync;
  checkDuplicates: typeof checkDuplicates;
  crons: typeof crons;
  init: typeof init;
  "lib/auth": typeof lib_auth;
  "lib/bookingComSlugScraper": typeof lib_bookingComSlugScraper;
  "lib/bookingParser": typeof lib_bookingParser;
  "lib/dateUtils": typeof lib_dateUtils;
  "lib/errors": typeof lib_errors;
  "lib/helpers": typeof lib_helpers;
  "lib/logging": typeof lib_logging;
  "lib/reviewParser": typeof lib_reviewParser;
  "lib/reviewUrlBuilder": typeof lib_reviewUrlBuilder;
  "lib/scrapingAnt": typeof lib_scrapingAnt;
  "model/beds24": typeof model_beds24;
  "model/beds24Api": typeof model_beds24Api;
  "model/beds24Bookings": typeof model_beds24Bookings;
  "model/otaReviews": typeof model_otaReviews;
  "model/userSettings": typeof model_userSettings;
  otaMaster: typeof otaMaster;
  otaMasterSeed: typeof otaMasterSeed;
  otaReviews: typeof otaReviews;
  otaReviewsInternal: typeof otaReviewsInternal;
  otaReviewsMutations: typeof otaReviewsMutations;
  otaReviewsQueries: typeof otaReviewsQueries;
  otaReviewsSync: typeof otaReviewsSync;
  "types/beds24": typeof types_beds24;
  "types/beds24Bookings": typeof types_beds24Bookings;
  "types/otaReviews": typeof types_otaReviews;
  "types/reviews": typeof types_reviews;
  "types/userSettings": typeof types_userSettings;
  userSettings: typeof userSettings;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
