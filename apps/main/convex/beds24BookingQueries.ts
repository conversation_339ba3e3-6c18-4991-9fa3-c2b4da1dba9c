/**
 * Beds24 Bookings Internal Query Functions
 *
 * このファイルは内部クエリ関数を提供します（V8環境）。
 * アクション関数から呼び出される内部専用のクエリ関数です。
 *
 * 注意: "use node"を使用しないV8環境で動作します
 */

import { v } from "convex/values";
import { internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import { internalQuery } from "./_generated/server";
import { createLogger } from "./lib/logging";

/**
 * 同期タスクの詳細を取得
 *
 * 同期キューから予約同期タスクの詳細情報を取得します。
 * jobTypeが"sync_bookings"のタスクのみを対象とします。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {Id<"syncQueue">} args.taskId - 同期タスクのID
 * @returns {Promise<SyncTask | null>} 同期タスクの詳細情報、またはnull
 * @internal
 *
 * @example
 * const task = await getBookingSyncTask({ taskId: "task123" });
 * if (task) {
 *   console.log(`Status: ${task.status}`);
 *   console.log(`Attempts: ${task.attempts}`);
 * }
 */
export const getBookingSyncTask = internalQuery({
	args: {
		taskId: v.id("syncQueue"),
	},
	returns: v.union(
		v.object({
			_id: v.id("syncQueue"),
			_creationTime: v.number(),
			userId: v.string(),
			jobType: v.string(),
			priority: v.number(),
			status: v.union(
				v.literal("pending"),
				v.literal("processing"),
				v.literal("completed"),
				v.literal("failed"),
			),
			attempts: v.number(),
			maxAttempts: v.number(),
			scheduledFor: v.number(),
			startedAt: v.optional(v.number()),
			completedAt: v.optional(v.number()),
			lastError: v.optional(v.string()),
			nextRetryAt: v.optional(v.number()),
			metadata: v.optional(v.any()),
			result: v.optional(v.any()),
			createdAt: v.number(),
			updatedAt: v.number(),
		}),
		v.null(),
	),
	handler: async (ctx, args) => {
		const logger = createLogger("getBookingSyncTask", ctx);
		logger.debug("同期タスクを取得", { taskId: args.taskId });

		const task = await ctx.db.get(args.taskId);

		if (!task) {
			logger.warn("同期タスクが見つかりません", { taskId: args.taskId });
			return null;
		}

		// jobTypeがsync_bookingsであることを確認
		if (task.jobType !== "sync_bookings") {
			logger.warn("無効なジョブタイプ", {
				taskId: args.taskId,
				jobType: task.jobType,
			});
			return null;
		}

		logger.debug("同期タスクを取得しました", {
			taskId: args.taskId,
			status: task.status,
			attempts: task.attempts,
		});

		return task;
	},
});

/**
 * Beds24トークンを持つユーザーの詳細情報を取得
 *
 * has_beds24_tokenフラグがtrueのユーザーを検索し、
 * 各ユーザーのアクセストークン情報を取得します。
 * トークンの有効期限やアクティブ状態などの詳細情報を含みます。
 *
 * @returns {Promise<UserTokenDetails[]>} トークンを持つユーザーの詳細リスト
 * @returns {string} returns[].userId - ユーザーID
 * @returns {Id<"beds24AccessTokens">} returns[].tokenId - トークンID
 * @returns {boolean} returns[].hasActiveToken - アクティブなトークンかどうか
 * @returns {number} returns[].expiresAt - トークンの有効期限
 * @returns {number} returns[].lastRefreshedAt - 最後にリフレッシュされた日時
 * @returns {boolean} returns[].hasRefreshToken - リフレッシュトークンを持っているか
 * @internal
 *
 * @example
 * const userTokens = await getUsersWithBeds24TokenDetails();
 * const activeUsers = userTokens.filter(u => u.hasActiveToken);
 * console.log(`アクティブトークンを持つユーザー: ${activeUsers.length}人`);
 */
export const getUsersWithBeds24TokenDetails = internalQuery({
	args: {},
	returns: v.array(
		v.object({
			userId: v.string(),
			tokenId: v.id("beds24AccessTokens"),
			hasActiveToken: v.boolean(),
			expiresAt: v.number(),
			lastRefreshedAt: v.number(),
			hasRefreshToken: v.boolean(),
		}),
	),
	handler: async (ctx) => {
		const logger = createLogger("getUsersWithBeds24TokenDetails", ctx);

		logger.debug("Beds24トークンを持つユーザーの詳細を検索");

		// has_beds24_tokenフラグがtrueのユーザー設定を取得
		const userSettings = await ctx.db
			.query("userSettings")
			.withIndex("by_has_beds24_token", (q) => q.eq("has_beds24_token", true))
			.collect();

		logger.debug("トークンフラグを持つユーザー数", {
			count: userSettings.length,
		});

		const usersWithTokens = [];

		// 各ユーザーのアクセストークンを確認
		for (const setting of userSettings) {
			const accessToken = await ctx.db
				.query("beds24AccessTokens")
				.withIndex("by_userId", (q) => q.eq("userId", setting.userId))
				.first();

			if (accessToken) {
				const now = Date.now();
				const hasActiveToken = accessToken.expiresAt > now;

				usersWithTokens.push({
					userId: setting.userId,
					tokenId: accessToken._id,
					hasActiveToken,
					expiresAt: accessToken.expiresAt,
					lastRefreshedAt: accessToken.lastRefreshedAt,
					hasRefreshToken: !!setting.beds24?.refreshToken,
				});

				logger.debug("ユーザーのトークン情報", {
					userId: setting.userId,
					hasActiveToken,
					expiresIn: Math.max(0, accessToken.expiresAt - now),
				});
			} else {
				// アクセストークンはないがリフレッシュトークンがある場合
				if (setting.beds24?.refreshToken) {
					logger.warn("アクセストークンが見つかりません（リフレッシュ可能）", {
						userId: setting.userId,
					});
				}
			}
		}

		logger.info("Beds24トークンを持つユーザーを取得しました", {
			totalUsers: userSettings.length,
			activeTokens: usersWithTokens.filter((u) => u.hasActiveToken).length,
			expiredTokens: usersWithTokens.filter((u) => !u.hasActiveToken).length,
		});

		return usersWithTokens;
	},
});

/**
 * 特定のユーザーのアクセス可能な施設IDを取得（内部用）
 *
 * userPropertiesテーブルからユーザーのアクセス可能な施設IDを取得します。
 * 削除済み施設のフィルタリングオプションを提供します。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {string} args.userId - ユーザーID
 * @param {boolean} [args.includeDeleted] - 削除済み施設を含むかどうか（デフォルト: false）
 * @returns {Promise<Id<"beds24Properties">[]>} アクセス可能な施設IDの配列
 * @internal
 *
 * @example
 * const propertyIds = await getUserAccessiblePropertyIds({
 *   userId: "user123",
 *   includeDeleted: false
 * });
 */
export const getUserAccessiblePropertyIds = internalQuery({
	args: {
		userId: v.string(),
		includeDeleted: v.optional(v.boolean()),
	},
	returns: v.array(v.id("beds24Properties")),
	handler: async (ctx, args) => {
		const logger = createLogger("getUserAccessiblePropertyIds", ctx);
		logger.debug("アクセス可能な施設IDを取得", {
			userId: args.userId,
			includeDeleted: args.includeDeleted,
		});

		// userPropertiesテーブルから関連を取得
		const userRelations = await ctx.db
			.query("userProperties")
			.withIndex("by_userId", (q) => q.eq("userId", args.userId))
			.take(100);

		const propertyIds: Id<"beds24Properties">[] = [];

		for (const relation of userRelations) {
			const property = await ctx.db.get(relation.propertyId);

			// 削除済み施設の扱い
			if (property && (args.includeDeleted || !property.isDeleted)) {
				propertyIds.push(relation.propertyId);
			}
		}

		logger.debug("アクセス可能な施設数", {
			userId: args.userId,
			count: propertyIds.length,
		});

		return propertyIds;
	},
});

/**
 * 同期対象の施設情報を取得（予約同期用）
 *
 * 予約同期処理のために、ユーザーがアクセス可能な施設情報を取得します。
 * 特定の施設IDを指定するか、ユーザーの全施設を取得できます。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {string} args.userId - ユーザーID
 * @param {Id<"beds24Properties">[]} [args.propertyIds] - 対象施設IDの配列（オプション）
 * @returns {Promise<PropertySyncInfo[]>} 同期用施設情報の配列
 * @returns {Id<"beds24Properties">} returns[]._id - 施設ID
 * @returns {string} returns[].beds24PropertyId - Beds24施設ID
 * @returns {string} returns[].name - 施設名
 * @returns {number} returns[].lastSyncedAt - 最終同期日時
 * @returns {string} [returns[].bookingComFacilitySlug] - Booking.comの施設スラッグ
 * @internal
 *
 * @example
 * // ユーザーの全施設を取得
 * const allProperties = await getPropertiesForBookingSync({
 *   userId: "user123"
 * });
 *
 * // 特定の施設のみ取得
 * const specificProperties = await getPropertiesForBookingSync({
 *   userId: "user123",
 *   propertyIds: ["prop1", "prop2"]
 * });
 */
export const getPropertiesForBookingSync = internalQuery({
	args: {
		userId: v.string(),
		propertyIds: v.optional(v.array(v.id("beds24Properties"))),
	},
	returns: v.array(
		v.object({
			_id: v.id("beds24Properties"),
			beds24PropertyId: v.string(),
			name: v.string(),
			lastSyncedAt: v.number(),
			bookingComFacilitySlug: v.optional(v.string()),
		}),
	),
	handler: async (ctx, args) => {
		const logger = createLogger("getPropertiesForBookingSync", ctx);

		let targetPropertyIds: Id<"beds24Properties">[];

		if (args.propertyIds && args.propertyIds.length > 0) {
			// 指定された施設IDを使用
			targetPropertyIds = args.propertyIds;
		} else {
			// ユーザーのアクセス可能な施設を取得
			targetPropertyIds = await ctx.runQuery(
				internal.beds24BookingQueries.getUserAccessiblePropertyIds,
				{
					userId: args.userId,
					includeDeleted: false,
				},
			);
		}

		logger.debug("同期対象施設数", {
			userId: args.userId,
			count: targetPropertyIds.length,
		});

		const properties = [];

		for (const propertyId of targetPropertyIds) {
			const property = await ctx.db.get(propertyId);
			if (property && !property.isDeleted) {
				properties.push({
					_id: property._id,
					beds24PropertyId: property.beds24PropertyId,
					name: property.name,
					lastSyncedAt: property.lastSyncedAt,
					bookingComFacilitySlug: property.bookingComFacilitySlug,
				});
			}
		}

		return properties;
	},
});
