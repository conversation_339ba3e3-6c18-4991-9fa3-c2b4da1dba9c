import { v } from "convex/values";
import { internal } from "./_generated/api";
import { internalMutation, internalQuery } from "./_generated/server";
import { QueueJobType } from "./types/beds24";

/**
 * スラッグ取得タスクの詳細を取得
 * @internal
 */
export const getSlugSyncTask = internalQuery({
	args: {
		taskId: v.id("syncQueue"),
	},
	returns: v.union(
		v.null(),
		v.object({
			task: v.object({
				_id: v.id("syncQueue"),
				_creationTime: v.number(),
				userId: v.string(),
				jobType: v.string(),
				priority: v.number(),
				status: v.string(),
				attempts: v.number(),
				maxAttempts: v.number(),
				scheduledFor: v.number(),
				startedAt: v.optional(v.number()),
				completedAt: v.optional(v.number()),
				lastError: v.optional(v.string()),
				nextRetryAt: v.optional(v.number()),
				metadata: v.optional(v.any()),
				result: v.optional(v.any()),
				createdAt: v.number(),
				updatedAt: v.number(),
			}),
		}),
	),
	handler: async (ctx, args) => {
		const task = await ctx.db.get(args.taskId);

		if (!task) {
			return null;
		}

		// jobTypeがscrape_booking_com_slugでない場合はnullを返す
		if (task.jobType !== QueueJobType.SCRAPE_BOOKING_COM_SLUG) {
			return null;
		}

		return { task };
	},
});

/**
 * Booking.comスラッグ取得アクションを開始するミューテーション
 *
 * Convex公式パターンに従い、ミューテーションからアクションをスケジュールすることで
 * "Connection lost while action was in flight"エラーを防ぐ
 *
 * @internal
 */
export const startScrapeBookingComSlug = internalMutation({
	args: {
		taskId: v.id("syncQueue"),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		// タスクの存在確認
		const task = await ctx.db.get(args.taskId);
		if (!task) {
			throw new Error(`Task not found: ${args.taskId}`);
		}

		// タスクのタイプ確認
		if (task.jobType !== QueueJobType.SCRAPE_BOOKING_COM_SLUG) {
			throw new Error(`Invalid job type: ${task.jobType}`);
		}

		// タスクのステータス確認
		if (task.status !== "pending" && task.status !== "processing") {
			throw new Error(
				`Task is not in pending or processing state: ${task.status} (taskId: ${args.taskId})`,
			);
		}

		// アクションをスケジュール（即座に実行）
		await ctx.scheduler.runAfter(
			0,
			internal.bookingComSlugSync.scrapeBookingComSlug,
			{
				taskId: args.taskId,
			},
		);

		// タスクを処理中に更新
		await ctx.db.patch(args.taskId, {
			status: "processing",
			startedAt: Date.now(),
			updatedAt: Date.now(),
		});

		return null;
	},
});
