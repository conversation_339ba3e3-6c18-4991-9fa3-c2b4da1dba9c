import { v } from "convex/values";
import type { Id } from "./_generated/dataModel";
import { internalMutation, internalQuery } from "./_generated/server";
import { ConvexError, ERROR_CODES } from "./lib/errors";
import { createLogger } from "./lib/logging";

/**
 * 施設データを更新または挿入する
 *
 * Beds24 APIから取得した施設データを一括で登録/更新します。
 * 既存の施設は更新され、新規施設は作成されます。
 * Booking.com関連フィールドは保持されます。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {string} args.userId - ユーザーID
 * @param {Array<PropertyData>} args.properties - Beds24 APIから取得した施設データの配列
 * @param {string} args.properties[].propId - Beds24施設ID
 * @param {string} args.properties[].propKey - Beds24施設キー
 * @param {any} args.properties[].data - 完全な施設データ
 * @returns {Promise<UpsertResult>} アップサート結果
 * @returns {number} returns.upserted - 処理された総件数
 * @returns {number} returns.created - 新規作成された件数
 * @returns {number} returns.updated - 更新された件数
 * @returns {string[]} returns.errors - エラーメッセージの配列
 * @internal
 *
 * @example
 * const result = await upsertProperties({
 *   userId: "user123",
 *   properties: [
 *     { propId: "123", propKey: "abc", data: {...} },
 *     { propId: "456", propKey: "def", data: {...} }
 *   ]
 * });
 */
export const upsertProperties = internalMutation({
	args: {
		userId: v.string(),
		properties: v.array(
			v.object({
				propId: v.string(),
				propKey: v.string(),
				data: v.any(), // 完全な施設データ（Beds24 APIからの全データを受け入れる）
			}),
		),
	},
	returns: v.object({
		upserted: v.number(),
		created: v.number(),
		updated: v.number(),
		errors: v.array(v.string()),
	}),
	handler: async (ctx, args) => {
		const logger = createLogger("upsertProperties", ctx);
		const timestamp = Date.now();
		let upserted = 0;
		let created = 0;
		let updated = 0;
		const errors: string[] = [];

		for (const property of args.properties) {
			try {
				// まず、このbeds24PropertyIdを持つ既存の施設を検索（ユーザー問わず）
				const existingByPropertyId = await ctx.db
					.query("beds24Properties")
					.withIndex("by_beds24PropertyId", (q) =>
						q.eq("beds24PropertyId", property.propId),
					)
					.first();

				let propertyId: Id<"beds24Properties">;

				if (existingByPropertyId) {
					// 既存の施設が見つかった場合
					propertyId = existingByPropertyId._id;

					// Booking.com関連フィールドの保持状況をログ出力
					if (existingByPropertyId.bookingComFacilitySlug) {
						logger.info(
							`Preserving Booking.com slug for property ${property.propId}`,
							{
								beds24PropertyId: property.propId,
								bookingComFacilitySlug:
									existingByPropertyId.bookingComFacilitySlug,
								bookingComLastScrapedAt:
									existingByPropertyId.bookingComLastScrapedAt,
							},
						);
					}

					// 最新データで更新（Booking.com関連フィールドを保持）
					await ctx.db.patch(existingByPropertyId._id, {
						beds24PropertyKey: property.propKey,
						name: property.data.name || "",
						propertyType: property.data.propertyType || "",
						currency: property.data.currency || "",
						country: property.data.country || undefined,
						city: property.data.city || undefined,
						data: property.data,
						lastSyncedAt: timestamp,
						updatedAt: timestamp,
						isDeleted: false,
						deletedAt: undefined,
						// 既存のBooking.com関連フィールドを保持（nullでない場合）
						...(existingByPropertyId.bookingComFacilitySlug && {
							bookingComFacilitySlug:
								existingByPropertyId.bookingComFacilitySlug,
						}),
						...(existingByPropertyId.bookingComLastScrapedAt && {
							bookingComLastScrapedAt:
								existingByPropertyId.bookingComLastScrapedAt,
						}),
					});

					logger.debug(
						`Updated property ${property.propId} (existing property)`,
					);
					updated++;
				} else {
					// 新規施設を作成
					propertyId = await ctx.db.insert("beds24Properties", {
						beds24PropertyId: property.propId,
						beds24PropertyKey: property.propKey,
						name: property.data.name || "",
						propertyType: property.data.propertyType || "",
						currency: property.data.currency || "",
						country: property.data.country || undefined,
						city: property.data.city || undefined,
						data: property.data,
						lastSyncedAt: timestamp,
						createdAt: timestamp,
						updatedAt: timestamp,
						isDeleted: false,
					});

					logger.debug(
						`Created new property ${property.propId} for user ${args.userId}`,
					);
					created++;
				}

				// userPropertiesテーブルに関係を作成/確認
				const existingRelation = await ctx.db
					.query("userProperties")
					.withIndex("by_userId_and_propertyId", (q) =>
						q.eq("userId", args.userId).eq("propertyId", propertyId),
					)
					.unique();

				if (!existingRelation) {
					// 新しい関係を作成
					await ctx.db.insert("userProperties", {
						userId: args.userId,
						propertyId: propertyId,
						createdAt: timestamp,
					});

					logger.debug(
						`Created user-property relation for user ${args.userId} and property ${propertyId}`,
					);
				}

				upserted++;
			} catch (error) {
				const errorMsg = `Failed to upsert property ${property.propId}`;
				logger.error(errorMsg, error);
				errors.push(errorMsg);
			}
		}

		logger.info(`Upserted ${upserted} properties for user ${args.userId}`, {
			totalProperties: args.properties.length,
			created,
			updated,
			errorCount: errors.length,
		});

		return { upserted, created, updated, errors };
	},
});

/**
 * APIレスポンスに含まれない施設との関係を削除する
 *
 * APIから取得したアクティブな施設以外の関係を削除します。
 * 施設が他のユーザーとも関係がない場合は、施設自体をソフト削除します。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {string} args.userId - ユーザーID
 * @param {string[]} args.activePropertyIds - APIから取得したアクティブな施設IDのリスト
 * @returns {Promise<DeleteResult>} 削除結果
 * @returns {number} returns.deletedCount - ソフト削除された施設数
 * @returns {number} returns.relationshipsRemoved - 削除された関係数
 * @internal
 *
 * @example
 * const result = await markDeletedProperties({
 *   userId: "user123",
 *   activePropertyIds: ["123", "456"]
 * });
 * console.log(`${result.relationshipsRemoved}件の関係を削除しました`);
 */
export const markDeletedProperties = internalMutation({
	args: {
		userId: v.string(),
		activePropertyIds: v.array(v.string()),
	},
	returns: v.object({
		deletedCount: v.number(),
		relationshipsRemoved: v.number(),
	}),
	handler: async (ctx, args) => {
		const logger = createLogger("markDeletedProperties", ctx);
		const timestamp = Date.now();
		let deletedCount = 0;
		let relationshipsRemoved = 0;

		// ユーザーに関連する全ての施設を取得（新方式）
		const userPropertyRelations = await ctx.db
			.query("userProperties")
			.withIndex("by_userId", (q) => q.eq("userId", args.userId))
			.collect();

		// 各関係をチェック
		for (const relation of userPropertyRelations) {
			const property = await ctx.db.get(relation.propertyId);
			if (!property) {
				// 施設が存在しない場合は関係を削除
				await ctx.db.delete(relation._id);
				relationshipsRemoved++;
				continue;
			}

			// APIレスポンスにない施設との関係を削除
			if (!args.activePropertyIds.includes(property.beds24PropertyId)) {
				await ctx.db.delete(relation._id);
				relationshipsRemoved++;

				logger.info(
					`Removed relationship between user ${args.userId} and property ${property.beds24PropertyId}`,
				);

				// この施設が他のユーザーとも関係がない場合は、施設自体をソフトデリート
				const otherRelations = await ctx.db
					.query("userProperties")
					.withIndex("by_propertyId", (q) => q.eq("propertyId", property._id))
					.collect();

				if (otherRelations.length === 0) {
					await ctx.db.patch(property._id, {
						isDeleted: true,
						deletedAt: timestamp,
						updatedAt: timestamp,
					});
					deletedCount++;
					logger.info(
						`Soft deleted property ${property.beds24PropertyId} (no users associated)`,
					);
				}
			}
		}

		if (deletedCount > 0 || relationshipsRemoved > 0) {
			logger.info(`Processed deletion for user ${args.userId}`, {
				deletedCount,
				relationshipsRemoved,
				activeProperties: args.activePropertyIds.length,
			});
		}

		return { deletedCount, relationshipsRemoved };
	},
});

/**
 * ユーザーのアクティブな施設一覧を取得する
 *
 * userPropertiesテーブルを経由して、ユーザーがアクセス可能な施設を取得します。
 * デフォルトではアクティブな施設のみを返します。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {string} args.userId - ユーザーID
 * @param {boolean} [args.includeDeleted] - 削除済み施設も含むかどうか（デフォルト: false）
 * @returns {Promise<Property[]>} 施設情報の配列
 * @internal
 *
 * @example
 * // アクティブな施設のみ取得
 * const activeProperties = await getPropertiesByUserId({
 *   userId: "user123"
 * });
 *
 * // 削除済み施設も含めて取得
 * const allProperties = await getPropertiesByUserId({
 *   userId: "user123",
 *   includeDeleted: true
 * });
 */
export const getPropertiesByUserId = internalQuery({
	args: {
		userId: v.string(),
		includeDeleted: v.optional(v.boolean()),
	},
	returns: v.array(
		v.object({
			_id: v.id("beds24Properties"),
			_creationTime: v.number(),
			beds24PropertyId: v.string(),
			beds24PropertyKey: v.string(),
			name: v.string(),
			propertyType: v.string(),
			currency: v.string(),
			country: v.optional(v.string()),
			city: v.optional(v.string()),
			data: v.any(),
			lastSyncedAt: v.number(),
			createdAt: v.number(),
			updatedAt: v.number(),
			isDeleted: v.boolean(),
			deletedAt: v.optional(v.number()),
			bookingComFacilitySlug: v.optional(v.string()),
			bookingComLastScrapedAt: v.optional(v.number()),
		}),
	),
	handler: async (ctx, args) => {
		const logger = createLogger("getPropertiesByUserId", ctx);
		const properties = new Map<string, any>();

		// 新方式: userPropertiesテーブル経由で取得
		const userRelations = await ctx.db
			.query("userProperties")
			.withIndex("by_userId", (q) => q.eq("userId", args.userId))
			.collect();

		for (const relation of userRelations) {
			const property = await ctx.db.get(relation.propertyId);
			if (property && (args.includeDeleted || !property.isDeleted)) {
				properties.set(property._id, property);
			}
		}

		const result = Array.from(properties.values());

		logger.debug(
			`Retrieved ${result.length} properties for user ${args.userId}`,
			{
				includeDeleted: args.includeDeleted || false,
				fromNewStyle: userRelations.length,
			},
		);

		return result;
	},
});

/**
 * 特定の施設データを取得する
 *
 * ユーザーのアクセス権限を確認してから施設データを返します。
 * アクセス権限がない場合はnullを返します。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {string} args.userId - ユーザーID
 * @param {string} args.beds24PropertyId - Beds24施設ID
 * @returns {Promise<Property | null>} 施設情報、またはnull
 * @internal
 *
 * @example
 * const property = await getPropertyById({
 *   userId: "user123",
 *   beds24PropertyId: "prop456"
 * });
 * if (property) {
 *   console.log("施設名:", property.name);
 * }
 */
export const getPropertyById = internalQuery({
	args: {
		userId: v.string(),
		beds24PropertyId: v.string(),
	},
	returns: v.union(
		v.object({
			_id: v.id("beds24Properties"),
			_creationTime: v.number(),
			beds24PropertyId: v.string(),
			beds24PropertyKey: v.string(),
			name: v.string(),
			propertyType: v.string(),
			currency: v.string(),
			country: v.optional(v.string()),
			city: v.optional(v.string()),
			data: v.any(),
			lastSyncedAt: v.number(),
			createdAt: v.number(),
			updatedAt: v.number(),
			isDeleted: v.boolean(),
			deletedAt: v.optional(v.number()),
			bookingComFacilitySlug: v.optional(v.string()),
			bookingComLastScrapedAt: v.optional(v.number()),
		}),
		v.null(),
	),
	handler: async (ctx, args) => {
		const logger = createLogger("getPropertyById", ctx);

		// まず施設を検索（ユーザー問わず）
		const property = await ctx.db
			.query("beds24Properties")
			.withIndex("by_beds24PropertyId", (q) =>
				q.eq("beds24PropertyId", args.beds24PropertyId),
			)
			.first();

		if (!property) {
			return null;
		}

		// アクセス権限をチェック
		// userPropertiesテーブルでチェック
		const hasAccess = await ctx.db
			.query("userProperties")
			.withIndex("by_userId_and_propertyId", (q) =>
				q.eq("userId", args.userId).eq("propertyId", property._id),
			)
			.unique();

		// アクセス権限がある場合のみ返す
		if (hasAccess) {
			logger.debug(
				`User ${args.userId} has access to property ${args.beds24PropertyId}`,
			);
			return property;
		}

		logger.debug(
			`User ${args.userId} does not have access to property ${args.beds24PropertyId}`,
		);
		return null;
	},
});

/**
 * Beds24施設IDで施設データを取得する（ユーザー問わず）
 *
 * アクセス権限チェックを行わずに施設データを取得します。
 * 内部処理でのみ使用し、ユーザーに直接データを返す際は必ず権限チェックを行ってください。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {string} args.beds24PropertyId - Beds24施設ID
 * @returns {Promise<Property | null>} 施設情報、またはnull
 * @internal
 *
 * @example
 * const property = await getPropertyByBeds24Id({
 *   beds24PropertyId: "123456"
 * });
 */
export const getPropertyByBeds24Id = internalQuery({
	args: {
		beds24PropertyId: v.string(),
	},
	returns: v.union(
		v.object({
			_id: v.id("beds24Properties"),
			_creationTime: v.number(),
			beds24PropertyId: v.string(),
			beds24PropertyKey: v.string(),
			name: v.string(),
			propertyType: v.string(),
			currency: v.string(),
			country: v.optional(v.string()),
			city: v.optional(v.string()),
			data: v.any(),
			lastSyncedAt: v.number(),
			createdAt: v.number(),
			updatedAt: v.number(),
			isDeleted: v.boolean(),
			deletedAt: v.optional(v.number()),
			bookingComFacilitySlug: v.optional(v.string()),
			bookingComLastScrapedAt: v.optional(v.number()),
		}),
		v.null(),
	),
	handler: async (ctx, args) => {
		const property = await ctx.db
			.query("beds24Properties")
			.withIndex("by_beds24PropertyId", (q) =>
				q.eq("beds24PropertyId", args.beds24PropertyId),
			)
			.first();

		return property;
	},
});

/**
 * 施設のBooking.comスラッグ情報を更新する
 *
 * 施設のBooking.comスラッグと最終スクレイピング日時を更新します。
 * Booking.comとの統合時に使用されます。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {Id<"beds24Properties">} args.propertyId - 施設ID
 * @param {string} args.bookingComFacilitySlug - Booking.comの施設スラッグ
 * @returns {Promise<SlugUpdateResult>} 更新結果
 * @returns {boolean} returns.success - 成功フラグ
 * @returns {Id<"beds24Properties">} returns.propertyId - 更新された施設ID
 * @returns {Object} returns.updatedFields - 更新されたフィールド
 * @throws {ConvexError} 施設が存在しない場合
 * @internal
 *
 * @example
 * const result = await updatePropertySlug({
 *   propertyId: "prop123",
 *   bookingComFacilitySlug: "hotel-tokyo-123"
 * });
 */
export const updatePropertySlug = internalMutation({
	args: {
		propertyId: v.id("beds24Properties"),
		bookingComFacilitySlug: v.string(),
	},
	returns: v.object({
		success: v.boolean(),
		propertyId: v.id("beds24Properties"),
		updatedFields: v.object({
			bookingComFacilitySlug: v.string(),
			bookingComLastScrapedAt: v.number(),
		}),
	}),
	handler: async (ctx, args) => {
		const logger = createLogger("updatePropertySlug", ctx);
		const timestamp = Date.now();

		logger.info("updatePropertySlug開始", {
			propertyId: args.propertyId,
			bookingComFacilitySlug: args.bookingComFacilitySlug,
		});

		// 施設の存在確認
		const property = await ctx.db.get(args.propertyId);
		if (!property) {
			logger.error(`Property not found: ${args.propertyId}`);
			throw new ConvexError({
				code: ERROR_CODES.NOT_FOUND,
				message: `Property with ID ${args.propertyId} not found`,
			});
		}

		logger.info("施設情報取得成功", {
			propertyId: args.propertyId,
			beds24PropertyId: property.beds24PropertyId,
			propertyName: property.name,
			currentSlug: property.bookingComFacilitySlug || null,
		});

		// 施設情報を更新
		await ctx.db.patch(args.propertyId, {
			bookingComFacilitySlug: args.bookingComFacilitySlug,
			bookingComLastScrapedAt: timestamp,
			updatedAt: timestamp,
		});

		logger.info(
			`Updated Booking.com slug for property ${property.beds24PropertyId}`,
			{
				propertyId: args.propertyId,
				propertyName: property.name,
				bookingComFacilitySlug: args.bookingComFacilitySlug,
				previousSlug: property.bookingComFacilitySlug || null,
				timestamp: new Date(timestamp).toISOString(),
			},
		);

		return {
			success: true,
			propertyId: args.propertyId,
			updatedFields: {
				bookingComFacilitySlug: args.bookingComFacilitySlug,
				bookingComLastScrapedAt: timestamp,
			},
		};
	},
});

/**
 * ページネーションで施設データを取得する
 *
 * 大量の施設データをページ単位で取得します。
 * バッチ処理や大規模データのエクスポート時に使用します。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {number} args.limit - 1ページあたりの件数
 * @param {string | null} args.cursor - ページネーションカーソル
 * @returns {Promise<PaginatedResult>} ページネーション結果
 * @returns {Property[]} returns.page - 現在ページの施設データ
 * @returns {string | null} returns.continueCursor - 次ページのカーソル
 * @returns {boolean} returns.isDone - 最終ページかどうか
 * @internal
 *
 * @example
 * let cursor = null;
 * do {
 *   const result = await getPropertiesPaginated({
 *     limit: 100,
 *     cursor: cursor
 *   });
 *   console.log(`${result.page.length}件を取得`);
 *   cursor = result.continueCursor;
 * } while (cursor);
 */
export const getPropertiesPaginated = internalQuery({
	args: {
		limit: v.number(),
		cursor: v.union(v.string(), v.null()),
	},
	returns: v.object({
		page: v.array(
			v.object({
				_id: v.id("beds24Properties"),
				_creationTime: v.number(),
				beds24PropertyId: v.string(),
				beds24PropertyKey: v.string(),
				name: v.string(),
				propertyType: v.string(),
				currency: v.string(),
				country: v.optional(v.string()),
				city: v.optional(v.string()),
				data: v.any(),
				lastSyncedAt: v.number(),
				createdAt: v.number(),
				updatedAt: v.number(),
				isDeleted: v.boolean(),
				deletedAt: v.optional(v.number()),
				bookingComFacilitySlug: v.optional(v.string()),
				bookingComLastScrapedAt: v.optional(v.number()),
			}),
		),
		continueCursor: v.union(v.string(), v.null()),
		isDone: v.boolean(),
	}),
	handler: async (ctx, args) => {
		const logger = createLogger("getPropertiesPaginated", ctx);

		const result = await ctx.db.query("beds24Properties").paginate({
			numItems: args.limit,
			cursor: args.cursor,
		});

		logger.debug(`Retrieved ${result.page.length} properties`, {
			hasMore: !!result.continueCursor,
			isDone: result.isDone,
		});

		return {
			page: result.page,
			continueCursor: result.continueCursor ?? null,
			isDone: result.isDone,
		};
	},
});

/**
 * 施設IDから関連するユーザーの一覧を取得する
 *
 * userPropertiesテーブルから、指定された施設に関連するユーザーの一覧を取得します。
 * 共同所有者の確認やアクセス管理に使用します。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {Id<"beds24Properties">} args.propertyId - 施設ID
 * @returns {Promise<UserProperty[]>} ユーザーと施設の関係情報の配列
 * @internal
 *
 * @example
 * const userRelations = await getUsersByPropertyId({
 *   propertyId: "prop123"
 * });
 * console.log(`この施設には${userRelations.length}人のユーザーがアクセスできます`);
 */
export const getUsersByPropertyId = internalQuery({
	args: {
		propertyId: v.id("beds24Properties"),
	},
	returns: v.array(
		v.object({
			_id: v.id("userProperties"),
			_creationTime: v.number(),
			userId: v.string(),
			propertyId: v.id("beds24Properties"),
			createdAt: v.number(),
		}),
	),
	handler: async (ctx, args) => {
		const logger = createLogger("getUsersByPropertyId", ctx);
		const startTime = Date.now();

		logger.info("[getUsersByPropertyId] 開始", {
			propertyId: args.propertyId,
			timestamp: startTime,
		});

		// クエリ実行
		const queryStartTime = Date.now();
		const userProperties = await ctx.db
			.query("userProperties")
			.withIndex("by_propertyId", (q) => q.eq("propertyId", args.propertyId))
			.collect();
		const queryTime = Date.now() - queryStartTime;

		const totalTime = Date.now() - startTime;

		logger.info("[getUsersByPropertyId] 完了", {
			propertyId: args.propertyId,
			userCount: userProperties.length,
			queryDurationMs: queryTime,
			totalDurationMs: totalTime,
			slowQuery: queryTime > 50,
		});

		return userProperties;
	},
});
