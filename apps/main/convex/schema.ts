import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

// The schema is entirely optional.
// You can delete this file (schema.ts) and the
// app will continue to work.
// The schema provides more precise TypeScript types.

// スキーマ定義
export default defineSchema({
	// ユーザー設定テーブル
	userSettings: defineTable({
		// Clerk User ID (外部サービスのID)
		userId: v.string(),
		// テーマ設定 (例: "light", "dark", "system")
		theme: v.union(v.literal("light"), v.literal("dark"), v.literal("system")),
		// Beds24設定
		beds24: v.optional(
			v.object({
				refreshToken: v.optional(v.string()),
			}),
		),
		// Beds24トークンの存在フラグ（インデックス用）
		has_beds24_token: v.boolean(),
		// 作成日時
		createdAt: v.number(),
		// 更新日時
		updatedAt: v.number(),
	})
		// ユーザーIDでインデックスを作成（高速検索用）
		.index("by_userId", ["userId"])
		// Beds24トークンを持つユーザーの効率的な検索用
		.index("by_has_beds24_token", ["has_beds24_token"]),

	// Beds24アクセストークン管理テーブル
	beds24AccessTokens: defineTable({
		// Clerk User ID
		userId: v.string(),
		// 現在のアクセストークン
		accessToken: v.string(),
		// 有効期限（Unix timestamp）
		expiresAt: v.number(),
		// 最終更新日時
		lastRefreshedAt: v.number(),
		// 作成日時
		createdAt: v.number(),
		// 更新日時
		updatedAt: v.number(),
	})
		// ユーザーIDでインデックスを作成（高速検索用）
		.index("by_userId", ["userId"]),

	// OTA(オンライン旅行代理店)マスタテーブル
	otaMaster: defineTable({
		// OTAのフルネーム（例: "Booking.com", "Expedia Group", "楽天トラベル"）
		fullName: v.string(),
		// OTAの短縮名（例: "Booking", "Expedia", "Rakuten"）
		shortName: v.string(),
		// 作成日時（Unix timestamp）
		createdAt: v.number(),
		// 更新日時（Unix timestamp）
		updatedAt: v.number(),
	})
		// 短縮名でインデックスを作成（高速検索用）
		.index("by_shortName", ["shortName"]),

	// Beds24施設情報テーブル
	beds24Properties: defineTable({
		// Beds24側の施設ID
		beds24PropertyId: v.string(),

		// Beds24施設キー
		beds24PropertyKey: v.string(),

		// 検索・フィルタリング用の基本情報
		name: v.string(),
		propertyType: v.string(),
		currency: v.string(),
		country: v.optional(v.string()),
		city: v.optional(v.string()),

		// Beds24 APIから取得した全データ
		// 複雑なネスト構造をそのまま保存
		// 任意の構造を受け入れるため v.any() を使用
		data: v.any(),

		// 同期管理用フィールド
		lastSyncedAt: v.number(),
		createdAt: v.number(),
		updatedAt: v.number(),

		// ソフトデリート用フィールド
		isDeleted: v.boolean(),
		deletedAt: v.optional(v.number()),

		// Booking.com関連フィールド
		bookingComFacilitySlug: v.optional(v.string()),
		bookingComLastScrapedAt: v.optional(v.number()),
	})
		// 施設IDでインデックス（グローバルな施設検索用）
		.index("by_beds24PropertyId", ["beds24PropertyId"])
		// ソフトデリート用インデックス（アクティブな施設の効率的な取得用）
		.index("by_isDeleted", ["isDeleted"])
		// Booking.comスラッグ検索用インデックス
		.index("by_bookingComFacilitySlug", ["bookingComFacilitySlug"]),

	// 同期キューテーブル（汎用的な外部サービス同期用）
	syncQueue: defineTable({
		// 基本フィールド
		userId: v.string(),
		jobType: v.union(
			v.literal("sync_properties"),
			v.literal("sync_reviews"),
			v.literal("scrape_booking_com_slug"),
			v.literal("sync_bookings"),
		), // 有効なジョブタイプのみを許可
		priority: v.number(), // 優先度（低い値が高優先度）
		status: v.union(
			v.literal("pending"),
			v.literal("processing"),
			v.literal("completed"),
			v.literal("failed"),
		), // 有効なステータスのみを許可

		// 実行管理フィールド
		attempts: v.number(), // 実行試行回数
		maxAttempts: v.number(), // 最大試行回数
		scheduledFor: v.number(), // 実行予定時刻（Unix timestamp）
		startedAt: v.optional(v.number()), // 実行開始時刻
		completedAt: v.optional(v.number()), // 実行完了時刻

		// エラー情報フィールド
		lastError: v.optional(v.string()), // 最後のエラーメッセージ
		nextRetryAt: v.optional(v.number()), // 次回リトライ時刻

		// メタデータフィールド
		metadata: v.optional(v.any()), // ジョブ固有のメタデータ
		result: v.optional(v.any()), // 実行結果

		// タイムスタンプ
		createdAt: v.number(),
		updatedAt: v.number(),
	})
		// ステータスと実行予定時刻でインデックス（キュー処理用）
		.index("by_status_and_scheduledFor", ["status", "scheduledFor"])
		// ユーザーIDとステータスでインデックス（ユーザー別の状況確認用）
		.index("by_userId_and_status", ["userId", "status"]),

	// Beds24同期履歴テーブル
	beds24SyncHistory: defineTable({
		// 基本フィールド
		userId: v.string(),
		jobType: v.union(
			v.literal("sync_properties"),
			v.literal("sync_reviews"),
			v.literal("scrape_booking_com_slug"),
			v.literal("sync_bookings"),
		), // 同期ジョブのタイプ
		status: v.union(
			v.literal("processing"),
			v.literal("success"),
			v.literal("partial_success"),
			v.literal("failed"),
		), // 同期結果のステータス

		// 同期実行履歴の記録用フィールド
		startedAt: v.number(), // 開始時刻
		completedAt: v.optional(v.number()), // 完了時刻（同期完了時に設定）
		duration: v.number(), // 実行時間（ミリ秒）

		// 同期結果
		totalItems: v.number(), // 処理対象アイテム数
		successCount: v.number(), // 成功したアイテム数
		failedCount: v.number(), // 失敗したアイテム数

		// エラー詳細の配列フィールド
		errors: v.array(
			v.object({
				code: v.string(), // エラーコード
				message: v.string(), // エラーメッセージ
				detail: v.optional(v.any()), // 詳細情報
				occurredAt: v.number(), // 発生時刻
			}),
		),

		// メタデータ
		metadata: v.optional(v.any()), // その他の情報

		// タイムスタンプ
		createdAt: v.number(),
		updatedAt: v.number(),
	})
		// ユーザーIDと開始時刻でインデックス（履歴検索用）
		.index("by_userId_and_startedAt", ["userId", "startedAt"])
		// ユーザーID、ジョブタイプ、開始時刻でインデックス（効率的なジョブタイプフィルタリング用）
		.index("by_userId_and_jobType_and_startedAt", [
			"userId",
			"jobType",
			"startedAt",
		]),

	// OTAレビューテーブル
	otaReviews: defineTable({
		// 基本識別情報
		beds24PropertyId: v.id("beds24Properties"), // 施設への参照
		otaId: v.id("otaMaster"), // OTAへの参照

		// 重複防止用のハッシュ
		uniqueHash: v.string(), // レビュー全文ベースのハッシュ

		// レビュー情報
		score: v.number(), // 総合スコア（例: 8.5）
		title: v.optional(v.string()), // レビュータイトル

		// レビュー内容（全OTA共通）
		reviewContent: v.string(), // 統合されたレビュー本文

		// 構造化レビュー（Booking.com等）
		reviewContentStructured: v.optional(
			v.object({
				positive: v.optional(v.string()), // 良かった点
				negative: v.optional(v.string()), // 改善点
			}),
		),

		// レビュアー情報
		reviewerName: v.string(), // レビュアー名
		reviewerCountry: v.optional(v.string()), // 国/地域

		// 日付情報
		reviewDate: v.number(), // レビュー投稿日（Unix timestamp）

		// タイムスタンプ
		createdAt: v.number(), // 作成日時
		updatedAt: v.number(), // 更新日時
	})
		.index("by_beds24PropertyId", ["beds24PropertyId"])
		.index("by_uniqueHash", ["uniqueHash"])
		.index("by_beds24PropertyId_and_reviewDate", [
			"beds24PropertyId",
			"reviewDate",
		]),

	// ユーザーと施設の多対多関係を管理する中間テーブル
	userProperties: defineTable({
		// Clerk User ID
		userId: v.string(),
		// 施設への参照
		propertyId: v.id("beds24Properties"),
		// 関連付け日時
		createdAt: v.number(),
	})
		// ユーザーIDでインデックス（ユーザーの施設一覧取得用）
		.index("by_userId", ["userId"])
		// 施設IDでインデックス（施設の管理ユーザー一覧取得用）
		.index("by_propertyId", ["propertyId"])
		// 複合インデックス（重複防止と特定の関連チェック用）
		.index("by_userId_and_propertyId", ["userId", "propertyId"]),

	// Beds24予約データテーブル
	beds24Bookings: defineTable({
		// 基本識別情報
		bookingId: v.string(), // Beds24予約ID（一意）
		propertyId: v.id("beds24Properties"), // 施設への参照
		userId: v.string(), // Clerk User ID

		// ゲスト情報（詳細）
		guestInfo: v.optional(
			v.object({
				firstName: v.optional(v.string()),
				lastName: v.optional(v.string()),
				email: v.optional(v.string()),
				phone: v.optional(v.string()),
				country: v.optional(v.string()),
				address: v.optional(v.string()),
				city: v.optional(v.string()),
				postCode: v.optional(v.string()),
			}),
		),

		// 日付情報
		checkIn: v.string(), // ISO形式の日付文字列
		checkOut: v.string(), // ISO形式の日付文字列
		bookingDate: v.optional(v.string()), // 予約日時（ISO datetime）

		// 予約情報
		status: v.union(
			v.literal("confirmed"),
			v.literal("cancelled"),
			v.literal("checked_in"),
			v.literal("checked_out"),
			v.literal("no_show"),
		),
		totalPrice: v.number(),
		currency: v.string(),

		// 金額詳細情報
		amountInfo: v.optional(
			v.object({
				totalPrice: v.number(),
				currency: v.string(),
				roomPrice: v.optional(v.number()),
				extras: v.optional(v.number()),
				taxes: v.optional(v.number()),
				commission: v.optional(v.number()),
				deposit: v.optional(v.number()),
				paid: v.optional(v.number()),
				balance: v.optional(v.number()),
			}),
		),

		// 部屋情報
		roomId: v.optional(v.string()),
		roomName: v.optional(v.string()),
		adults: v.optional(v.number()),
		children: v.optional(v.number()),

		// チャネル情報
		channel: v.optional(v.string()),
		channelId: v.optional(v.string()),

		// メモ・特記事項
		notes: v.optional(v.string()),
		internalNotes: v.optional(v.string()),

		// Beds24 APIレスポンス全体を保存
		data: v.any(),

		// 同期管理
		modifiedDate: v.number(), // Beds24側の更新日時
		lastSyncedAt: v.number(), // 最終同期日時
		createdAt: v.number(),
		updatedAt: v.number(),

		// ソフトデリート
		isDeleted: v.boolean(),
		deletedAt: v.optional(v.number()),
	})
		.index("by_bookingId", ["bookingId"])
		.index("by_property", ["propertyId"])
		.index("by_user", ["userId"])
		.index("by_property_and_checkIn", ["propertyId", "checkIn"]),

	// 今後追加される機能のテーブルは、以下のように整理します：
	// resources: defineTable({ ... }), // リソース機能
});
