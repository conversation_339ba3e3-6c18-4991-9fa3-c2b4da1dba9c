// Beds24 sync job types

import { v } from "convex/values";
import type { Id } from "../_generated/dataModel";

/**
 * 同期キュージョブタイプの定義
 *
 * @description Beds24との同期処理で使用される各種ジョブタイプを定義
 */
export const QueueJobType = {
	/** 施設データの同期 */
	SYNC_PROPERTIES: "sync_properties",
	/** レビューデータの同期 */
	SYNC_REVIEWS: "sync_reviews",
	/** Booking.comのスラッグをスクレイピング */
	SCRAPE_BOOKING_COM_SLUG: "scrape_booking_com_slug",
	/** 予約データの同期 */
	SYNC_BOOKINGS: "sync_bookings",
} as const;

export type QueueJobType = (typeof QueueJobType)[keyof typeof QueueJobType];

/**
 * ジョブ優先度の定義
 *
 * @description 同期ジョブの実行優先度を定義（数値が小さいほど優先度が高い）
 */
export const JobPriority = {
	/** 高優先度（値: 1） */
	HIGH: 1,
	/** 中優先度（値: 3） */
	MEDIUM: 3,
	/** 通常優先度（値: 5） */
	NORMAL: 5,
	/** 低優先度（値: 10） */
	LOW: 10,
} as const;

export type JobPriority = (typeof JobPriority)[keyof typeof JobPriority];

/**
 * 同期ステータスの定義
 *
 * @description 同期ジョブの実行状態を表すステータス値
 */
export const SyncStatus = {
	/** 実行待ち */
	PENDING: "pending",
	/** 実行中 */
	PROCESSING: "processing",
	/** 完了 */
	COMPLETED: "completed",
	/** 失敗 */
	FAILED: "failed",
} as const;

export type SyncStatus = (typeof SyncStatus)[keyof typeof SyncStatus];

/**
 * 同期履歴ステータスの定義
 *
 * @description 同期履歴に記録される最終的な実行結果ステータス
 */
export const SyncHistoryStatus = {
	/** 処理中 */
	PROCESSING: "processing",
	/** 成功 */
	SUCCESS: "success",
	/** 部分的成功（一部エラーあり） */
	PARTIAL_SUCCESS: "partial_success",
	/** 失敗 */
	FAILED: "failed",
} as const;

export type SyncHistoryStatus =
	(typeof SyncHistoryStatus)[keyof typeof SyncHistoryStatus];

/**
 * 同期エラーコードの定義
 *
 * @description 同期処理で発生する可能性のあるエラータイプ
 */
export const SyncErrorCode = {
	/** 不明なエラー */
	UNKNOWN_ERROR: "UNKNOWN_ERROR",
	/** APIエラー（Beds24 APIからのエラーレスポンス） */
	API_ERROR: "API_ERROR",
	/** 認証エラー（トークンの期限切れや無効など） */
	AUTH_ERROR: "AUTH_ERROR",
	/** ネットワークエラー（通信障害など） */
	NETWORK_ERROR: "NETWORK_ERROR",
} as const;

export type SyncErrorCode = (typeof SyncErrorCode)[keyof typeof SyncErrorCode];

// Model層で使用する型定義

/**
 * Booking.comスラッグ同期のメタデータ
 *
 * @description Booking.comのプロパティスラッグを同期する際に必要な情報
 */
export interface BookingComSlugSyncMetadata {
	/** 施設ID（Convexデータベース内のID） */
	propertyId: Id<"beds24Properties">;
	/** 施設名 */
	propertyName: string;
	/** Beds24のプロパティキー */
	beds24PropertyKey: string;
	/** ユーザーID（Clerk認証のユーザーID） */
	userId: string;
}

/**
 * 予約同期のメタデータ
 *
 * @description 予約データを同期する際に必要な情報とページネーション、再帰的処理の管理情報
 */
export interface BookingSyncMetadata {
	/** Beds24のプロパティID（数値） */
	propertyId: number;
	/** 施設名 */
	propertyName: string;
	/** ユーザーID（Clerk認証のユーザーID） */
	userId: string;
	/** 現在のページ番号 */
	pageNumber: number;
	/** 同期開始日（ISO形式: YYYY-MM-DD）- 初回同期時は未指定 */
	dateFrom?: string;
	/** 同期終了日（ISO形式: YYYY-MM-DD）- 初回同期時は未指定 */
	dateTo?: string;
	/** 次ページのURL（ページネーション用） */
	url?: string;
	/** 遡り処理中かどうかのフラグ（Phase 1: 再帰的日付範囲処理用） */
	isRecursiveBacktrack?: boolean;
	/** 遡り回数のカウンター（無限ループ防止用） */
	recursionDepth?: number;
	/** 最初の同期開始日（ISO形式）- 遡り処理の基準日 */
	originalStartDate?: string;
	/** 現在の日付範囲に未取得データがあるかどうか */
	hasMoreInDateRange?: boolean;
}

/**
 * 施設サマリー情報
 *
 * @description 施設の基本情報を含むサマリーデータ
 */
export type PropertySummary = {
	/** Convexデータベース内のユニークID */
	_id: Id<"beds24Properties">;
	/** レコード作成日時（Unixタイムスタンプ） */
	_creationTime: number;
	/** Beds24でのプロパティID（文字列） */
	beds24PropertyId: string;
	/** Beds24でのプロパティキー（URL生成等に使用） */
	beds24PropertyKey: string;
	/** 施設名 */
	name: string;
	/** 削除フラグ */
	isDeleted: boolean;
	/** 削除日時（Unixタイムスタンプ） */
	deletedAt?: number;
	/** 最終同期日時（Unixタイムスタンプ） */
	lastSyncedAt: number;
	/** 住所 */
	address?: string;
	/** 都市名 */
	city?: string;
	/** 国コード（2文字ISOコード） */
	country?: string;
	/** 通貨コード */
	currency?: string;
	/** タイムゾーン */
	timeZone?: string;
};

/**
 * 同期ステータス情報
 *
 * @description 同期処理の現在の状態と最新の同期履歴情報
 */
export type SyncStatusInfo = {
	/** 同期処理中かどうか */
	isProcessing: boolean;
	/** 最新の同期履歴 */
	lastSync?: {
		/** 同期履歴ID */
		_id: Id<"beds24SyncHistory">;
		/** 同期ステータス */
		status: string;
		/** 開始日時（Unixタイムスタンプ） */
		startedAt: number;
		/** 完了日時（Unixタイムスタンプ） */
		completedAt: number;
		/** 処理時間（ミリ秒） */
		duration: number;
		/** 処理対象アイテム総数 */
		totalItems: number;
		/** 成功件数 */
		successCount: number;
		/** 失敗件数 */
		failedCount: number;
	};
	/** 待機中のジョブ数 */
	pendingJobs: number;
};

/**
 * 接続ステータス情報
 *
 * @description Beds24 APIへの接続状態とトークンの有効性情報
 */
export type ConnectionStatus = {
	/** Beds24 APIへの接続状態 */
	isConnected: boolean;
	/** リフレッシュトークンの有無 */
	hasRefreshToken: boolean;
	/** 有効なアクセストークンの有無 */
	hasAccessToken: boolean;
	/** 最終トークン更新日時（Unixタイムスタンプ） */
	lastTokenRefresh?: number;
	/** トークン有効期限（Unixタイムスタンプ） */
	tokenExpiresAt?: number;
};

/**
 * 施設詳細情報
 *
 * @description 施設の完全な詳細データ（Beds24 APIから取得した生データを含む）
 */
export type PropertyDetails = {
	/** Convexデータベース内のユニークID */
	_id: Id<"beds24Properties">;
	/** レコード作成日時（Unixタイムスタンプ） */
	_creationTime: number;
	/** Beds24でのプロパティID（文字列） */
	beds24PropertyId: string;
	/** Beds24でのプロパティキー（URL生成等に使用） */
	beds24PropertyKey: string;
	/** Beds24 APIから取得した生データ */
	data: any;
	/** 最終同期日時（Unixタイムスタンプ） */
	lastSyncedAt: number;
	/** 削除フラグ */
	isDeleted: boolean;
	/** 削除日時（Unixタイムスタンプ） */
	deletedAt?: number;
};

/**
 * データベースから取得した生の施設データ
 *
 * @description Convexデータベースに保存されている施設データの完全な構造
 */
export type Beds24PropertyFromDB = {
	/** Convexデータベース内のユニークID */
	_id: Id<"beds24Properties">;
	/** レコード作成日時（Unixタイムスタンプ） */
	_creationTime: number;
	/** 所有者のユーザーID（Clerk認証） */
	userId?: string;
	/** Beds24でのプロパティID（文字列） */
	beds24PropertyId: string;
	/** Beds24でのプロパティキー（URL生成等に使用） */
	beds24PropertyKey: string;
	/** 施設名 */
	name: string;
	/** 施設タイプ */
	propertyType: string;
	/** 通貨コード */
	currency: string;
	/** 国コード（2文字ISOコード） */
	country?: string;
	/** 都市名 */
	city?: string;
	/** Beds24 APIから取得した生データ */
	data: any;
	/** 最終同期日時（Unixタイムスタンプ） */
	lastSyncedAt: number;
	/** レコード作成日時（Unixタイムスタンプ） */
	createdAt: number;
	/** レコード更新日時（Unixタイムスタンプ） */
	updatedAt: number;
	/** 削除フラグ */
	isDeleted: boolean;
	/** 削除日時（Unixタイムスタンプ） */
	deletedAt?: number;
};

// ========== Convex Validators ==========

/**
 * Beds24 APIプロパティのバリデーター
 *
 * @description Beds24 APIから取得する施設データの構造を検証するバリデーター
 */
export const beds24APIPropertyValidator = v.object({
	// 基本情報
	/** Beds24のプロパティID（数値） */
	id: v.number(),
	/** 施設名 */
	name: v.string(),
	/** 施設タイプ（例: hotel, apartment, houseなど） */
	propertyType: v.string(),
	/** 通貨コード（例: JPY, USD, EUR） */
	currency: v.string(),

	// 住所情報
	/** 住所 */
	address: v.optional(v.string()),
	/** 都市名 */
	city: v.optional(v.string()),
	/** 州・都道府県 */
	state: v.optional(v.string()),
	/** 国コード（2文字ISOコード - 例: "JP", "US"） */
	country: v.optional(v.string()),
	/** 郵便番号 */
	postcode: v.optional(v.string()),
	/** 緯度 */
	latitude: v.optional(v.number()),
	/** 経度 */
	longitude: v.optional(v.number()),

	// 連絡先情報
	/** 電話番号 */
	phone: v.optional(v.string()),
	/** 携帯電話番号 */
	mobile: v.optional(v.string()),
	/** メールアドレス */
	email: v.optional(v.string()),
	/** ウェブサイトURL */
	web: v.optional(v.string()),
	/** 担当者の名 */
	contactFirstName: v.optional(v.string()),
	/** 担当者の姓 */
	contactLastName: v.optional(v.string()),

	// チェックイン/アウト時間
	/** チェックイン開始時刻 */
	checkInStart: v.optional(v.string()),
	/** チェックイン終了時刻 */
	checkInEnd: v.optional(v.string()),
	/** チェックアウト終了時刻 */
	checkOutEnd: v.optional(v.string()),

	// アカウント情報
	/** アカウント関連情報 */
	account: v.optional(
		v.object({
			/** オーナーID */
			ownerId: v.optional(v.number()),
			/** ユニットステータス設定 */
			unitStatuses: v.optional(
				v.array(
					v.object({
						/** ステータステキスト */
						text: v.string(),
						/** ステータス色 */
						color: v.string(),
					}),
				),
			),
		}),
	),

	// 予約質問設定
	/** 予約時の質問設定 */
	bookingQuestions: v.optional(
		v.record(
			v.string(),
			v.object({
				/** 表示順序（Beds24 APIではnullの場合あり） */
				order: v.optional(v.union(v.number(), v.null())),
				/** 質問タイプ（Beds24 APIではnullの場合あり） */
				type: v.optional(v.union(v.string(), v.null())),
				/** 使用目的 */
				usage: v.string(),
			}),
		),
	),
});

/**
 * 同期キュージョブのバリデーター
 *
 * @description 同期キューに登録されるジョブデータの構造を検証するバリデーター
 */
export const syncQueueJobValidator = v.object({
	/** 同期キューテーブルのID */
	_id: v.id("syncQueue"),
	/** レコード作成日時（Unixタイムスタンプ） */
	_creationTime: v.number(),
	/** ユーザーID（Clerk認証） */
	userId: v.string(),
	/** ジョブタイプ（QueueJobTypeの値） */
	jobType: v.string(),
	/** ジョブ優先度（JobPriorityの値） */
	priority: v.number(),
	/** ジョブステータス（SyncStatusの値） */
	status: v.string(),
	/** 現在の試行回数 */
	attempts: v.number(),
	/** 最大試行回数 */
	maxAttempts: v.number(),
	/** 実行予定日時（Unixタイムスタンプ） */
	scheduledFor: v.number(),
	/** 実行開始日時（Unixタイムスタンプ） */
	startedAt: v.optional(v.number()),
	/** 実行完了日時（Unixタイムスタンプ） */
	completedAt: v.optional(v.number()),
	/** 最後のエラーメッセージ */
	lastError: v.optional(v.string()),
	/** 次のリトライ日時（Unixタイムスタンプ） */
	nextRetryAt: v.optional(v.number()),
	/** ジョブ固有のメタデータ */
	metadata: v.optional(v.any()),
	/** ジョブ実行結果 */
	result: v.optional(v.any()),
	/** レコード作成日時（Unixタイムスタンプ） */
	createdAt: v.number(),
	/** レコード更新日時（Unixタイムスタンプ） */
	updatedAt: v.number(),
});

/**
 * アクセストークン情報のバリデーター
 *
 * @description Beds24 APIアクセストークンの情報を検証するバリデーター
 */
export const tokenInfoValidator = v.object({
	/** ユーザーID（Clerk認証） */
	userId: v.string(),
	/** Beds24 APIアクセストークン */
	accessToken: v.string(),
	/** トークン有効期限（Unixタイムスタンプ） */
	expiresAt: v.number(),
	/** レコード作成日時（Unixタイムスタンプ） */
	createdAt: v.number(),
	/** レコード更新日時（Unixタイムスタンプ） */
	updatedAt: v.number(),
	/** 最終トークン更新日時（Unixタイムスタンプ） */
	lastRefreshedAt: v.number(),
});
