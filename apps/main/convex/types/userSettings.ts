/**
 * User Settings Type Definitions and Validators
 *
 * このファイルはuserSettings関連の型定義とConvexバリデータを集約し、
 * 他のConvex関数から再利用できるようにします。
 */

import { type Infer, v } from "convex/values";
import type { Doc } from "../_generated/dataModel";

// ===================================
// 定数定義
// ===================================

/**
 * サポートされているテーマの値の配列
 *
 * @description
 * アプリケーションで利用可能なテーマオプションを定義します。
 * - "light": ライトテーマ
 * - "dark": ダークテーマ
 * - "system": システム設定に従う
 *
 * @example
 * ```typescript
 * // テーマの選択肢を表示
 * THEME_VALUES.forEach(theme => {
 *   console.log(`Available theme: ${theme}`);
 * });
 * ```
 *
 * @constant
 */
export const THEME_VALUES = ["light", "dark", "system"] as const;

/**
 * テーマの型定義
 *
 * @description
 * THEME_VALUESから導出されるテーマの型です。
 * "light" | "dark" | "system" のいずれかの値を持ちます。
 *
 * @example
 * ```typescript
 * const currentTheme: Theme = "dark";
 * const invalidTheme: Theme = "blue"; // TypeScriptエラー
 * ```
 */
export type Theme = (typeof THEME_VALUES)[number];

/**
 * ユーザー設定のデフォルト値
 *
 * @description
 * 新規ユーザー作成時に適用されるデフォルト設定です。
 * テーマはシステム設定に従うように初期化されます。
 *
 * @example
 * ```typescript
 * // 新規ユーザーの設定を初期化
 * const newUserSettings = {
 *   ...DEFAULT_SETTINGS,
 *   userId: "user123"
 * };
 * ```
 *
 * @constant
 */
export const DEFAULT_SETTINGS = {
	theme: "system" as Theme,
} as const;

// ===================================
// 基本バリデーター定義
// ===================================

/**
 * Beds24設定のバリデータ
 *
 * @description
 * Beds24 APIとの連携に必要な設定を検証するバリデータです。
 * refreshTokenはオプショナルで、設定されている場合はAPIアクセスが可能になります。
 *
 * @example
 * ```typescript
 * // Beds24設定の検証
 * const settings = {
 *   refreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 * };
 * const validated = beds24SettingsValidator.parse(settings);
 * ```
 *
 * @see {@link https://beds24.com/api/} Beds24 API Documentation
 */
export const beds24SettingsValidator = v.object({
	refreshToken: v.optional(v.string()),
});

/**
 * テーマのバリデータ
 *
 * @description
 * テーマの値を検証するバリデータです。
 * "light"、"dark"、"system"のいずれかの値のみを許可します。
 *
 * @example
 * ```typescript
 * // テーマ値の検証
 * themeValidator.parse("dark"); // OK
 * themeValidator.parse("blue"); // ValidationError
 * ```
 *
 * @see {@link Theme} テーマの型定義
 */
export const themeValidator = v.union(
	v.literal("light"),
	v.literal("dark"),
	v.literal("system"),
);

/**
 * userSettings作成/更新時の引数用バリデータ
 *
 * @description
 * ユーザー設定の作成または更新時に使用される引数を検証するバリデータです。
 * すべてのフィールドはオプショナルで、指定されたフィールドのみが更新されます。
 *
 * @example
 * ```typescript
 * // 部分的な更新
 * const updateArgs = {
 *   theme: "dark" as const
 * };
 * const validated = userSettingsArgsValidator.parse(updateArgs);
 * ```
 *
 * @see {@link UserSettingsArgs} 引数の型定義
 */
export const userSettingsArgsValidator = v.object({
	theme: v.optional(themeValidator),
	beds24: v.optional(beds24SettingsValidator),
});

/**
 * userSettingsドキュメントの完全なバリデータ
 *
 * @description
 * データベースに保存されるuserSettingsドキュメントの完全な構造を検証するバリデータです。
 * Convexのシステムフィールド（_id、_creationTime）を含みます。
 *
 * @property _id - ドキュメントの一意識別子
 * @property _creationTime - Convexによる作成タイムスタンプ
 * @property userId - Clerkユーザーの識別子
 * @property theme - ユーザーが選択したテーマ
 * @property beds24 - Beds24 API連携設定（オプショナル）
 * @property has_beds24_token - Beds24トークンの有無フラグ
 * @property createdAt - 作成日時（Unix timestamp）
 * @property updatedAt - 最終更新日時（Unix timestamp）
 *
 * @example
 * ```typescript
 * // ドキュメントの検証
 * const doc = await ctx.db.get(id);
 * const validated = userSettingsValidator.parse(doc);
 * ```
 *
 * @see {@link UserSettingsDoc} ドキュメントの型定義
 */
export const userSettingsValidator = v.object({
	_id: v.id("userSettings"),
	_creationTime: v.number(),
	userId: v.string(),
	theme: themeValidator,
	beds24: v.optional(beds24SettingsValidator),
	has_beds24_token: v.boolean(),
	createdAt: v.number(),
	updatedAt: v.number(),
});

/**
 * query関数の返り値用バリデータ（nullを許可）
 *
 * @description
 * ユーザー設定取得クエリの返り値を検証するバリデータです。
 * ユーザー設定が存在しない場合はnullを返すことができます。
 *
 * @example
 * ```typescript
 * // クエリの返り値検証
 * export const getUserSettings = query({
 *   args: {},
 *   returns: userSettingsReturnValidator,
 *   handler: async (ctx) => {
 *     // ユーザー設定が見つからない場合はnullを返す
 *     return settings || null;
 *   }
 * });
 * ```
 *
 * @see {@link UserSettingsResult} 返り値の型定義
 */
export const userSettingsReturnValidator = v.union(
	userSettingsValidator,
	v.null(),
);

/**
 * Beds24設定更新の返り値用バリデータ
 *
 * @description
 * Beds24設定の更新結果を検証するバリデータです。
 * 更新または新規作成の結果を示すフラグを含みます。
 *
 * @property id - 更新されたuserSettingsドキュメントのID
 * @property wasUpdated - 既存の設定が更新された場合true
 * @property wasCreated - 新規に設定が作成された場合true
 *
 * @example
 * ```typescript
 * // 更新結果の返却
 * return {
 *   id: settingsId,
 *   wasUpdated: true,
 *   wasCreated: false
 * };
 * ```
 *
 * @see {@link Beds24UpdateResult} 更新結果の型定義
 */
export const beds24UpdateResultValidator = v.object({
	id: v.id("userSettings"),
	wasUpdated: v.boolean(),
	wasCreated: v.boolean(),
});

// ===================================
// Infer型を使用した型定義
// ===================================

/**
 * Beds24設定の型（バリデーターから自動推論）
 *
 * @description
 * beds24SettingsValidatorから自動的に推論される型定義です。
 * Beds24 APIとの連携に必要な設定情報を表現します。
 *
 * @example
 * ```typescript
 * const beds24Config: Beds24Settings = {
 *   refreshToken: "your-refresh-token"
 * };
 * ```
 *
 * @see {@link beds24SettingsValidator} 対応するバリデータ
 */
export type Beds24Settings = Infer<typeof beds24SettingsValidator>;

/**
 * userSettings引数の型（バリデーターから自動推論）
 *
 * @description
 * userSettingsArgsValidatorから自動的に推論される型定義です。
 * ユーザー設定の作成・更新時に使用される引数の型を表現します。
 *
 * @example
 * ```typescript
 * const updateArgs: UserSettingsArgs = {
 *   theme: "dark",
 *   beds24: { refreshToken: "new-token" }
 * };
 * ```
 *
 * @see {@link userSettingsArgsValidator} 対応するバリデータ
 */
export type UserSettingsArgs = Infer<typeof userSettingsArgsValidator>;

/**
 * userSettingsドキュメントの型（Convexのビルトイン型を使用）
 *
 * @description
 * Convexのスキーマから自動生成されるuserSettingsテーブルのドキュメント型です。
 * この型はschema.tsで定義されたテーブル構造と完全に一致します。
 *
 * @example
 * ```typescript
 * // データベースから取得したドキュメントの型注釈
 * const settings: UserSettingsDoc = await ctx.db
 *   .query("userSettings")
 *   .filter(q => q.eq(q.field("userId"), userId))
 *   .unique();
 * ```
 *
 * @see {@link userSettingsValidator} 対応するバリデータ
 */
export type UserSettingsDoc = Doc<"userSettings">;

/**
 * userSettingsクエリ結果の型（バリデーターから自動推論）
 *
 * @description
 * userSettingsReturnValidatorから自動的に推論される型定義です。
 * クエリ結果としてuserSettingsドキュメントまたはnullを表現します。
 *
 * @example
 * ```typescript
 * const result: UserSettingsResult = await getUserSettings(ctx);
 * if (result) {
 *   console.log(`Current theme: ${result.theme}`);
 * } else {
 *   console.log("Settings not found");
 * }
 * ```
 *
 * @see {@link userSettingsReturnValidator} 対応するバリデータ
 */
export type UserSettingsResult = Infer<typeof userSettingsReturnValidator>;

/**
 * Beds24更新結果の型（バリデーターから自動推論）
 *
 * @description
 * beds24UpdateResultValidatorから自動的に推論される型定義です。
 * Beds24設定の更新操作の結果を表現します。
 *
 * @example
 * ```typescript
 * const result: Beds24UpdateResult = await updateBeds24Settings(ctx, {
 *   refreshToken: "new-token"
 * });
 *
 * if (result.wasCreated) {
 *   console.log("New settings created");
 * } else if (result.wasUpdated) {
 *   console.log("Settings updated");
 * }
 * ```
 *
 * @see {@link beds24UpdateResultValidator} 対応するバリデータ
 */
export type Beds24UpdateResult = Infer<typeof beds24UpdateResultValidator>;

/**
 * システムフィールドを除いたuserSettings型（新規作成時に使用）
 *
 * @description
 * Convexのシステムフィールド（_id、_creationTime）を除外したuserSettings型です。
 * 新規ドキュメント作成時に必要なフィールドを表現します。
 *
 * @example
 * ```typescript
 * // 新規ドキュメントの作成
 * const newSettings: UserSettingsWithoutSystemFields = {
 *   userId: "user123",
 *   theme: "system",
 *   has_beds24_token: false,
 *   createdAt: Date.now(),
 *   updatedAt: Date.now()
 * };
 *
 * const id = await ctx.db.insert("userSettings", newSettings);
 * ```
 */
export type UserSettingsWithoutSystemFields = Omit<
	UserSettingsDoc,
	"_id" | "_creationTime"
>;

/**
 * 更新可能なフィールドの型
 *
 * @description
 * ユーザーが更新可能なuserSettingsのフィールド名を表現する型です。
 * "theme" | "beds24" のいずれかの値を持ちます。
 *
 * @example
 * ```typescript
 * // フィールド名の型安全な使用
 * const fieldToUpdate: UpdatableUserSettingsFields = "theme";
 *
 * // 動的なフィールド更新
 * function updateField(
 *   field: UpdatableUserSettingsFields,
 *   value: any
 * ) {
 *   // フィールド名が型安全に保証される
 * }
 * ```
 */
export type UpdatableUserSettingsFields = keyof Pick<
	UserSettingsWithoutSystemFields,
	"theme" | "beds24"
>;

/**
 * フィールド更新用のバリデータ
 *
 * @description
 * 個別フィールドの更新に使用されるバリデータです。
 * フィールド名とその値のペアを型安全に検証します。
 *
 * @note
 * Convexはmutationのargsとしてv.object()またはv.any()のみをサポートするため、
 * unionをオブジェクトでラップしています
 *
 * @example
 * ```typescript
 * // テーマの更新
 * const themeUpdate = {
 *   update: {
 *     field: "theme" as const,
 *     value: "dark" as const
 *   }
 * };
 *
 * // Beds24設定の更新
 * const beds24Update = {
 *   update: {
 *     field: "beds24" as const,
 *     value: { refreshToken: "new-token" }
 *   }
 * };
 *
 * // バリデータによる検証
 * const validated = updateFieldArgsValidator.parse(themeUpdate);
 * ```
 *
 * @see {@link UpdatableUserSettingsFields} 更新可能なフィールド名の型
 */
export const updateFieldArgsValidator = v.object({
	update: v.union(
		v.object({
			field: v.literal("theme"),
			value: themeValidator,
		}),
		v.object({
			field: v.literal("beds24"),
			value: beds24SettingsValidator,
		}),
	),
});
