/**
 * Type declarations for external modules used in Convex functions
 *
 * Convex functions run in a separate environment and need explicit type declarations
 * for modules that are dynamically imported.
 */

// Cheerio types for HTML parsing
declare module "cheerio" {
	export interface Cheerio<T = any> {
		length: number;
		[index: number]: T;
		first(): Cheerio<T>;
		attr(name: string): string | undefined;
		text(): string;
		html(): string | null;
		find(selector: string): Cheerio<T>;
		each(func: (index: number, element: T) => void): Cheerio<T>;
		map<TReturn>(
			func: (index: number, element: T) => TReturn,
		): Cheerio<TReturn>;
		get(): T[];
		parent(): Cheerio<T>;
		prop(name: string): any;
		toArray(): T[];
		children(selector?: string): Cheerio<T>;
		index(element?: any): number;
	}

	export interface CheerioAPI {
		(selector: string): Cheerio;
		(element: any): Cheerio;
		load(html: string): CheerioAPI;
	}

	export interface CheerioStatic {
		load(html: string): CheerioAPI;
	}

	export type Element = any;

	const cheerio: CheerioStatic & {
		default: CheerioStatic;
		load: (html: string) => CheerioAPI;
	};

	export default cheerio;
	export const load: typeof cheerio.load;
}

// Luxon types for date/time handling
declare module "luxon" {
	export class DateTime {
		static now(): DateTime;
		static fromISO(text: string, opts?: DateTimeOptions): DateTime;
		static fromJSDate(date: Date, opts?: DateTimeOptions): DateTime;
		static fromMillis(milliseconds: number, opts?: DateTimeOptions): DateTime;
		static fromFormat(
			text: string,
			fmt: string,
			opts?: DateTimeOptions,
		): DateTime;
		static local(): DateTime;
		static local(
			year: number,
			month?: number,
			day?: number,
			hour?: number,
			minute?: number,
			second?: number,
			millisecond?: number,
		): DateTime;
		static local(
			year: number,
			month: number,
			day: number,
			hour: number,
			minute: number,
			second: number,
			millisecond: number,
			opts: DateTimeOptions,
		): DateTime;

		year: number;
		month: number;
		day: number;
		hour: number;
		minute: number;
		second: number;
		isValid: boolean;

		toISO(): string;
		toISODate(): string;
		toFormat(fmt: string): string;
		toJSDate(): Date;
		valueOf(): number;
		toMillis(): number;

		plus(duration: any): DateTime;
		minus(duration: any): DateTime;
		diff(other: DateTime, unit?: string): any;
	}

	export interface DateTimeOptions {
		zone?: string;
		locale?: string;
	}
}

// Additional namespace declarations for TypeScript
declare namespace cheerio {
	export type Element = any;
	export type CheerioAPI = import("cheerio").CheerioAPI;
}
