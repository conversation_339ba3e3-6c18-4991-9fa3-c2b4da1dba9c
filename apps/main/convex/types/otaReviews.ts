/**
 * OTA Reviews Type Definitions and Validators
 *
 * このファイルはotaReviews関連の型定義とConvexバリデータを集約し、
 * 他のConvex関数から再利用できるようにします。
 *
 * ベストプラクティス:
 * - バリデータから型を自動導出（Infer使用）
 * - 重複する型定義を避ける
 * - WithoutSystemFieldsで明示的な型定義
 */

import type { WithoutSystemFields } from "convex/server";
import type { Infer } from "convex/values";
import { v } from "convex/values";
import type { Doc } from "../_generated/dataModel";

/**
 * レビューの構造化コンテンツバリデータ
 *
 * @description レビューの肯定的・否定的な内容を構造化して保存するためのバリデータ。
 * OTAのレビューを分析しやすい形式で保存し、センチメント分析に活用する。
 *
 * @property {string} [positive] - 肯定的なコメント内容
 * @property {string} [negative] - 否定的なコメント内容
 *
 * @example
 * ```typescript
 * const structured = {
 *   positive: "スタッフが親切で、立地も良い",
 *   negative: "部屋が少し狭い"
 * }
 * ```
 */
export const reviewContentStructuredValidator = v.object({
	positive: v.optional(v.string()),
	negative: v.optional(v.string()),
});

/**
 * 基本的なレビュー情報のバリデータ（システムフィールドを含む）
 *
 * @description OTAレビューの基本情報を定義するバリデータ。
 * システムフィールド（_id、_creationTime）を含む完全な形式。
 *
 * @property {Id<"otaReviews">} _id - レビューの一意識別子
 * @property {number} _creationTime - Convexによる作成タイムスタンプ
 * @property {string} [userId] - ユーザーID（オプション）
 * @property {Id<"beds24Properties">} beds24PropertyId - 関連するBeds24プロパティのID
 * @property {Id<"otaMaster">} otaId - OTAマスターテーブルのID
 * @property {string} uniqueHash - 重複防止用のユニークハッシュ値
 * @property {number} score - レビューのスコア（通常1-10）
 * @property {string} [title] - レビューのタイトル
 * @property {string} reviewContent - レビューの本文
 * @property {ReviewContentStructured} [reviewContentStructured] - 構造化されたレビュー内容
 * @property {string} reviewerName - レビュアーの名前
 * @property {string} [reviewerCountry] - レビュアーの国
 * @property {number} reviewDate - レビュー日時（UnixタイムスタンプMS）
 * @property {number} createdAt - 作成日時（UnixタイムスタンプMS）
 * @property {number} updatedAt - 更新日時（UnixタイムスタンプMS）
 */
export const baseReviewValidator = v.object({
	_id: v.id("otaReviews"),
	_creationTime: v.number(),
	userId: v.optional(v.string()),
	beds24PropertyId: v.id("beds24Properties"),
	otaId: v.id("otaMaster"),
	uniqueHash: v.string(),
	score: v.number(),
	title: v.optional(v.string()),
	reviewContent: v.string(),
	reviewContentStructured: v.optional(reviewContentStructuredValidator),
	reviewerName: v.string(),
	reviewerCountry: v.optional(v.string()),
	reviewDate: v.number(),
	createdAt: v.number(),
	updatedAt: v.number(),
});

/**
 * 関連情報を含むレビューバリデータ（一覧表示用）
 *
 * @description レビュー情報に関連するプロパティやOTA情報を結合した拡張版バリデータ。
 * 管理画面での一覧表示やレポート生成に使用される。
 *
 * @extends baseReviewValidator
 * @property {string} propertyName - プロパティ名（beds24Propertiesから結合）
 * @property {string} otaName - OTAの正式名称（otaMasterから結合）
 * @property {string} otaShortName - OTAの略称（otaMasterから結合）
 *
 * @example
 * ```typescript
 * const reviewWithDetails = {
 *   ...baseReview,
 *   propertyName: "ホテル東京",
 *   otaName: "Booking.com",
 *   otaShortName: "BDC"
 * }
 * ```
 */
export const reviewWithDetailsValidator = v.object({
	...baseReviewValidator.fields,
	// 結合情報
	propertyName: v.string(),
	otaName: v.string(),
	otaShortName: v.string(),
});

/**
 * 軽量なレビュー情報バリデータ（一覧表示用に最適化）
 *
 * @description パフォーマンスを考慮して最小限の情報に絞ったレビューバリデータ。
 * uniqueHashなどの内部的なフィールドを除外し、表示に必要な情報のみを含む。
 *
 * @property {Id<"otaReviews">} _id - レビューの一意識別子
 * @property {number} _creationTime - Convexによる作成タイムスタンプ
 * @property {string} [userId] - ユーザーID（オプション）
 * @property {Id<"beds24Properties">} beds24PropertyId - 関連するBeds24プロパティのID
 * @property {Id<"otaMaster">} otaId - OTAマスターテーブルのID
 * @property {number} score - レビューのスコア
 * @property {string} [title] - レビューのタイトル
 * @property {string} reviewContent - レビューの本文
 * @property {ReviewContentStructured} [reviewContentStructured] - 構造化されたレビュー内容
 * @property {string} reviewerName - レビュアーの名前
 * @property {string} [reviewerCountry] - レビュアーの国
 * @property {number} reviewDate - レビュー日時
 * @property {number} createdAt - 作成日時
 * @property {number} updatedAt - 更新日時
 * @property {string} propertyName - プロパティ名（結合情報）
 * @property {string} otaName - OTAの正式名称（結合情報）
 * @property {string} otaShortName - OTAの略称（結合情報）
 */
export const reviewSummaryValidator = v.object({
	_id: v.id("otaReviews"),
	_creationTime: v.number(),
	userId: v.optional(v.string()),
	beds24PropertyId: v.id("beds24Properties"),
	otaId: v.id("otaMaster"),
	score: v.number(),
	title: v.optional(v.string()),
	reviewContent: v.string(),
	reviewContentStructured: v.optional(reviewContentStructuredValidator),
	reviewerName: v.string(),
	reviewerCountry: v.optional(v.string()),
	reviewDate: v.number(),
	createdAt: v.number(),
	updatedAt: v.number(),
	// 結合情報
	propertyName: v.string(),
	otaName: v.string(),
	otaShortName: v.string(),
});

/**
 * レビュー統計情報のバリデータ
 *
 * @description レビューの集計・分析結果を表すバリデータ。
 * ダッシュボードやレポート機能で使用される統計情報を定義。
 *
 * @property {number} totalCount - レビューの総数
 * @property {number} averageScore - 平均スコア
 * @property {Record<string, number>} scoreDistribution - スコア別の分布（キー：スコア、値：件数）
 * @property {Record<string, number>} countryDistribution - 国別の分布（キー：国コード、値：件数）
 * @property {number} recentReviewsCount - 直近30日間のレビュー数
 *
 * @example
 * ```typescript
 * const stats = {
 *   totalCount: 150,
 *   averageScore: 8.5,
 *   scoreDistribution: { "8": 45, "9": 60, "10": 30, "7": 15 },
 *   countryDistribution: { "JP": 80, "US": 40, "CN": 30 },
 *   recentReviewsCount: 25
 * }
 * ```
 */
export const reviewStatsValidator = v.object({
	totalCount: v.number(),
	averageScore: v.number(),
	scoreDistribution: v.record(v.string(), v.number()), // 動的キー対応
	countryDistribution: v.record(v.string(), v.number()),
	recentReviewsCount: v.number(), // 過去30日間のレビュー数
});

/**
 * ページネーション結果のバリデータ
 *
 * @description Convexのページネーション機能を使用したレビュー一覧の結果を定義。
 * 大量のレビューを効率的に取得・表示するために使用される。
 *
 * @property {ReviewWithDetails[]} page - 現在のページのレビュー配列
 * @property {boolean} isDone - 全てのデータを取得済みかどうか
 * @property {string | null} continueCursor - 次のページを取得するためのカーソル
 *
 * @example
 * ```typescript
 * const paginatedResult = {
 *   page: [review1, review2, review3],
 *   isDone: false,
 *   continueCursor: "eyJpZCI6IjEyMzQ1Njc4OTAifQ=="
 * }
 * ```
 */
export const paginatedReviewsValidator = v.object({
	page: v.array(reviewWithDetailsValidator),
	isDone: v.boolean(),
	continueCursor: v.union(v.string(), v.null()),
});

/**
 * アクセス権限チェックの結果バリデータ
 *
 * @description ユーザーが特定のレビューやプロパティにアクセスできるかをチェックした結果。
 * セキュリティとデータ保護のために使用される。
 *
 * @property {boolean} hasAccess - アクセス権限があるかどうか
 * @property {string} [reason] - アクセス拒否の理由（権限がない場合）
 *
 * @example
 * ```typescript
 * // アクセス許可
 * { hasAccess: true }
 *
 * // アクセス拒否
 * { hasAccess: false, reason: "このプロパティの管理権限がありません" }
 * ```
 */
export const accessCheckResultValidator = v.object({
	hasAccess: v.boolean(),
	reason: v.optional(v.string()),
});

/**
 * レビューフィルタオプションのバリデータ
 *
 * @description レビュー一覧を取得する際のフィルタリング条件を定義。
 * プロパティIDやOTA IDでレビューを絞り込むために使用される。
 *
 * @property {Id<"beds24Properties">} [propertyId] - フィルタリング対象のプロパティID
 * @property {Id<"otaMaster">} [otaId] - フィルタリング対象のOTA ID
 *
 * @example
 * ```typescript
 * // 特定のプロパティのレビューのみ取得
 * { propertyId: "jx7abc..." }
 *
 * // 特定のOTAのレビューのみ取得
 * { otaId: "jx7def..." }
 *
 * // 両方の条件でフィルタリング
 * { propertyId: "jx7abc...", otaId: "jx7def..." }
 * ```
 */
export const reviewFilterOptionsValidator = v.object({
	propertyId: v.optional(v.id("beds24Properties")),
	otaId: v.optional(v.id("otaMaster")),
});

/**
 * リスト取得の共通オプションバリデータ
 *
 * @description レビュー一覧を取得する際の共通オプションを定義。
 * 取得件数の制限などを指定するために使用される。
 *
 * @property {number} [limit] - 取得する最大件数
 *
 * @example
 * ```typescript
 * // 最新10件のレビューを取得
 * { limit: 10 }
 * ```
 */
export const listOptionsValidator = v.object({
	limit: v.optional(v.number()),
});

// ====================
// 型定義（すべてバリデータから自動導出）
// ====================

/**
 * Docから取得したレビューの型（システムフィールド含む）
 *
 * @description Convexデータベースから直接取得したレビューの完全な型定義。
 * _id、_creationTimeなどのシステムフィールドを含む。
 *
 * @example
 * ```typescript
 * const review: BaseReview = await ctx.db.get(reviewId);
 * console.log(review._id); // "jx7abc..."
 * console.log(review.score); // 8.5
 * ```
 */
export type BaseReview = Doc<"otaReviews">;

/**
 * バリデータから自動導出される型定義
 *
 * @description 各バリデータから自動的に導出される型定義。
 * バリデータと型の一貫性を保証し、型安全性を向上させる。
 */

/** @type {ReviewContentStructured} - 構造化されたレビュー内容の型 */
export type ReviewContentStructured = Infer<
	typeof reviewContentStructuredValidator
>;

/** @type {ReviewWithDetails} - 詳細情報を含むレビューの型 */
export type ReviewWithDetails = Infer<typeof reviewWithDetailsValidator>;

/** @type {ReviewSummary} - 軽量なレビューサマリーの型 */
export type ReviewSummary = Infer<typeof reviewSummaryValidator>;

/** @type {ReviewStats} - レビュー統計情報の型 */
export type ReviewStats = Infer<typeof reviewStatsValidator>;

/** @type {PaginatedReviews} - ページネーション付きレビュー結果の型 */
export type PaginatedReviews = Infer<typeof paginatedReviewsValidator>;

/** @type {AccessCheckResult} - アクセス権限チェック結果の型 */
export type AccessCheckResult = Infer<typeof accessCheckResultValidator>;

/** @type {ReviewFilterOptions} - レビューフィルタオプションの型 */
export type ReviewFilterOptions = Infer<typeof reviewFilterOptionsValidator>;

/**
 * システムフィールドを除外したレビューの入力型
 *
 * @description 新規レビュー作成時に使用する入力型。
 * システムフィールド（_id、_creationTime）を除外し、
 * クライアントから送信されるデータの型を定義。
 *
 * @example
 * ```typescript
 * const newReview: ReviewInput = {
 *   userId: "user123",
 *   beds24PropertyId: "jx7abc...",
 *   otaId: "jx7def...",
 *   uniqueHash: "hash123",
 *   score: 9,
 *   reviewContent: "素晴らしい滞在でした",
 *   reviewerName: "山田太郎",
 *   reviewDate: Date.now(),
 *   createdAt: Date.now(),
 *   updatedAt: Date.now()
 * };
 * ```
 */
export type ReviewInput = WithoutSystemFields<
	Omit<BaseReview, "_id" | "_creationTime">
>;

/**
 * 部分的なレビュー更新用の型
 *
 * @description 既存レビューの部分的な更新に使用する型。
 * システムフィールドとuniqueHash（変更不可）を除外し、
 * 更新可能なフィールドのみを含む。全フィールドがオプショナル。
 *
 * @example
 * ```typescript
 * const updateData: ReviewUpdate = {
 *   score: 10, // スコアのみ更新
 *   updatedAt: Date.now()
 * };
 *
 * // 複数フィールドの更新
 * const multiUpdate: ReviewUpdate = {
 *   reviewContentStructured: {
 *     positive: "更新された肯定的コメント",
 *     negative: "更新された否定的コメント"
 *   },
 *   updatedAt: Date.now()
 * };
 * ```
 */
export type ReviewUpdate = Partial<
	WithoutSystemFields<Omit<BaseReview, "_id" | "_creationTime" | "uniqueHash">>
>;
