/**
 * レビュー同期関連の型定義
 *
 * このファイルは、レビュー同期機能に必要な型定義を提供します。
 * ScrapingAntクライアント、レビューパーサー、同期タスクなどの型を含みます。
 */

import type { Infer } from "convex/values";
import { v } from "convex/values";
import type { Doc } from "../_generated/dataModel";

// ================================
// ScrapingAnt関連の型定義
// ================================

/**
 * ScrapingAntクライアントのコンストラクタオプション
 */
export interface ScrapingAntOptions {
	apiKey: string;
	maxRetries?: number;
	minDelayBetweenRetriesMillis?: number;
	timeoutSecs?: number;
}

/**
 * ScrapingAntのscrapeメソッドのパラメータ
 */
export interface ScrapingAntScrapeParameters {
	/** ブラウザモードを使用するかどうか */
	browser?: boolean;
	/** リクエストに含めるCookie */
	cookies?: string;
	/** カスタムヘッダー */
	headers?: Record<string, string>;
	/** 実行するJavaScriptスニペット */
	js_snippet?: string;
	/** プロキシタイプ: 'datacenter' または 'residential' */
	proxy_type?: "datacenter" | "residential";
	/** プロキシの国コード */
	proxy_country?: string;
	/** 待機するCSSセレクタ */
	wait_for_selector?: string;
	/** テキストのみを返すかどうか */
	return_text?: boolean;
}

/**
 * ScrapingAntのレスポンス
 */
export interface ScrapingAntResponse {
	/** スクレイピングしたコンテンツ */
	content: string;
	/** レスポンスのCookie */
	cookies?: string;
	/** HTTPステータスコード */
	statusCode?: number;
	/** レスポンスヘッダー */
	headers?: Record<string, string>;
	/** 拡張レスポンス用の追加フィールド */
	[key: string]: unknown;
}

/**
 * ScrapingAntのAPIエラー
 */
export class ScrapingAntApiError extends Error {
	statusCode: number;
	httpMethod?: string;

	constructor(message: string, statusCode: number, httpMethod?: string) {
		super(message);
		this.name = "ScrapingAntApiError";
		this.statusCode = statusCode;
		this.httpMethod = httpMethod;
	}
}

/**
 * ScrapingAntクライアントのインターフェース
 * 注：実際のクライアントはJavaScriptライブラリのため、
 * コンストラクタシグネチャは型定義として表現しません
 */
export interface ScrapingAntClientInstance {
	scrape(
		url: string,
		parameters?: ScrapingAntScrapeParameters,
	): Promise<ScrapingAntResponse>;
}

/**
 * ScrapingAntクライアントのコンストラクタ型
 */
export interface ScrapingAntClientConstructor {
	new (options: ScrapingAntOptions): ScrapingAntClientInstance;
}

// ================================
// レビューパーサー関連の型定義
// ================================

/**
 * パースされたレビューデータ
 */
export interface ParsedReview {
	/** レビューのユニークID（OTA内での識別子） */
	reviewId: string;
	/** レビュースコア（0-10または0-100のスケール） */
	score: number;
	/** レビュータイトル */
	title?: string;
	/** レビュー本文 */
	content: string;
	/** 構造化されたレビュー内容 */
	contentStructured?: {
		positive?: string;
		negative?: string;
	};
	/** レビュアー名 */
	reviewerName: string;
	/** レビュアーの国 */
	reviewerCountry?: string;
	/** レビュー日時（Unix timestamp） */
	reviewDate: number;
}

/**
 * パース結果
 */
export interface ParseResult {
	/** パースされたレビューの配列 */
	reviews: ParsedReview[];
	/** 次ページのURL（存在する場合） */
	nextPageUrl?: string;
	/** 総レビュー数（取得可能な場合） */
	totalReviews?: number;
}

/**
 * OTAタイプ
 */
export type OTAType = "booking.com" | "expedia" | "hotels.com" | "agoda";

/**
 * レビューパーサーのインターフェース
 */
export interface ReviewParser {
	/**
	 * HTMLからレビューをパース
	 */
	parseHtml(html: string, otaType: OTAType, url: string): ParseResult;
	/**
	 * レビューのハッシュを生成（重複チェック用）
	 */
	generateHash(
		review: ParsedReview,
		propertyId: string,
		otaId: string,
	): Promise<string>;
}

// ================================
// 同期タスク関連の型定義
// ================================

/**
 * レビュー同期タスクのメタデータのバリデータ
 */
export const reviewSyncMetadataValidator = v.object({
	/** スクレイピング対象のURL */
	url: v.string(),
	/** OTA ID */
	otaId: v.optional(v.id("otaMaster")),
	/** OTAタイプ */
	otaType: v.union(
		v.literal("booking.com"),
		v.literal("expedia"),
		v.literal("hotels.com"),
		v.literal("agoda"),
	),
	/** ページ番号 */
	pageNumber: v.number(),
	/** 最大ページ数（設定されている場合） */
	maxPages: v.optional(v.number()),
	/** プロパティID */
	propertyId: v.id("beds24Properties"),
});

/**
 * レビュー同期タスクのメタデータ
 */
export type ReviewSyncMetadata = Infer<typeof reviewSyncMetadataValidator>;

/**
 * 同期タスクの実行結果
 */
export interface SyncTaskResult {
	/** 処理されたレビュー数 */
	processedCount: number;
	/** 新規作成されたレビュー数 */
	createdCount: number;
	/** 更新されたレビュー数 */
	updatedCount: number;
	/** エラーが発生したレビュー数 */
	errorCount: number;
	/** 次ページが存在するかどうか */
	hasNextPage: boolean;
	/** エラーメッセージ（エラーが発生した場合） */
	error?: string;
}

// ================================
// ユーティリティ型
// ================================

/**
 * 同期キューのタスク型（型安全性のため）
 */
export interface ReviewSyncTask extends Doc<"syncQueue"> {
	jobType: "sync_reviews";
	metadata: ReviewSyncMetadata;
}

/**
 * レビュー同期タスクかどうかをチェックする型ガード
 */
export function isReviewSyncTask(
	task: Doc<"syncQueue">,
): task is ReviewSyncTask {
	return task.jobType === "sync_reviews";
}
