/**
 * Beds24 Bookings Type Definitions and Validators
 *
 * このファイルはbeds24Bookings関連の型定義とConvexバリデータを集約し、
 * 他のConvex関数から再利用できるようにします。
 *
 * ベストプラクティス:
 * - バリデータから型を自動導出（Infer使用）
 * - 重複する型定義を避ける
 * - WithoutSystemFieldsで明示的な型定義
 */

import type { WithoutSystemFields } from "convex/server";
import type { Infer } from "convex/values";
import { v } from "convex/values";
import type { Doc } from "../_generated/dataModel";

/**
 * 予約ステータスの定義
 *
 * @description Beds24の予約状態を表すステータス値。予約のライフサイクル全体を管理
 */
export const BookingStatus = {
	/** 確定済み - ゲストが予約を確定し、宿泊予定 */
	CONFIRMED: "confirmed",
	/** キャンセル済み - 予約がキャンセルされた状態 */
	CANCELLED: "cancelled",
	/** チェックイン済み - ゲストが到着し、チェックイン完了 */
	CHECKED_IN: "checked_in",
	/** チェックアウト済み - ゲストが退室し、滞在完了 */
	CHECKED_OUT: "checked_out",
	/** ノーショー - 予約があったが、ゲストが現れなかった */
	NO_SHOW: "no_show",
} as const;

export type BookingStatus = (typeof BookingStatus)[keyof typeof BookingStatus];

/**
 * 予約ステータスのバリデータ
 *
 * @description BookingStatus定数に対応するConvexバリデータ。
 * 予約のステータスを検証し、型安全性を保証
 */
export const bookingStatusValidator = v.union(
	v.literal("confirmed"),
	v.literal("cancelled"),
	v.literal("checked_in"),
	v.literal("checked_out"),
	v.literal("no_show"),
);

/**
 * ゲスト情報のバリデータ
 *
 * @description 予約したゲストの個人情報を管理するためのバリデータ。
 * すべてのフィールドはオプショナルで、プライバシー設定により一部情報が欠落する場合がある
 */
export const guestInfoValidator = v.object({
	/** ゲストの名前（ファーストネーム） */
	firstName: v.optional(v.string()),
	/** ゲストの姓（ラストネーム） */
	lastName: v.optional(v.string()),
	/** 連絡先メールアドレス */
	email: v.optional(v.string()),
	/** 連絡先電話番号 */
	phone: v.optional(v.string()),
	/** 国籍・居住国（ISO国コードまたは国名） */
	country: v.optional(v.string()),
	/** 住所（番地・通り名など） */
	address: v.optional(v.string()),
	/** 都市名 */
	city: v.optional(v.string()),
	/** 郵便番号 */
	postCode: v.optional(v.string()),
});

/**
 * 金額情報のバリデータ
 *
 * @description 予約に関連する全ての金銭情報を管理。
 * 料金の内訳、支払い状況、手数料などの財務データを包括的に扱う
 */
export const amountInfoValidator = v.object({
	/** 総額（すべての料金を含む最終金額） */
	totalPrice: v.number(),
	/** 通貨コード（ISO 4217形式: JPY, USD, EUR等） */
	currency: v.string(),
	/** 部屋料金（基本宿泊料金） */
	roomPrice: v.optional(v.number()),
	/** 追加料金（朝食、エキストラベッド等） */
	extras: v.optional(v.number()),
	/** 税金（消費税、宿泊税等） */
	taxes: v.optional(v.number()),
	/** OTA手数料（予約サイトへの手数料） */
	commission: v.optional(v.number()),
	/** デポジット（保証金） */
	deposit: v.optional(v.number()),
	/** 支払済み金額 */
	paid: v.optional(v.number()),
	/** 残高（未払い金額） */
	balance: v.optional(v.number()),
});

/**
 * 予約の基本情報バリデータ（システムフィールドなし）
 *
 * @description Beds24予約データの核となるバリデータ。
 * 予約に関する全ての必要情報を定義し、データの整合性を保証。
 * システムフィールド（_id, _creationTime）は含まない
 */
export const bookingDataValidator = v.object({
	bookingId: v.string(), // Beds24の予約ID
	propertyId: v.id("beds24Properties"),
	userId: v.string(),

	// ゲスト情報
	guestInfo: v.optional(guestInfoValidator),

	// 日付情報
	checkIn: v.string(), // ISO date string (YYYY-MM-DD)
	checkOut: v.string(), // ISO date string (YYYY-MM-DD)
	bookingDate: v.optional(v.string()), // ISO datetime string

	// ステータス
	status: bookingStatusValidator,

	// 金額情報
	totalPrice: v.number(),
	currency: v.string(),
	amountInfo: v.optional(amountInfoValidator),

	// 部屋情報
	roomId: v.optional(v.string()),
	roomName: v.optional(v.string()),
	adults: v.optional(v.number()),
	children: v.optional(v.number()),

	// チャネル情報
	channel: v.optional(v.string()),
	channelId: v.optional(v.string()),

	// メモ・特記事項
	notes: v.optional(v.string()),
	internalNotes: v.optional(v.string()),

	// APIレスポンス全体を保存（beds24Propertiesと同じパターン）
	data: v.any(),

	// メタデータ
	modifiedDate: v.number(), // Beds24側の最終更新日時
	lastSyncedAt: v.number(),
	createdAt: v.number(),
	updatedAt: v.number(),
	isDeleted: v.boolean(),
	deletedAt: v.optional(v.number()),
});

/**
 * データベースの予約レコード（システムフィールド含む）
 *
 * @description Convexデータベースに保存される予約レコードの完全な構造。
 * システムフィールド（_id, _creationTime）を含む、実際のDBレコード形式
 */
export const databaseBookingValidator = v.object({
	_id: v.id("beds24Bookings"),
	_creationTime: v.number(),
	...bookingDataValidator.fields,
});

/**
 * 関連情報を含む予約データ（一覧表示用）
 *
 * @description 予約一覧画面で使用される拡張データ構造。
 * 予約情報に加えて、関連する施設情報を結合して表示用に最適化
 */
export const bookingWithDetailsValidator = v.object({
	...databaseBookingValidator.fields,
	// 結合情報
	propertyName: v.string(),
	propertyCity: v.optional(v.string()),
	propertyCountry: v.optional(v.string()),
});

/**
 * 予約サマリー（軽量版）
 *
 * @description パフォーマンスを重視した軽量な予約データ構造。
 * 一覧表示や検索結果など、大量のデータを扱う場面で使用
 */
export const bookingSummaryValidator = v.object({
	_id: v.id("beds24Bookings"),
	_creationTime: v.number(),
	bookingId: v.string(),
	propertyId: v.id("beds24Properties"),
	checkIn: v.string(),
	checkOut: v.string(),
	status: bookingStatusValidator,
	totalPrice: v.number(),
	currency: v.string(),
	// 結合情報
	propertyName: v.string(),
});

/**
 * 予約統計情報
 *
 * @description ビジネス分析とレポーティングのための統計データ構造。
 * 売上分析、稼働率、チャネル分析など、経営判断に必要な指標を提供
 */
export const bookingStatsValidator = v.object({
	/** 総予約数 */
	totalBookings: v.number(),
	/** 総売上（すべての通貨を基準通貨に換算した合計） */
	totalRevenue: v.number(),
	/** 通貨別売上内訳（キー: 通貨コード、値: 売上金額） */
	currencyBreakdown: v.record(v.string(), v.number()),
	/** ステータス別予約件数（キー: ステータス、値: 件数） */
	statusBreakdown: v.record(v.string(), v.number()),
	/** 予約チャネル別件数（キー: チャネル名、値: 件数） */
	channelBreakdown: v.record(v.string(), v.number()),
	/** 月別予約数（キー: YYYY-MM形式、値: 件数） */
	monthlyBookings: v.record(v.string(), v.number()),
	/** 平均滞在日数 */
	averageStayLength: v.number(),
	/** 稼働率（0-100のパーセンテージ値） */
	occupancyRate: v.number(),
	/** 統計の対象期間 */
	dateRange: v.object({
		/** 開始日（YYYY-MM-DD形式） */
		from: v.string(),
		/** 終了日（YYYY-MM-DD形式） */
		to: v.string(),
	}),
});

/**
 * ページネーション結果
 *
 * @description Convexの標準ページネーション形式に準拠した予約データ構造。
 * 大量の予約データを効率的に取得・表示するための仕組み
 */
export const paginatedBookingsValidator = v.object({
	page: v.array(bookingWithDetailsValidator),
	isDone: v.boolean(),
	continueCursor: v.union(v.string(), v.null()),
});

/**
 * バッチ処理結果
 *
 * @description 複数予約の一括処理結果を表すデータ構造。
 * 同期処理やインポート処理の結果を詳細に記録し、エラー追跡を可能にする
 */
export const batchResultValidator = v.object({
	/** 成功した処理の総数 */
	successful: v.number(),
	/** 失敗した処理の総数 */
	failed: v.number(),
	/** 新規作成された予約数 */
	created: v.number(),
	/** 更新された既存予約数 */
	updated: v.number(),
	/** 処理をスキップした予約数（変更なし等） */
	skipped: v.number(),
	/** すべての予約が既存データだったかどうか */
	allExisting: v.boolean(),
	/** エラー詳細のリスト */
	errors: v.array(
		v.object({
			/** エラーが発生した予約ID */
			bookingId: v.string(),
			/** エラーメッセージ */
			error: v.string(),
		}),
	),
});

/**
 * 予約アップサート結果
 *
 * @description 単一予約の作成・更新処理結果。
 * 処理の成否と実行されたアクションを明確に記録
 */
export const bookingResultValidator = v.object({
	/** 処理の成功/失敗フラグ */
	success: v.boolean(),
	/** 処理対象の予約ID */
	bookingId: v.string(),
	/** 実行されたアクション */
	action: v.union(
		/** 新規作成された */
		v.literal("created"),
		/** 既存データが更新された */
		v.literal("updated"),
		/** 変更がないためスキップされた */
		v.literal("skipped"),
	),
	/** エラーメッセージ（失敗時のみ） */
	error: v.optional(v.string()),
});

/**
 * 削除結果
 *
 * @description 予約の削除処理結果。論理削除の件数とエラー情報を記録
 */
export const deleteResultValidator = v.object({
	/** 削除（論理削除）された予約数 */
	deletedCount: v.number(),
	/** 削除処理中に発生したエラーのリスト */
	errors: v.array(v.string()),
});

/**
 * 予約フィルタオプション
 *
 * @description 予約検索・フィルタリング用のパラメータ。
 * 複数の条件を組み合わせて柔軟な検索を可能にする
 */
export const bookingFilterOptionsValidator = v.object({
	/** 施設IDでフィルタ */
	propertyId: v.optional(v.id("beds24Properties")),
	/** 予約ステータスでフィルタ */
	status: v.optional(bookingStatusValidator),
	/** 予約チャネル（OTA名等）でフィルタ */
	channel: v.optional(v.string()),
	/** 開始日（この日以降の予約を対象、YYYY-MM-DD形式） */
	dateFrom: v.optional(v.string()),
	/** 終了日（この日以前の予約を対象、YYYY-MM-DD形式） */
	dateTo: v.optional(v.string()),
});

/**
 * リスト取得の共通オプション
 *
 * @description 予約一覧取得時のソートや表示件数を制御するオプション
 */
export const listOptionsValidator = v.object({
	/** 取得件数の上限 */
	limit: v.optional(v.number()),
	/** ソート対象フィールド */
	orderBy: v.optional(
		v.union(
			/** チェックイン日でソート */
			v.literal("checkIn"),
			/** 予約作成日でソート */
			v.literal("bookingDate"),
			/** 総額でソート */
			v.literal("totalPrice"),
		),
	),
	/** ソート順（昇順/降順） */
	order: v.optional(v.union(v.literal("asc"), v.literal("desc"))),
});

/**
 * Beds24 APIレスポンスの参考型（実際のAPIレスポンスに基づいて拡張可能）
 *
 * @description Beds24 APIから返される予約データの構造。
 * APIのレスポンス形式に準拠し、必要に応じて拡張可能
 */
export const beds24BookingApiResponseValidator = v.object({
	id: v.string(),
	propId: v.string(),
	roomId: v.string(),
	unitId: v.optional(v.string()),

	// ゲスト情報
	guestFirstName: v.string(),
	guestName: v.string(),
	guestEmail: v.optional(v.string()),
	guestPhone: v.optional(v.string()),
	guestCountry: v.optional(v.string()),
	guestAddress: v.optional(v.string()),
	guestCity: v.optional(v.string()),
	guestPostcode: v.optional(v.string()),

	// 日付
	arrival: v.string(),
	departure: v.string(),
	bookingTime: v.string(),

	// ステータス
	/** Beds24のステータスコード（0: キャンセル, 1: 確定, 2: 新規, 3: リクエスト） */
	status: v.union(
		v.literal("0"),
		v.literal("1"),
		v.literal("2"),
		v.literal("3"),
	),

	// 金額
	price: v.string(),
	currency: v.optional(v.string()),
	deposit: v.optional(v.string()),
	tax: v.optional(v.string()),
	commission: v.optional(v.string()),

	// 人数
	numAdult: v.string(),
	numChild: v.optional(v.string()),

	// チャネル
	referer: v.optional(v.string()),
	refererId: v.optional(v.string()),

	// その他任意のフィールド（API拡張用）
	notes: v.optional(v.string()),
	internalNotes: v.optional(v.string()),
	modified: v.optional(v.string()),
});

/**
 * パーサー結果型
 *
 * @description Beds24 APIレスポンスのパース結果。
 * パースの成否と変換後のデータ、エラー情報を含む
 */
export const parsedBookingResultValidator = v.object({
	/** パースの成功/失敗フラグ */
	success: v.boolean(),
	/** パース成功時の予約データ */
	booking: v.optional(bookingDataValidator),
	/** パース失敗時のエラーメッセージ */
	error: v.optional(v.string()),
	/** 元のAPIレスポンス（デバッグ用） */
	rawData: v.any(),
});

// ====================
// 型定義（すべてバリデータから自動導出）
// ====================

/**
 * 型の自動導出
 *
 * @description すべての型はバリデータから自動的に導出される。
 * これにより、バリデータと型定義の一貫性が保証される
 */
export type BookingData = Infer<typeof bookingDataValidator>;
export type DatabaseBooking = Doc<"beds24Bookings">;
export type BookingWithDetails = Infer<typeof bookingWithDetailsValidator>;
export type BookingSummary = Infer<typeof bookingSummaryValidator>;
export type BookingStats = Infer<typeof bookingStatsValidator>;
export type PaginatedBookings = Infer<typeof paginatedBookingsValidator>;
export type BatchResult = Infer<typeof batchResultValidator>;
export type BookingResult = Infer<typeof bookingResultValidator>;
export type DeleteResult = Infer<typeof deleteResultValidator>;
export type BookingFilterOptions = Infer<typeof bookingFilterOptionsValidator>;
export type ListOptions = Infer<typeof listOptionsValidator>;
export type Beds24BookingApiResponse = Infer<
	typeof beds24BookingApiResponseValidator
>;
export type ParsedBookingResult = Infer<typeof parsedBookingResultValidator>;
export type GuestInfo = Infer<typeof guestInfoValidator>;
export type AmountInfo = Infer<typeof amountInfoValidator>;

/**
 * システムフィールドを除外した予約の入力型
 *
 * @description 新規予約作成時に使用する入力データ型。
 * Convexのシステムフィールド（_id, _creationTime）は自動生成されるため除外
 */
export type BookingInput = WithoutSystemFields<
	Omit<DatabaseBooking, "_id" | "_creationTime">
>;

/**
 * 部分的な予約更新用の型
 *
 * @description 既存予約の部分更新時に使用する型。
 * 識別子（bookingId, propertyId, userId）とシステムフィールドは変更不可のため除外
 */
export type BookingUpdate = Partial<
	WithoutSystemFields<
		Omit<
			DatabaseBooking,
			"_id" | "_creationTime" | "bookingId" | "propertyId" | "userId"
		>
	>
>;

/**
 * 同期タスクのメタデータ型（既にbeds24.tsで定義済み）
 *
 * @description 予約同期処理で使用されるメタデータ構造。
 * 詳細はbeds24.tsで定義されており、そちらからインポートして使用する
 *
 * @see beds24.ts
 */
