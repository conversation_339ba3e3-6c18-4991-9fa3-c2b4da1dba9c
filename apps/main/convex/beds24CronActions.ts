"use node";

/**
 * Beds24 Cron Actions
 *
 * このファイルはBeds24のCronジョブから呼び出されるアクションを提供します。
 * 毎日の同期ジョブを投入する処理を含みます。
 */

import type { FunctionReference } from "convex/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import { internalAction } from "./_generated/server";
import { createLogger } from "./lib/logging";
import { JobPriority, QueueJobType } from "./types/beds24";

/**
 * Beds24トークンを持つユーザーの配列型
 *
 * @typedef {Array} UsersWithBeds24
 * @property {string} userId - ユーザーID
 */
type UsersWithBeds24 = { userId: string }[];

/**
 * getUserIdsWithBeds24Token関数の型定義
 *
 * TypeScriptの型推論の深さ制限を回避するために使用
 * Convexの内部クエリ関数の参照型
 */
type GetUserIdsWithBeds24TokenType = FunctionReference<
	"query",
	"internal",
	Record<string, never>,
	UsersWithBeds24
>;

/**
 * 全ユーザーの施設同期ジョブを投入する内部アクション
 *
 * Beds24のrefreshTokenを持つ全ユーザーを対象に、
 * 施設データの同期ジョブをキューに投入します。
 *
 * 処理フロー：
 * 1. Beds24トークンを持つユーザーのリストを取得
 * 2. 各ユーザーに対して施設同期ジョブを投入
 * 3. エラーが発生した場合はログに記録し、次のユーザーの処理を継続
 *
 * @returns 投入結果（投入成功数、エラー情報）
 */
export const enqueueDailySync = internalAction({
	args: {},
	returns: v.object({
		enqueuedCount: v.number(),
		errors: v.array(
			v.object({
				userId: v.string(),
				error: v.string(),
			}),
		),
	}),
	handler: async (
		ctx,
	): Promise<{
		enqueuedCount: number;
		errors: Array<{
			userId: string;
			error: string;
		}>;
	}> => {
		const logger = createLogger("enqueueDailySync", ctx);
		logger.info("毎日の同期ジョブ投入を開始");

		// Beds24のrefreshTokenを持つユーザーを取得
		const usersWithBeds24 = await ctx.runQuery(
			internal.beds24SyncQueries
				.getUserIdsWithBeds24Token as GetUserIdsWithBeds24TokenType,
			{},
		);

		logger.info(`Beds24トークンを持つユーザー数: ${usersWithBeds24.length}`);

		let enqueuedCount = 0;
		const errors: { userId: string; error: string }[] = [];

		// 各ユーザーに対して同期ジョブを投入
		for (const user of usersWithBeds24) {
			try {
				await ctx.runMutation(internal.beds24Queue.enqueueJob, {
					userId: user.userId,
					jobType: QueueJobType.SYNC_PROPERTIES,
					priority: JobPriority.NORMAL,
					metadata: {
						source: "daily_cron",
						enqueuedAt: Date.now(),
					},
				});
				enqueuedCount++;
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : String(error);
				errors.push({
					userId: user.userId,
					error: errorMessage,
				});
				logger.error(`ユーザー ${user.userId} のジョブ投入に失敗しました`, {
					error: errorMessage,
				});
			}
		}

		logger.info("毎日の同期ジョブ投入が完了しました", {
			totalUsers: usersWithBeds24.length,
			enqueuedCount,
			errorCount: errors.length,
		});

		return { enqueuedCount, errors };
	},
});

/**
 * 全施設のレビュー同期ジョブを投入する内部アクション
 *
 * すべてのアクティブな施設に対して、Booking.comからのレビュー取得ジョブを投入します。
 * スラッグ未取得の施設は先にスラッグ取得ジョブを投入します。
 *
 * 処理フロー：
 * 1. OTAマスタからBooking.comの情報を取得
 * 2. 全施設をバッチ処理（100件ずつ）
 * 3. 各施設の関連ユーザーを取得
 * 4. スラッグ有無により適切なジョブを投入
 *   - スラッグなし：スラッグ取得ジョブ（中優先度）
 *   - スラッグあり：レビュー同期ジョブ（低優先度）
 *
 * パフォーマンス最適化：
 * - バッチサイズ: 100件
 * - 最大バッチ数: 100
 * - 処理統計とパフォーマンスメトリクスを記録
 *
 * @returns 投入結果（レビュー同期数、スラッグ取得数、エラー情報）
 */
export const enqueueDailyReviewSync = internalAction({
	args: {},
	returns: v.object({
		enqueuedReviewCount: v.number(),
		enqueuedSlugCount: v.number(),
		errors: v.array(
			v.object({
				propertyId: v.optional(v.id("beds24Properties")),
				error: v.string(),
			}),
		),
	}),
	handler: async (
		ctx,
	): Promise<{
		enqueuedReviewCount: number;
		enqueuedSlugCount: number;
		errors: Array<{
			propertyId?: Id<"beds24Properties">;
			error: string;
		}>;
	}> => {
		const logger = createLogger("enqueueDailyReviewSync", ctx);
		const functionStartTime = Date.now();
		logger.info("=== enqueueDailyReviewSync開始 ===", {
			timestamp: functionStartTime,
			isoTime: new Date(functionStartTime).toISOString(),
		});

		// パフォーマンスメトリクス
		const performanceMetrics = {
			otaQueryTime: 0,
			totalBatchFetchTime: 0,
			totalPropertyProcessingTime: 0,
			totalUserQueryTime: 0,
			totalEnqueueTime: 0,
			slowestOperation: { type: "", time: 0, details: {} },
		};

		// 必要なインポート
		const importStartTime = Date.now();
		const { createBookingReviewUrl } = await import("./lib/reviewUrlBuilder");
		const importTime = Date.now() - importStartTime;
		logger.info("インポート完了", { durationMs: importTime });

		// OTAマスタから"booking"のレコードを取得
		const otaQueryStart = Date.now();
		const otaMaster = await ctx.runQuery(internal.otaMaster.getByShortName, {
			shortName: "booking",
		});
		performanceMetrics.otaQueryTime = Date.now() - otaQueryStart;
		logger.info("OTAマスタ取得完了", {
			durationMs: performanceMetrics.otaQueryTime,
			found: !!otaMaster,
		});

		if (!otaMaster) {
			logger.error("OTAマスタ（booking）が見つかりません");
			return { enqueuedReviewCount: 0, enqueuedSlugCount: 0, errors: [] };
		}

		let enqueuedReviewCount = 0;
		let enqueuedSlugCount = 0;
		const errors: { propertyId?: Id<"beds24Properties">; error: string }[] = [];

		// 処理統計用の変数
		let totalPropertiesProcessed = 0;
		let totalActiveProperties = 0;
		let totalUsersProcessed = 0;
		let batchCount = 0;

		// バッチサイズの定義
		const BATCH_SIZE = 100;
		const MAX_BATCHES = 100; // 安全のため最大バッチ数を制限
		let cursor: string | null | undefined = null;
		let isDone = false;

		do {
			// バッチ処理の開始時刻
			const batchStartTime = Date.now();
			batchCount++;

			logger.info(`[バッチ${batchCount}] 開始`, {
				timestamp: batchStartTime,
				cursor: cursor ? `${cursor.substring(0, 10)}...` : "null",
			});

			// 安全のため最大バッチ数をチェック
			if (batchCount > MAX_BATCHES) {
				logger.error("最大バッチ数を超えました。処理を中断します。", {
					batchCount,
					maxBatches: MAX_BATCHES,
				});
				break;
			}

			// 全beds24Propertiesをページネーションで取得
			const batchFetchStart = Date.now();
			const batch: {
				page: Array<{
					_id: Id<"beds24Properties">;
					_creationTime: number;
					beds24PropertyId: string;
					beds24PropertyKey: string;
					name: string;
					propertyType: string;
					currency: string;
					country?: string;
					city?: string;
					data: any;
					lastSyncedAt: number;
					createdAt: number;
					updatedAt: number;
					isDeleted: boolean;
					deletedAt?: number;
					bookingComFacilitySlug?: string;
					bookingComLastScrapedAt?: number;
				}>;
				continueCursor: string | null;
				isDone: boolean;
			} = await ctx.runQuery(internal.beds24Properties.getPropertiesPaginated, {
				limit: BATCH_SIZE,
				cursor: cursor,
			});
			const batchFetchTime = Date.now() - batchFetchStart;
			performanceMetrics.totalBatchFetchTime += batchFetchTime;

			// isDoneフラグを更新
			isDone = batch.isDone;

			// isDeleted=falseでフィルタ
			const activeProperties = batch.page.filter((p) => !p.isDeleted);
			totalActiveProperties += activeProperties.length;

			logger.info(`[バッチ${batchCount}] 施設取得完了`, {
				fetchDurationMs: batchFetchTime,
				totalProperties: batch.page.length,
				activeProperties: activeProperties.length,
				deletedProperties: batch.page.length - activeProperties.length,
				isDone: batch.isDone,
				continueCursor: batch.continueCursor ? "exists" : "null",
			});

			// 各施設の処理
			let batchPropertyCount = 0;
			const _batchPropertyStartTime = Date.now();

			for (const property of activeProperties) {
				const propertyStartTime = Date.now();
				batchPropertyCount++;
				totalPropertiesProcessed++;

				try {
					// userPropertiesテーブルから関連するユーザーを取得
					const userQueryStartTime = Date.now();
					const relatedUsers = await ctx.runQuery(
						internal.beds24Properties.getUsersByPropertyId,
						{
							propertyId: property._id,
						},
					);
					const userQueryTime = Date.now() - userQueryStartTime;
					performanceMetrics.totalUserQueryTime += userQueryTime;

					// パフォーマンス記録
					if (userQueryTime > performanceMetrics.slowestOperation.time) {
						performanceMetrics.slowestOperation = {
							type: "getUsersByPropertyId",
							time: userQueryTime,
							details: {
								propertyId: property._id,
								userCount: relatedUsers.length,
							},
						};
					}

					logger.info(`[施設${totalPropertiesProcessed}] ユーザー取得`, {
						propertyId: `${property._id.substring(0, 10)}...`,
						propertyName: property.name,
						queryDurationMs: userQueryTime,
						userCount: relatedUsers.length,
					});

					if (relatedUsers.length === 0) {
						logger.warn(`施設 ${property._id} に関連するユーザーがいません`);
						continue;
					}

					totalUsersProcessed += relatedUsers.length;

					// 各ユーザーに対してタスクを作成
					for (const userProperty of relatedUsers) {
						const enqueueStartTime = Date.now();
						try {
							if (!property.bookingComFacilitySlug) {
								// スラッグ未取得施設の場合
								logger.info(`[enqueue開始] スラッグ取得ジョブ`, {
									propertyId: `${property._id.substring(0, 10)}...`,
									userId: `${userProperty.userId.substring(0, 20)}...`,
								});

								await ctx.runMutation(internal.beds24Queue.enqueueJob, {
									userId: userProperty.userId,
									jobType: QueueJobType.SCRAPE_BOOKING_COM_SLUG,
									priority: JobPriority.MEDIUM, // レビュー同期より高優先度
									metadata: {
										propertyId: property._id,
										propertyName: property.name,
										beds24PropertyKey: property.beds24PropertyKey,
										userId: userProperty.userId, // レビュー同期タスク登録用
									},
								});
								enqueuedSlugCount++;
								const enqueueTime = Date.now() - enqueueStartTime;
								performanceMetrics.totalEnqueueTime += enqueueTime;

								// パフォーマンス記録
								if (enqueueTime > performanceMetrics.slowestOperation.time) {
									performanceMetrics.slowestOperation = {
										type: "enqueueJob-slug",
										time: enqueueTime,
										details: {
											propertyId: property._id,
											jobType: "SCRAPE_BOOKING_COM_SLUG",
										},
									};
								}

								logger.info(`[enqueue完了] スラッグ取得ジョブ`, {
									durationMs: enqueueTime,
									slowOperation: enqueueTime > 100,
								});
							} else {
								// スラッグ取得済み施設の場合
								const reviewUrl = createBookingReviewUrl({
									property: {
										id: property.beds24PropertyId,
										name: property.name,
										booking_com_facility_name: property.bookingComFacilitySlug,
									},
								});

								logger.info(`[enqueue開始] レビュー同期ジョブ`, {
									propertyId: `${property._id.substring(0, 10)}...`,
									userId: `${userProperty.userId.substring(0, 20)}...`,
								});

								// デバッグ：メタデータの詳細をログ出力
								const reviewSyncMetadata = {
									url: reviewUrl,
									otaId: otaMaster._id,
									otaType: "booking.com",
									pageNumber: 1,
									propertyId: property._id,
								};

								logger.info("[DEBUG] レビュー同期ジョブのメタデータ作成", {
									propertyId: property._id,
									userId: userProperty.userId,
									metadata: reviewSyncMetadata,
									reviewUrl,
									otaMasterId: otaMaster._id,
									metadataStringified: JSON.stringify(reviewSyncMetadata),
								});

								await ctx.runMutation(internal.beds24Queue.enqueueJob, {
									userId: userProperty.userId,
									jobType: QueueJobType.SYNC_REVIEWS,
									priority: JobPriority.LOW, // プロパティ同期より低優先度
									metadata: reviewSyncMetadata,
								});
								enqueuedReviewCount++;
								const enqueueTime = Date.now() - enqueueStartTime;
								performanceMetrics.totalEnqueueTime += enqueueTime;

								// パフォーマンス記録
								if (enqueueTime > performanceMetrics.slowestOperation.time) {
									performanceMetrics.slowestOperation = {
										type: "enqueueJob-review",
										time: enqueueTime,
										details: {
											propertyId: property._id,
											jobType: "SYNC_REVIEWS",
										},
									};
								}

								logger.info(`[enqueue完了] レビュー同期ジョブ`, {
									durationMs: enqueueTime,
									slowOperation: enqueueTime > 100,
								});
							}
						} catch (error) {
							const errorMessage =
								error instanceof Error ? error.message : String(error);
							errors.push({
								propertyId: property._id,
								error: errorMessage,
							});
							logger.error(`施設 ${property._id} のタスク投入に失敗しました`, {
								error: errorMessage,
								userId: userProperty.userId,
							});
						}
					}

					// 施設処理時間を記録
					const propertyProcessingTime = Date.now() - propertyStartTime;
					performanceMetrics.totalPropertyProcessingTime +=
						propertyProcessingTime;

					logger.info(`[施設${totalPropertiesProcessed}] 処理完了`, {
						propertyId: `${property._id.substring(0, 10)}...`,
						processingDurationMs: propertyProcessingTime,
						userCount: relatedUsers.length,
						jobsEnqueued: relatedUsers.length,
					});
				} catch (error) {
					const errorMessage =
						error instanceof Error ? error.message : String(error);
					errors.push({
						propertyId: property._id,
						error: errorMessage,
					});
					logger.error(`施設 ${property._id} の処理に失敗しました`, {
						error: errorMessage,
					});
				}
			}

			// バッチ処理完了ログ
			const batchEndTime = Date.now();
			const batchDuration = batchEndTime - batchStartTime;
			logger.info(`[バッチ${batchCount}] 完了`, {
				batchDurationMs: batchDuration,
				processedProperties: batchPropertyCount,
				averageTimePerProperty:
					batchPropertyCount > 0 ? batchDuration / batchPropertyCount : 0,
			});

			// 次のページのカーソルを設定
			cursor = batch.continueCursor;

			// 空のページでisDoneがfalseの場合の警告
			if (batch.page.length === 0 && !isDone) {
				logger.warn("空のページですが、まだデータがあります", {
					batchCount,
					cursor: cursor ? "exists" : "null",
					isDone,
				});
			}
		} while (!isDone && cursor !== null);

		const totalTime = Date.now() - functionStartTime;

		// 最終的なパフォーマンスサマリー
		logger.info("=== enqueueDailyReviewSync完了 ===", {
			// 全体の結果
			totalDurationMs: totalTime,
			totalDurationSec: (totalTime / 1000).toFixed(2),
			totalDurationMin: (totalTime / 60000).toFixed(2),

			// 処理件数
			stats: {
				totalPropertiesProcessed,
				totalActiveProperties,
				totalUsersProcessed,
				totalBatches: batchCount,
				enqueuedReviewCount,
				enqueuedSlugCount,
				errorCount: errors.length,
			},

			// パフォーマンスメトリクス
			performance: {
				otaQueryTime: performanceMetrics.otaQueryTime,
				totalBatchFetchTime: performanceMetrics.totalBatchFetchTime,
				totalPropertyProcessingTime:
					performanceMetrics.totalPropertyProcessingTime,
				totalUserQueryTime: performanceMetrics.totalUserQueryTime,
				totalEnqueueTime: performanceMetrics.totalEnqueueTime,
				averageUserQueryTime:
					totalPropertiesProcessed > 0
						? performanceMetrics.totalUserQueryTime / totalPropertiesProcessed
						: 0,
				averageEnqueueTime:
					enqueuedReviewCount + enqueuedSlugCount > 0
						? performanceMetrics.totalEnqueueTime /
							(enqueuedReviewCount + enqueuedSlugCount)
						: 0,
			},

			// 最も遅い操作
			slowestOperation: performanceMetrics.slowestOperation,

			// エラー詳細（最初の3件）
			sampleErrors: errors.slice(0, 3),
		});

		return { enqueuedReviewCount, enqueuedSlugCount, errors };
	},
});

/**
 * 全ユーザーの予約同期ジョブを投入する内部アクション
 *
 * Beds24のrefreshTokenを持つ全ユーザーの全施設に対して、
 * 予約データの同期ジョブをキューに投入します。
 *
 * 処理フロー：
 * 1. Beds24トークンを持つユーザーのリストを取得
 * 2. 各ユーザーの施設一覧を取得
 * 3. 各施設に対して予約同期ジョブを投入
 * 4. 初回同期として現在日時以降の予約を取得
 *
 * エラーハンドリング：
 * - 無効なpropertyIdはスキップ
 * - エラーは記録して次の処理を継続
 *
 * @returns 投入結果（投入成功数、エラー情報）
 */
export const enqueueDailyBookingSync = internalAction({
	args: {},
	returns: v.object({
		enqueuedCount: v.number(),
		errors: v.array(
			v.object({
				userId: v.string(),
				propertyId: v.string(),
				error: v.string(),
			}),
		),
	}),
	handler: async (
		ctx,
	): Promise<{
		enqueuedCount: number;
		errors: Array<{
			userId: string;
			propertyId: string;
			error: string;
		}>;
	}> => {
		const logger = createLogger("enqueueDailyBookingSync", ctx);
		logger.info("毎日の予約同期ジョブ投入を開始");

		// Beds24のrefreshTokenを持つユーザーを取得
		const usersWithBeds24 = await ctx.runQuery(
			internal.beds24SyncQueries
				.getUserIdsWithBeds24Token as GetUserIdsWithBeds24TokenType,
			{},
		);

		logger.info(`Beds24トークンを持つユーザー数: ${usersWithBeds24.length}`);

		let enqueuedCount = 0;
		const errors: { userId: string; propertyId: string; error: string }[] = [];

		// 各ユーザーの各プロパティに対して同期ジョブを投入
		for (const user of usersWithBeds24) {
			try {
				// ユーザーのプロパティ一覧を取得
				const properties = await ctx.runQuery(
					internal.beds24Properties.getPropertiesByUserId,
					{ userId: user.userId },
				);

				logger.info(
					`ユーザー ${user.userId} のプロパティ数: ${properties.length}`,
				);

				for (const property of properties) {
					try {
						// beds24PropertyIdを数値に変換
						const propertyIdNum = Number(property.beds24PropertyId);
						if (Number.isNaN(propertyIdNum)) {
							logger.error("無効なpropertyId", {
								userId: user.userId,
								beds24PropertyId: property.beds24PropertyId,
								propertyName: property.name,
							});
							errors.push({
								userId: user.userId,
								propertyId: property.beds24PropertyId,
								error: `無効なpropertyId: ${property.beds24PropertyId}`,
							});
							continue;
						}

						await ctx.runMutation(internal.beds24Queue.enqueueJob, {
							userId: user.userId,
							jobType: QueueJobType.SYNC_BOOKINGS,
							priority: JobPriority.NORMAL,
							metadata: {
								propertyId: propertyIdNum,
								propertyName: property.name,
								userId: user.userId,
								pageNumber: 1,
								source: "daily_cron",
								enqueuedAt: Date.now(),
								// 日付範囲は指定しない（初回同期として現在日時以降を取得）
							},
						});
						enqueuedCount++;
					} catch (error) {
						const errorMessage =
							error instanceof Error ? error.message : String(error);
						errors.push({
							userId: user.userId,
							propertyId: property.beds24PropertyId,
							error: errorMessage,
						});
						logger.error(`予約同期ジョブの投入に失敗しました`, {
							userId: user.userId,
							propertyId: property.beds24PropertyId,
							error: errorMessage,
						});
					}
				}
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : String(error);
				logger.error(`ユーザー ${user.userId} のプロパティ取得に失敗しました`, {
					error: errorMessage,
				});
				// プロパティ取得エラーの場合はダミーのpropertyIdを使用
				errors.push({
					userId: user.userId,
					propertyId: "unknown",
					error: `プロパティ取得エラー: ${errorMessage}`,
				});
			}
		}

		logger.info("毎日の予約同期ジョブ投入が完了しました", {
			totalUsers: usersWithBeds24.length,
			enqueuedCount,
			errorCount: errors.length,
		});

		return { enqueuedCount, errors };
	},
});
