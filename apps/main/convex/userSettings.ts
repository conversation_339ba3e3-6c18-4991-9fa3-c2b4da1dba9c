/**
 * User Settings Convex Functions
 *
 * このファイルはユーザー設定に関するすべてのConvex関数（queries, mutations, actions）を
 * 統合して提供します。ビジネスロジックはmodel/userSettings.tsに実装されています。
 *
 * Convexベストプラクティスの適用:
 * - Row Level Security: getCurrentUserSettingsのみを公開し、他ユーザーの設定へのアクセスを防止
 * - パフォーマンス最適化: 単一トランザクションでの読み書き、不要な更新のスキップ
 * - 型安全性: discriminated unionとvalidatorによる厳密な型チェック
 * - アクセス制御: withAuthenticatedMutationによる認証チェックの統一
 */

import { v } from "convex/values";
import { internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import {
	action,
	internalMutation,
	internalQuery,
	mutation,
	query,
} from "./_generated/server";
import { getAuthenticatedUserId } from "./lib/auth";
import { withAuthenticatedMutation } from "./lib/helpers";
import { createLogger } from "./lib/logging";
import * as UserSettings from "./model/userSettings";
import {
	beds24UpdateResultValidator,
	themeValidator,
	updateFieldArgsValidator,
	userSettingsArgsValidator,
	userSettingsReturnValidator,
} from "./types/userSettings";

// =================
// Queries
// =================

// getUserSettingsは削除 - Row Level Securityのため、他のユーザーの設定への公開アクセスを防ぐ
// 必要な場合は内部関数として実装済み (getByUserId)

/**
 * 現在の認証済みユーザーの設定を取得する
 *
 * 認証されたユーザーの設定情報（テーマ、Beds24連携設定など）を取得します。
 * Row Level Securityにより、他のユーザーの設定にはアクセスできません。
 *
 * @returns ユーザー設定オブジェクト、未認証または設定がない場合はnull
 * @example
 * // クライアントサイドでの使用例
 * const settings = useQuery(api.userSettings.getCurrentUserSettings);
 * if (settings) {
 *   console.log('Theme:', settings.theme);
 *   console.log('Has Beds24 token:', !!settings.beds24?.refreshToken);
 * }
 */
export const getCurrentUserSettings = query({
	args: {},
	returns: userSettingsReturnValidator,
	handler: async (ctx) => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			return null;
		}

		const userId = getAuthenticatedUserId(identity);

		return await UserSettings.getUserSettingsByUserId(ctx, userId);
	},
});

/**
 * 内部使用: 特定のユーザーの設定を取得する
 *
 * 内部関数から特定ユーザーの設定を取得する際に使用します。
 * 外部からは呼び出せません。
 *
 * @param userId - Clerk User ID
 * @returns ユーザー設定オブジェクト、存在しない場合はnull
 * @example
 * // 内部関数での使用例
 * const settings = await ctx.runQuery(internal.userSettings.getByUserId, {
 *   userId: "user_abc123"
 * });
 */
export const getByUserId = internalQuery({
	args: {
		userId: v.string(),
	},
	returns: userSettingsReturnValidator,
	handler: async (ctx, args) => {
		return await UserSettings.getUserSettingsByUserId(ctx, args.userId);
	},
});

// =================
// Mutations
// =================

/**
 * ユーザー設定を作成または更新する
 *
 * 現在の認証ユーザーの設定を作成または更新します。
 * 設定が存在しない場合は新規作成し、存在する場合は指定されたフィールドのみを更新します。
 *
 * @param settings - 更新する設定項目（theme, beds24）
 * @returns 作成または更新されたドキュメントのID
 * @throws 認証されていない場合エラー
 * @example
 * // テーマとBeds24設定を更新
 * const settingsId = await upsertUserSettings({
 *   theme: 'dark',
 *   beds24: {
 *     refreshToken: 'your-refresh-token'
 *   }
 * });
 */
export const upsertUserSettings = mutation({
	args: userSettingsArgsValidator,
	returns: v.id("userSettings"),
	handler: async (ctx, args) =>
		withAuthenticatedMutation(
			ctx,
			"upsertUserSettings",
			args,
			async (ctx, userId, logger) => {
				logger.info("ユーザー設定の更新を開始");
				// model層のヘルパー関数を使用
				return await UserSettings.upsertUserSettings(ctx, userId, args);
			},
		),
});

/**
 * ユーザー設定を削除する
 *
 * 現在の認証ユーザーの設定をデータベースから完全に削除します。
 * テーマやBeds24連携情報など、すべての設定が削除されます。
 *
 * @returns 削除成功の場合はtrue、設定が存在しない場合はfalse
 * @throws 認証されていない場合エラー
 * @example
 * // ユーザー設定を削除
 * const deleted = await deleteUserSettings();
 * if (deleted) {
 *   console.log('設定が削除されました');
 * } else {
 *   console.log('設定が存在しませんでした');
 * }
 */
export const deleteUserSettings = mutation({
	args: {},
	returns: v.boolean(),
	handler: async (ctx) =>
		withAuthenticatedMutation(
			ctx,
			"deleteUserSettings",
			{},
			async (ctx, userId, logger) => {
				logger.info("ユーザー設定の削除を開始");
				// model層のヘルパー関数を使用
				return await UserSettings.deleteUserSettingsByUserId(ctx, userId);
			},
		),
});

/**
 * 特定の設定項目のみを更新する
 *
 * 指定したフィールドのみを更新します。他のフィールドは変更されません。
 * discriminated unionにより型安全性が保証されています。
 *
 * @param update - 更新内容を含むオブジェクト
 * @param update.field - 更新するフィールド名（'theme' | 'beds24'）
 * @param update.value - 新しい値（フィールドに応じた型）
 * @returns 更新されたドキュメントのID
 * @throws 認証されていない場合エラー
 * @throws 未知のフィールドが指定された場合エラー
 * @example
 * // テーマのみ更新
 * await updateUserSettingsField({
 *   update: {
 *     field: 'theme',
 *     value: 'dark'
 *   }
 * });
 *
 * // Beds24設定のみ更新
 * await updateUserSettingsField({
 *   update: {
 *     field: 'beds24',
 *     value: { refreshToken: 'new-token' }
 *   }
 * });
 */
export const updateUserSettingsField = mutation({
	args: updateFieldArgsValidator,
	returns: v.id("userSettings"),
	handler: async (ctx, args) =>
		withAuthenticatedMutation(
			ctx,
			"updateUserSettingsField",
			args,
			async (ctx, userId, logger) => {
				logger.info("フィールド更新を開始", { field: args.update.field });
				// discriminated unionを使用した型安全な処理
				switch (args.update.field) {
					case "theme":
						return await UserSettings.updateUserSettingsField(
							ctx,
							userId,
							"theme",
							args.update.value,
						);
					case "beds24":
						return await UserSettings.updateUserSettingsField(
							ctx,
							userId,
							"beds24",
							args.update.value,
						);
					default: {
						// TypeScriptの網羅性チェック
						const _exhaustiveCheck: never = args.update;
						throw new Error(`未知のフィールド: ${(args.update as any).field}`);
					}
				}
			},
		),
});

/**
 * Beds24の設定を更新する（refreshToken専用）
 *
 * Beds24 APIとの連携に必要なリフレッシュトークンを設定します。
 * トークンが同じ場合は更新をスキップし、パフォーマンスを最適化します。
 *
 * @param refreshToken - Beds24のリフレッシュトークン（nullで削除）
 * @returns 更新結果オブジェクト
 * @returns.id - 更新されたドキュメントのID
 * @returns.updated - 実際に更新されたかどうか（同じ値の場合はfalse）
 * @returns.created - 新規作成されたかどうか
 * @throws 認証されていない場合エラー
 * @example
 * // リフレッシュトークンを設定
 * const result = await updateBeds24Settings({
 *   refreshToken: 'your-refresh-token'
 * });
 * console.log('Updated:', result.updated);
 *
 * // リフレッシュトークンを削除
 * await updateBeds24Settings({
 *   refreshToken: null
 * });
 */
export const updateBeds24Settings = mutation({
	args: {
		refreshToken: v.optional(v.string()),
	},
	returns: beds24UpdateResultValidator,
	handler: async (ctx, args) =>
		withAuthenticatedMutation(
			ctx,
			"updateBeds24Settings",
			args,
			async (ctx, userId, logger) => {
				logger.info("Beds24設定の更新を開始");
				// model層のヘルパー関数を使用
				return await UserSettings.updateBeds24RefreshToken(
					ctx,
					userId,
					args.refreshToken,
				);
			},
		),
});

/**
 * 内部使用: ユーザー設定を作成または更新する
 *
 * 内部関数から直接ユーザー設定を操作する際に使用します。
 * 認証チェックは呼び出し元で行う必要があります。
 *
 * @param userId - Clerk User ID
 * @param settings - 設定内容オブジェクト
 * @param settings.theme - テーマ設定（'light' | 'dark' | 'system'）
 * @param settings.beds24 - Beds24連携設定（オプション）
 * @returns 作成または更新されたドキュメントのID
 * @example
 * // 内部関数からの使用例
 * const settingsId = await ctx.runMutation(internal.userSettings.upsert, {
 *   userId: 'user_abc123',
 *   settings: {
 *     theme: 'dark',
 *     beds24: { refreshToken: 'token' }
 *   }
 * });
 */
export const upsert = internalMutation({
	args: {
		userId: v.string(),
		settings: v.object({
			theme: themeValidator,
			beds24: v.optional(
				v.object({
					refreshToken: v.optional(v.string()),
				}),
			),
		}),
	},
	returns: v.id("userSettings"),
	handler: async (ctx, args) => {
		const logger = createLogger("internal.upsert", ctx);
		logger.setArgs(args);
		logger.debug("内部upsert呼び出し", { userId: args.userId });

		// model層のヘルパー関数を使用
		return await UserSettings.upsertUserSettings(
			ctx,
			args.userId,
			args.settings,
		);
	},
});

/**
 * 内部使用: ユーザー設定を取得または作成する（パフォーマンス最適化版）
 *
 * 単一のトランザクションで設定の確認と作成を行うパフォーマンス最適化版です。
 * 設定が存在する場合は既存のIDを返し、存在しない場合はデフォルト設定で新規作成します。
 *
 * @param userId - Clerk User ID
 * @returns 既存または新規作成された設定のID
 * @example
 * // 初回ログイン時の処理で使用
 * const settingsId = await ctx.runMutation(
 *   internal.userSettings.getOrCreateSettings,
 *   { userId: 'user_abc123' }
 * );
 */
export const getOrCreateSettings = internalMutation({
	args: {
		userId: v.string(),
	},
	returns: v.id("userSettings"),
	handler: async (ctx, args) => {
		const logger = createLogger("internal.getOrCreateSettings", ctx);
		logger.setArgs(args);
		logger.debug("設定の取得または作成", { userId: args.userId });

		// model層のヘルパー関数を使用
		return await UserSettings.getOrCreateUserSettings(ctx, args.userId);
	},
});

// =================
// Actions
// =================

/**
 * ユーザーの初回ログイン時に設定を初期化する
 *
 * Clerkで認証されたユーザーの初回ログイン時に、デフォルト設定を作成します。
 * 既に設定が存在する場合は、既存の設定IDを返します。
 *
 * @returns 作成されたユーザー設定のID、既に存在する場合は既存のID
 * @throws 認証されていない場合エラー
 * @example
 * // クライアントサイドで初回ログイン時に呼び出し
 * try {
 *   const settingsId = await initializeUserSettings();
 *   console.log('設定が初期化されました:', settingsId);
 * } catch (error) {
 *   console.error('設定の初期化に失敗:', error);
 * }
 */
export const initializeUserSettings = action({
	args: {},
	returns: v.id("userSettings"),
	handler: async (ctx): Promise<Id<"userSettings">> => {
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new Error("認証が必要です");
		}

		const userId = getAuthenticatedUserId(identity);

		// 単一のmutationで既存確認と作成を行う（パフォーマンス最適化）
		const settingsId = await ctx.runMutation(
			internal.userSettings.getOrCreateSettings,
			{ userId },
		);

		return settingsId;
	},
});
