import { paginationOptsValidator } from "convex/server";
import { v } from "convex/values";
import { query } from "./_generated/server";
import * as OtaReviewsModel from "./model/otaReviews";
import {
	listOptionsValidator,
	paginatedReviewsValidator,
	reviewFilterOptionsValidator,
	reviewStatsValidator,
	reviewWithDetailsValidator,
} from "./types/otaReviews";

// Queries

/**
 * 施設別のレビュー一覧を取得（ページネーション対応）
 * @returns {PaginatedReviews} ページネーションされたレビュー一覧（関連情報付き）
 */
export const getReviewsByProperty = query({
	args: {
		beds24PropertyId: v.id("beds24Properties"),
		paginationOpts: paginationOptsValidator,
	},
	returns: paginatedReviewsValidator,
	handler: async (ctx, args) => {
		// 認証とデータ取得をmodel層に委譲
		const userId = await OtaReviewsModel.requireAuth(ctx);
		return await OtaReviewsModel.getReviewsByPropertyWithAuth(
			ctx,
			userId,
			args.beds24PropertyId,
			args.paginationOpts,
		);
	},
});

/**
 * ユーザーの全レビューを取得
 * @returns {ReviewWithDetails[]} レビュー一覧（施設名、OTA名含む）
 */
export const getReviewsByUser = query({
	args: listOptionsValidator.fields,
	returns: v.array(reviewWithDetailsValidator),
	handler: async (ctx, args) => {
		// 認証とデータ取得をmodel層に委譲
		const userId = await OtaReviewsModel.requireAuth(ctx);
		return await OtaReviewsModel.getUserReviewsOptimized(
			ctx,
			userId,
			args.limit ?? 100,
		);
	},
});

/**
 * レビュー統計情報を取得
 * @returns {ReviewStats} レビューの統計情報（合計数、平均スコア、分布等）
 */
export const getReviewStats = query({
	args: reviewFilterOptionsValidator.fields,
	returns: reviewStatsValidator,
	handler: async (ctx, args) => {
		// 認証とデータ取得をmodel層に委譲
		const userId = await OtaReviewsModel.requireAuth(ctx);
		return await OtaReviewsModel.calculateReviewStatsOptimized(ctx, userId, {
			propertyId: args.propertyId,
			otaId: args.otaId,
		});
	},
});

/**
 * 最新レビューを取得
 *
 * 指定された件数の最新レビューを取得します。
 * 特定の施設またはユーザーの全施設から取得可能です。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {number} [args.limit] - 取得する最大件数（デフォルト: 10）
 * @param {Id<"beds24Properties">} [args.beds24PropertyId] - 特定施設のIDで絞り込み（オプション）
 * @returns {ReviewWithDetails[]} 最新レビューの詳細一覧（関連情報付き）
 * @throws {ConvexError} 認証エラーまたはアクセス権限エラー
 *
 * @example
 * // 全施設の最新10件を取得
 * const recentReviews = await getRecentReviews({});
 *
 * @example
 * // 特定施設の最新5件を取得
 * const propertyReviews = await getRecentReviews({
 *   beds24PropertyId: "property123",
 *   limit: 5
 * });
 */
export const getRecentReviews = query({
	args: {
		...listOptionsValidator.fields,
		beds24PropertyId: v.optional(v.id("beds24Properties")),
	},
	returns: v.array(reviewWithDetailsValidator),
	handler: async (ctx, args) => {
		const userId = await OtaReviewsModel.requireAuth(ctx);

		if (args.beds24PropertyId) {
			// 特定施設の最新レビュー
			const result = await OtaReviewsModel.getReviewsByPropertyWithAuth(
				ctx,
				userId,
				args.beds24PropertyId,
				{ numItems: args.limit ?? 10, cursor: null },
			);
			return result.page;
		} else {
			// 全施設の最新レビュー
			return await OtaReviewsModel.getUserReviewsOptimized(
				ctx,
				userId,
				args.limit ?? 10,
			);
		}
	},
});

/**
 * 特定のレビューを取得
 *
 * レビューIDを指定して単一のレビュー詳細を取得します。
 * ユーザーがアクセス権限を持つレビューのみ取得可能です。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {Id<"otaReviews">} args.reviewId - 取得するレビューのID
 * @returns {ReviewWithDetails | null} レビュー詳細（アクセス権限がない場合はnull）
 * @throws {ConvexError} 認証エラー
 *
 * @example
 * const review = await getReview({ reviewId: "review123" });
 * if (review) {
 *   console.log(review.reviewContent);
 * }
 */
export const getReview = query({
	args: {
		reviewId: v.id("otaReviews"),
	},
	returns: v.union(v.null(), reviewWithDetailsValidator),
	handler: async (ctx, args) => {
		// 認証とデータ取得をmodel層に委譲
		const userId = await OtaReviewsModel.requireAuth(ctx);
		return await OtaReviewsModel.getReviewWithAuth(ctx, userId, args.reviewId);
	},
});

/**
 * 複数施設の最新レビューを並列で取得
 *
 * 指定された複数の施設から、それぞれの最新レビューを並列で効率的に取得します。
 * ダッシュボードや一覧画面での使用に最適化されています。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {Id<"beds24Properties">[]} args.propertyIds - 取得対象の施設IDの配列
 * @param {number} [args.limitPerProperty] - 各施設から取得する最大件数（デフォルト: 10）
 * @returns {PropertyReviewsMap[]} 施設ごとのレビューマップの配列
 * @throws {ConvexError} 認証エラーまたはアクセス権限エラー
 *
 * @example
 * const reviewsByProperties = await getRecentReviewsByProperties({
 *   propertyIds: ["prop1", "prop2", "prop3"],
 *   limitPerProperty: 5
 * });
 * // 結果: [{propertyId: "prop1", reviews: [...]}, ...]
 */
export const getRecentReviewsByProperties = query({
	args: {
		propertyIds: v.array(v.id("beds24Properties")),
		limitPerProperty: v.optional(v.number()), // デフォルト: 10
	},
	returns: v.array(
		v.object({
			propertyId: v.id("beds24Properties"),
			reviews: v.array(reviewWithDetailsValidator),
		}),
	),
	handler: async (ctx, args) => {
		const userId = await OtaReviewsModel.requireAuth(ctx);
		const limitPerProperty = args.limitPerProperty ?? 10;

		// 各施設のレビューを並列で取得（ページネーションなし）
		const reviewPromises = args.propertyIds.map(async (propertyId) => {
			const reviews =
				await OtaReviewsModel.getReviewsByPropertyWithAuthNoPagination(
					ctx,
					userId,
					propertyId,
					limitPerProperty,
				);
			return { propertyId, reviews };
		});

		const results = await Promise.all(reviewPromises);

		return results;
	},
});

/**
 * 複数施設の月別レビュー統計を取得
 *
 * 指定された複数の施設から、全期間のレビューを月別で集計した累積統計を取得します。
 * 各月の統計には、その月までの累積レビュー件数と累積平均スコアが含まれます。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {Id<"beds24Properties">[]} args.propertyIds - 取得対象の施設IDの配列
 * @returns {PropertyMonthlyStats[]} 施設ごとの月別累積統計の配列
 * @throws {ConvexError} 認証エラー
 *
 * @example
 * const monthlyStats = await getMonthlyReviewStatsByProperties({
 *   propertyIds: ["prop1", "prop2"]
 * });
 * // 結果: [{propertyId: "prop1", stats: [{date: "2024-01", reviewCount: 5, averageScore: 8.5}, ...]}, ...]
 */
export const getMonthlyReviewStatsByProperties = query({
	args: {
		propertyIds: v.array(v.id("beds24Properties")),
	},
	returns: v.array(
		v.object({
			propertyId: v.id("beds24Properties"),
			stats: v.array(
				v.object({
					date: v.string(), // "YYYY-MM"形式
					reviewCount: v.number(), // 累積レビュー件数
					averageScore: v.number(), // 累積平均スコア
				}),
			),
		}),
	),
	handler: async (ctx, args) => {
		// 認証チェック
		const userId = await OtaReviewsModel.requireAuth(ctx);

		// 空の配列の場合は空を返す
		if (args.propertyIds.length === 0) {
			return [];
		}

		// ユーザーがアクセス権限を持つ施設のみを取得
		const userProperties = await ctx.db
			.query("userProperties")
			.withIndex("by_userId", (q) => q.eq("userId", userId))
			.collect();

		const authorizedPropertyIds = new Set(
			userProperties.map((up) => up.propertyId),
		);

		// 認可された施設のみをフィルタリング
		const filteredPropertyIds = args.propertyIds.filter((propertyId) =>
			authorizedPropertyIds.has(propertyId),
		);

		// 各施設の月別統計を並列で取得
		const statsPromises = filteredPropertyIds.map(async (propertyId) => {
			// 施設の全レビューを取得
			const reviews = await ctx.db
				.query("otaReviews")
				.withIndex("by_beds24PropertyId", (q) =>
					q.eq("beds24PropertyId", propertyId),
				)
				.collect();

			// レビューがない場合は空の統計を返す
			if (reviews.length === 0) {
				return { propertyId, stats: [] };
			}

			// 月別に集計
			const monthlyData = new Map<
				string,
				{ scores: number[]; count: number }
			>();

			reviews.forEach((review) => {
				const date = new Date(review.reviewDate);
				const monthKey = `${date.getFullYear()}-${String(
					date.getMonth() + 1,
				).padStart(2, "0")}`;

				if (!monthlyData.has(monthKey)) {
					monthlyData.set(monthKey, { scores: [], count: 0 });
				}

				const data = monthlyData.get(monthKey)!;
				data.scores.push(review.score);
				data.count++;
			});

			// 月別キーをソート
			const sortedMonths = Array.from(monthlyData.keys()).sort();

			// 累積統計を計算
			let cumulativeCount = 0;
			let cumulativeTotalScore = 0;

			const stats = sortedMonths.map((month) => {
				const monthData = monthlyData.get(month)!;
				const monthTotalScore = monthData.scores.reduce(
					(sum, score) => sum + score,
					0,
				);

				cumulativeCount += monthData.count;
				cumulativeTotalScore += monthTotalScore;

				// 累積平均スコアを計算（小数第1位まで）
				const averageScore =
					Math.round((cumulativeTotalScore / cumulativeCount) * 10) / 10;

				return {
					date: month,
					reviewCount: cumulativeCount,
					averageScore,
				};
			});

			return { propertyId, stats };
		});

		const results = await Promise.all(statsPromises);

		return results;
	},
});
