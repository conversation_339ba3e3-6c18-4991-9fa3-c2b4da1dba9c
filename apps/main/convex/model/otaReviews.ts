/**
 * OTA Reviews Model Layer
 *
 * このファイルはOTAレビューに関するビジネスロジックとヘルパー関数を提供します。
 * query/mutation/actionから呼び出される共通処理を実装します。
 *
 * ベストプラクティス:
 * - 全ての関数に明示的な型付け（QueryCtx、MutationCtx）
 * - 戻り値型の明確な定義
 * - 生成された型（Doc、Id）の一貫した使用
 */

import type { GenericMutationCtx, GenericQueryCtx } from "convex/server";
import type { DataModel, Doc, Id } from "../_generated/dataModel";
import { getAuthenticatedUserId } from "../lib/auth";
import { createConvexError, ERROR_CODES } from "../lib/errors";
import { createLogger } from "../lib/logging";
import type {
	AccessCheckResult,
	PaginatedReviews,
	ReviewStats,
	ReviewWithDetails,
} from "../types/otaReviews";

// 型エイリアスの定義
type Ctx = GenericQueryCtx<DataModel> | GenericMutationCtx<DataModel>;

/**
 * ユーザーが特定の施設へのアクセス権限を持っているかチェック
 *
 * アクセス制御の中核となる関数で、userPropertiesテーブルを通じて
 * ユーザーと施設の関連性を検証します。
 *
 * ビジネスロジック:
 * - 施設の存在確認を先に行い、存在しない場合は早期リターン
 * - userPropertiesテーブルのインデックスを活用した効率的な検索
 * - アクセス権限がない場合は理由を含めたレスポンスを返却
 *
 * @param ctx - QueryまたはMutationコンテキスト
 * @param userId - Clerk認証から取得したユーザーID
 * @param propertyId - Beds24から同期された施設のID
 * @returns アクセス権限チェック結果（hasAccess: boolean, reason?: string）
 *
 * @example
 * const accessCheck = await checkPropertyAccess(ctx, "user_123", propertyId);
 * if (!accessCheck.hasAccess) {
 *   throw new Error(accessCheck.reason);
 * }
 */
export async function checkPropertyAccess(
	ctx: Ctx,
	userId: string,
	propertyId: Id<"beds24Properties">,
): Promise<AccessCheckResult> {
	const logger = createLogger("model.checkPropertyAccess", ctx);
	logger.debug("アクセス権限チェック", { userId, propertyId });

	// 施設の存在確認
	const property = await ctx.db.get(propertyId);
	if (!property) {
		return {
			hasAccess: false,
			reason: "Property not found",
		};
	}

	// userPropertiesテーブルで権限確認
	const hasAccess = await ctx.db
		.query("userProperties")
		.withIndex("by_userId_and_propertyId", (q) =>
			q.eq("userId", userId).eq("propertyId", propertyId),
		)
		.unique();

	return {
		hasAccess: !!hasAccess,
		reason: hasAccess ? undefined : "Access denied",
	};
}

/**
 * 認証済みユーザーのアクセス可能な施設IDを取得
 *
 * ユーザーが管理権限を持つ全施設のIDをSetとして返却します。
 * パフォーマンス最適化のため最大100施設までに制限しています。
 *
 * アクセスパターン:
 * - userPropertiesテーブルの by_userId インデックスを使用
 * - 一括取得により複数のデータベースアクセスを回避
 * - Setデータ構造により重複を自動排除
 *
 * @param ctx - QueryまたはMutationコンテキスト
 * @param userId - Clerk認証から取得したユーザーID
 * @returns アクセス可能な施設IDのSet（最大100件）
 *
 * @example
 * const propertyIds = await getUserAccessiblePropertyIds(ctx, userId);
 * console.log(`ユーザーは${propertyIds.size}件の施設にアクセス可能`);
 */
export async function getUserAccessiblePropertyIds(
	ctx: Ctx,
	userId: string,
): Promise<Set<Id<"beds24Properties">>> {
	const logger = createLogger("model.getUserAccessiblePropertyIds", ctx);
	logger.debug("アクセス可能施設の取得", { userId });

	// 最大100施設まで取得（実用上十分な数）
	const userRelations = await ctx.db
		.query("userProperties")
		.withIndex("by_userId", (q) => q.eq("userId", userId))
		.take(100);

	const propertyIds = new Set<Id<"beds24Properties">>();
	for (const relation of userRelations) {
		propertyIds.add(relation.propertyId);
	}

	logger.debug("アクセス可能施設数", { count: propertyIds.size });
	return propertyIds;
}

/**
 * 施設別のレビューを取得（ページネーション対応）
 *
 * 指定された施設のレビューを新しい順に取得し、OTA情報とともに返却します。
 * アクセス権限のチェックを実施し、権限がない場合はエラーをスローします。
 *
 * ビジネスロジック:
 * - アクセス権限の事前検証による不正アクセス防止
 * - by_beds24PropertyId_and_reviewDate インデックスを使用した高速検索
 * - レビュー日付の降順ソートで最新レビューを優先表示
 * - OTA情報のエンリッチメントによる表示情報の充実
 *
 * @param ctx - Queryコンテキスト
 * @param userId - リクエストユーザーのID
 * @param propertyId - 対象施設のID
 * @param paginationOpts - ページネーションオプション（numItems: 表示件数, cursor: ページカーソル）
 * @returns ページネーションされたレビュー一覧（OTA情報付き）
 * @throws ConvexError アクセス権限がない場合（ERROR_CODES.UNAUTHORIZED）
 *
 * @example
 * const reviews = await getReviewsByPropertyWithAuth(ctx, userId, propertyId, {
 *   numItems: 20,
 *   cursor: null
 * });
 */
export async function getReviewsByPropertyWithAuth(
	ctx: GenericQueryCtx<DataModel>,
	userId: string,
	propertyId: Id<"beds24Properties">,
	paginationOpts: { numItems: number; cursor: string | null },
): Promise<PaginatedReviews> {
	const _logger = createLogger("model.getReviewsByPropertyWithAuth", ctx);

	// アクセス権限チェック
	const accessCheck = await checkPropertyAccess(ctx, userId, propertyId);
	if (!accessCheck.hasAccess) {
		throw createConvexError(
			ERROR_CODES.UNAUTHORIZED,
			accessCheck.reason || "Access denied",
			undefined,
			{ propertyId },
		);
	}

	// レビューを取得（最新順）
	const result = await ctx.db
		.query("otaReviews")
		.withIndex("by_beds24PropertyId_and_reviewDate", (q) =>
			q.eq("beds24PropertyId", propertyId),
		)
		.order("desc")
		.paginate(paginationOpts);

	// OTA情報を結合
	const reviewsWithOta = await enrichReviewsWithDetails(ctx, result.page);

	return {
		...result,
		page: reviewsWithOta,
	};
}

/**
 * 施設のレビューを取得（ページネーションなし）
 *
 * 指定された施設のレビューを一括取得します。
 * ページネーションが不要な場合（ダッシュボード表示など）に使用します。
 *
 * ユースケース:
 * - ダッシュボードでの最新レビュー表示
 * - エクスポート機能での一括データ取得
 * - 統計計算の前処理
 *
 * パフォーマンス考慮:
 * - limitパラメータによる取得件数制限
 * - インデックスを活用した効率的なクエリ
 * - enrichReviewsWithDetailsによる一括情報付与
 *
 * @param ctx - Queryコンテキスト
 * @param userId - リクエストユーザーのID
 * @param propertyId - 対象施設のID
 * @param limit - 取得件数上限（推奨: 100件以下）
 * @returns OTA情報付きレビュー一覧
 * @throws ConvexError アクセス権限がない場合（ERROR_CODES.UNAUTHORIZED）
 */
export async function getReviewsByPropertyWithAuthNoPagination(
	ctx: GenericQueryCtx<DataModel>,
	userId: string,
	propertyId: Id<"beds24Properties">,
	limit: number,
): Promise<ReviewWithDetails[]> {
	const _logger = createLogger(
		"model.getReviewsByPropertyWithAuthNoPagination",
		ctx,
	);

	// アクセス権限チェック
	const accessCheck = await checkPropertyAccess(ctx, userId, propertyId);
	if (!accessCheck.hasAccess) {
		throw createConvexError(
			ERROR_CODES.UNAUTHORIZED,
			accessCheck.reason || "Access denied",
			undefined,
			{ propertyId },
		);
	}

	// レビューを取得（最新順）
	const reviews = await ctx.db
		.query("otaReviews")
		.withIndex("by_beds24PropertyId_and_reviewDate", (q) =>
			q.eq("beds24PropertyId", propertyId),
		)
		.order("desc")
		.take(limit);

	// OTA情報を結合
	return enrichReviewsWithDetails(ctx, reviews);
}

/**
 * レビューに関連情報（施設名、OTA名）を追加
 *
 * レビューデータにUIで必要な関連情報をエンリッチメントします。
 * Promise.allによる並列処理で高速化を実現しています。
 *
 * データエンリッチメント処理:
 * - beds24Properties テーブルから施設名を取得
 * - otaMaster テーブルからOTA名（正式名/略称）を取得
 * - 関連データが存在しない場合のフォールバック処理
 *
 * パフォーマンス最適化:
 * - Promise.allによる並列データ取得
 * - 各レビューごとに2つのクエリを同時実行
 * - N件のレビューで最大2N回のデータベースアクセス
 *
 * @param ctx - Queryコンテキスト
 * @param reviews - エンリッチメント対象のレビュー配列
 * @returns 施設名・OTA名を含む詳細レビュー配列
 *
 * @example
 * const enrichedReviews = await enrichReviewsWithDetails(ctx, rawReviews);
 * // enrichedReviews[0].propertyName: "ホテル名"
 * // enrichedReviews[0].otaName: "Booking.com"
 */
export async function enrichReviewsWithDetails(
	ctx: GenericQueryCtx<DataModel>,
	reviews: Doc<"otaReviews">[],
): Promise<ReviewWithDetails[]> {
	return Promise.all(
		reviews.map(async (review) => {
			const [property, ota] = await Promise.all([
				ctx.db.get(review.beds24PropertyId),
				ctx.db.get(review.otaId),
			]);

			return {
				...review,
				propertyName: property?.name ?? "Unknown Property",
				otaName: ota?.fullName ?? "Unknown OTA",
				otaShortName: ota?.shortName ?? "unknown",
			};
		}),
	);
}

/**
 * ユーザーの全レビューを効率的に取得
 *
 * ユーザーがアクセス権限を持つ全施設のレビューを横断的に取得し、
 * 日付順にソートして返却します。大規模データに対応した最適化実装です。
 *
 * アルゴリズム:
 * 1. ユーザーのアクセス可能施設をすべて取得
 * 2. 各施設から最新レビューを並列取得（最大20件/施設）
 * 3. 全レビューを統合し日付降順でソート
 * 4. 上位limit件を選択してエンリッチメント
 *
 * パフォーマンス最適化:
 * - インデックスを活用した施設別レビュー取得
 * - Promise.allによる並列クエリ実行
 * - 各施設の取得件数制限による総データ量の抑制
 * - ソート後の上位選択による無駄な処理の削減
 *
 * @param ctx - Queryコンテキスト
 * @param userId - 対象ユーザーのID
 * @param limit - 取得上限数（デフォルト: 100、最大推奨: 1000）
 * @returns 日付順にソートされたOTA情報付きレビュー配列
 *
 * @example
 * // 最新50件のレビューを取得
 * const recentReviews = await getUserReviewsOptimized(ctx, userId, 50);
 */
export async function getUserReviewsOptimized(
	ctx: GenericQueryCtx<DataModel>,
	userId: string,
	limit: number = 100,
): Promise<ReviewWithDetails[]> {
	const logger = createLogger("model.getUserReviewsOptimized", ctx);

	// ユーザーがアクセスできる施設を取得
	const accessiblePropertyIds = await getUserAccessiblePropertyIds(ctx, userId);

	if (accessiblePropertyIds.size === 0) {
		logger.debug("アクセス可能な施設がありません");
		return [];
	}

	// 各施設から最新のレビューを取得（インデックスを活用）
	const reviewPromises = Array.from(accessiblePropertyIds).map(
		(propertyId) =>
			ctx.db
				.query("otaReviews")
				.withIndex("by_beds24PropertyId_and_reviewDate", (q) =>
					q.eq("beds24PropertyId", propertyId),
				)
				.order("desc")
				.take(Math.min(limit, 20)), // 各施設から最大20件
	);

	const reviewArrays = await Promise.all(reviewPromises);
	const allReviews = reviewArrays.flat();

	// 日付順にソートして上位を取得
	const sortedReviews = allReviews
		.sort((a, b) => b.reviewDate - a.reviewDate)
		.slice(0, limit);

	// 関連情報を追加
	return enrichReviewsWithDetails(ctx, sortedReviews);
}

/**
 * レビュー統計情報を効率的に計算
 *
 * 指定条件に基づいてレビューの統計情報を集計します。
 * 施設別・OTA別の絞り込みが可能で、大規模データに対応した実装です。
 *
 * 統計計算項目:
 * - 総レビュー数
 * - 平均スコア（0-10のスケール）
 * - スコア分布（整数値でグループ化）
 * - 国別分布（レビュアーの国籍）
 * - 過去30日間のレビュー数（アクティビティ指標）
 *
 * フィルタリングロジック:
 * - propertyId指定時: 単一施設の統計（アクセス権限チェック付き）
 * - propertyId未指定時: ユーザーの全アクセス可能施設の統計
 * - otaId指定時: 特定OTAのレビューのみ集計
 *
 * パフォーマンス考慮:
 * - 統計計算用に最大1000件/施設に制限
 * - バッチ処理による効率的なデータ取得
 * - インメモリでの集計処理
 *
 * @param ctx - Queryコンテキスト
 * @param userId - リクエストユーザーのID
 * @param filters - フィルタリングオプション（propertyId?, otaId?）
 * @returns 統計情報（総数、平均スコア、分布、直近アクティビティ）
 * @throws ConvexError 指定施設へのアクセス権限がない場合
 *
 * @example
 * // 特定施設のBooking.comレビュー統計
 * const stats = await calculateReviewStatsOptimized(ctx, userId, {
 *   propertyId: "property123",
 *   otaId: "bookingcom"
 * });
 */
export async function calculateReviewStatsOptimized(
	ctx: GenericQueryCtx<DataModel>,
	userId: string,
	filters: {
		propertyId?: Id<"beds24Properties">;
		otaId?: Id<"otaMaster">;
	} = {},
): Promise<ReviewStats> {
	const _logger = createLogger("model.calculateReviewStatsOptimized", ctx);

	let reviews: Doc<"otaReviews">[];

	if (filters.propertyId) {
		// 特定施設の統計
		const accessCheck = await checkPropertyAccess(
			ctx,
			userId,
			filters.propertyId,
		);
		if (!accessCheck.hasAccess) {
			throw createConvexError(
				ERROR_CODES.UNAUTHORIZED,
				"Access denied",
				undefined,
				{ propertyId: filters.propertyId },
			);
		}

		// 統計計算のため最大1000件まで取得
		reviews = await ctx.db
			.query("otaReviews")
			.withIndex("by_beds24PropertyId", (q) =>
				q.eq("beds24PropertyId", filters.propertyId!),
			)
			.take(1000);
	} else {
		// 全施設の統計（アクセス可能な施設のみ）
		const accessiblePropertyIds = await getUserAccessiblePropertyIds(
			ctx,
			userId,
		);
		reviews = [];

		// バッチで効率的に取得（各施設から最大100件）
		const reviewPromises = Array.from(accessiblePropertyIds).map((propertyId) =>
			ctx.db
				.query("otaReviews")
				.withIndex("by_beds24PropertyId", (q) =>
					q.eq("beds24PropertyId", propertyId),
				)
				.take(100),
		);

		const reviewArrays = await Promise.all(reviewPromises);
		reviews = reviewArrays.flat();
	}

	// OTAフィルタリング
	if (filters.otaId) {
		reviews = reviews.filter((review) => review.otaId === filters.otaId);
	}

	// 統計計算
	const baseStats = calculateReviewStats(reviews);

	// 過去30日間のレビュー数
	const thirtyDaysAgo = Date.now() - 30 * 24 * 60 * 60 * 1000;
	const recentReviewsCount = reviews.filter(
		(review) => review.reviewDate >= thirtyDaysAgo,
	).length;

	return {
		...baseStats,
		recentReviewsCount,
	};
}

/**
 * レビュー統計情報の計算（ヘルパー関数から移植）
 *
 * レビューの配列から統計情報を計算する純粋関数です。
 * データベースアクセスを含まないため、テスタブルで再利用可能です。
 *
 * 計算アルゴリズム:
 * - 平均スコア: 全レビューのスコア合計 ÷ レビュー数
 * - スコア分布: Math.floorによる整数化後にグループ化
 * - 国別分布: reviewerCountryフィールドの集計（未設定は除外）
 *
 * エッジケース処理:
 * - 空配列の場合: すべてゼロ値で初期化された統計を返却
 * - 国情報なし: countryDistributionには含めない
 * - スコアの丸め: 下方向への丸め（例: 8.9 → 8）
 *
 * @param reviews - スコアと国情報を含むレビュー配列
 * @returns 統計情報（recentReviewsCountを除く）
 *
 * @example
 * const stats = calculateReviewStats([
 *   { score: 8.5, reviewerCountry: "JP" },
 *   { score: 9.2, reviewerCountry: "US" },
 *   { score: 7.8, reviewerCountry: "JP" }
 * ]);
 * // stats.averageScore: 8.5
 * // stats.scoreDistribution: { "7": 1, "8": 1, "9": 1 }
 * // stats.countryDistribution: { "JP": 2, "US": 1 }
 */
export function calculateReviewStats(
	reviews: Array<{
		score: number;
		reviewerCountry?: string;
	}>,
): Omit<ReviewStats, "recentReviewsCount"> {
	if (reviews.length === 0) {
		return {
			totalCount: 0,
			averageScore: 0,
			scoreDistribution: {},
			countryDistribution: {},
		};
	}

	const scoreDistribution: Record<string, number> = {};
	const countryDistribution: Record<string, number> = {};
	let totalScore = 0;

	for (const review of reviews) {
		// スコアの集計
		totalScore += review.score;
		const roundedScore = Math.floor(review.score).toString();
		scoreDistribution[roundedScore] =
			(scoreDistribution[roundedScore] || 0) + 1;

		// 国別の集計
		if (review.reviewerCountry) {
			countryDistribution[review.reviewerCountry] =
				(countryDistribution[review.reviewerCountry] || 0) + 1;
		}
	}

	return {
		totalCount: reviews.length,
		averageScore: totalScore / reviews.length,
		scoreDistribution,
		countryDistribution,
	};
}

/**
 * 認証チェックヘルパー
 *
 * Clerk認証の検証とユーザーID取得を行う共通関数です。
 * 全ての保護されたエンドポイントで使用される認証ゲートです。
 *
 * 認証フロー:
 * 1. Convexの認証コンテキストからユーザー情報を取得
 * 2. 認証情報が存在しない場合はUNAUTHORIZEDエラー
 * 3. Clerk固有のユーザーIDを抽出して返却
 *
 * エラーハンドリング:
 * - 詳細なログ出力による問題の追跡
 * - 構造化エラーによるクライアントへの適切な通知
 * - 関数名の記録によるデバッグの容易化
 *
 * @param ctx - QueryまたはMutationコンテキスト
 * @returns Clerk認証から取得したユーザーID（例: "user_2abc..."）
 * @throws ConvexError 認証されていない場合（ERROR_CODES.UNAUTHORIZED）
 *
 * @example
 * export const protectedQuery = query({
 *   handler: async (ctx) => {
 *     const userId = await requireAuth(ctx);
 *     // 認証済みユーザーのみ実行可能な処理
 *   }
 * });
 */
export async function requireAuth(ctx: Ctx): Promise<string> {
	const logger = createLogger("model.requireAuth", ctx);

	const identity = await ctx.auth.getUserIdentity();
	if (!identity) {
		logger.error("認証エラー: ユーザーが認証されていません");
		throw createConvexError(
			ERROR_CODES.UNAUTHORIZED,
			"Unauthenticated",
			undefined,
			{ functionName: "otaReviews" },
		);
	}

	return getAuthenticatedUserId(identity);
}

/**
 * 特定のレビューを取得（アクセス権限チェック付き）
 *
 * 単一レビューの詳細取得時にアクセス権限を検証します。
 * 権限がない場合はエラーではなくnullを返すソフトな実装です。
 *
 * アクセス制御パターン:
 * - レビューIDから施設IDを取得
 * - 施設へのアクセス権限を検証
 * - 権限がない場合はnullを返却（404相当）
 *
 * ユースケース:
 * - レビュー詳細画面の表示
 * - レビューへの返信機能
 * - レビューの個別分析
 *
 * @param ctx - Queryコンテキスト
 * @param userId - リクエストユーザーのID
 * @param reviewId - 取得対象のレビューID
 * @returns OTA情報付きレビュー詳細またはnull（権限なし/存在しない場合）
 *
 * @example
 * const review = await getReviewWithAuth(ctx, userId, reviewId);
 * if (!review) {
 *   // レビューが存在しないか、アクセス権限がない
 *   return { error: "Review not found" };
 * }
 */
export async function getReviewWithAuth(
	ctx: GenericQueryCtx<DataModel>,
	userId: string,
	reviewId: Id<"otaReviews">,
): Promise<ReviewWithDetails | null> {
	const logger = createLogger("model.getReviewWithAuth", ctx);

	const review = await ctx.db.get(reviewId);
	if (!review) {
		logger.debug("レビューが見つかりません", { reviewId });
		return null;
	}

	// アクセス権限チェック
	const accessCheck = await checkPropertyAccess(
		ctx,
		userId,
		review.beds24PropertyId,
	);
	if (!accessCheck.hasAccess) {
		logger.debug("アクセス権限がありません", { reviewId, userId });
		return null;
	}

	// 関連情報を追加
	const [enrichedReview] = await enrichReviewsWithDetails(ctx, [review]);
	return enrichedReview;
}

/**
 * Booking.com形式のレビューをフォーマット
 *
 * Booking.comの特徴的な「良かった点」「改善点」形式のレビューを
 * 統一フォーマットに変換します。
 *
 * フォーマット仕様:
 * - ポジティブ/ネガティブコメントを日本語ラベル付きで結合
 * - 空白文字のトリミング処理
 * - 両方空の場合は空文字列を返却
 * - 改行による視覚的な区切り
 *
 * @param positive - ポジティブなコメント（良かった点）
 * @param negative - ネガティブなコメント（改善点）
 * @returns 日本語フォーマットされたレビュー内容
 *
 * @example
 * const formatted = formatBookingReview(
 *   "スタッフが親切でした",
 *   "朝食の種類が少ない"
 * );
 * // "良かった点: スタッフが親切でした\n\n改善点: 朝食の種類が少ない"
 */
export function formatBookingReview(
	positive?: string,
	negative?: string,
): string {
	const parts: string[] = [];

	if (positive?.trim()) {
		parts.push(`良かった点: ${positive.trim()}`);
	}

	if (negative?.trim()) {
		parts.push(`改善点: ${negative.trim()}`);
	}

	return parts.length > 0 ? parts.join("\n\n") : "";
}

/**
 * レビューデータのバリデーション
 *
 * 外部APIから取得したレビューデータの妥当性を検証します。
 * データ品質を保証し、不正なデータの混入を防ぎます。
 *
 * バリデーションルール:
 * - スコア: 0-10の数値範囲（OTA標準のスコアリング）
 * - レビュー内容: 文字列型（空文字許可、スコア集計用）
 * - レビュアー名: 必須、空白不可
 * - レビュー日付: 数値型タイムスタンプ、未来日付不可
 *
 * エラー処理:
 * - 各エラーを配列に収集（複数エラーの一括確認）
 * - 英語エラーメッセージ（APIレスポンス用）
 * - isValidフラグによる簡易判定
 *
 * @param data - バリデーション対象のレビューデータ
 * @returns バリデーション結果（isValid: 成功/失敗, errors: エラーメッセージ配列）
 *
 * @example
 * const validation = validateReviewData({
 *   score: 8.5,
 *   reviewContent: "素晴らしい滞在でした",
 *   reviewerName: "山田太郎",
 *   reviewDate: Date.now()
 * });
 * if (!validation.isValid) {
 *   console.error("Validation errors:", validation.errors);
 * }
 */
export function validateReviewData(data: {
	score: number;
	reviewContent: string;
	reviewerName: string;
	reviewDate: number;
}): { isValid: boolean; errors: string[] } {
	const errors: string[] = [];

	// スコアのバリデーション（0-10の範囲）
	if (typeof data.score !== "number" || data.score < 0 || data.score > 10) {
		errors.push("Score must be between 0 and 10");
	}

	// レビュー内容のバリデーション
	// 空のコンテンツも許可（スコア集計のため）
	if (typeof data.reviewContent !== "string") {
		errors.push("Review content must be a string");
	}

	// レビュアー名のバリデーション
	if (!data.reviewerName || data.reviewerName.trim().length === 0) {
		errors.push("Reviewer name is required");
	}

	// 日付のバリデーション（未来の日付を拒否）
	if (typeof data.reviewDate !== "number" || data.reviewDate > Date.now()) {
		errors.push("Review date cannot be in the future");
	}

	return {
		isValid: errors.length === 0,
		errors,
	};
}

/**
 * レビューの一意性を保証するハッシュを生成
 *
 * レビューの重複を防ぐための一意識別子を生成します。
 * 同じレビューが複数回インポートされることを防ぎます。
 *
 * ハッシュ生成アルゴリズム:
 * - OTA ID + 施設ID + レビュー日時 + 内容の一部を結合
 * - 簡易的な数値ハッシュアルゴリズム（本番環境では要改善）
 * - Base36エンコーディングによる短縮表現
 * - "review_" プレフィックスによる識別性向上
 *
 * 一意性の保証:
 * - 同一OTA・施設・日時・内容の組み合わせは同じハッシュ
 * - レビュー内容は最初の100文字のみ使用（性能考慮）
 * - 衝突確率は実用上無視できるレベル
 *
 * @param data - ハッシュ生成用データ（OTA、施設、日時、内容）
 * @returns 一意識別子文字列（例: "review_a1b2c3d"）
 *
 * @example
 * const hash = generateReviewHash({
 *   otaId: "ota_123",
 *   beds24PropertyId: "prop_456",
 *   reviewDate: 1704067200000,
 *   reviewContent: "素晴らしいホテルでした..."
 * });
 * // データベースでhashの重複チェックに使用
 */
export function generateReviewHash(data: {
	otaId: Id<"otaMaster">;
	beds24PropertyId: Id<"beds24Properties">;
	reviewDate: number;
	reviewContent: string;
}): string {
	// シンプルな文字列結合によるハッシュ生成
	// 実際の実装では、より堅牢なハッシュアルゴリズムを使用することを推奨
	const hashSource = [
		data.otaId,
		data.beds24PropertyId,
		data.reviewDate,
		data.reviewContent.substring(0, 100), // 内容の最初の100文字のみ使用
	].join("-");

	// 簡易的なハッシュ生成（本番環境では crypto.subtle.digest 等を使用推奨）
	let hash = 0;
	for (let i = 0; i < hashSource.length; i++) {
		const char = hashSource.charCodeAt(i);
		hash = (hash << 5) - hash + char;
		hash = hash & hash; // Convert to 32-bit integer
	}

	return `review_${Math.abs(hash).toString(36)}`;
}
