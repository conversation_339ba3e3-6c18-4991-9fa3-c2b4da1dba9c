/**
 * User Settings Model Layer
 *
 * このファイルはユーザー設定に関するビジネスロジックとヘルパー関数を提供します。
 * query/mutation/actionから呼び出される共通処理を実装します。
 */

import type { Id } from "../_generated/dataModel";
import type { MutationCtx, QueryCtx } from "../_generated/server";
import { createLogger } from "../lib/logging";
import {
	type Beds24UpdateResult,
	DEFAULT_SETTINGS,
	type UpdatableUserSettingsFields,
	type UserSettingsArgs,
	type UserSettingsDoc,
	type UserSettingsWithoutSystemFields,
} from "../types/userSettings";

/**
 * ユーザーIDで設定を取得する（最適化版）
 *
 * ユーザーIDを元に、そのユーザーの設定ドキュメントを効率的に取得します。
 * by_userIdインデックスを使用することで、フルテーブルスキャンを回避し、
 * O(log n)の時間計算量で検索を実行します。
 *
 * パフォーマンス最適化:
 * - インデックスを使用した高速検索（by_userIdインデックス使用）
 * - unique()を使用して単一結果を保証（ユーザーごとに1つの設定のみ）
 * - 不要なログ出力を削減（デバッグモード以外ではログなし）
 *
 * @param ctx - Convexのクエリまたはミューテーションコンテキスト
 * @param userId - 検索対象のユーザーID（Clerk提供のユーザーID）
 * @returns ユーザー設定ドキュメント、存在しない場合はnull
 *
 * @example
 * // クエリ内での使用例
 * const settings = await getUserSettingsByUserId(ctx, "user_123");
 * if (settings) {
 *   console.log("現在のテーマ:", settings.theme);
 * }
 */
export async function getUserSettingsByUserId(
	ctx: QueryCtx | MutationCtx,
	userId: string,
): Promise<UserSettingsDoc | null> {
	return await ctx.db
		.query("userSettings")
		.withIndex("by_userId", (q) => q.eq("userId", userId))
		.unique();
}

/**
 * 新規ユーザー設定を作成する
 *
 * 指定されたユーザーIDに対して新しい設定ドキュメントを作成します。
 * この関数は直接呼び出されることは少なく、通常はupsertUserSettingsや
 * getOrCreateUserSettingsから内部的に呼び出されます。
 *
 * 作成時の処理:
 * - デフォルト値の適用（themeがない場合はDEFAULT_SETTINGSを使用）
 * - has_beds24_tokenフラグの自動計算
 * - タイムスタンプ（createdAt, updatedAt）の設定
 *
 * @param ctx - Convexのミューテーションコンテキスト
 * @param userId - 設定を作成するユーザーのID
 * @param settings - 初期設定値（theme, beds24を含む）
 * @returns 作成された設定ドキュメントのID
 * @throws データベースエラーが発生した場合
 *
 * @example
 * // 新規ユーザーの設定を作成
 * const settingsId = await createUserSettings(ctx, "user_123", {
 *   theme: "dark",
 *   beds24: { refreshToken: "token_abc" }
 * });
 */
export async function createUserSettings(
	ctx: MutationCtx,
	userId: string,
	settings: UserSettingsArgs,
): Promise<Id<"userSettings">> {
	const logger = createLogger("model.createUserSettings", ctx);
	logger.debug("新規設定を作成", { userId, settings });

	const now = Date.now();
	const newSettings: UserSettingsWithoutSystemFields = {
		userId,
		theme: settings.theme || DEFAULT_SETTINGS.theme,
		beds24: settings.beds24,
		has_beds24_token: !!settings.beds24?.refreshToken,
		createdAt: now,
		updatedAt: now,
	};

	const id = await ctx.db.insert("userSettings", newSettings);
	logger.info("ユーザー設定を作成しました", { settingsId: id });

	return id;
}

/**
 * 既存のユーザー設定を更新する
 *
 * 指定されたIDの設定ドキュメントを部分的に更新します。
 * 更新対象のフィールドのみを含むオブジェクトを渡すことで、
 * 必要な部分だけを効率的に更新できます。
 *
 * 更新戦略:
 * - 部分更新（patchメソッド使用）により、指定されたフィールドのみ更新
 * - beds24フィールド更新時はhas_beds24_tokenも自動更新
 * - updatedAtタイムスタンプの自動更新
 *
 * @param ctx - Convexのミューテーションコンテキスト
 * @param settingsId - 更新対象の設定ドキュメントID
 * @param updates - 更新するフィールドと値（undefinedのフィールドは無視）
 * @returns void
 * @throws 指定されたIDの設定が存在しない場合
 *
 * @example
 * // テーマのみを更新
 * await updateUserSettings(ctx, settingsId, { theme: "light" });
 *
 * // Beds24設定を更新
 * await updateUserSettings(ctx, settingsId, {
 *   beds24: { refreshToken: "new_token" }
 * });
 */
export async function updateUserSettings(
	ctx: MutationCtx,
	settingsId: Id<"userSettings">,
	updates: UserSettingsArgs,
): Promise<void> {
	const logger = createLogger("model.updateUserSettings", ctx);
	logger.debug("設定を更新", { settingsId, updates });

	const now = Date.now();
	const updateData = {
		...(updates.theme !== undefined && { theme: updates.theme }),
		...(updates.beds24 !== undefined && {
			beds24: updates.beds24,
			has_beds24_token: !!updates.beds24?.refreshToken,
		}),
		updatedAt: now,
	};

	await ctx.db.patch(settingsId, updateData);
	logger.info("ユーザー設定を更新しました", { settingsId });
}

/**
 * ユーザー設定を作成または更新する（最適化版）
 *
 * ユーザー設定の存在確認と作成/更新を単一のトランザクションで実行します。
 * 設定が既に存在する場合は変更があるフィールドのみを更新し、
 * 存在しない場合は新規作成します。
 *
 * パフォーマンス最適化:
 * - 単一のトランザクションで読み取りと更新を実行（ラウンドトリップ削減）
 * - 変更がない場合は更新をスキップ（不要なDB書き込みを回避）
 * - 不要なヘルパー関数の呼び出しを削減
 * - フィールドごとの差分検出により最小限の更新
 *
 * 効率的な更新戦略:
 * 1. 既存設定の取得（インデックス使用）
 * 2. 変更検出（深い比較による差分検出）
 * 3. 変更がある場合のみDB更新
 * 4. 更新フィールドのログ記録
 *
 * @param ctx - Convexのミューテーションコンテキスト
 * @param userId - 対象ユーザーのID
 * @param settings - 作成または更新する設定値
 * @returns 設定ドキュメントのID（新規作成または既存のID）
 *
 * @example
 * // ユーザー設定を作成または更新（変更がない場合はスキップ）
 * const settingsId = await upsertUserSettings(ctx, "user_123", {
 *   theme: "dark",
 *   beds24: { refreshToken: "token_xyz" }
 * });
 */
export async function upsertUserSettings(
	ctx: MutationCtx,
	userId: string,
	settings: UserSettingsArgs,
): Promise<Id<"userSettings">> {
	const logger = createLogger("model.upsertUserSettings", ctx);
	const now = Date.now();
	const existingSettings = await getUserSettingsByUserId(ctx, userId);

	if (existingSettings) {
		// 更新が必要なフィールドのみを抽出
		const updates: Partial<UserSettingsWithoutSystemFields> = {};
		let hasChanges = false;

		// themeの変更確認
		if (
			settings.theme !== undefined &&
			settings.theme !== existingSettings.theme
		) {
			updates.theme = settings.theme;
			hasChanges = true;
		}

		// beds24の変更確認
		if (settings.beds24 !== undefined) {
			const currentToken = existingSettings.beds24?.refreshToken;
			const newToken = settings.beds24.refreshToken;

			if (currentToken !== newToken) {
				updates.beds24 = settings.beds24;
				updates.has_beds24_token = !!newToken;
				hasChanges = true;
			}
		}

		// 変更がない場合は更新をスキップ
		if (!hasChanges) {
			logger.debug("変更なし、更新をスキップ", {
				settingsId: existingSettings._id,
			});
			return existingSettings._id;
		}

		// 変更がある場合のみ更新
		updates.updatedAt = now;
		await ctx.db.patch(existingSettings._id, updates);
		logger.info("ユーザー設定を更新しました", {
			settingsId: existingSettings._id,
			updatedFields: Object.keys(updates),
		});
		return existingSettings._id;
	} else {
		return await createUserSettings(ctx, userId, settings);
	}
}

/**
 * ユーザー設定を削除する
 *
 * 指定されたユーザーIDに関連付けられた設定ドキュメントを削除します。
 * ユーザーアカウントの削除時やデータのクリーンアップ時に使用されます。
 *
 * 削除処理の流れ:
 * 1. ユーザーIDで設定を検索（インデックス使用）
 * 2. 設定が存在する場合は削除
 * 3. 削除の成否を返す
 *
 * @param ctx - Convexのミューテーションコンテキスト
 * @param userId - 削除対象のユーザーID
 * @returns 削除に成功した場合true、設定が存在しなかった場合false
 *
 * @example
 * // ユーザーアカウント削除時の処理
 * const wasDeleted = await deleteUserSettingsByUserId(ctx, "user_123");
 * if (wasDeleted) {
 *   console.log("ユーザー設定を削除しました");
 * } else {
 *   console.log("削除対象の設定が見つかりませんでした");
 * }
 */
export async function deleteUserSettingsByUserId(
	ctx: MutationCtx,
	userId: string,
): Promise<boolean> {
	const logger = createLogger("model.deleteUserSettingsByUserId", ctx);
	logger.debug("設定削除を開始", { userId });

	const existingSettings = await getUserSettingsByUserId(ctx, userId);

	if (existingSettings) {
		await ctx.db.delete(existingSettings._id);
		logger.info("ユーザー設定を削除しました", {
			settingsId: existingSettings._id,
		});
		return true;
	}

	logger.warn("削除対象のユーザー設定が見つかりませんでした");
	return false;
}

/**
 * 特定フィールドを効率的に更新する（最適化版）
 *
 * 単一のフィールドを対象とした効率的な更新を実行します。
 * ジェネリック型パラメータにより型安全性を保証しながら、
 * 変更検出により不要な更新を回避します。
 *
 * パフォーマンス最適化:
 * - 必要なフィールドのみを更新（部分更新）
 * - 既存の値と比較して変更がない場合はスキップ
 * - 型安全性を保ちながらany型の使用を削減
 * - JSON.stringifyによる深い比較で複雑なオブジェクトも対応
 *
 * 特殊な処理:
 * - beds24フィールド更新時は has_beds24_token も自動更新
 * - 設定が存在しない場合はデフォルト値で新規作成
 *
 * @param ctx - Convexのミューテーションコンテキスト
 * @param userId - 対象ユーザーのID
 * @param field - 更新するフィールド名（型安全）
 * @param value - 新しい値（フィールドの型に応じた値）
 * @returns 設定ドキュメントのID
 *
 * @example
 * // テーマフィールドのみを更新
 * await updateUserSettingsField(ctx, "user_123", "theme", "dark");
 *
 * // Beds24設定を更新（has_beds24_tokenも自動更新される）
 * await updateUserSettingsField(ctx, "user_123", "beds24", {
 *   refreshToken: "new_token"
 * });
 */
export async function updateUserSettingsField<
	TField extends UpdatableUserSettingsFields,
>(
	ctx: MutationCtx,
	userId: string,
	field: TField,
	value: UserSettingsWithoutSystemFields[TField],
): Promise<Id<"userSettings">> {
	const logger = createLogger("model.updateUserSettingsField", ctx);
	const now = Date.now();
	const existingSettings = await getUserSettingsByUserId(ctx, userId);

	if (existingSettings) {
		// 既存の値と比較
		const currentValue = existingSettings[field];

		// 値が同じ場合は更新をスキップ
		if (JSON.stringify(currentValue) === JSON.stringify(value)) {
			logger.debug("値に変更なし、更新をスキップ", {
				settingsId: existingSettings._id,
				field,
			});
			return existingSettings._id;
		}

		// 更新ペイロードを構築
		const updatePayload: Partial<UserSettingsWithoutSystemFields> = {
			[field]: value,
			updatedAt: now,
		};

		// beds24フィールドの場合はhas_beds24_tokenも更新
		if (field === "beds24") {
			const beds24Value = value as UserSettingsWithoutSystemFields["beds24"];
			updatePayload.has_beds24_token = !!beds24Value?.refreshToken;
		}

		await ctx.db.patch(existingSettings._id, updatePayload);
		logger.info("フィールドを更新しました", {
			settingsId: existingSettings._id,
			field,
		});
		return existingSettings._id;
	} else {
		// 設定が存在しない場合は、デフォルト値で新規作成
		const defaultSettings: UserSettingsWithoutSystemFields = {
			userId,
			theme: DEFAULT_SETTINGS.theme,
			has_beds24_token: false,
			createdAt: now,
			updatedAt: now,
			[field]: value,
		} as UserSettingsWithoutSystemFields;

		// beds24フィールドが更新される場合はhas_beds24_tokenも更新
		if (field === "beds24") {
			const beds24Value = value as UserSettingsWithoutSystemFields["beds24"];
			defaultSettings.has_beds24_token = !!beds24Value?.refreshToken;
		}

		const id = await ctx.db.insert("userSettings", defaultSettings);
		logger.info("新規設定を作成しました", { settingsId: id });
		return id;
	}
}

/**
 * Beds24のリフレッシュトークンを更新する
 *
 * Beds24 APIアクセス用のリフレッシュトークンを効率的に更新します。
 * この関数はBeds24統合の認証フローで使用され、トークンの設定と削除の
 * 両方に対応しています。
 *
 * Beds24トークン管理の特徴:
 * - リフレッシュトークンの存在に基づくhas_beds24_tokenフラグの自動管理
 * - 変更検出による不要な更新の回避
 * - 設定が存在しない場合の自動作成
 *
 * 返り値の詳細:
 * - id: 設定ドキュメントのID
 * - wasUpdated: データベースへの書き込みが発生したか
 * - wasCreated: 新規作成されたか（既存更新の場合はfalse）
 *
 * @param ctx - Convexのミューテーションコンテキスト
 * @param userId - 対象ユーザーのID
 * @param refreshToken - 新しいリフレッシュトークン（undefinedで削除）
 * @returns 更新結果を表すオブジェクト
 *
 * @example
 * // トークンを設定
 * const result = await updateBeds24RefreshToken(ctx, "user_123", "refresh_token_abc");
 * if (result.wasCreated) {
 *   console.log("新規設定を作成しました");
 * }
 *
 * // トークンを削除
 * await updateBeds24RefreshToken(ctx, "user_123", undefined);
 */
export async function updateBeds24RefreshToken(
	ctx: MutationCtx,
	userId: string,
	refreshToken?: string,
): Promise<Beds24UpdateResult> {
	const logger = createLogger("model.updateBeds24RefreshToken", ctx);
	logger.debug("Beds24トークン更新", { userId, hasToken: !!refreshToken });

	const now = Date.now();
	const existingSettings = await getUserSettingsByUserId(ctx, userId);

	if (existingSettings) {
		const currentBeds24 = existingSettings.beds24 || {};

		// 値が変更されていない場合はスキップ
		if (currentBeds24.refreshToken === refreshToken) {
			logger.debug("Beds24設定に変更なし");
			return {
				id: existingSettings._id,
				wasUpdated: false,
				wasCreated: false,
			};
		}

		// 変更がある場合のみ更新
		const updatedBeds24 = {
			...currentBeds24,
			...(refreshToken !== undefined && { refreshToken }),
		};

		await ctx.db.patch(existingSettings._id, {
			beds24: updatedBeds24,
			has_beds24_token: !!refreshToken,
			updatedAt: now,
		});

		logger.info("Beds24設定を更新しました", {
			settingsId: existingSettings._id,
			hasRefreshToken: !!refreshToken,
		});

		return {
			id: existingSettings._id,
			wasUpdated: true,
			wasCreated: false,
		};
	} else {
		// 設定が存在しない場合は、新規作成
		const id = await createUserSettings(ctx, userId, {
			theme: DEFAULT_SETTINGS.theme,
			beds24: { refreshToken },
		});

		logger.info("新規Beds24設定を作成しました", {
			settingsId: id,
			hasRefreshToken: !!refreshToken,
		});

		return {
			id,
			wasUpdated: true,
			wasCreated: true,
		};
	}
}

/**
 * ユーザー設定の初期化または既存確認（最適化版）
 *
 * ユーザー設定が存在する場合はそのIDを返し、存在しない場合は
 * デフォルト値で新規作成します。この関数は単一インスタンスを
 * 保証するパターンを実装しています。
 *
 * パフォーマンス最適化:
 * - 単一のトランザクションで読み取りと作成を実行
 * - 競合状態を防ぐための適切なエラーハンドリング
 *
 * 単一インスタンス保証パターン:
 * 1. まず既存の設定を検索（高速なインデックス検索）
 * 2. 存在しない場合のみ新規作成を試行
 * 3. 同時実行による競合が発生した場合は再検索
 * 4. 最終的に必ず有効なIDを返す
 *
 * このパターンにより、複数のリクエストが同時に実行されても
 * ユーザーごとに設定ドキュメントが1つだけ作成されることを保証します。
 *
 * @param ctx - Convexのミューテーションコンテキスト
 * @param userId - 対象ユーザーのID
 * @returns 既存または新規作成された設定ドキュメントのID
 * @throws データベースエラー（競合以外）が発生した場合
 *
 * @example
 * // ユーザーの初回ログイン時
 * const settingsId = await getOrCreateUserSettings(ctx, "user_123");
 * // この関数は冪等性があり、何度呼んでも同じIDが返される
 *
 * @example
 * // 同時実行に対する安全性
 * // 複数のAPIリクエストが同時に実行されても、
 * // 設定ドキュメントは1つだけ作成される
 * const [id1, id2] = await Promise.all([
 *   getOrCreateUserSettings(ctx, "user_123"),
 *   getOrCreateUserSettings(ctx, "user_123")
 * ]);
 * // id1 === id2 が保証される
 */
export async function getOrCreateUserSettings(
	ctx: MutationCtx,
	userId: string,
): Promise<Id<"userSettings">> {
	const logger = createLogger("model.getOrCreateUserSettings", ctx);

	// 既存の設定を確認
	const existingSettings = await getUserSettingsByUserId(ctx, userId);

	if (existingSettings) {
		return existingSettings._id;
	}

	// デフォルト設定で新規作成
	const now = Date.now();
	const newSettings: UserSettingsWithoutSystemFields = {
		userId,
		theme: DEFAULT_SETTINGS.theme,
		has_beds24_token: false,
		createdAt: now,
		updatedAt: now,
	};

	// 競合状態を考慮して作成
	try {
		const id = await ctx.db.insert("userSettings", newSettings);
		logger.info("ユーザー設定を新規作成しました", { settingsId: id });
		return id;
	} catch (error) {
		// 同時実行により既に作成されている可能性があるため再度確認
		const settings = await getUserSettingsByUserId(ctx, userId);
		if (settings) {
			logger.debug("競合検出：既存の設定を使用", { settingsId: settings._id });
			return settings._id;
		}
		throw error;
	}
}
