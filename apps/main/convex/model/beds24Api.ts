import type { createLogger } from "../lib/logging";

// ========== 型定義 ==========

/**
 * Beds24 API施設データの型定義
 *
 * @description
 * Beds24 APIから返される施設（プロパティ）情報の型定義です。
 * 施設の基本情報、住所、連絡先、チェックイン/アウト時間、
 * アカウント情報、予約質問設定など、包括的な施設データを含みます。
 *
 * @example
 * ```typescript
 * const property: Beds24APIProperty = {
 *   id: 12345,
 *   name: "サンプルホテル",
 *   propertyType: "hotel",
 *   currency: "JPY",
 *   country: "JP",
 *   city: "東京"
 * };
 * ```
 */
export interface Beds24APIProperty {
	// 基本情報
	id: number;
	name: string;
	propertyType: string;
	currency: string;

	// 住所情報
	address?: string;
	city?: string;
	state?: string;
	country?: string; // 2文字のISOコード（例: "JP", "US"）
	postcode?: string;
	latitude?: number;
	longitude?: number;

	// 連絡先情報
	phone?: string;
	mobile?: string;
	email?: string;
	web?: string;
	contactFirstName?: string;
	contactLastName?: string;

	// チェックイン/アウト時間
	checkInStart?: string;
	checkInEnd?: string;
	checkOutEnd?: string;

	// アカウント情報
	account?: {
		ownerId?: number;
		unitStatuses?: Array<{
			text: string;
			color: string;
		}>;
	};

	// 予約質問設定
	bookingQuestions?: {
		[key: string]: {
			order?: number | null; // Beds24 APIではorderフィールドがnullの場合がある
			type?: string | null; // Beds24 APIではtypeフィールドがnullの場合がある
			usage: string;
		};
	};
}

// ========== ヘルパー関数 ==========

/**
 * APIレスポンスの施設データが有効かどうかを検証
 *
 * @description
 * Beds24 APIから返されたデータが正しい施設データ形式かどうかを
 * 型ガードとして検証します。必須フィールドの存在と型、
 * オプショナルフィールドの型を厳密にチェックします。
 *
 * @param data - 検証対象のデータ（unknown型）
 * @returns データが有効な施設データかどうか（型ガード）
 *
 * @internal
 */
function isValidPropertyData(data: unknown): data is Beds24APIProperty {
	if (typeof data !== "object" || data === null) {
		return false;
	}

	const property = data as any;

	// 必須フィールドの検証
	if (typeof property.id !== "number") {
		return false;
	}

	// オプショナルフィールドの型検証（存在する場合のみ）
	const stringFields = [
		"name",
		"propertyType",
		"currency",
		"address",
		"city",
		"state",
		"country",
		"postcode",
		"phone",
		"mobile",
		"email",
		"web",
	];
	for (const field of stringFields) {
		if (property[field] !== undefined && typeof property[field] !== "string") {
			return false;
		}
	}

	const numberFields = ["latitude", "longitude"];
	for (const field of numberFields) {
		if (property[field] !== undefined && typeof property[field] !== "number") {
			return false;
		}
	}

	return true;
}

/**
 * APIレスポンスデータの検証とログ記録
 *
 * @description
 * Beds24 APIから返された施設データ配列を検証し、有効なデータのみを抽出します。
 * 無効なデータが含まれていた場合は、ロガーに警告を記録します。
 * データの整合性を保証し、型安全性を確保するための重要な処理です。
 *
 * @param data - 検証対象のデータ配列
 * @param logger - ロガーインスタンス（オプション）
 * @returns 検証済みの施設データ配列
 */
export function validatePropertiesResponse(
	data: unknown[],
	logger?: ReturnType<typeof createLogger>,
): Beds24APIProperty[] {
	const validProperties: Beds24APIProperty[] = [];
	const invalidItems: any[] = [];

	for (const item of data) {
		if (isValidPropertyData(item)) {
			validProperties.push(item);
		} else {
			invalidItems.push(item);
		}
	}

	if (invalidItems.length > 0) {
		logger?.warn(`無効な施設データが含まれています`, {
			invalidCount: invalidItems.length,
			validCount: validProperties.length,
			sampleInvalid: invalidItems.slice(0, 3), // 最初の3件のみログに記録
		});
	}

	return validProperties;
}

// ========== API関数 ==========

// API呼び出し関数は beds24ApiActions.ts に移動しました。
// このファイルは型定義とヘルパー関数のみを含みます。
