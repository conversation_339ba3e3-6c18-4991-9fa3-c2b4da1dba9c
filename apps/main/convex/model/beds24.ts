/**
 * Beds24 Model Layer
 *
 * このファイルはBeds24に関するビジネスロジックとヘルパー関数を提供します。
 * query/mutation/actionから呼び出される共通処理を実装します。
 */

import { internal } from "../_generated/api";
import type { Id } from "../_generated/dataModel";
import type { MutationCtx, QueryCtx } from "../_generated/server";
import { createConvexError, ERROR_CODES } from "../lib/errors";
import { createLogger } from "../lib/logging";
import type {
	Beds24PropertyFromDB,
	ConnectionStatus,
	PropertyDetails,
	PropertySummary,
	SyncStatusInfo,
} from "../types/beds24";
import {
	JobPriority,
	QueueJobType,
	SyncErrorCode,
	SyncHistoryStatus,
} from "../types/beds24";
import type { Beds24APIProperty } from "./beds24Api";

/**
 * ユーザーのBeds24施設一覧を取得する
 *
 * ユーザーに紐づく全ての施設を取得し、フロントエンド用のサマリー形式に変換します。
 * userPropertiesテーブルを経由してアクセス権限を確認し、削除済み施設のフィルタリングも行います。
 *
 * @param ctx - Convexクエリコンテキスト
 * @param userId - 対象ユーザーのID
 * @param includeDeleted - 削除済み施設を含めるかどうか（デフォルト: false）
 * @returns 施設サマリーの配列。各施設には基本情報（名前、住所、通貨など）が含まれます
 * @example
 * const properties = await getPropertiesByUserId(ctx, "user123", false);
 * // 返却値: [{ _id: "...", name: "ホテル東京", city: "Tokyo", ... }]
 */
export async function getPropertiesByUserId(
	ctx: QueryCtx,
	userId: string,
	includeDeleted = false,
): Promise<PropertySummary[]> {
	const logger = createLogger("model.getPropertiesByUserId", ctx);
	logger.debug("施設一覧を取得", { userId, includeDeleted });

	// 直接データベースから施設を取得
	const properties = new Map<string, Beds24PropertyFromDB>();

	// userPropertiesテーブル経由で取得
	const userRelations = await ctx.db
		.query("userProperties")
		.withIndex("by_userId", (q) => q.eq("userId", userId))
		.collect();

	for (const relation of userRelations) {
		const property = await ctx.db.get(relation.propertyId);
		if (property && (includeDeleted || !property.isDeleted)) {
			properties.set(property._id, property as Beds24PropertyFromDB);
		}
	}

	const result = Array.from(properties.values());

	// フロントエンド用にデータを整形
	return result.map((prop) => ({
		_id: prop._id,
		_creationTime: prop._creationTime,
		beds24PropertyId: prop.beds24PropertyId,
		beds24PropertyKey: prop.beds24PropertyKey,
		name: prop.data?.name || "名称未設定",
		isDeleted: prop.isDeleted,
		deletedAt: prop.deletedAt,
		lastSyncedAt: prop.lastSyncedAt,
		address: prop.data?.address,
		city: prop.data?.city,
		country: prop.data?.country,
		currency: prop.data?.currency,
		timeZone: prop.data?.timeZone,
	}));
}

/**
 * ユーザーの同期状態を取得する
 *
 * 指定されたジョブタイプ（施設同期、レビュー同期など）の現在の同期状態を取得します。
 * 処理中のジョブ、保留中のジョブ、最新の同期履歴の情報を集約して返します。
 *
 * @param ctx - Convexクエリコンテキスト
 * @param userId - 対象ユーザーのID
 * @param jobType - 同期ジョブの種類（省略時は施設同期）
 * @returns 同期状態情報。処理中フラグ、最新同期の詳細、保留ジョブ数を含む。nullの場合は同期履歴なし
 * @example
 * const status = await getSyncStatusForUser(ctx, "user123", "sync_properties");
 * // status.isProcessing === true の場合、現在同期処理中
 * // status.lastSync には最新の同期結果が含まれる
 */
export async function getSyncStatusForUser(
	ctx: QueryCtx,
	userId: string,
	jobType?:
		| "sync_properties"
		| "sync_reviews"
		| "scrape_booking_com_slug"
		| "sync_bookings",
): Promise<SyncStatusInfo | null> {
	const logger = createLogger("model.getSyncStatusForUser", ctx);
	logger.debug("同期状態を取得", { userId, jobType });

	const targetJobType = jobType || QueueJobType.SYNC_PROPERTIES;

	// 最新の同期履歴を取得
	const syncQuery = ctx.db
		.query("beds24SyncHistory")
		.withIndex("by_userId_and_jobType_and_startedAt", (q) =>
			q.eq("userId", userId).eq("jobType", targetJobType),
		)
		.order("desc");

	const latestSyncRecord = await syncQuery.first();

	// キューの状態を確認
	const pendingJobs = await ctx.db
		.query("syncQueue")
		.withIndex("by_userId_and_status", (q) =>
			q.eq("userId", userId).eq("status", "pending"),
		)
		.collect();

	const processingJobs = await ctx.db
		.query("syncQueue")
		.withIndex("by_userId_and_status", (q) =>
			q.eq("userId", userId).eq("status", "processing"),
		)
		.collect();

	const isProcessing: boolean =
		processingJobs.length > 0 ||
		(latestSyncRecord && latestSyncRecord.status === "processing") ||
		false;

	return {
		isProcessing,
		lastSync: latestSyncRecord
			? {
					_id: latestSyncRecord._id,
					status: latestSyncRecord.status,
					startedAt: latestSyncRecord.startedAt,
					completedAt:
						latestSyncRecord.completedAt || latestSyncRecord.startedAt,
					duration: latestSyncRecord.duration || 0,
					totalItems: latestSyncRecord.totalItems || 0,
					successCount: latestSyncRecord.successCount || 0,
					failedCount: latestSyncRecord.failedCount || 0,
				}
			: undefined,
		pendingJobs: pendingJobs.length,
	};
}

/**
 * 手動同期をトリガーする
 *
 * ユーザーの明示的な操作により施設同期を開始します。
 * Beds24の連携設定を確認し、処理中のジョブがないことを確認してから、
 * 高優先度の同期ジョブをキューに追加します。
 *
 * @param ctx - Convexミューテーションコンテキスト
 * @param userId - 対象ユーザーのID
 * @returns 同期トリガーの結果。成功/失敗フラグ、メッセージ、ジョブIDを含む
 * @throws Beds24の連携設定が未完了の場合、または既に同期処理中の場合はエラーメッセージを返す
 * @example
 * const result = await triggerManualSyncForUser(ctx, "user123");
 * if (result.success) {
 *   console.log("同期開始:", result.jobId);
 * } else {
 *   console.error("同期失敗:", result.message);
 * }
 */
export async function triggerManualSyncForUser(
	ctx: MutationCtx,
	userId: string,
): Promise<{
	success: boolean;
	message: string;
	jobId?: Id<"syncQueue">;
}> {
	const logger = createLogger("model.triggerManualSyncForUser", ctx);
	logger.debug("手動同期をトリガー", { userId });

	// Beds24設定を確認
	const userSettings = await ctx.db
		.query("userSettings")
		.withIndex("by_userId", (q) => q.eq("userId", userId))
		.unique();

	if (!userSettings?.beds24?.refreshToken) {
		return {
			success: false,
			message:
				"Beds24の連携設定が完了していません。設定画面からAPIキーを設定してください。",
		};
	}

	// 現在処理中のジョブがあるか確認
	const processingJobs = await ctx.db
		.query("syncQueue")
		.withIndex("by_userId_and_status", (q) =>
			q.eq("userId", userId).eq("status", "processing"),
		)
		.filter((q) => q.eq(q.field("jobType"), QueueJobType.SYNC_PROPERTIES))
		.collect();

	if (processingJobs.length > 0) {
		return {
			success: false,
			message: "現在同期処理が実行中です。しばらくお待ちください。",
		};
	}

	// 同期ジョブをキューに追加
	const jobId = await ctx.runMutation(internal.beds24Queue.enqueueJob, {
		userId,
		jobType: QueueJobType.SYNC_PROPERTIES,
		priority: JobPriority.HIGH, // 手動同期は高優先度
		metadata: {
			triggeredBy: "manual",
			triggeredAt: Date.now(),
		},
	});

	logger.info("手動同期がトリガーされました", { userId, jobId });

	return {
		success: true,
		message: "同期を開始しました。完了まで数分かかる場合があります。",
		jobId,
	};
}

/**
 * Beds24のAPIキーを更新する
 *
 * ユーザーが提供したAPIキー（リフレッシュトークン）を検証し、
 * userSettingsテーブルに保存します。既存のアクセストークンは削除され、
 * 次回のAPI呼び出し時に新しいトークンが取得されます。
 *
 * @param ctx - Convexミューテーションコンテキスト
 * @param userId - 対象ユーザーのID
 * @param apiKey - Beds24のAPIキー（Base64形式のリフレッシュトークン）
 * @returns 更新結果。成功/失敗フラグとメッセージを含む
 * @throws APIキーが無効な形式（非Base64、長さ制限超過など）の場合はエラーメッセージを返す
 * @example
 * const result = await updateBeds24ApiKey(ctx, "user123", "base64EncodedKey==");
 * if (result.success) {
 *   console.log("APIキー更新成功");
 * }
 */
export async function updateBeds24ApiKey(
	ctx: MutationCtx,
	userId: string,
	apiKey: string,
): Promise<{
	success: boolean;
	message: string;
}> {
	const logger = createLogger("model.updateBeds24ApiKey", ctx);
	logger.debug("APIキーを更新", { userId, hasApiKey: !!apiKey });

	// APIキーの基本的なバリデーション
	if (!apiKey || apiKey.length < 10) {
		return {
			success: false,
			message: "有効なAPIキーを入力してください。",
		};
	}

	// Base64形式の基本チェック（緩やかな検証）
	const base64Pattern = /^[A-Za-z0-9+/]+=*$/;
	if (!base64Pattern.test(apiKey)) {
		return {
			success: false,
			message: "APIキーの形式が正しくありません。",
		};
	}

	// 長さの上限チェック（異常に長い文字列を防ぐ）
	if (apiKey.length > 500) {
		return {
			success: false,
			message: "APIキーが長すぎます。",
		};
	}

	// ユーザー設定を取得または作成
	const userSettings = await ctx.db
		.query("userSettings")
		.withIndex("by_userId", (q) => q.eq("userId", userId))
		.unique();

	const timestamp = Date.now();

	if (userSettings) {
		// 既存の設定を更新
		await ctx.db.patch(userSettings._id, {
			beds24: {
				refreshToken: apiKey,
			},
			updatedAt: timestamp,
		});
	} else {
		// 新規設定を作成
		await ctx.db.insert("userSettings", {
			userId,
			theme: "system", // デフォルトのテーマ
			beds24: {
				refreshToken: apiKey,
			},
			has_beds24_token: true, // APIキーがあるのでtrue
			createdAt: timestamp,
			updatedAt: timestamp,
		});
	}

	// 既存のアクセストークンを削除（新しいAPIキーで再取得が必要）
	const existingTokens = await ctx.db
		.query("beds24AccessTokens")
		.withIndex("by_userId", (q) => q.eq("userId", userId))
		.collect();

	for (const token of existingTokens) {
		await ctx.db.delete(token._id);
	}

	logger.info("Beds24設定が更新されました", { userId });

	return {
		success: true,
		message:
			"Beds24の連携設定を更新しました。初回同期は「同期を開始」ボタンから実行してください。",
	};
}

/**
 * Beds24連携状態を確認する
 *
 * ユーザーのBeds24連携の各種状態を確認します。
 * リフレッシュトークンの有無、アクセストークンの有効性、
 * トークンの有効期限などを総合的に判断して連携状態を返します。
 *
 * @param ctx - Convexクエリコンテキスト
 * @param userId - 対象ユーザーのID
 * @returns 連携状態の詳細情報。接続状態、トークンの有無、有効期限を含む
 * @example
 * const status = await getConnectionStatus(ctx, "user123");
 * if (status.isConnected) {
 *   console.log("Beds24と接続中");
 * } else if (status.hasRefreshToken && !status.hasAccessToken) {
 *   console.log("アクセストークンの再取得が必要");
 * }
 */
export async function getConnectionStatus(
	ctx: QueryCtx,
	userId: string,
): Promise<ConnectionStatus> {
	const logger = createLogger("model.getConnectionStatus", ctx);
	logger.debug("連携状態を確認", { userId });

	// ユーザー設定を確認
	const userSettings = await ctx.db
		.query("userSettings")
		.withIndex("by_userId", (q) => q.eq("userId", userId))
		.unique();

	const hasRefreshToken = !!userSettings?.beds24?.refreshToken;

	// アクセストークンを確認
	const accessToken = await ctx.db
		.query("beds24AccessTokens")
		.withIndex("by_userId", (q) => q.eq("userId", userId))
		.first();

	const hasAccessToken = !!accessToken;
	const isConnected =
		hasRefreshToken &&
		hasAccessToken &&
		(accessToken?.expiresAt || 0) > Date.now();

	return {
		isConnected,
		hasRefreshToken,
		hasAccessToken,
		lastTokenRefresh: accessToken?.lastRefreshedAt,
		tokenExpiresAt: accessToken?.expiresAt,
	};
}

/**
 * 特定の施設の詳細情報を取得する
 *
 * 指定された施設IDの詳細情報を取得します。
 * ユーザーのアクセス権限を確認し、権限がある場合のみ施設の完全なデータを返します。
 *
 * @param ctx - Convexクエリコンテキスト
 * @param userId - 対象ユーザーのID
 * @param beds24PropertyId - Beds24の施設ID（数値を文字列化したもの）
 * @returns 施設の詳細情報。データにはBeds24 APIから取得した全ての情報が含まれる。アクセス権限がない場合はnull
 * @example
 * const details = await getPropertyDetailsForUser(ctx, "user123", "456789");
 * if (details) {
 *   console.log("施設名:", details.data.name);
 *   console.log("住所:", details.data.address);
 * }
 */
export async function getPropertyDetailsForUser(
	ctx: QueryCtx,
	userId: string,
	beds24PropertyId: string,
): Promise<PropertyDetails | null> {
	const logger = createLogger("model.getPropertyDetailsForUser", ctx);
	logger.debug("施設詳細を取得", { userId, beds24PropertyId });

	// まず施設を検索（ユーザー問わず）
	const property = await ctx.db
		.query("beds24Properties")
		.withIndex("by_beds24PropertyId", (q) =>
			q.eq("beds24PropertyId", beds24PropertyId),
		)
		.first();

	if (!property) {
		return null;
	}

	// アクセス権限をチェック
	const hasAccess = await ctx.db
		.query("userProperties")
		.withIndex("by_userId_and_propertyId", (q) =>
			q.eq("userId", userId).eq("propertyId", property._id),
		)
		.first();

	if (!hasAccess) {
		logger.warn("ユーザーにアクセス権限がありません", {
			userId,
			beds24PropertyId,
		});
		return null;
	}

	return {
		_id: property._id,
		_creationTime: property._creationTime,
		beds24PropertyId: property.beds24PropertyId,
		beds24PropertyKey: property.beds24PropertyKey,
		data: property.data,
		lastSyncedAt: property.lastSyncedAt,
		isDeleted: property.isDeleted,
		deletedAt: property.deletedAt,
	};
}

// ========== 同期処理のヘルパー関数 ==========

/**
 * 同期処理の準備データ
 *
 * 同期処理を開始する前に必要な認証情報と履歴IDをまとめたインターフェース。
 * アクセストークンの有効期限を含むことで、同期処理中のトークン切れを防ぎます。
 */
export interface SyncPrepareData {
	historyId: Id<"beds24SyncHistory">;
	accessToken: string;
	tokenExpiresAt: number;
}

/**
 * 同期処理の結果データ
 *
 * 同期処理完了後の統計情報とエラー詳細をまとめたインターフェース。
 * 作成・更新・削除の各カウントとエラー情報を含みます。
 */
export interface SyncResultData {
	totalProperties: number;
	createdCount: number;
	updatedCount: number;
	deletedCount: number;
	errors: Array<{
		propertyId?: number;
		error: string;
	}>;
}

/**
 * 施設の更新結果
 *
 * 施設データのupsert操作の結果をまとめたインターフェース。
 * 作成・更新の内訳とエラーメッセージのリストを含みます。
 */
export interface PropertyUpdateResult {
	upserted: number;
	created: number;
	updated: number;
	errors: string[];
}

/**
 * 1時間以内に同期済みの施設をフィルタリング
 *
 * パフォーマンス最適化のため、指定時間内に同期済みの施設をスキップします。
 * これによりAPI呼び出し回数を削減し、システム負荷を軽減します。
 *
 * @param ctx - Convexクエリコンテキスト
 * @param properties - Beds24 APIから取得した施設リスト
 * @param hoursThreshold - 同期をスキップする時間閾値（時間単位、デフォルト: 1時間）
 * @returns フィルタリング後の施設リスト（同期が必要な施設のみ）
 * @example
 * const allProperties = await fetchFromBeds24API();
 * const toSync = await filterRecentlySyncedProperties(ctx, allProperties, 2);
 * console.log(`${toSync.length}/${allProperties.length} 件を同期対象に選択`);
 */
export async function filterRecentlySyncedProperties(
	ctx: QueryCtx,
	properties: any[], // Beds24APIProperty[]
	hoursThreshold: number = 1,
): Promise<any[]> {
	const logger = createLogger("model.filterRecentlySyncedProperties", ctx);
	const thresholdTime = Date.now() - hoursThreshold * 60 * 60 * 1000;
	const filteredProperties: any[] = [];

	// バッチ処理で既存の施設を確認
	const propertyIds = properties.map((p) => p.id.toString());
	const existingPropertiesMap = new Map<string, { lastSyncedAt: number }>();

	// インデックスを使用した効率的な検索
	for (const propId of propertyIds) {
		const existingProperty = await ctx.db
			.query("beds24Properties")
			.withIndex("by_beds24PropertyId", (q) => q.eq("beds24PropertyId", propId))
			.unique();

		if (existingProperty) {
			existingPropertiesMap.set(propId, {
				lastSyncedAt: existingProperty.lastSyncedAt,
			});
		}
	}

	// フィルタリング処理
	for (const prop of properties) {
		const propId = prop.id.toString();
		const existing = existingPropertiesMap.get(propId);

		if (!existing || existing.lastSyncedAt <= thresholdTime) {
			filteredProperties.push(prop);
		} else {
			logger.debug(`施設 ${propId} は最近同期済みのためスキップ`, {
				lastSyncedAt: new Date(existing.lastSyncedAt).toISOString(),
			});
		}
	}

	logger.info(
		`フィルタリング結果: ${filteredProperties.length}/${properties.length} 件を同期対象に選択`,
	);

	return filteredProperties;
}

/**
 * 施設データの更新準備
 *
 * Beds24 APIレスポンスをデータベース保存用の形式に変換します。
 * 各施設に一意のキーを生成し、データ構造を標準化します。
 *
 * @param _userId - ユーザーID（現在は未使用だが、将来の拡張用）
 * @param properties - Beds24 APIから取得した施設データの配列
 * @returns データベース保存用に変換された施設データの配列
 * @example
 * const apiData = [{ id: 123, name: "Hotel Tokyo", ... }];
 * const dbData = preparePropertiesForUpsert("user123", apiData);
 * // 結果: [{ propId: "123", propKey: "property_123", data: {...} }]
 */
export function preparePropertiesForUpsert(
	_userId: string,
	properties: any[], // Beds24APIProperty[]
): Array<{
	propId: string;
	propKey: string;
	data: any;
}> {
	return properties.map((prop) => ({
		propId: prop.id.toString(),
		propKey: `property_${prop.id}`, // IDから一意のキーを生成
		data: prop,
	}));
}

/**
 * 同期結果の集計
 *
 * 施設の作成・更新・削除の各処理結果を統合し、
 * 同期処理全体の結果として構造化されたデータを返します。
 *
 * @param totalProperties - 処理対象の施設総数
 * @param updateResult - 施設の作成・更新処理の結果
 * @param deleteResult - 施設の削除処理の結果
 * @returns 統合された同期結果データ。各種カウントとエラー情報を含む
 * @example
 * const result = aggregateSyncResults(
 *   100, // 総施設数
 *   { created: 10, updated: 80, upserted: 90, errors: [] },
 *   { deletedCount: 5 }
 * );
 * console.log(`成功: ${result.createdCount + result.updatedCount}件`);
 */
export function aggregateSyncResults(
	totalProperties: number,
	updateResult: PropertyUpdateResult,
	deleteResult: { deletedCount: number },
): SyncResultData {
	return {
		totalProperties,
		createdCount: updateResult.created,
		updatedCount: updateResult.updated,
		deletedCount: deleteResult.deletedCount,
		errors: updateResult.errors.map((error) => ({ error })),
	};
}

/**
 * 同期ステータスの判定
 *
 * エラーの有無と数に基づいて同期処理の最終ステータスを決定します。
 * エラーがない場合は成功、一部エラーの場合は部分的成功として扱います。
 *
 * @param errors - 同期処理中に発生したエラーメッセージの配列
 * @returns 同期ステータス（"success" | "partial_success" | "failed"）
 * @example
 * const status1 = determineSyncStatus([]); // "success"
 * const status2 = determineSyncStatus(["施設123の更新失敗"]); // "partial_success"
 */
export function determineSyncStatus(
	errors: string[],
): "success" | "partial_success" | "failed" {
	if (errors.length === 0) {
		return "success";
	} else if (errors.length > 0) {
		return "partial_success";
	}
	return "failed";
}

/**
 * エラーメッセージの正規化
 *
 * 様々な形式のエラー（Error オブジェクト、文字列、カスタムオブジェクトなど）を
 * 統一的な文字列メッセージに変換します。ログ記録やユーザー表示に使用されます。
 *
 * @param error - 任意の形式のエラー情報
 * @returns 正規化されたエラーメッセージ文字列
 * @example
 * normalizeErrorMessage(new Error("接続失敗")); // "接続失敗"
 * normalizeErrorMessage("タイムアウト"); // "タイムアウト"
 * normalizeErrorMessage({ message: "認証エラー" }); // "認証エラー"
 * normalizeErrorMessage(null); // "不明なエラー"
 */
export function normalizeErrorMessage(error: unknown): string {
	if (error instanceof Error) {
		return error.message;
	}
	if (typeof error === "string") {
		return error;
	}
	if (error && typeof error === "object" && "message" in error) {
		return String(error.message);
	}
	return "不明なエラー";
}

/**
 * リトライ可能なエラーかどうかの判定
 *
 * エラーメッセージの内容を分析し、一時的な問題によるエラーかどうかを判定します。
 * ネットワークエラー、タイムアウト、レート制限などはリトライで解決する可能性があります。
 *
 * @param error - 判定対象のエラー
 * @returns リトライ可能な場合はtrue、恒久的なエラーの場合はfalse
 * @example
 * isRetryableError(new Error("ECONNREFUSED")); // true (接続拒否)
 * isRetryableError("rate limit exceeded"); // true (レート制限)
 * isRetryableError("Invalid API key"); // false (認証エラー)
 */
export function isRetryableError(error: unknown): boolean {
	const message = normalizeErrorMessage(error).toLowerCase();

	// リトライ可能なエラーパターン
	const retryablePatterns = [
		"network",
		"timeout",
		"econnrefused",
		"enotfound",
		"etimedout",
		"esockettimedout",
		"rate limit",
		"too many requests",
		"503",
		"502",
		"504",
	];

	return retryablePatterns.some((pattern) => message.includes(pattern));
}

/**
 * 次回リトライまでの待機時間を計算
 *
 * 指数バックオフアルゴリズムを使用して、リトライ回数に応じた待機時間を計算します。
 * ジッター（ランダムな揺らぎ）を追加することで、複数のクライアントが同時にリトライすることを防ぎます。
 *
 * @param attempt - リトライ試行回数（1から開始）
 * @returns 待機時間（ミリ秒）。最小60秒、最大5分、各試行で2倍に増加
 * @example
 * calculateRetryDelay(1); // 約60秒（±10%のジッター）
 * calculateRetryDelay(2); // 約120秒（±10%のジッター）
 * calculateRetryDelay(3); // 約240秒（±10%のジッター）
 * calculateRetryDelay(4); // 最大300秒（5分）に制限
 */
export function calculateRetryDelay(attempt: number): number {
	// 基本待機時間: 60秒
	const baseDelay = 60000;
	// 最大待機時間: 5分
	const maxDelay = 300000;
	// 指数バックオフ: 2^(attempt-1) * baseDelay
	const delay = Math.min(baseDelay * 2 ** (attempt - 1), maxDelay);

	// ジッターを追加（±10%）
	const jitter = delay * 0.1;
	const randomJitter = Math.random() * jitter * 2 - jitter;

	return Math.round(delay + randomJitter);
}

/**
 * アクティブな施設IDのセットを作成
 *
 * Beds24 APIレスポンスから有効な施設IDのセットを作成します。
 * このセットは、APIに存在しない（＝削除された）施設を特定するために使用されます。
 *
 * @param properties - Beds24 APIから取得した施設データの配列
 * @returns 施設IDの文字列セット。高速な存在確認が可能
 * @example
 * const properties = [{ id: 123 }, { id: 456 }];
 * const activeIds = createActivePropertyIdSet(properties);
 * activeIds.has("123"); // true
 * activeIds.has("789"); // false（削除された施設）
 */
export function createActivePropertyIdSet(properties: any[]): Set<string> {
	return new Set(properties.map((p) => p.id.toString()));
}

/**
 * 施設データのupsert処理
 *
 * Beds24 APIから取得した施設データをデータベースに反映します。
 * 新規施設は作成し、既存施設は更新します。削除済みフラグもリセットされます。
 *
 * 処理内容:
 * - 新規施設の場合: beds24Propertiesへの挿入とuserPropertiesへの関連付け
 * - 既存施設の場合: データの更新とisDeleted状態のリセット
 *
 * @param ctx - Convexミューテーションコンテキスト
 * @param userId - 施設を所有するユーザーのID
 * @param properties - Beds24 APIから取得した施設データの配列
 * @param now - 現在のタイムスタンプ（ミリ秒）
 * @returns 更新結果。作成数、更新数、エラーを含む
 * @throws 個別の施設更新でエラーが発生した場合はエラーリストに追加されるが、処理は継続される
 * @example
 * const result = await upsertBeds24Properties(ctx, "user123", apiProperties, Date.now());
 * console.log(`作成: ${result.created}件、更新: ${result.updated}件`);
 */
export async function upsertBeds24Properties(
	ctx: MutationCtx,
	userId: string,
	properties: Beds24APIProperty[],
	now: number,
): Promise<PropertyUpdateResult> {
	const logger = createLogger("model.upsertBeds24Properties", ctx);
	logger.info("施設データのupsert開始", { userId, count: properties.length });

	let createdCount = 0;
	let updatedCount = 0;
	const errors: string[] = [];

	for (const prop of properties) {
		try {
			const propId = prop.id.toString();
			const existing = await ctx.db
				.query("beds24Properties")
				.withIndex("by_beds24PropertyId", (q) =>
					q.eq("beds24PropertyId", propId),
				)
				.unique();

			if (existing) {
				// 既存の施設を更新
				await ctx.db.patch(existing._id, {
					name: prop.name,
					propertyType: prop.propertyType,
					currency: prop.currency,
					country: prop.country,
					city: prop.city,
					data: prop,
					lastSyncedAt: now,
					updatedAt: now,
					isDeleted: false,
					deletedAt: undefined,
				});
				updatedCount++;
				logger.debug("施設を更新", { propId, name: prop.name });
			} else {
				// 新規施設を作成
				const newId = await ctx.db.insert("beds24Properties", {
					beds24PropertyId: propId,
					beds24PropertyKey: `property_${propId}`,
					name: prop.name,
					propertyType: prop.propertyType,
					currency: prop.currency,
					country: prop.country,
					city: prop.city,
					data: prop,
					lastSyncedAt: now,
					createdAt: now,
					updatedAt: now,
					isDeleted: false,
				});

				// ユーザーとの関連を作成
				await ctx.db.insert("userProperties", {
					userId: userId,
					propertyId: newId,
					createdAt: now,
				});

				createdCount++;
				logger.debug("新規施設を作成", { propId, name: prop.name });
			}
		} catch (error) {
			const errorMsg = `施設 ${prop.id} の更新に失敗: ${error instanceof Error ? error.message : String(error)}`;
			errors.push(errorMsg);
			logger.error(errorMsg, { propId: prop.id, error });
		}
	}

	logger.info("施設データのupsert完了", {
		createdCount,
		updatedCount,
		errorCount: errors.length,
	});

	return {
		upserted: createdCount + updatedCount,
		created: createdCount,
		updated: updatedCount,
		errors,
	};
}

/**
 * 非アクティブ施設のソフトデリート
 *
 * Beds24 APIレスポンスに含まれない施設を削除済みとしてマークします。
 * 物理削除ではなくソフトデリート（論理削除）を行うため、必要に応じて復元可能です。
 *
 * 処理フロー:
 * 1. userPropertiesを経由してユーザーに紐づく全施設を取得
 * 2. アクティブな施設IDセットに含まれない施設を特定
 * 3. 該当施設にisDeleted=trueとdeletedAtタイムスタンプを設定
 *
 * @param ctx - Convexミューテーションコンテキスト
 * @param userId - 対象ユーザーのID
 * @param activePropertyIds - Beds24 APIに存在する施設IDのセット
 * @param now - 現在のタイムスタンプ（ミリ秒）
 * @returns ソフトデリートされた施設数
 * @example
 * const activeIds = new Set(["123", "456"]);
 * const deletedCount = await softDeleteInactiveProperties(ctx, "user123", activeIds, Date.now());
 * console.log(`${deletedCount}件の施設を削除済みとしてマーク`);
 */
export async function softDeleteInactiveProperties(
	ctx: MutationCtx,
	userId: string,
	activePropertyIds: Set<string>,
	now: number,
): Promise<number> {
	const logger = createLogger("model.softDeleteInactiveProperties", ctx);
	logger.info("非アクティブ施設のソフトデリート開始", {
		userId,
		activeCount: activePropertyIds.size,
	});

	let deletedCount = 0;

	// ユーザーに紐づく全施設を取得
	const userRelations = await ctx.db
		.query("userProperties")
		.withIndex("by_userId", (q) => q.eq("userId", userId))
		.collect();

	for (const relation of userRelations) {
		const property = await ctx.db.get(relation.propertyId);
		if (
			property &&
			!property.isDeleted &&
			!activePropertyIds.has(property.beds24PropertyId)
		) {
			await ctx.db.patch(property._id, {
				isDeleted: true,
				deletedAt: now,
				updatedAt: now,
			});
			deletedCount++;
			logger.debug("施設をソフトデリート", {
				propId: property.beds24PropertyId,
				name: property.name,
			});
		}
	}

	logger.info("非アクティブ施設のソフトデリート完了", { deletedCount });
	return deletedCount;
}

/**
 * 同期履歴のステータス更新
 *
 * 同期処理完了後、結果に基づいて履歴レコードを更新します。
 * 成功・部分的成功・失敗のステータスを判定し、詳細な統計情報を記録します。
 *
 * 記録内容:
 * - ステータスの判定: SUCCESS/PARTIAL_SUCCESS/FAILED
 * - エラー情報の構造化（エラーコード、メッセージ、発生時刻）
 * - 処理時間の計算（開始時刻から完了時刻までの経過時間）
 * - 各種カウント（作成・更新・削除・エラー数）
 *
 * @param ctx - Convexミューテーションコンテキスト
 * @param historyId - 更新対象の同期履歴レコードID
 * @param result - 同期処理の結果データ
 * @param now - 現在のタイムスタンプ（ミリ秒）
 * @throws 同期履歴レコードが存在しない場合はエラーをスロー
 * @example
 * await updateSyncHistoryStatus(ctx, historyId, {
 *   success: true,
 *   propertiesCount: 100,
 *   createdCount: 10,
 *   updatedCount: 80,
 *   deletedCount: 5,
 *   errors: []
 * }, Date.now());
 */
export async function updateSyncHistoryStatus(
	ctx: MutationCtx,
	historyId: Id<"beds24SyncHistory">,
	result: {
		success: boolean;
		propertiesCount: number;
		createdCount: number;
		updatedCount: number;
		deletedCount: number;
		errors: string[];
		error?: string;
	},
	now: number,
): Promise<void> {
	const logger = createLogger("model.updateSyncHistoryStatus", ctx);

	// 同期ステータスの判定
	const syncStatus = result.success
		? result.errors.length === 0
			? SyncHistoryStatus.SUCCESS
			: SyncHistoryStatus.PARTIAL_SUCCESS
		: SyncHistoryStatus.FAILED;

	// 履歴レコードを取得して処理時間を計算
	const history = await ctx.db.get(historyId);
	if (!history) {
		throw createConvexError(ERROR_CODES.NOT_FOUND, "同期履歴が見つかりません");
	}

	// エラー情報の構造化
	const errorRecords = result.error
		? [
				{
					code: SyncErrorCode.UNKNOWN_ERROR,
					message: result.error,
					occurredAt: now,
				},
			]
		: result.errors.map((msg) => ({
				code: SyncErrorCode.API_ERROR,
				message: msg,
				occurredAt: now,
			}));

	// 履歴を更新
	await ctx.db.patch(historyId, {
		status: syncStatus,
		completedAt: now,
		duration: now - history.startedAt,
		totalItems: result.propertiesCount,
		successCount: result.createdCount + result.updatedCount,
		failedCount: result.errors.length,
		errors: errorRecords,
		metadata: {
			createdCount: result.createdCount,
			updatedCount: result.updatedCount,
			deletedCount: result.deletedCount,
		},
		updatedAt: now,
	});

	logger.info("同期履歴を更新", {
		historyId,
		status: syncStatus,
		duration: now - history.startedAt,
		results: {
			total: result.propertiesCount,
			created: result.createdCount,
			updated: result.updatedCount,
			deleted: result.deletedCount,
			errors: result.errors.length,
		},
	});
}
