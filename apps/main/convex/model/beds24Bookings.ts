/**
 * Beds24 Bookings Model Layer
 *
 * このファイルはBeds24予約に関するビジネスロジックとヘルパー関数を提供します。
 * query/mutation/actionから呼び出される共通処理を実装します。
 *
 * ベストプラクティス:
 * - 全ての関数に明示的な型付け（QueryCtx、MutationCtx）
 * - 戻り値型の明確な定義
 * - 生成された型（Doc、Id）の一貫した使用
 * - dataフィールドにAPIレスポンス全体を保存するパターン
 */

import type { GenericMutationCtx, GenericQueryCtx } from "convex/server";
import type { DataModel, Doc, Id } from "../_generated/dataModel";
import { getAuthenticatedUserId } from "../lib/auth";
import { createConvexError, ERROR_CODES } from "../lib/errors";
import { createLogger } from "../lib/logging";
import type {
	BookingData,
	BookingFilterOptions,
	BookingStats,
	BookingWithDetails,
	PaginatedBookings,
} from "../types/beds24Bookings";

// 型エイリアスの定義
type Ctx = GenericQueryCtx<DataModel> | GenericMutationCtx<DataModel>;

/**
 * ユーザーが特定の施設へのアクセス権限を持っているかチェック
 *
 * このメソッドは施設へのアクセス制御の中核となる関数です。
 * userPropertiesテーブルを使用してユーザーと施設の関連付けを確認します。
 *
 * ビジネスロジック:
 * - 施設が存在しない場合はアクセス拒否
 * - 削除済み施設へのアクセスは常に拒否（データ保護）
 * - userPropertiesテーブルでの明示的な権限付与が必要
 *
 * セキュリティ考慮事項:
 * - 施設の存在確認を最初に行い、不正なIDでの情報漏洩を防止
 * - 削除済みフラグのチェックで論理削除されたデータへのアクセスを制限
 * - 権限チェックはインデックスを使用し、効率的かつ確実に実行
 *
 * @param ctx - QueryまたはMutationコンテキスト
 * @param userId - チェック対象のユーザーID（Clerk認証から取得）
 * @param propertyId - アクセス確認対象の施設ID
 * @returns {Object} アクセス権限チェック結果
 * @returns {boolean} hasAccess - アクセス可能な場合true
 * @returns {string} [reason] - アクセス拒否の理由（デバッグ用）
 *
 * @example
 * const access = await checkPropertyAccess(ctx, userId, propertyId);
 * if (!access.hasAccess) {
 *   throw new Error(`Access denied: ${access.reason}`);
 * }
 */
export async function checkPropertyAccess(
	ctx: Ctx,
	userId: string,
	propertyId: Id<"beds24Properties">,
): Promise<{ hasAccess: boolean; reason?: string }> {
	const logger = createLogger("Beds24BookingsModel.checkPropertyAccess", ctx);
	logger.debug("アクセス権限チェック", { userId, propertyId });

	// 施設の存在確認
	const property = await ctx.db.get(propertyId);
	if (!property) {
		return {
			hasAccess: false,
			reason: "Property not found",
		};
	}

	// 削除済み施設へのアクセスを制限
	if (property.isDeleted) {
		return {
			hasAccess: false,
			reason: "Property is deleted",
		};
	}

	// userPropertiesテーブルで権限確認
	const hasAccess = await ctx.db
		.query("userProperties")
		.withIndex("by_userId_and_propertyId", (q) =>
			q.eq("userId", userId).eq("propertyId", propertyId),
		)
		.unique();

	return {
		hasAccess: !!hasAccess,
		reason: hasAccess ? undefined : "Access denied",
	};
}

/**
 * 認証済みユーザーのアクセス可能な施設IDを取得
 *
 * ユーザーがアクセス権を持つ全施設のIDをSetとして返します。
 * 統計情報の集計や一覧表示で使用される重要な関数です。
 *
 * ビジネスロジック:
 * - userPropertiesテーブルから最大100施設まで取得（実用上十分）
 * - 削除済み施設は自動的に除外される
 * - Setを使用して重複を排除（データ整合性の問題に対応）
 *
 * パフォーマンス考慮事項:
 * - インデックス（by_userId）を使用した効率的なクエリ
 * - 100施設の制限により過度なメモリ使用を防止
 * - 施設の存在確認は個別に実行（バッチ処理の検討余地あり）
 *
 * セキュリティ考慮事項:
 * - ユーザーIDに基づく厳密なフィルタリング
 * - 削除済み施設の自動除外によりデータ漏洩を防止
 * - 権限のない施設IDは結果に含まれない
 *
 * @param ctx - QueryまたはMutationコンテキスト
 * @param userId - 権限を確認するユーザーID
 * @returns {Set<Id<"beds24Properties">>} アクセス可能な施設IDのSet
 *
 * @throws エラーは発生しないが、空のSetが返る可能性あり
 *
 * @example
 * const propertyIds = await getUserAccessiblePropertyIds(ctx, userId);
 * console.log(`ユーザーは${propertyIds.size}施設にアクセス可能`);
 */
export async function getUserAccessiblePropertyIds(
	ctx: Ctx,
	userId: string,
): Promise<Set<Id<"beds24Properties">>> {
	const logger = createLogger(
		"Beds24BookingsModel.getUserAccessiblePropertyIds",
		ctx,
	);
	logger.debug("アクセス可能施設の取得", { userId });

	// 最大100施設まで取得（実用上十分な数）
	const userRelations = await ctx.db
		.query("userProperties")
		.withIndex("by_userId", (q) => q.eq("userId", userId))
		.take(100);

	const propertyIds = new Set<Id<"beds24Properties">>();
	for (const relation of userRelations) {
		// 削除済み施設を除外
		const property = await ctx.db.get(relation.propertyId);
		if (property && !property.isDeleted) {
			propertyIds.add(relation.propertyId);
		}
	}

	logger.debug("アクセス可能施設数", { count: propertyIds.size });
	return propertyIds;
}

/**
 * 施設別の予約を取得（ページネーション対応）
 *
 * 特定施設の予約一覧を取得する主要なクエリ関数です。
 * アクセス制御、ページネーション、詳細情報の付加を一括で処理します。
 *
 * ビジネスロジック:
 * - アクセス権限の事前チェックによりセキュアなデータアクセス
 * - チェックイン日の降順（最新順）でソート
 * - 削除済み予約は自動的に除外
 * - ページネーションによる効率的なデータ取得
 * - 施設情報を含む詳細データへの自動変換
 *
 * アクセス制御:
 * - checkPropertyAccessによる厳密な権限確認
 * - 権限がない場合は即座にUNAUTHORIZEDエラー
 * - エラーメッセージにはpropertyIdを含めてデバッグを支援
 *
 * パフォーマンス最適化:
 * - by_property_and_checkInインデックスによる高速クエリ
 * - ページネーションによるメモリ効率的な処理
 * - 必要な分だけデータを取得（遅延読み込み）
 *
 * セキュリティ考慮事項:
 * - ユーザーの権限確認を最優先で実行
 * - 削除済みデータへのアクセスを完全にブロック
 * - エラー情報の適切な制限（内部情報の漏洩防止）
 *
 * @param ctx - Queryコンテキスト（読み取り専用）
 * @param userId - リクエストユーザーのID
 * @param propertyId - 取得対象の施設ID
 * @param paginationOpts - ページネーションオプション
 * @param paginationOpts.numItems - 1ページあたりの件数
 * @param paginationOpts.cursor - 次ページ取得用カーソル
 * @returns {PaginatedBookings} ページネーションされた予約一覧
 *
 * @throws {ConvexError} ERROR_CODES.UNAUTHORIZED - アクセス権限がない場合
 *
 * @example
 * const bookings = await getBookingsByPropertyWithAuth(
 *   ctx,
 *   userId,
 *   propertyId,
 *   { numItems: 20, cursor: null }
 * );
 */
export async function getBookingsByPropertyWithAuth(
	ctx: GenericQueryCtx<DataModel>,
	userId: string,
	propertyId: Id<"beds24Properties">,
	paginationOpts: { numItems: number; cursor: string | null },
): Promise<PaginatedBookings> {
	const _logger = createLogger(
		"Beds24BookingsModel.getBookingsByPropertyWithAuth",
		ctx,
	);

	// アクセス権限チェック
	const accessCheck = await checkPropertyAccess(ctx, userId, propertyId);
	if (!accessCheck.hasAccess) {
		throw createConvexError(
			ERROR_CODES.UNAUTHORIZED,
			accessCheck.reason || "Access denied",
			undefined,
			{ propertyId },
		);
	}

	// 予約を取得（チェックイン日順 - 最新順）
	const result = await ctx.db
		.query("beds24Bookings")
		.withIndex("by_property_and_checkIn", (q) => q.eq("propertyId", propertyId))
		.filter((q) => q.eq(q.field("isDeleted"), false))
		.order("desc")
		.paginate(paginationOpts);

	// 施設情報を含む詳細データに変換
	const bookingsWithDetails = await enrichBookingsWithDetails(ctx, result.page);

	return {
		...result,
		page: bookingsWithDetails,
	};
}

/**
 * 予約に関連情報（dataフィールドから詳細情報）を追加
 *
 * 予約の基本情報に施設情報とdataフィールドの詳細情報を追加します。
 * この関数により、フロントエンドで必要な全情報を一度に取得できます。
 *
 * ビジネスロジック:
 * - 各予約に対して施設情報を非同期で取得
 * - dataフィールドから追加の詳細情報を展開
 * - 施設が削除されていても予約情報は保持（"Unknown Property"として表示）
 * - Promise.allによる並列処理で高速化
 *
 * データ構造の変換:
 * - propertyName: 施設名（削除時は"Unknown Property"）
 * - propertyCity/Country: 施設の所在地情報
 * - specialRequests: ゲストの特別リクエスト
 * - arrivalTime: 到着予定時刻
 * - paymentDetails: 支払い詳細情報
 * - bookingSource: 予約経路（refererフィールドから）
 *
 * パフォーマンス考慮事項:
 * - Promise.allによる並列処理
 * - N+1問題の可能性（将来的にバッチ取得の検討余地）
 * - dataフィールドの存在チェックによる安全な展開
 *
 * @param ctx - Queryコンテキスト（読み取り専用）
 * @param bookings - 詳細情報を追加する予約の配列
 * @returns {Promise<BookingWithDetails[]>} 詳細情報付き予約の配列
 *
 * @internal この関数は主に他の公開関数から呼び出される内部ヘルパー
 *
 * @example
 * const enrichedBookings = await enrichBookingsWithDetails(ctx, rawBookings);
 */
export async function enrichBookingsWithDetails(
	ctx: GenericQueryCtx<DataModel>,
	bookings: Doc<"beds24Bookings">[],
): Promise<BookingWithDetails[]> {
	return Promise.all(
		bookings.map(async (booking) => {
			const property = await ctx.db.get(booking.propertyId);

			// dataフィールドから追加情報を抽出
			const additionalInfo = booking.data || {};

			return {
				...booking,
				propertyName: property?.name ?? "Unknown Property",
				propertyCity: property?.city,
				propertyCountry: property?.country,
				// dataフィールドから追加の詳細情報を展開
				// 例: 特別リクエスト、到着時刻、支払い詳細など
				specialRequests: additionalInfo.specialRequests,
				arrivalTime: additionalInfo.arrivalTime,
				paymentDetails: additionalInfo.paymentDetails,
				bookingSource: additionalInfo.referer,
			};
		}),
	);
}

/**
 * 特定の予約の詳細を取得（構造化フィールドとdataフィールドの組み合わせ）
 *
 * 単一の予約の完全な詳細情報を取得します。
 * アクセス権限チェックを含む、セキュアな予約詳細取得メソッドです。
 *
 * ビジネスロジック:
 * - 予約の存在確認と削除フラグチェック
 * - 施設レベルでのアクセス権限確認
 * - 詳細情報の自動付加（enrichBookingsWithDetails使用）
 * - 権限がない場合はnullを返す（エラーではない）
 *
 * アクセス制御の流れ:
 * 1. 予約の存在確認（削除済みも含めて拒否）
 * 2. 予約が属する施設への権限確認
 * 3. 権限がある場合のみ詳細情報を返す
 *
 * セキュリティ考慮事項:
 * - 予約の存在自体を権限のないユーザーに漏らさない（null返却）
 * - ログには最小限の情報のみ記録（プライバシー保護）
 * - 施設経由での間接的な権限確認（予約直接の権限はない）
 *
 * エラーハンドリング:
 * - エラーをthrowせずnullを返すことで、存在しない予約と
 *   権限のない予約を区別できないようにする（情報漏洩防止）
 *
 * @param ctx - Queryコンテキスト（読み取り専用）
 * @param userId - リクエストユーザーのID
 * @param bookingId - 取得対象の予約ID
 * @returns {Promise<BookingWithDetails | null>} 予約詳細またはnull
 *
 * @example
 * const booking = await getBookingDetails(ctx, userId, bookingId);
 * if (!booking) {
 *   // 予約が存在しないか、アクセス権限がない
 *   return { error: "Booking not found" };
 * }
 */
export async function getBookingDetails(
	ctx: GenericQueryCtx<DataModel>,
	userId: string,
	bookingId: Id<"beds24Bookings">,
): Promise<BookingWithDetails | null> {
	const logger = createLogger("Beds24BookingsModel.getBookingDetails", ctx);

	const booking = await ctx.db.get(bookingId);
	if (!booking || booking.isDeleted) {
		logger.debug("予約が見つかりません", { bookingId });
		return null;
	}

	// アクセス権限チェック
	const accessCheck = await checkPropertyAccess(
		ctx,
		userId,
		booking.propertyId,
	);
	if (!accessCheck.hasAccess) {
		logger.debug("アクセス権限がありません", { bookingId, userId });
		return null;
	}

	// 詳細情報を追加
	const [enrichedBooking] = await enrichBookingsWithDetails(ctx, [booking]);
	return enrichedBooking;
}

/**
 * 予約統計情報を効率的に計算
 *
 * ユーザーがアクセス可能な予約データから各種統計情報を計算します。
 * ダッシュボードやレポート機能で使用される重要な分析関数です。
 *
 * ビジネスロジック:
 * - 特定施設または全アクセス可能施設の統計を計算
 * - キャンセル済み予約は売上から除外
 * - 複数通貨の売上を個別に集計
 * - チャネル別、ステータス別、月別の分析
 * - 平均滞在日数と稼働率の計算
 *
 * フィルタリング機能:
 * - propertyId: 特定施設のみの統計
 * - status: 予約ステータスでフィルター
 * - channel: 予約チャネル（OTA等）でフィルター
 * - dateFrom/dateTo: 期間指定フィルター
 *
 * パフォーマンス最適化:
 * - 施設ごとに最大100件の制限（統計には十分）
 * - 全体で最大1000件の制限（メモリ保護）
 * - Promise.allによる並列データ取得
 * - インデックスを活用した効率的なクエリ
 *
 * 統計計算の詳細:
 * - totalRevenue: キャンセル以外の総売上
 * - currencyBreakdown: 通貨別売上（多通貨対応）
 * - averageStayLength: 全予約の平均滞在日数
 * - occupancyRate: 簡易稼働率（実装は改善余地あり）
 * - monthlyBookings: 月別予約件数の推移
 *
 * セキュリティ考慮事項:
 * - アクセス権限の厳密なチェック
 * - 権限のない施設のデータは含まれない
 * - 集計のみで個別予約の詳細は返さない
 *
 * @param ctx - Queryコンテキスト（読み取り専用）
 * @param userId - リクエストユーザーのID
 * @param filters - 統計計算用のフィルター条件
 * @returns {Promise<BookingStats>} 計算された統計情報
 *
 * @throws {ConvexError} ERROR_CODES.UNAUTHORIZED - 特定施設へのアクセス権限がない場合
 *
 * @example
 * // 全施設の統計
 * const stats = await calculateBookingStatsOptimized(ctx, userId, {});
 *
 * // 特定施設の今月の統計
 * const monthStats = await calculateBookingStatsOptimized(ctx, userId, {
 *   propertyId: "property123",
 *   dateFrom: "2024-01-01",
 *   dateTo: "2024-01-31"
 * });
 */
export async function calculateBookingStatsOptimized(
	ctx: GenericQueryCtx<DataModel>,
	userId: string,
	filters: BookingFilterOptions = {},
): Promise<BookingStats> {
	const _logger = createLogger(
		"Beds24BookingsModel.calculateBookingStatsOptimized",
		ctx,
	);

	let bookings: Doc<"beds24Bookings">[];

	if (filters.propertyId) {
		// 特定施設の統計
		const accessCheck = await checkPropertyAccess(
			ctx,
			userId,
			filters.propertyId,
		);
		if (!accessCheck.hasAccess) {
			throw createConvexError(
				ERROR_CODES.UNAUTHORIZED,
				"Access denied",
				undefined,
				{ propertyId: filters.propertyId },
			);
		}

		// 統計計算のため最大1000件まで取得
		bookings = await ctx.db
			.query("beds24Bookings")
			.withIndex("by_property", (q) => q.eq("propertyId", filters.propertyId!))
			.filter((q) => q.eq(q.field("isDeleted"), false))
			.take(1000);
	} else {
		// 全施設の統計（アクセス可能な施設のみ）
		const accessiblePropertyIds = await getUserAccessiblePropertyIds(
			ctx,
			userId,
		);
		bookings = [];

		// バッチで効率的に取得（各施設から最大100件）
		const bookingPromises = Array.from(accessiblePropertyIds).map(
			(propertyId) =>
				ctx.db
					.query("beds24Bookings")
					.withIndex("by_property", (q) => q.eq("propertyId", propertyId))
					.filter((q) => q.eq(q.field("isDeleted"), false))
					.take(100),
		);

		const bookingArrays = await Promise.all(bookingPromises);
		bookings = bookingArrays.flat();
	}

	// フィルタリング適用
	if (filters.status) {
		bookings = bookings.filter((booking) => booking.status === filters.status);
	}

	if (filters.channel) {
		bookings = bookings.filter(
			(booking) =>
				booking.data?.channel === filters.channel ||
				booking.data?.referer === filters.channel,
		);
	}

	if (filters.dateFrom || filters.dateTo) {
		bookings = bookings.filter((booking) => {
			const checkIn = new Date(booking.checkIn).getTime();
			if (filters.dateFrom && checkIn < new Date(filters.dateFrom).getTime()) {
				return false;
			}
			if (filters.dateTo && checkIn > new Date(filters.dateTo).getTime()) {
				return false;
			}
			return true;
		});
	}

	// 統計計算
	return calculateBookingStats(bookings, filters);
}

/**
 * 予約データのバリデーション
 *
 * 外部APIから取得した予約データの妥当性を検証します。
 * データ品質を保証し、不正なデータの混入を防ぐ重要な関数です。
 *
 * バリデーション項目:
 * - 必須フィールド: bookingId, propertyId, userId, checkIn, checkOut
 * - 日付の論理的整合性: チェックアウト > チェックイン
 * - 金額の妥当性: 0以上の有限数値
 * - 通貨コード: ISO 4217準拠の3文字コード
 * - ステータス: 定義済みの値のみ許可
 * - ゲスト人数: 現実的な範囲内（1-20人）
 *
 * ビジネスルール:
 * - チェックイン日とチェックアウト日は同日不可
 * - 大人の人数は1人以上必須
 * - 子供の人数は0人以上20人以下
 * - 金額はNaNやInfinityを許可しない
 *
 * セキュリティ考慮事項:
 * - 外部APIデータの信頼性を担保
 * - SQLインジェクション等の攻撃を防ぐ
 * - 不正な値によるシステムエラーを防止
 * - エラーメッセージは具体的だが情報漏洩しない
 *
 * 使用場面:
 * - Beds24 APIからのデータ同期時
 * - 手動予約データ入力時
 * - データインポート処理時
 *
 * @param data - バリデーション対象の予約データ（部分的でも可）
 * @returns {Object} バリデーション結果
 * @returns {boolean} isValid - 全項目が有効な場合true
 * @returns {string[]} errors - エラーメッセージの配列
 *
 * @example
 * const validation = validateBookingData(bookingData);
 * if (!validation.isValid) {
 *   console.error("Validation errors:", validation.errors);
 *   throw new Error("Invalid booking data");
 * }
 */
export function validateBookingData(data: Partial<BookingData>): {
	isValid: boolean;
	errors: string[];
} {
	const errors: string[] = [];

	// 必須フィールドのチェック
	if (!data.bookingId) {
		errors.push("Booking ID is required");
	}

	if (!data.propertyId) {
		errors.push("Property ID is required");
	}

	if (!data.userId) {
		errors.push("User ID is required");
	}

	// 日付の妥当性チェック
	if (!data.checkIn) {
		errors.push("Check-in date is required");
	}

	if (!data.checkOut) {
		errors.push("Check-out date is required");
	}

	if (data.checkIn && data.checkOut) {
		const checkInDate = new Date(data.checkIn);
		const checkOutDate = new Date(data.checkOut);

		if (checkInDate >= checkOutDate) {
			errors.push("Check-out date must be after check-in date");
		}
	}

	// 金額のバリデーション
	if (
		typeof data.totalPrice !== "number" ||
		data.totalPrice < 0 ||
		!Number.isFinite(data.totalPrice)
	) {
		errors.push("Total price must be a valid positive number");
	}

	// 通貨コードのバリデーション（3文字のISO通貨コード）
	if (!data.currency || !/^[A-Z]{3}$/.test(data.currency)) {
		errors.push("Currency must be a valid 3-letter ISO code");
	}

	// ステータスのバリデーション
	const validStatuses = [
		"confirmed",
		"cancelled",
		"checked_in",
		"checked_out",
		"no_show",
	];
	if (data.status && !validStatuses.includes(data.status)) {
		errors.push(`Status must be one of: ${validStatuses.join(", ")}`);
	}

	// ゲスト人数のバリデーション
	if (data.adults !== undefined && (data.adults < 1 || data.adults > 20)) {
		errors.push("Adults count must be between 1 and 20");
	}

	if (
		data.children !== undefined &&
		(data.children < 0 || data.children > 20)
	) {
		errors.push("Children count must be between 0 and 20");
	}

	return {
		isValid: errors.length === 0,
		errors,
	};
}

// ============ プライベートヘルパー関数 ============

/**
 * 予約統計情報の計算（内部ヘルパー）
 * @param bookings - 予約の配列
 * @param filters - 適用されたフィルター
 * @returns 計算された予約統計
 */
function calculateBookingStats(
	bookings: Doc<"beds24Bookings">[],
	filters: BookingFilterOptions,
): BookingStats {
	if (bookings.length === 0) {
		const dateRange = {
			from: filters.dateFrom || new Date().toISOString().split("T")[0],
			to: filters.dateTo || new Date().toISOString().split("T")[0],
		};

		return {
			totalBookings: 0,
			totalRevenue: 0,
			currencyBreakdown: {},
			statusBreakdown: {},
			channelBreakdown: {},
			monthlyBookings: {},
			averageStayLength: 0,
			occupancyRate: 0,
			dateRange,
		};
	}

	const currencyBreakdown: Record<string, number> = {};
	const statusBreakdown: Record<string, number> = {};
	const channelBreakdown: Record<string, number> = {};
	const monthlyBookings: Record<string, number> = {};

	let totalRevenue = 0;
	let totalNights = 0;
	let totalRoomNights = 0;

	// 日付範囲の計算
	let minDate = new Date(bookings[0].checkIn);
	let maxDate = new Date(bookings[0].checkOut);

	for (const booking of bookings) {
		// 売上の集計（通貨別）
		if (booking.status !== "cancelled") {
			totalRevenue += booking.totalPrice;
			currencyBreakdown[booking.currency] =
				(currencyBreakdown[booking.currency] || 0) + booking.totalPrice;
		}

		// ステータス別件数
		statusBreakdown[booking.status] =
			(statusBreakdown[booking.status] || 0) + 1;

		// チャネル別件数
		const channel = booking.data?.channel || booking.data?.referer || "Direct";
		channelBreakdown[channel] = (channelBreakdown[channel] || 0) + 1;

		// 月別予約数
		const checkInDate = new Date(booking.checkIn);
		const monthKey = `${checkInDate.getFullYear()}-${String(checkInDate.getMonth() + 1).padStart(2, "0")}`;
		monthlyBookings[monthKey] = (monthlyBookings[monthKey] || 0) + 1;

		// 滞在日数の計算
		const checkIn = new Date(booking.checkIn);
		const checkOut = new Date(booking.checkOut);
		const nights = Math.ceil(
			(checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24),
		);
		totalNights += nights;

		// 日付範囲の更新
		if (checkIn < minDate) minDate = checkIn;
		if (checkOut > maxDate) maxDate = checkOut;

		// 稼働率計算のための客室泊数
		if (booking.status !== "cancelled" && booking.status !== "no_show") {
			totalRoomNights += nights;
		}
	}

	// 平均滞在日数
	const averageStayLength =
		bookings.length > 0 ? totalNights / bookings.length : 0;

	// 稼働率の計算（簡易版）
	// 実際の実装では、施設の総客室数と期間を考慮する必要がある
	const daysBetween = Math.ceil(
		(maxDate.getTime() - minDate.getTime()) / (1000 * 60 * 60 * 24),
	);
	const occupancyRate =
		daysBetween > 0 ? (totalRoomNights / daysBetween) * 100 : 0;

	return {
		totalBookings: bookings.length,
		totalRevenue,
		currencyBreakdown,
		statusBreakdown,
		channelBreakdown,
		monthlyBookings,
		averageStayLength,
		occupancyRate: Math.min(100, occupancyRate), // 100%を上限とする
		dateRange: {
			from: filters.dateFrom || minDate.toISOString().split("T")[0],
			to: filters.dateTo || maxDate.toISOString().split("T")[0],
		},
	};
}

/**
 * 認証チェックヘルパー
 *
 * Clerk認証を使用してユーザーの認証状態を確認し、ユーザーIDを取得します。
 * すべての認証が必要な操作の入り口となる重要な関数です。
 *
 * 認証フロー:
 * 1. Convexの認証コンテキストからユーザー情報取得
 * 2. 認証されていない場合は即座にエラー
 * 3. getAuthenticatedUserIdでClerk IDを抽出
 *
 * セキュリティ考慮事項:
 * - 認証失敗は明確にUNAUTHORIZEDエラーとして扱う
 * - エラーログに詳細情報を記録（監査証跡）
 * - 関数名を含めることでデバッグを容易に
 * - タイミング攻撃を防ぐため、処理時間を一定に保つ
 *
 * エラーハンドリング:
 * - 認証エラーは回復不可能なため、即座にthrow
 * - クライアントは再ログインを促す必要がある
 * - エラーメッセージは最小限の情報のみ
 *
 * 使用パターン:
 * ```typescript
 * // すべての認証が必要な関数の最初に呼び出す
 * const userId = await requireAuth(ctx);
 * // 以降の処理はuserIdを使用
 * ```
 *
 * @param ctx - QueryまたはMutationコンテキスト
 * @returns {Promise<string>} 認証済みユーザーのID
 *
 * @throws {ConvexError} ERROR_CODES.UNAUTHORIZED - 未認証の場合
 *
 * @example
 * export const secureQuery = query({
 *   handler: async (ctx) => {
 *     const userId = await requireAuth(ctx);
 *     // 認証済みユーザーとして処理を続行
 *   }
 * });
 */
export async function requireAuth(ctx: Ctx): Promise<string> {
	const logger = createLogger("Beds24BookingsModel.requireAuth", ctx);

	const identity = await ctx.auth.getUserIdentity();
	if (!identity) {
		logger.error("認証エラー: ユーザーが認証されていません");
		throw createConvexError(
			ERROR_CODES.UNAUTHORIZED,
			"Unauthenticated",
			undefined,
			{ functionName: "beds24Bookings" },
		);
	}

	return getAuthenticatedUserId(identity);
}
