import { ConvexError, v } from "convex/values";
import type { Id } from "./_generated/dataModel";
import type { MutationCtx } from "./_generated/server";
import { mutation } from "./_generated/server";
import { getAuthenticatedUserId } from "./lib/auth";
import {
	formatBookingReview,
	generateReviewHash,
	validateReviewData,
} from "./model/otaReviews";

// Mutations

/**
 * レビューの作成または更新の共通ロジック
 *
 * 新規レビューの作成または既存レビューの更新を行う内部共通関数です。
 * ユニークハッシュを使用して重複を防ぎ、適切なバリデーションを実行します。
 *
 * ビジネスロジック:
 * - 施設の存在確認を先に行い、存在しない場合は早期リターン
 * - userPropertiesテーブルのインデックスを活用した効率的な検索
 * - アクセス権限がない場合は理由を含めたレスポンスを返却
 *
 * @param ctx - Convexミューテーションコンテキスト
 * @param userId - 実行ユーザーのID
 * @param args - レビューデータ
 * @param args.beds24PropertyId - 施設ID
 * @param args.otaId - OTA ID
 * @param args.score - レビュースコア
 * @param args.title - レビュータイトル（オプション）
 * @param args.reviewContent - レビュー内容（通常のOTA用）
 * @param args.reviewContentStructured - 構造化レビュー内容（Booking.com用）
 * @param args.reviewContentStructured.positive - ポジティブな内容
 * @param args.reviewContentStructured.negative - ネガティブな内容
 * @param args.reviewerName - レビュアー名
 * @param args.reviewerCountry - レビュアーの国（オプション）
 * @param args.reviewDate - レビュー日時（タイムスタンプ）
 * @returns 作成/更新されたレビューIDと新規作成フラグ
 * @throws ConvexError 施設未検出、アクセス権限エラー、OTA未検出、バリデーションエラー
 */
async function upsertReviewLogic(
	ctx: MutationCtx,
	userId: string,
	args: {
		beds24PropertyId: Id<"beds24Properties">;
		otaId: Id<"otaMaster">;
		score: number;
		title?: string;
		reviewContent?: string;
		reviewContentStructured?: {
			positive?: string;
			negative?: string;
		};
		reviewerName: string;
		reviewerCountry?: string;
		reviewDate: number;
	},
): Promise<{ reviewId: Id<"otaReviews">; isNew: boolean }> {
	// 施設の所有権確認
	const property = await ctx.db.get(args.beds24PropertyId);
	if (!property) {
		throw new ConvexError("Property not found");
	}

	// アクセス権限確認
	const hasAccess = await ctx.db
		.query("userProperties")
		.withIndex("by_userId_and_propertyId", (q) =>
			q.eq("userId", userId).eq("propertyId", args.beds24PropertyId),
		)
		.unique();

	if (!hasAccess) {
		throw new ConvexError("Access denied");
	}

	// OTAの存在確認
	const ota = await ctx.db.get(args.otaId);
	if (!ota) {
		throw new ConvexError("OTA not found");
	}

	// レビュー内容の生成
	let finalReviewContent: string;
	if (args.reviewContent !== undefined) {
		// 通常のOTA（楽天トラベル、じゃらん等）
		finalReviewContent = args.reviewContent;
	} else if (args.reviewContentStructured) {
		// Booking.com形式
		finalReviewContent = formatBookingReview(
			args.reviewContentStructured.positive,
			args.reviewContentStructured.negative,
		);
	} else {
		throw new ConvexError("Review content is required");
	}

	// バリデーション
	const validation = validateReviewData({
		score: args.score,
		reviewContent: finalReviewContent,
		reviewerName: args.reviewerName,
		reviewDate: args.reviewDate,
	});

	if (!validation.isValid) {
		throw new ConvexError(`Validation failed: ${validation.errors.join(", ")}`);
	}

	// ユニークハッシュの生成
	const uniqueHash = generateReviewHash({
		otaId: args.otaId,
		beds24PropertyId: args.beds24PropertyId,
		reviewDate: args.reviewDate,
		reviewContent: finalReviewContent,
	});

	// 既存レビューのチェック
	const existing = await ctx.db
		.query("otaReviews")
		.withIndex("by_uniqueHash", (q) => q.eq("uniqueHash", uniqueHash))
		.unique();

	const now = Date.now();

	if (existing) {
		// 既存レビューの更新（スコアやタイトルが変更された可能性）
		await ctx.db.patch(existing._id, {
			score: args.score,
			title: args.title,
			reviewerCountry: args.reviewerCountry,
			updatedAt: now,
		});

		return {
			reviewId: existing._id,
			isNew: false,
		};
	} else {
		// 新規レビューの作成
		const reviewId = await ctx.db.insert("otaReviews", {
			beds24PropertyId: args.beds24PropertyId,
			otaId: args.otaId,
			uniqueHash,
			score: args.score,
			title: args.title,
			reviewContent: finalReviewContent,
			reviewContentStructured: args.reviewContentStructured,
			reviewerName: args.reviewerName,
			reviewerCountry: args.reviewerCountry,
			reviewDate: args.reviewDate,
			createdAt: now,
			updatedAt: now,
		});

		return {
			reviewId,
			isNew: true,
		};
	}
}

/**
 * レビューの作成または更新（公開用）
 *
 * クライアントから呼び出される公開ミューテーション関数です。
 * 認証チェックを行い、ユーザーのアクセス権限を確認してからレビューを作成/更新します。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {Id<"beds24Properties">} args.beds24PropertyId - 施設ID
 * @param {Id<"otaMaster">} args.otaId - OTA ID
 * @param {number} args.score - レビュースコア（1-10）
 * @param {string} [args.title] - レビュータイトル
 * @param {string} [args.reviewContent] - レビュー内容（通常のOTA用）
 * @param {Object} [args.reviewContentStructured] - 構造化レビュー内容（Booking.com用）
 * @param {string} args.reviewerName - レビュアー名
 * @param {string} [args.reviewerCountry] - レビュアーの国
 * @param {number} args.reviewDate - レビュー日時（タイムスタンプ）
 * @returns {Promise<{reviewId: Id<"otaReviews">, isNew: boolean}>} レビューIDと新規作成フラグ
 * @throws {ConvexError} 認証エラー、アクセス権限エラー、バリデーションエラー
 *
 * @example
 * const result = await upsertReview({
 *   beds24PropertyId: "prop123",
 *   otaId: "ota456",
 *   score: 8.5,
 *   title: "素晴らしい滞在",
 *   reviewContent: "とても良いホテルでした。",
 *   reviewerName: "山田太郎",
 *   reviewerCountry: "日本",
 *   reviewDate: Date.now()
 * });
 */
export const upsertReview = mutation({
	args: {
		beds24PropertyId: v.id("beds24Properties"),
		otaId: v.id("otaMaster"),
		score: v.number(),
		title: v.optional(v.string()),
		reviewContent: v.optional(v.string()),
		reviewContentStructured: v.optional(
			v.object({
				positive: v.optional(v.string()),
				negative: v.optional(v.string()),
			}),
		),
		reviewerName: v.string(),
		reviewerCountry: v.optional(v.string()),
		reviewDate: v.number(),
	},
	returns: v.object({
		reviewId: v.id("otaReviews"),
		isNew: v.boolean(),
	}),
	handler: async (ctx, args) => {
		// 認証チェック
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new ConvexError("Unauthenticated");
		}

		return upsertReviewLogic(ctx, getAuthenticatedUserId(identity), args);
	},
});

/**
 * 複数レビューの一括登録（スクレイピング用）
 *
 * スクレイピングなどで取得した複数のレビューを一括で登録/更新します。
 * 各レビューは個別に処理され、エラーが発生しても他のレビューの処理は継続されます。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {Array} args.reviews - レビューデータの配列
 * @returns {Promise<BatchResult>} バッチ処理結果
 * @returns {number} returns.totalProcessed - 処理された総件数
 * @returns {number} returns.newReviews - 新規作成されたレビュー数
 * @returns {number} returns.updatedReviews - 更新されたレビュー数
 * @returns {Array} returns.errors - エラー情報の配列
 * @throws {ConvexError} 認証エラー
 *
 * @example
 * const result = await batchUpsertReviews({
 *   reviews: [
 *     { beds24PropertyId: "prop1", otaId: "ota1", score: 8, ... },
 *     { beds24PropertyId: "prop2", otaId: "ota1", score: 9, ... }
 *   ]
 * });
 * console.log(`${result.newReviews}件の新規レビューを作成しました`);
 */
export const batchUpsertReviews = mutation({
	args: {
		reviews: v.array(
			v.object({
				beds24PropertyId: v.id("beds24Properties"),
				otaId: v.id("otaMaster"),
				score: v.number(),
				title: v.optional(v.string()),
				reviewContent: v.optional(v.string()),
				reviewContentStructured: v.optional(
					v.object({
						positive: v.optional(v.string()),
						negative: v.optional(v.string()),
					}),
				),
				reviewerName: v.string(),
				reviewerCountry: v.optional(v.string()),
				reviewDate: v.number(),
			}),
		),
	},
	returns: v.object({
		totalProcessed: v.number(),
		newReviews: v.number(),
		updatedReviews: v.number(),
		errors: v.array(
			v.object({
				index: v.number(),
				error: v.string(),
			}),
		),
	}),
	handler: async (ctx, args) => {
		// 認証チェック
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new ConvexError("Unauthenticated");
		}

		const results = {
			totalProcessed: 0,
			newReviews: 0,
			updatedReviews: 0,
			errors: [] as Array<{ index: number; error: string }>,
		};

		// 各レビューを処理
		for (let i = 0; i < args.reviews.length; i++) {
			const review = args.reviews[i];
			results.totalProcessed++;

			try {
				// 共通ロジックを使用してレビューを作成または更新
				const result = await upsertReviewLogic(
					ctx,
					getAuthenticatedUserId(identity),
					review,
				);
				if (result.isNew) {
					results.newReviews++;
				} else {
					results.updatedReviews++;
				}
			} catch (error) {
				results.errors.push({
					index: i,
					error:
						error instanceof Error ? error.message : "Unknown error occurred",
				});
			}
		}

		return results;
	},
});

/**
 * レビューの一括削除（施設削除時等）
 *
 * 指定された施設に関連する全てのレビューを削除します。
 * 施設の削除時や、データのクリーンアップ時に使用します。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {Id<"beds24Properties">} args.beds24PropertyId - 削除対象の施設ID
 * @returns {Promise<{deletedCount: number}>} 削除されたレビュー数
 * @throws {ConvexError} 認証エラー、施設未検出、アクセス権限エラー
 *
 * @example
 * const result = await deleteReviewsByProperty({
 *   beds24PropertyId: "prop123"
 * });
 * console.log(`${result.deletedCount}件のレビューを削除しました`);
 */
export const deleteReviewsByProperty = mutation({
	args: {
		beds24PropertyId: v.id("beds24Properties"),
	},
	returns: v.object({
		deletedCount: v.number(),
	}),
	handler: async (ctx, args) => {
		// 認証チェック
		const identity = await ctx.auth.getUserIdentity();
		if (!identity) {
			throw new ConvexError("Unauthenticated");
		}

		// 施設の所有権確認
		const property = await ctx.db.get(args.beds24PropertyId);
		if (!property) {
			throw new ConvexError("Property not found");
		}

		// アクセス権限確認
		const hasAccess = await ctx.db
			.query("userProperties")
			.withIndex("by_userId_and_propertyId", (q) =>
				q
					.eq("userId", getAuthenticatedUserId(identity))
					.eq("propertyId", args.beds24PropertyId),
			)
			.unique();

		if (!hasAccess) {
			throw new ConvexError("Access denied");
		}

		// 該当施設のレビューを取得
		const reviews = await ctx.db
			.query("otaReviews")
			.withIndex("by_beds24PropertyId", (q) =>
				q.eq("beds24PropertyId", args.beds24PropertyId),
			)
			.collect();

		// 各レビューを削除
		let deletedCount = 0;
		for (const review of reviews) {
			await ctx.db.delete(review._id);
			deletedCount++;
		}

		return { deletedCount };
	},
});
