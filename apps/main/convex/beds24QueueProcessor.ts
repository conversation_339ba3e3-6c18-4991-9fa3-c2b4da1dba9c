"use node";

/**
 * Beds24 Queue Processor
 *
 * このファイルはBeds24のキュー処理とジョブ実行を管理します。
 * Node.js環境で実行され、並列処理を含みます。
 */

import { v } from "convex/values";
import { internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import { internalAction } from "./_generated/server";
import { ConvexError } from "./lib/errors";
import { createLogger } from "./lib/logging";
import {
	QueueJobType,
	SyncStatus,
	syncQueueJobValidator,
} from "./types/beds24";

// processQueueの戻り値型
type ProcessQueueResult = {
	processed: boolean;
	jobId?: Id<"syncQueue">;
	jobType?: string;
	userId?: string;
	status?: string;
	error?: string;
	// 並列処理の統計情報
	parallelStats?: {
		totalJobs: number;
		successful: number;
		failed: number;
		processedJobTypes: string[];
	};
} | null;

/**
 * キューから並列処理で複数ジョブを処理する
 *
 * 各ジョブタイプから1つずつジョブを取得し、並列で実行します。
 * 処理の効率化のため、異なるジョブタイプのジョブを同時に処理します。
 *
 * @returns 処理結果（処理されたジョブの情報と統計データ）、またはジョブがない場合はnull
 * @example
 * // キュー処理を実行
 * const result = await ctx.runAction(
 *   internal.beds24QueueProcessor.processQueue,
 *   {}
 * );
 * if (result?.parallelStats) {
 *   console.log(`${result.parallelStats.totalJobs}件のジョブを並列処理しました`);
 *   console.log(`成功: ${result.parallelStats.successful}件`);
 *   console.log(`失敗: ${result.parallelStats.failed}件`);
 * }
 *
 * @note 並列処理により、順次処理と比較して処理時間を短縮できます
 */
export const processQueue = internalAction({
	args: {},
	returns: v.union(
		v.object({
			processed: v.boolean(),
			jobId: v.optional(v.id("syncQueue")),
			jobType: v.optional(v.string()),
			userId: v.optional(v.string()),
			status: v.optional(v.string()),
			error: v.optional(v.string()),
			// 並列処理の統計情報
			parallelStats: v.optional(
				v.object({
					totalJobs: v.number(),
					successful: v.number(),
					failed: v.number(),
					processedJobTypes: v.array(v.string()),
				}),
			),
		}),
		v.null(),
	),
	handler: async (ctx): Promise<ProcessQueueResult> => {
		const logger = createLogger("processQueue", ctx);
		const startTime = Date.now();

		logger.info("並列ジョブ処理を開始");

		// 各ジョブタイプからジョブを取得
		const jobTypes = [
			QueueJobType.SYNC_PROPERTIES,
			QueueJobType.SYNC_REVIEWS,
			QueueJobType.SYNC_BOOKINGS,
			QueueJobType.SCRAPE_BOOKING_COM_SLUG,
		];

		// 各ジョブタイプから1つずつジョブを取得
		const dequeueStartTime = Date.now();
		const jobPromises = jobTypes.map((jobType) =>
			ctx.runQuery(internal.beds24Queue.dequeueJobByType, { jobType }),
		);

		const jobResults = await Promise.all(jobPromises);
		const dequeueTime = Date.now() - dequeueStartTime;

		// ジョブタイプ別の取得結果を集計
		const jobTypesFound: Record<string, boolean> = {};
		jobTypes.forEach((jobType, index) => {
			jobTypesFound[jobType] = jobResults[index] !== null;
		});

		const validJobs = jobResults
			.filter((result): result is NonNullable<typeof result> => result !== null)
			.map((result) => result.job);

		if (validJobs.length === 0) {
			logger.info("実行可能なジョブがありません", {
				dequeueTimeMs: dequeueTime,
				jobTypesChecked: jobTypes,
			});
			return { processed: false };
		}

		logger.info("並列処理するジョブを取得", {
			totalJobs: validJobs.length,
			jobTypes: validJobs.map((job) => job.jobType),
			dequeueTimeMs: dequeueTime,
			jobTypesFound,
		});

		// ジョブを並列実行
		const processingStartTime = Date.now();
		const jobStartTimes: Record<string, number> = {};

		// 各ジョブの開始時刻を記録
		validJobs.forEach((job) => {
			jobStartTimes[job._id] = Date.now();
		});

		const processPromises = validJobs.map((job) =>
			ctx.runAction(internal.beds24QueueProcessor.processJobWithTracking, {
				job,
			}),
		);

		const results = await Promise.allSettled(processPromises);
		const totalProcessingTime = Date.now() - processingStartTime;

		// 結果の集計
		let successful = 0;
		let failed = 0;
		const processedJobTypes: string[] = [];
		const jobProcessingTimes: Record<string, number> = {};
		const jobTypeStats: Record<
			string,
			{ success: number; failed: number; avgTimeMs: number }
		> = {};

		// ジョブタイプ別の統計を初期化
		jobTypes.forEach((jobType) => {
			jobTypeStats[jobType] = { success: 0, failed: 0, avgTimeMs: 0 };
		});

		results.forEach((result, index) => {
			const job = validJobs[index];
			const processingTime = Date.now() - jobStartTimes[job._id];
			jobProcessingTimes[job._id] = processingTime;
			processedJobTypes.push(job.jobType);

			if (result.status === "fulfilled" && result.value.success) {
				successful++;
				jobTypeStats[job.jobType].success++;
				logger.info("ジョブ処理成功", {
					jobId: job._id,
					jobType: job.jobType,
					userId: job.userId,
					processingTimeMs: processingTime,
				});
			} else {
				failed++;
				jobTypeStats[job.jobType].failed++;
				const error =
					result.status === "rejected" ? result.reason : result.value.error;
				logger.error("ジョブ処理失敗", {
					jobId: job._id,
					jobType: job.jobType,
					userId: job.userId,
					processingTimeMs: processingTime,
					error: error instanceof Error ? error.message : String(error),
					errorStack: error instanceof Error ? error.stack : undefined,
				});
			}

			// ジョブタイプ別の平均処理時間を更新
			const jobType = job.jobType;
			const sameTypeJobs = validJobs.filter((j) => j.jobType === jobType);
			const sameTypeProcessingTimes = sameTypeJobs
				.map((j) => jobProcessingTimes[j._id])
				.filter((time) => time !== undefined);

			if (sameTypeProcessingTimes.length > 0) {
				jobTypeStats[jobType].avgTimeMs =
					sameTypeProcessingTimes.reduce((sum, time) => sum + time, 0) /
					sameTypeProcessingTimes.length;
			}
		});

		const elapsedTime = Date.now() - startTime;

		// 並列処理の効果を計算（順次処理との比較）
		const maxProcessingTime = Math.max(...Object.values(jobProcessingTimes));
		const totalSequentialTime = Object.values(jobProcessingTimes).reduce(
			(sum, time) => sum + time,
			0,
		);
		const parallelSpeedup =
			totalSequentialTime > 0 ? totalSequentialTime / maxProcessingTime : 1;

		logger.info("並列ジョブ処理完了", {
			totalJobs: validJobs.length,
			successful,
			failed,
			elapsedTimeMs: elapsedTime,
			jobTypes: processedJobTypes,
			performance: {
				dequeueTimeMs: dequeueTime,
				totalProcessingTimeMs: totalProcessingTime,
				maxJobProcessingTimeMs: maxProcessingTime,
				estimatedSequentialTimeMs: totalSequentialTime,
				parallelSpeedup: `${parallelSpeedup.toFixed(2)}x`,
			},
			jobTypeStats,
		});

		// 最初に処理されたジョブの情報を返す（後方互換性のため）
		const firstJob = validJobs[0];
		const firstResult = results[0];
		const status =
			firstResult.status === "fulfilled" && firstResult.value.success
				? SyncStatus.COMPLETED
				: SyncStatus.FAILED;

		return {
			processed: true,
			jobId: firstJob._id,
			jobType: firstJob.jobType,
			userId: firstJob.userId,
			status,
			error:
				firstResult.status === "rejected"
					? String(firstResult.reason)
					: firstResult.value.error,
			parallelStats: {
				totalJobs: validJobs.length,
				successful,
				failed,
				processedJobTypes,
			},
		};
	},
});

/**
 * ジョブの処理を一括で管理するヘルパー関数
 *
 * processQueueの最適化のため、sequential mutationを減らし、
 * ジョブの実行とステータス更新を統合的に処理します。
 * ジョブタイプに応じて適切なアクションを実行し、エラー時のリトライ設定も行います。
 *
 * @param job - 処理するジョブの情報
 * @returns 処理結果（成功/失敗とエラー情報）
 * @example
 * // ジョブを処理
 * const result = await ctx.runAction(
 *   internal.beds24QueueProcessor.processJobWithTracking,
 *   {
 *     job: {
 *       _id: "job123",
 *       jobType: QueueJobType.SYNC_PROPERTIES,
 *       userId: "user456",
 *       // ... その他のジョブ情報
 *     }
 *   }
 * );
 * if (result.success) {
 *   console.log("ジョブ処理成功");
 * } else {
 *   console.log("ジョブ処理失敗:", result.error);
 * }
 *
 * @throws 未知のジョブタイプの場合はエラーをスロー
 */
export const processJobWithTracking = internalAction({
	args: {
		job: syncQueueJobValidator,
	},
	returns: v.object({
		success: v.boolean(),
		error: v.optional(v.string()),
	}),
	handler: async (ctx, args) => {
		const logger = createLogger("processJobWithTracking", ctx);
		const { job } = args;

		// ジョブの詳細情報をログ出力
		logger.info("[DEBUG] processJobWithTracking 開始", {
			jobId: job._id,
			jobType: job.jobType,
			userId: job.userId,
			status: job.status,
			attempts: job.attempts,
			createdAt: new Date(job.createdAt).toISOString(),
			hasMetadata: !!job.metadata,
		});

		// sync_reviewsジョブの場合、メタデータを詳細にログ出力
		if (job.jobType === QueueJobType.SYNC_REVIEWS && job.metadata) {
			logger.info("[DEBUG] SYNC_REVIEWSジョブのメタデータ詳細", {
				jobId: job._id,
				metadataType: typeof job.metadata,
				metadataKeys: Object.keys(job.metadata),
				metadataStringified: JSON.stringify(job.metadata),
				metadata: job.metadata,
			});
		}

		try {
			// ジョブステータスをPROCESSINGに更新
			await ctx.runMutation(internal.beds24Queue.updateJobStatus, {
				jobId: job._id,
				status: SyncStatus.PROCESSING,
			});

			// ジョブタイプに応じて処理を実行
			switch (job.jobType) {
				case QueueJobType.SYNC_PROPERTIES:
					await ctx.runAction(internal.beds24PropertySync.syncUserProperties, {
						userId: job.userId,
						jobId: job._id,
					});
					break;

				case QueueJobType.SYNC_REVIEWS:
					logger.info("[DEBUG] syncReviewsFromOTA呼び出し前", {
						jobId: job._id,
						taskId: job._id,
						metadata: job.metadata,
					});

					await ctx.runAction(internal.otaReviewsSync.syncReviewsFromOTA, {
						taskId: job._id,
					});
					break;

				case QueueJobType.SCRAPE_BOOKING_COM_SLUG:
					// Convex公式パターン: ミューテーションからアクションをスケジュール
					await ctx.runMutation(
						internal.bookingComSlugQueries.startScrapeBookingComSlug,
						{
							taskId: job._id,
						},
					);
					// SCRAPE_BOOKING_COM_SLUGの場合、ジョブステータスの更新はアクション側で行うため、ここでreturn
					return { success: true };

				// 予約同期関連のジョブタイプ

				case QueueJobType.SYNC_BOOKINGS: {
					logger.info("[DEBUG] syncBookingsFromBeds24呼び出し前", {
						jobId: job._id,
						taskId: job._id,
						metadata: job.metadata,
					});

					await ctx.runAction(
						internal.beds24BookingSync.syncBookingsFromBeds24,
						{
							taskId: job._id,
						},
					);
					break;
				}

				default: {
					// 未知のジョブタイプの詳細なログ記録
					const validJobTypes = Object.values(QueueJobType).join(", ");
					const errorMessage = `不明なジョブタイプ: ${job.jobType}。有効なジョブタイプ: ${validJobTypes}`;

					logger.error("未知のジョブタイプが検出されました", {
						jobId: job._id,
						jobType: job.jobType,
						userId: job.userId,
						metadata: job.metadata,
						validJobTypes: Object.values(QueueJobType),
						createdAt: new Date(job.createdAt).toISOString(),
					});

					// Sentryにエラーを報告（本番環境の場合）
					if (
						typeof globalThis !== "undefined" &&
						(globalThis as any).Sentry?.captureException
					) {
						(globalThis as any).Sentry.captureException(
							new Error(errorMessage),
							{
								extra: {
									jobId: job._id,
									jobType: job.jobType,
									userId: job.userId,
									metadata: job.metadata,
								},
							},
						);
					}

					throw new Error(errorMessage);
				}
			}

			// ジョブを完了にマーク
			await ctx.runMutation(internal.beds24Queue.updateJobStatus, {
				jobId: job._id,
				status: SyncStatus.COMPLETED,
			});

			return { success: true };
		} catch (error) {
			// エラーメッセージの改善
			let errorMessage: string;
			let errorDetails: any = {};

			if (error instanceof ConvexError) {
				const errorData = error.data as any;
				errorMessage = `${errorData.message} [${errorData.code}]`;
				errorDetails = errorData.details || {};
			} else if (error instanceof Error) {
				errorMessage = error.message;
			} else {
				errorMessage = String(error);
			}

			// デバッグ用の詳細情報を含める
			const enrichedErrorMessage = JSON.stringify(
				{
					message: errorMessage,
					jobId: job._id,
					jobType: job.jobType,
					userId: job.userId,
					attempts: job.attempts,
					metadata: job.metadata,
					errorDetails,
					timestamp: new Date().toISOString(),
				},
				null,
				2,
			);

			const shouldRetry = job.attempts < job.maxAttempts;

			if (shouldRetry) {
				const retryDelay = Math.min(60000 * 2 ** (job.attempts - 1), 300000);
				const nextRetryAt = Date.now() + retryDelay;

				await ctx.runMutation(internal.beds24Queue.updateJobStatus, {
					jobId: job._id,
					status: SyncStatus.FAILED,
					error: enrichedErrorMessage, // より詳細なエラー情報を保存
					nextRetryAt,
				});
			} else {
				await ctx.runMutation(internal.beds24Queue.updateJobStatus, {
					jobId: job._id,
					status: SyncStatus.FAILED,
					error: enrichedErrorMessage,
				});
			}

			return { success: false, error: enrichedErrorMessage };
		}
	},
});
