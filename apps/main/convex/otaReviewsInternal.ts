import { v } from "convex/values";
import type { Id } from "./_generated/dataModel";
import { internalMutation, internalQuery } from "./_generated/server";
import { createConvexError, ERROR_CODES } from "./lib/errors";
import { createLogger } from "./lib/logging";
import { formatBookingReview, generateReviewHash } from "./model/otaReviews";
import type { ReviewSyncMetadata } from "./types/reviews";
import { reviewSyncMetadataValidator } from "./types/reviews";

// Internal Functions for Review Sync

/**
 * レビューの作成または更新（内部用）
 *
 * システムまたは内部プロセスから呼び出される内部ミューテーション関数です。
 * 認証チェックをバイパスし、userIdを引数として受け取ります。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {string} args.userId - ユーザーID（内部用のため直接指定）
 * @param {Id<"beds24Properties">} args.beds24PropertyId - 施設ID
 * @param {Id<"otaMaster">} args.otaId - OTA ID
 * @param {number} args.score - レビュースコア
 * @param {string} [args.title] - レビュータイトル
 * @param {string} [args.reviewContent] - レビュー内容（Booking.com以外用）
 * @param {Object} [args.reviewContentStructured] - 構造化レビュー内容（Booking.com用）
 * @param {string} args.reviewerName - レビュアー名
 * @param {string} [args.reviewerCountry] - レビュアーの国
 * @param {number} args.reviewDate - レビュー日時
 * @returns {Promise<{reviewId: Id<"otaReviews">, isNew: boolean}>} レビューIDと新規作成フラグ
 * @internal
 */
export const _upsertReview = internalMutation({
	args: {
		userId: v.string(), // 内部用のためuserIdを引数で受け取る
		beds24PropertyId: v.id("beds24Properties"),
		otaId: v.id("otaMaster"),
		score: v.number(),
		title: v.optional(v.string()),
		reviewContent: v.optional(v.string()), // Booking.com以外用
		reviewContentStructured: v.optional(
			v.object({
				positive: v.optional(v.string()),
				negative: v.optional(v.string()),
			}),
		), // Booking.com用
		reviewerName: v.string(),
		reviewerCountry: v.optional(v.string()),
		reviewDate: v.number(),
	},
	returns: v.object({
		reviewId: v.id("otaReviews"),
		isNew: v.boolean(),
	}),
	handler: async (ctx, args) => {
		// システムまたは内部プロセスからの呼び出し
		// userIdは引数から直接取得（認証チェックなし）

		// 施設の存在確認
		const property = await ctx.db.get(args.beds24PropertyId);
		if (!property) {
			throw new Error("Property not found");
		}

		// アクセス権限確認
		const hasAccess = await ctx.db
			.query("userProperties")
			.withIndex("by_userId_and_propertyId", (q) =>
				q.eq("userId", args.userId).eq("propertyId", args.beds24PropertyId),
			)
			.unique();

		if (!hasAccess) {
			throw new Error("Access denied");
		}

		// OTAの存在確認
		const ota = await ctx.db.get(args.otaId);
		if (!ota) {
			throw new Error("OTA not found");
		}

		// レビュー内容の生成
		let finalReviewContent: string;
		if (args.reviewContent !== undefined) {
			// 通常のOTA（楽天トラベル、じゃらん等）
			finalReviewContent = args.reviewContent;
		} else if (args.reviewContentStructured) {
			// Booking.com形式
			finalReviewContent = formatBookingReview(
				args.reviewContentStructured.positive,
				args.reviewContentStructured.negative,
			);
		} else {
			throw new Error("Review content is required");
		}

		// ユニークハッシュの生成
		const uniqueHash = generateReviewHash({
			otaId: args.otaId,
			beds24PropertyId: args.beds24PropertyId,
			reviewDate: args.reviewDate,
			reviewContent: finalReviewContent,
		});

		// 既存レビューのチェック
		const existing = await ctx.db
			.query("otaReviews")
			.withIndex("by_uniqueHash", (q) => q.eq("uniqueHash", uniqueHash))
			.unique();

		const now = Date.now();

		if (existing) {
			// 既存レビューの更新（スコアやタイトルが変更された可能性）
			await ctx.db.patch(existing._id, {
				score: args.score,
				title: args.title,
				reviewerCountry: args.reviewerCountry,
				updatedAt: now,
			});

			return {
				reviewId: existing._id,
				isNew: false,
			};
		} else {
			// 新規レビューの作成
			const reviewId = await ctx.db.insert("otaReviews", {
				beds24PropertyId: args.beds24PropertyId,
				otaId: args.otaId,
				uniqueHash,
				score: args.score,
				title: args.title,
				reviewContent: finalReviewContent,
				reviewContentStructured: args.reviewContentStructured,
				reviewerName: args.reviewerName,
				reviewerCountry: args.reviewerCountry,
				reviewDate: args.reviewDate,
				createdAt: now,
				updatedAt: now,
			});

			return {
				reviewId,
				isNew: true,
			};
		}
	},
});

/**
 * レビュー同期タスクの詳細を取得
 *
 * 同期キューからレビュー同期タスクの詳細情報を取得します。
 * jobTypeが"sync_reviews"のタスクのみを対象とし、メタデータの検証も行います。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {Id<"syncQueue">} args.taskId - タスクID
 * @returns {Promise<{task: SyncTask, metadata: ReviewSyncMetadata} | null>} タスク詳細とメタデータ、または null
 * @internal
 *
 * @example
 * const taskInfo = await getReviewSyncTask({ taskId: "task123" });
 * if (taskInfo) {
 *   console.log(`URL: ${taskInfo.metadata.url}`);
 *   console.log(`Status: ${taskInfo.task.status}`);
 * }
 */
export const getReviewSyncTask = internalQuery({
	args: {
		taskId: v.id("syncQueue"),
	},
	returns: v.union(
		v.null(),
		v.object({
			task: v.object({
				_id: v.id("syncQueue"),
				_creationTime: v.number(),
				userId: v.string(),
				jobType: v.string(),
				priority: v.number(),
				status: v.string(),
				attempts: v.number(),
				maxAttempts: v.number(),
				scheduledFor: v.number(),
				startedAt: v.optional(v.number()),
				completedAt: v.optional(v.number()),
				lastError: v.optional(v.string()),
				nextRetryAt: v.optional(v.number()),
				metadata: v.optional(v.any()),
				result: v.optional(v.any()),
				createdAt: v.number(),
				updatedAt: v.number(),
			}),
			metadata: reviewSyncMetadataValidator,
		}),
	),
	handler: async (ctx, args) => {
		const logger = createLogger("getReviewSyncTask", ctx);
		logger.debug("getReviewSyncTask 開始", {
			taskId: args.taskId,
			timestamp: new Date().toISOString(),
		});

		const task = await ctx.db.get(args.taskId);

		if (!task) {
			logger.error("タスクが見つかりません", undefined, {
				taskId: args.taskId,
				reason: "db.getがnullを返しました",
			});
			return null;
		}

		logger.debug("タスクが見つかりました", {
			taskId: args.taskId,
			jobType: task.jobType,
			status: task.status,
			userId: task.userId,
			attempts: task.attempts,
			createdAt: new Date(task.createdAt).toISOString(),
			metadataExists: !!task.metadata,
		});

		// jobTypeがsync_reviewsでない場合はnullを返す
		if (task.jobType !== "sync_reviews") {
			logger.warn("jobTypeが一致しません", {
				taskId: args.taskId,
				expectedJobType: "sync_reviews",
				actualJobType: task.jobType,
			});
			return null;
		}

		// メタデータの検証
		if (!task.metadata) {
			logger.error("メタデータが存在しません", undefined, {
				taskId: args.taskId,
				task: { ...task, metadata: undefined },
			});
			return null;
		}

		logger.debug("メタデータの検証開始", {
			taskId: args.taskId,
			metadataType: typeof task.metadata,
			metadataKeys: Object.keys(task.metadata),
			metadata: JSON.stringify(task.metadata),
		});

		try {
			// メタデータが正しい形式かチェック
			const metadata = task.metadata as ReviewSyncMetadata;

			// 必須フィールドの存在確認
			const validationErrors = [];
			if (!metadata.url) {
				validationErrors.push("urlが存在しません");
			}
			if (!metadata.otaType) {
				validationErrors.push("otaTypeが存在しません");
			}
			if (typeof metadata.pageNumber !== "number") {
				validationErrors.push(
					`pageNumberが数値ではありません: ${typeof metadata.pageNumber}`,
				);
			}

			if (validationErrors.length > 0) {
				logger.error("必須フィールドの検証に失敗", undefined, {
					taskId: args.taskId,
					validationErrors,
					metadata: {
						url: metadata.url,
						otaType: metadata.otaType,
						pageNumber: metadata.pageNumber,
						pageNumberType: typeof metadata.pageNumber,
					},
				});
				return null;
			}

			// OTAタイプの検証
			const validOtaTypes = ["booking.com", "expedia", "hotels.com", "agoda"];
			if (!validOtaTypes.includes(metadata.otaType)) {
				logger.error("無効なOTAタイプ", undefined, {
					taskId: args.taskId,
					otaType: metadata.otaType,
					validOtaTypes,
				});
				return null;
			}

			logger.debug("getReviewSyncTask 成功", {
				taskId: args.taskId,
				url: metadata.url,
				otaType: metadata.otaType,
				pageNumber: metadata.pageNumber,
				propertyId: metadata.propertyId,
				otaId: metadata.otaId,
			});

			return {
				task,
				metadata,
			};
		} catch (error) {
			logger.error(
				"メタデータの解析に失敗",
				error instanceof Error ? error : undefined,
				{
					taskId: args.taskId,
					error: error instanceof Error ? error.message : String(error),
					errorStack: error instanceof Error ? error.stack : undefined,
					metadata: task.metadata,
				},
			);
			return null;
		}
	},
});

/**
 * パースされたレビューのバリデータ
 */
const parsedReviewValidator = v.object({
	reviewId: v.string(),
	score: v.number(),
	title: v.optional(v.string()),
	content: v.string(),
	contentStructured: v.optional(
		v.object({
			positive: v.optional(v.string()),
			negative: v.optional(v.string()),
		}),
	),
	reviewerName: v.string(),
	reviewerCountry: v.optional(v.string()),
	reviewDate: v.number(),
});

/**
 * レビューバッチ処理
 *
 * パースされたレビューデータをバッチで処理し、データベースに保存します。
 * 新規レビューがある場合は次ページのタスクを自動的に作成します。
 *
 * @param {Object} args - 引数オブジェクト
 * @param {Id<"syncQueue">} args.taskId - 処理中のタスクID
 * @param {ParsedReview[]} args.reviews - パース済みレビューの配列
 * @param {boolean} args.hasNextPage - 次ページの存在フラグ
 * @param {string} [args.nextPageUrl] - 次ページのURL（オプション）
 * @returns {Promise<ProcessResult>} 処理結果
 * @returns {number} returns.processedCount - 処理されたレビュー数
 * @returns {number} returns.createdCount - 新規作成されたレビュー数
 * @returns {number} returns.updatedCount - 更新されたレビュー数
 * @returns {Id<"syncQueue">} [returns.nextTaskId] - 次ページのタスクID（該当時のみ）
 * @throws {ConvexError} タスク未検出、メタデータエラー、バッチ処理エラー
 * @internal
 */
export const processReviewBatch = internalMutation({
	args: {
		taskId: v.id("syncQueue"),
		reviews: v.array(parsedReviewValidator),
		hasNextPage: v.boolean(),
		nextPageUrl: v.optional(v.string()),
	},
	returns: v.object({
		processedCount: v.number(),
		createdCount: v.number(),
		updatedCount: v.number(),
		nextTaskId: v.optional(v.id("syncQueue")),
	}),
	handler: async (ctx, args) => {
		const logger = createLogger("processReviewBatch", ctx);
		const results = {
			processedCount: 0,
			createdCount: 0,
			updatedCount: 0,
			nextTaskId: undefined as Id<"syncQueue"> | undefined,
		};

		try {
			// タスクの取得
			const task = await ctx.db.get(args.taskId);
			if (!task) {
				throw createConvexError(ERROR_CODES.NOT_FOUND, "Sync task not found", {
					taskId: args.taskId,
				});
			}

			// メタデータの取得と検証
			const metadata = task.metadata as ReviewSyncMetadata;
			if (!metadata || !metadata.otaId) {
				throw createConvexError(
					ERROR_CODES.VALIDATION_ERROR,
					"Invalid task metadata",
					{ taskId: args.taskId },
				);
			}

			// 施設IDの取得（メタデータから）
			const propertyId = metadata.propertyId;

			// レビューの処理
			for (const review of args.reviews) {
				results.processedCount++;

				try {
					// レビュー内容の生成
					const finalReviewContent = review.contentStructured
						? formatBookingReview(
								review.contentStructured.positive,
								review.contentStructured.negative,
							)
						: review.content;

					// ユニークハッシュの生成
					const uniqueHash = generateReviewHash({
						otaId: metadata.otaId,
						beds24PropertyId: propertyId,
						reviewDate: review.reviewDate,
						reviewContent: finalReviewContent,
					});

					// 既存レビューのチェック
					const existing = await ctx.db
						.query("otaReviews")
						.withIndex("by_uniqueHash", (q) => q.eq("uniqueHash", uniqueHash))
						.unique();

					const now = Date.now();

					if (existing) {
						// 既存レビューの更新
						await ctx.db.patch(existing._id, {
							score: review.score,
							title: review.title,
							reviewerCountry: review.reviewerCountry,
							updatedAt: now,
						});
						results.updatedCount++;
					} else {
						// 新規レビューの作成
						await ctx.db.insert("otaReviews", {
							beds24PropertyId: propertyId,
							otaId: metadata.otaId,
							uniqueHash,
							score: review.score,
							title: review.title,
							reviewContent: finalReviewContent,
							reviewContentStructured: review.contentStructured,
							reviewerName: review.reviewerName,
							reviewerCountry: review.reviewerCountry,
							reviewDate: review.reviewDate,
							createdAt: now,
							updatedAt: now,
						});
						results.createdCount++;
					}
				} catch (error) {
					logger.error(
						"Failed to process review",
						error instanceof Error ? error : undefined,
						{
							reviewId: review.reviewId,
							error: error instanceof Error ? error.message : String(error),
						},
					);
					// 個別のレビューのエラーは無視して続行
				}
			}

			// 次ページの処理
			if (
				args.hasNextPage &&
				results.createdCount > 0 // 新規レビューがある場合のみ続行
			) {
				// 次ページのタスクを作成
				const nextPageNumber = metadata.pageNumber + 1;
				const nextUrl =
					args.nextPageUrl ||
					(metadata.url.includes("?")
						? `${metadata.url}&page=${nextPageNumber}`
						: `${metadata.url}?page=${nextPageNumber}`);

				const nextTaskId = await ctx.db.insert("syncQueue", {
					userId: task.userId,
					jobType: "sync_reviews",
					priority: task.priority,
					status: "pending",
					attempts: 0,
					maxAttempts: task.maxAttempts,
					scheduledFor: Date.now() + 5000, // 5秒後に実行
					metadata: {
						...metadata,
						pageNumber: nextPageNumber,
						url: nextUrl,
					},
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});

				results.nextTaskId = nextTaskId;
			}

			// 全レビューが既存の場合のログ
			if (results.createdCount === 0 && args.hasNextPage) {
				logger.info(
					"All reviews on this page already exist. Stopping pagination.",
					{
						taskId: args.taskId,
						pageNumber: metadata.pageNumber,
						processedCount: results.processedCount,
						updatedCount: results.updatedCount,
					},
				);
			}

			// タスクの完了
			await ctx.db.patch(args.taskId, {
				status: "completed",
				completedAt: Date.now(),
				result: results,
				updatedAt: Date.now(),
			});

			return results;
		} catch (error) {
			// エラー時の処理
			logger.error(
				"Review batch processing failed",
				error instanceof Error ? error : undefined,
				{
					taskId: args.taskId,
					error: error instanceof Error ? error.message : String(error),
					processedCount: results.processedCount,
				},
			);

			// タスクのステータスを失敗に更新
			await ctx.db.patch(args.taskId, {
				status: "failed",
				lastError:
					error instanceof Error ? error.message : "Unknown error occurred",
				updatedAt: Date.now(),
			});

			throw createConvexError(
				ERROR_CODES.REVIEW_BATCH_FAILED,
				"Failed to process review batch",
				{
					taskId: args.taskId,
					processedCount: results.processedCount,
					error: error instanceof Error ? error.message : String(error),
				},
			);
		}
	},
});
