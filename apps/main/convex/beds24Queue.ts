import { v } from "convex/values";
import type { Doc } from "./_generated/dataModel";
import { internalMutation, internalQuery } from "./_generated/server";
import { createConvexError, ERROR_CODES } from "./lib/errors";
import { createLogger } from "./lib/logging";
import { JobPriority, QueueJobType, SyncStatus } from "./types/beds24";

/**
 * ジョブをキューに追加する
 *
 * 指定されたジョブタイプとメタデータで新しいジョブをキューに追加します。
 * 予約同期の場合は重複チェックを行い、遡り処理の場合は自動的に低優先度を設定します。
 *
 * @param userId - ユーザーID
 * @param jobType - ジョブタイプ（施設同期、レビュー同期、スクレイピング、予約同期）
 * @param priority - 優先度（省略時はNORMAL、遡り処理の場合は自動的にLOWに設定）
 * @param scheduledFor - 実行予定時刻（省略時は即座に実行）
 * @param metadata - ジョブ固有のメタデータ
 * @param maxAttempts - 最大試行回数（デフォルト: 3）
 * @returns 作成されたジョブのID（重複時は既存のジョブID）
 * @example
 * // 施設同期ジョブをキューに追加
 * const jobId = await ctx.runMutation(
 *   internal.beds24Queue.enqueueJob,
 *   {
 *     userId: "user123",
 *     jobType: QueueJobType.SYNC_PROPERTIES,
 *     priority: JobPriority.HIGH
 *   }
 * );
 */
export const enqueueJob = internalMutation({
	args: {
		userId: v.string(),
		jobType: v.union(
			v.literal(QueueJobType.SYNC_PROPERTIES),
			v.literal(QueueJobType.SYNC_REVIEWS),
			v.literal(QueueJobType.SCRAPE_BOOKING_COM_SLUG),
			v.literal(QueueJobType.SYNC_BOOKINGS),
		),
		priority: v.optional(v.number()),
		scheduledFor: v.optional(v.number()),
		metadata: v.optional(v.any()),
		maxAttempts: v.optional(v.number()),
	},
	returns: v.id("syncQueue"),
	handler: async (ctx, args) => {
		const startTime = Date.now();
		const logger = createLogger("enqueueJob", ctx);
		logger.info("[enqueueJob] 開始", {
			userId: `${args.userId.substring(0, 20)}...`,
			jobType: args.jobType,
			priority: args.priority,
			timestamp: startTime,
		});

		// Phase 6: 重複ジョブチェック（予約同期の場合）
		const duplicateCheckStartTime = Date.now();
		if (args.jobType === QueueJobType.SYNC_BOOKINGS && args.metadata) {
			const metadata = args.metadata as any;
			const propertyId = metadata.propertyId;
			const dateFrom = metadata.dateFrom;
			const dateTo = metadata.dateTo;
			const pageNumber = metadata.pageNumber || 1;

			if (propertyId && dateFrom && dateTo) {
				// 同じ施設・日付範囲・ページのPENDINGまたはPROCESSINGジョブがあるかチェック
				const existingJobs = await ctx.db
					.query("syncQueue")
					.filter((q) =>
						q.and(
							q.eq(q.field("userId"), args.userId),
							q.eq(q.field("jobType"), QueueJobType.SYNC_BOOKINGS),
							q.or(
								q.eq(q.field("status"), SyncStatus.PENDING),
								q.eq(q.field("status"), SyncStatus.PROCESSING),
							),
						),
					)
					.collect();

				for (const job of existingJobs) {
					const jobMetadata = job.metadata as any;
					if (
						jobMetadata &&
						jobMetadata.propertyId === propertyId &&
						jobMetadata.dateFrom === dateFrom &&
						jobMetadata.dateTo === dateTo &&
						(jobMetadata.pageNumber || 1) === pageNumber
					) {
						logger.warn("[enqueueJob] 重複ジョブのためスキップ", {
							existingJobId: job._id,
							propertyId,
							dateFrom,
							dateTo,
							pageNumber,
						});
						// 既存のジョブIDを返す
						return job._id;
					}
				}
			}
		}
		const duplicateCheckTime = Date.now() - duplicateCheckStartTime;

		// Phase 6: 優先度の自動設定
		const priorityCalculationStartTime = Date.now();
		let finalPriority = args.priority ?? JobPriority.NORMAL;

		// 遡り処理のジョブは低優先度に設定
		if (args.jobType === QueueJobType.SYNC_BOOKINGS && args.metadata) {
			const metadata = args.metadata as any;
			if (metadata.isRecursiveBacktrack === true) {
				// 遡り処理は低優先度
				finalPriority = JobPriority.LOW;
				logger.info("[enqueueJob] 遡り処理のため低優先度を設定", {
					recursionDepth: metadata.recursionDepth || 0,
					originalPriority: args.priority,
					finalPriority,
				});
			}
		}
		const priorityCalculationTime = Date.now() - priorityCalculationStartTime;

		// ジョブデータの準備
		const dataPreparationStartTime = Date.now();
		const now = Date.now();
		const jobData = {
			userId: args.userId,
			jobType: args.jobType,
			priority: finalPriority,
			status: SyncStatus.PENDING,
			attempts: 0,
			maxAttempts: args.maxAttempts ?? 3,
			scheduledFor: args.scheduledFor ?? now,
			metadata: args.metadata,
			createdAt: now,
			updatedAt: now,
		};
		const dataPreparationTime = Date.now() - dataPreparationStartTime;

		// データベース挿入
		const insertStartTime = Date.now();
		const jobId = await ctx.db.insert("syncQueue", jobData);
		const insertTime = Date.now() - insertStartTime;

		const totalTime = Date.now() - startTime;

		logger.info("[enqueueJob] 完了", {
			jobId,
			jobType: args.jobType,
			priority: finalPriority,
			performance: {
				totalDurationMs: totalTime,
				duplicateCheckMs: duplicateCheckTime,
				priorityCalculationMs: priorityCalculationTime,
				dataPreparationMs: dataPreparationTime,
				dbInsertMs: insertTime,
			},
			slowOperation: totalTime > 100,
			slowInsert: insertTime > 50,
		});

		return jobId;
	},
});

/**
 * 次の実行可能なジョブを取得する
 *
 * キューから実行可能な次のジョブを優先度順で取得します。
 * PENDINGおよびリトライ可能なFAILEDジョブが対象となります。
 *
 * @returns 実行可能なジョブ、または存在しない場合はnull
 * @example
 * // 次のジョブを取得
 * const result = await ctx.runQuery(
 *   internal.beds24Queue.dequeueJob,
 *   {}
 * );
 * if (result) {
 *   console.log("次のジョブ:", result.job._id);
 * }
 */
export const dequeueJob = internalQuery({
	args: {},
	returns: v.union(
		v.object({
			job: v.object({
				_id: v.id("syncQueue"),
				_creationTime: v.number(),
				userId: v.string(),
				jobType: v.string(),
				priority: v.number(),
				status: v.string(),
				attempts: v.number(),
				maxAttempts: v.number(),
				scheduledFor: v.number(),
				startedAt: v.optional(v.number()),
				completedAt: v.optional(v.number()),
				lastError: v.optional(v.string()),
				nextRetryAt: v.optional(v.number()),
				metadata: v.optional(v.any()),
				result: v.optional(v.any()),
				createdAt: v.number(),
				updatedAt: v.number(),
			}),
		}),
		v.null(),
	),
	handler: async (ctx) => {
		const logger = createLogger("dequeueJob", ctx);
		logger.debug("実行可能なジョブを検索");

		const now = Date.now();

		// PENDINGまたはFAILED（リトライ可能）なジョブを検索
		// 優先度とscheduledForでソート
		const pendingJobs = await ctx.db
			.query("syncQueue")
			.withIndex("by_status_and_scheduledFor", (q) =>
				q.eq("status", SyncStatus.PENDING).lte("scheduledFor", now),
			)
			.collect();

		const failedJobs = await ctx.db
			.query("syncQueue")
			.withIndex("by_status_and_scheduledFor", (q) =>
				q.eq("status", SyncStatus.FAILED),
			)
			.collect();

		// リトライ可能なFAILEDジョブをフィルタ
		const retryableJobs = failedJobs.filter(
			(job) =>
				job.attempts < job.maxAttempts &&
				job.nextRetryAt &&
				job.nextRetryAt <= now,
		);

		// 全ての候補ジョブをマージして優先度でソート
		const allJobs = [...pendingJobs, ...retryableJobs].sort((a, b) => {
			// 優先度が低い値ほど高優先
			if (a.priority !== b.priority) {
				return a.priority - b.priority;
			}
			// 同じ優先度なら、scheduledForが早い順
			return a.scheduledFor - b.scheduledFor;
		});

		if (allJobs.length === 0) {
			logger.debug("実行可能なジョブが見つかりません");
			return null;
		}

		const job = allJobs[0];
		logger.info("ジョブを取得しました", {
			jobId: job._id,
			jobType: job.jobType,
			userId: job.userId,
			attempts: job.attempts,
		});

		return { job };
	},
});

/**
 * 特定のジョブタイプから次の実行可能なジョブを取得する
 *
 * 指定されたジョブタイプのキューから実行可能な次のジョブを優先度順で取得します。
 * PENDINGおよびリトライ可能なFAILEDジョブが対象となります。
 *
 * @param jobType - 取得対象のジョブタイプ
 * @returns 実行可能なジョブ、または存在しない場合はnull
 * @example
 * // 施設同期ジョブを取得
 * const result = await ctx.runQuery(
 *   internal.beds24Queue.dequeueJobByType,
 *   { jobType: QueueJobType.SYNC_PROPERTIES }
 * );
 * if (result) {
 *   console.log("施設同期ジョブ:", result.job._id);
 * }
 */
export const dequeueJobByType = internalQuery({
	args: {
		jobType: v.union(
			v.literal(QueueJobType.SYNC_PROPERTIES),
			v.literal(QueueJobType.SYNC_REVIEWS),
			v.literal(QueueJobType.SCRAPE_BOOKING_COM_SLUG),
			v.literal(QueueJobType.SYNC_BOOKINGS),
		),
	},
	returns: v.union(
		v.object({
			job: v.object({
				_id: v.id("syncQueue"),
				_creationTime: v.number(),
				userId: v.string(),
				jobType: v.string(),
				priority: v.number(),
				status: v.string(),
				attempts: v.number(),
				maxAttempts: v.number(),
				scheduledFor: v.number(),
				startedAt: v.optional(v.number()),
				completedAt: v.optional(v.number()),
				lastError: v.optional(v.string()),
				nextRetryAt: v.optional(v.number()),
				metadata: v.optional(v.any()),
				result: v.optional(v.any()),
				createdAt: v.number(),
				updatedAt: v.number(),
			}),
		}),
		v.null(),
	),
	handler: async (ctx, args) => {
		const logger = createLogger("dequeueJobByType", ctx);
		logger.debug("特定のジョブタイプから実行可能なジョブを検索", {
			jobType: args.jobType,
		});

		const now = Date.now();

		// 指定されたジョブタイプのPENDINGジョブを検索
		const pendingJobs = await ctx.db
			.query("syncQueue")
			.withIndex("by_status_and_scheduledFor", (q) =>
				q.eq("status", SyncStatus.PENDING).lte("scheduledFor", now),
			)
			.filter((q) => q.eq(q.field("jobType"), args.jobType))
			.collect();

		// 指定されたジョブタイプのFAILEDジョブを検索
		const failedJobs = await ctx.db
			.query("syncQueue")
			.withIndex("by_status_and_scheduledFor", (q) =>
				q.eq("status", SyncStatus.FAILED),
			)
			.filter((q) => q.eq(q.field("jobType"), args.jobType))
			.collect();

		// リトライ可能なFAILEDジョブをフィルタ
		const retryableJobs = failedJobs.filter(
			(job) =>
				job.attempts < job.maxAttempts &&
				job.nextRetryAt &&
				job.nextRetryAt <= now,
		);

		// 全ての候補ジョブをマージして優先度でソート
		const allJobs = [...pendingJobs, ...retryableJobs].sort((a, b) => {
			// 優先度が低い値ほど高優先
			if (a.priority !== b.priority) {
				return a.priority - b.priority;
			}
			// 同じ優先度なら、scheduledForが早い順
			return a.scheduledFor - b.scheduledFor;
		});

		if (allJobs.length === 0) {
			logger.debug("実行可能なジョブが見つかりません", {
				jobType: args.jobType,
			});
			return null;
		}

		const job = allJobs[0];
		logger.info("ジョブを取得しました", {
			jobId: job._id,
			jobType: job.jobType,
			userId: job.userId,
			attempts: job.attempts,
		});

		return { job };
	},
});

/**
 * ジョブのステータスを更新する
 *
 * ジョブの処理状態に応じてステータスと関連フィールドを更新します。
 * PROCESSINGの場合は試行回数をインクリメント、COMPLETEDの場合は結果を保存、
 * FAILEDの場合はエラー情報とリトライ時刻を記録します。
 *
 * @param jobId - 更新対象のジョブID
 * @param status - 新しいステータス
 * @param error - エラーメッセージ（FAILEDステータスの場合）
 * @param result - 処理結果（COMPLETEDステータスの場合）
 * @param nextRetryAt - 次回リトライ時刻（FAILEDステータスの場合）
 * @returns null
 * @throws ジョブが見つからない場合エラー
 * @example
 * // ジョブを処理中に更新
 * await ctx.runMutation(
 *   internal.beds24Queue.updateJobStatus,
 *   {
 *     jobId: "job123",
 *     status: SyncStatus.PROCESSING
 *   }
 * );
 */
export const updateJobStatus = internalMutation({
	args: {
		jobId: v.id("syncQueue"),
		status: v.union(
			v.literal(SyncStatus.PENDING),
			v.literal(SyncStatus.PROCESSING),
			v.literal(SyncStatus.COMPLETED),
			v.literal(SyncStatus.FAILED),
		),
		error: v.optional(v.string()),
		result: v.optional(v.any()),
		nextRetryAt: v.optional(v.number()),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		const logger = createLogger("updateJobStatus", ctx);
		logger.setArgs({ jobId: args.jobId, status: args.status });
		logger.debug("ジョブステータスの更新を開始");

		const job = await ctx.db.get(args.jobId);
		if (!job) {
			throw createConvexError(ERROR_CODES.NOT_FOUND, "ジョブが見つかりません", {
				jobId: args.jobId,
			});
		}

		const now = Date.now();
		const updateData: Partial<Doc<"syncQueue">> = {
			status: args.status,
			updatedAt: now,
		};

		// ステータスに応じた追加フィールドの設定
		switch (args.status) {
			case SyncStatus.PROCESSING:
				updateData.startedAt = now;
				updateData.attempts = job.attempts + 1;
				break;

			case SyncStatus.COMPLETED:
				updateData.completedAt = now;
				updateData.result = args.result;
				break;

			case SyncStatus.FAILED:
				updateData.lastError = args.error;
				if (args.nextRetryAt) {
					updateData.nextRetryAt = args.nextRetryAt;
				}
				break;
		}

		await ctx.db.patch(args.jobId, updateData);
		logger.info("ジョブステータスを更新しました", {
			jobId: args.jobId,
			status: args.status,
			attempts: updateData.attempts ?? job.attempts,
		});

		return null;
	},
});

/**
 * 失敗したジョブを再キューする
 *
 * FAILEDステータスのジョブをPENDINGに戻し、指定された遅延後に再実行可能にします。
 * 遅延時間が指定されない場合は、試行回数に応じて指数関数的に増加します（最大5分）。
 *
 * @param jobId - 再キューするジョブのID
 * @param delay - リトライまでの遅延時間（ミリ秒、省略時は自動計算）
 * @returns null
 * @throws ジョブが見つからない、FAILEDステータスでない、最大試行回数に達している場合エラー
 * @example
 * // 失敗したジョブを1分後に再実行可能にする
 * await ctx.runMutation(
 *   internal.beds24Queue.requeueFailedJob,
 *   {
 *     jobId: "job123",
 *     delay: 60000
 *   }
 * );
 */
export const requeueFailedJob = internalMutation({
	args: {
		jobId: v.id("syncQueue"),
		delay: v.optional(v.number()), // リトライまでの遅延（ミリ秒）
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		const logger = createLogger("requeueFailedJob", ctx);
		logger.setArgs({ jobId: args.jobId });
		logger.debug("失敗したジョブの再キューを開始");

		const job = await ctx.db.get(args.jobId);
		if (!job) {
			throw createConvexError(ERROR_CODES.NOT_FOUND, "ジョブが見つかりません", {
				jobId: args.jobId,
			});
		}

		if (job.status !== SyncStatus.FAILED) {
			throw createConvexError(
				ERROR_CODES.INVALID_REQUEST,
				"FAILEDステータスのジョブのみ再キュー可能です",
				{ jobId: args.jobId, currentStatus: job.status },
			);
		}

		if (job.attempts >= job.maxAttempts) {
			throw createConvexError(
				ERROR_CODES.INVALID_REQUEST,
				"最大試行回数に達しています",
				{
					jobId: args.jobId,
					attempts: job.attempts,
					maxAttempts: job.maxAttempts,
				},
			);
		}

		const now = Date.now();
		// デフォルトの遅延: 試行回数に応じて指数関数的に増加（1分、2分、4分...）
		const defaultDelay = Math.min(
			60000 * 2 ** (job.attempts - 1),
			300000, // 最大5分
		);
		const delay = args.delay ?? defaultDelay;
		const nextRetryAt = now + delay;

		await ctx.db.patch(args.jobId, {
			status: SyncStatus.PENDING,
			nextRetryAt,
			updatedAt: now,
		});

		logger.info("ジョブを再キューしました", {
			jobId: args.jobId,
			nextRetryAt,
			delayMs: delay,
			attempts: job.attempts,
		});

		return null;
	},
});

/**
 * 古いジョブを削除する
 *
 * 指定された日数より古いジョブをキューから削除します。
 * パフォーマンスのため、一度の実行で削除できるジョブ数は1000件に制限されています。
 *
 * @param olderThanDays - 削除対象の日数（デフォルト: 7日）
 * @param statuses - 削除対象のステータス（デフォルト: COMPLETEDとFAILED）
 * @returns 削除されたジョブ数
 * @example
 * // 30日以上前の完了済みジョブを削除
 * const result = await ctx.runMutation(
 *   internal.beds24Queue.cleanupOldJobs,
 *   {
 *     olderThanDays: 30,
 *     statuses: [SyncStatus.COMPLETED]
 *   }
 * );
 * console.log(`${result.deletedCount}件のジョブを削除しました`);
 */
export const cleanupOldJobs = internalMutation({
	args: {
		olderThanDays: v.optional(v.number()),
		statuses: v.optional(
			v.array(
				v.union(
					v.literal(SyncStatus.PENDING),
					v.literal(SyncStatus.PROCESSING),
					v.literal(SyncStatus.COMPLETED),
					v.literal(SyncStatus.FAILED),
				),
			),
		),
	},
	returns: v.object({
		deletedCount: v.number(),
	}),
	handler: async (ctx, args) => {
		const logger = createLogger("cleanupOldJobs", ctx);
		logger.setArgs({
			olderThanDays: args.olderThanDays,
			statuses: args.statuses,
		});
		logger.debug("古いジョブのクリーンアップを開始");

		const now = Date.now();
		const daysInMs = (args.olderThanDays ?? 7) * 24 * 60 * 60 * 1000;
		const cutoffTime = now - daysInMs;

		// クリーンアップ対象のステータス（デフォルト: COMPLETEDとFAILED）
		const targetStatuses = args.statuses ?? [
			SyncStatus.COMPLETED,
			SyncStatus.FAILED,
		];

		let deletedCount = 0;
		const MAX_DELETIONS_PER_RUN = 1000; // パフォーマンスのため一度の実行での最大削除数を制限

		// 各ステータスごとに古いジョブを検索して削除
		for (const status of targetStatuses) {
			const oldJobs = await ctx.db
				.query("syncQueue")
				.withIndex("by_status_and_scheduledFor", (q) => q.eq("status", status))
				.filter((q) => q.lt(q.field("updatedAt"), cutoffTime))
				.collect();

			for (const job of oldJobs) {
				if (deletedCount >= MAX_DELETIONS_PER_RUN) {
					// 削除数が上限に達したら処理を終了
					// TODO: Convexがバッチ削除APIを提供したら、それを使用するよう更新
					break;
				}
				await ctx.db.delete(job._id);
				deletedCount++;
			}

			if (deletedCount >= MAX_DELETIONS_PER_RUN) {
				break;
			}
		}

		logger.info("古いジョブをクリーンアップしました", {
			deletedCount,
			cutoffTime: new Date(cutoffTime).toISOString(),
			statuses: targetStatuses,
		});

		return { deletedCount };
	},
});

/**
 * 無効なジョブタイプのジョブを削除する
 *
 * 有効なジョブタイプに含まれないジョブを検出し、削除します。
 * ドライランモードでは実際の削除は行わず、削除対象のジョブ情報のみを返します。
 *
 * @param dryRun - trueの場合、実際の削除は行わない（デフォルト: false）
 * @returns 無効なジョブの情報と削除数
 * @example
 * // ドライランで無効なジョブを確認
 * const result = await ctx.runMutation(
 *   internal.beds24Queue.cleanupInvalidJobs,
 *   { dryRun: true }
 * );
 * console.log(`無効なジョブ: ${result.invalidJobs.length}件`);
 *
 * // 実際に削除
 * const deleteResult = await ctx.runMutation(
 *   internal.beds24Queue.cleanupInvalidJobs,
 *   { dryRun: false }
 * );
 * console.log(`削除数: ${deleteResult.deletedCount}件`);
 */
export const cleanupInvalidJobs = internalMutation({
	args: {
		dryRun: v.optional(v.boolean()), // trueの場合、実際の削除は行わない
	},
	returns: v.object({
		invalidJobs: v.array(
			v.object({
				jobId: v.id("syncQueue"),
				jobType: v.string(),
				userId: v.string(),
				status: v.string(),
				createdAt: v.string(),
				metadata: v.optional(v.any()),
			}),
		),
		deletedCount: v.number(),
	}),
	handler: async (ctx, args) => {
		const logger = createLogger("cleanupInvalidJobs", ctx);
		const dryRun = args.dryRun ?? false;
		logger.info("無効なジョブのクリーンアップを開始", { dryRun });

		// 有効なジョブタイプのリスト
		const validJobTypes = Object.values(QueueJobType);

		// ページネーションを使用してバッチ処理
		let cursor: string | null = null;
		let allJobs: Doc<"syncQueue">[] = [];
		const invalidJobs: Doc<"syncQueue">[] = [];
		const BATCH_SIZE = 1000;

		do {
			const batch = await ctx.db.query("syncQueue").paginate({
				numItems: BATCH_SIZE,
				cursor,
			});

			// 無効なジョブタイプを持つジョブを検出
			const invalidJobsInBatch = batch.page.filter(
				(job) => !validJobTypes.includes(job.jobType),
			);
			invalidJobs.push(...invalidJobsInBatch);
			allJobs = allJobs.concat(batch.page);

			cursor = batch.continueCursor;

			// 安全のため上限を設定
			if (allJobs.length >= 10000) {
				logger.warn(
					"ジョブ数が上限に達しました。残りのジョブは処理されません。",
					{
						processedCount: allJobs.length,
					},
				);
				break;
			}
		} while (cursor);

		const invalidJobsInfo = invalidJobs.map((job) => ({
			jobId: job._id,
			jobType: job.jobType,
			userId: job.userId,
			status: job.status,
			createdAt: new Date(job.createdAt).toISOString(),
			metadata: job.metadata,
		}));

		logger.info(`無効なジョブが${invalidJobs.length}件見つかりました`, {
			invalidJobsCount: invalidJobs.length,
			totalJobsCount: allJobs.length,
			validJobTypes,
		});

		// 詳細なログ出力
		for (const jobInfo of invalidJobsInfo) {
			logger.warn("無効なジョブを検出", jobInfo);
		}

		let deletedCount = 0;

		if (!dryRun && invalidJobs.length > 0) {
			// 無効なジョブを削除
			for (const job of invalidJobs) {
				await ctx.db.delete(job._id);
				deletedCount++;
				logger.info(`無効なジョブを削除しました: ${job._id}`, {
					jobType: job.jobType,
					userId: job.userId,
				});
			}
		}

		logger.info("無効なジョブのクリーンアップが完了しました", {
			dryRun,
			invalidJobsCount: invalidJobs.length,
			deletedCount,
		});

		return {
			invalidJobs: invalidJobsInfo,
			deletedCount,
		};
	},
});

/**
 * ジョブの詳細情報を取得する（診断用）
 *
 * 指定されたジョブの完全な情報と診断データを取得します。
 * ジョブタイプの妥当性やメタデータの詳細な情報を含みます。
 *
 * @param jobId - 取得対象のジョブID
 * @returns ジョブの詳細情報と診断データ、または存在しない場合はnull
 * @example
 * // ジョブの詳細情報を取得
 * const details = await ctx.runQuery(
 *   internal.beds24Queue.getJobDetails,
 *   { jobId: "job123" }
 * );
 * if (details) {
 *   console.log("ジョブタイプ:", details.job.jobType);
 *   console.log("妥当なジョブタイプ:", details.diagnostics.isValidJobType);
 * }
 */
export const getJobDetails = internalQuery({
	args: {
		jobId: v.id("syncQueue"),
	},
	returns: v.union(
		v.null(),
		v.object({
			job: v.object({
				_id: v.id("syncQueue"),
				_creationTime: v.number(),
				userId: v.string(),
				jobType: v.string(),
				priority: v.number(),
				status: v.string(),
				attempts: v.number(),
				maxAttempts: v.number(),
				scheduledFor: v.number(),
				startedAt: v.optional(v.number()),
				completedAt: v.optional(v.number()),
				lastError: v.optional(v.string()),
				nextRetryAt: v.optional(v.number()),
				metadata: v.optional(v.any()),
				result: v.optional(v.any()),
				createdAt: v.number(),
				updatedAt: v.number(),
			}),
			diagnostics: v.object({
				isValidJobType: v.boolean(),
				metadataType: v.string(),
				metadataKeys: v.array(v.string()),
				metadataStringified: v.string(),
			}),
		}),
	),
	handler: async (ctx, args) => {
		const logger = createLogger("getJobDetails", ctx);
		logger.info("[DEBUG] ジョブ詳細取得開始", { jobId: args.jobId });

		const job = await ctx.db.get(args.jobId);

		if (!job) {
			logger.warn("[DEBUG] ジョブが見つかりません", { jobId: args.jobId });
			return null;
		}

		const validJobTypes = Object.values(QueueJobType);
		const isValidJobType = validJobTypes.includes(job.jobType);

		const diagnostics = {
			isValidJobType,
			metadataType: typeof job.metadata,
			metadataKeys: job.metadata ? Object.keys(job.metadata) : [],
			metadataStringified: job.metadata ? JSON.stringify(job.metadata) : "null",
		};

		logger.info("[DEBUG] ジョブ詳細取得完了", {
			jobId: args.jobId,
			jobType: job.jobType,
			status: job.status,
			diagnostics,
		});

		return {
			job,
			diagnostics,
		};
	},
});

/**
 * ジョブのステータスのみ取得する（軽量版）
 *
 * 指定されたジョブの基本的なステータス情報のみを取得します。
 * 詳細情報が不要な場合に使用する軽量なクエリです。
 *
 * @param jobId - 取得対象のジョブID
 * @returns ジョブのステータス情報、または存在しない場合はnull
 * @example
 * // ジョブのステータスを確認
 * const status = await ctx.runQuery(
 *   internal.beds24Queue.getJobStatus,
 *   { jobId: "job123" }
 * );
 * if (status) {
 *   console.log("ステータス:", status.status);
 *   console.log("試行回数:", status.attempts);
 * }
 */
export const getJobStatus = internalQuery({
	args: {
		jobId: v.id("syncQueue"),
	},
	returns: v.union(
		v.null(),
		v.object({
			status: v.string(),
			jobType: v.string(),
			attempts: v.number(),
			lastError: v.optional(v.string()),
		}),
	),
	handler: async (ctx, args) => {
		const job = await ctx.db.get(args.jobId);

		if (!job) {
			return null;
		}

		return {
			status: job.status,
			jobType: job.jobType,
			attempts: job.attempts,
			lastError: job.lastError,
		};
	},
});

/**
 * 施設の予約同期ジョブを取得する
 *
 * 指定された施設の予約同期ジョブを優先度と日付順でソートして取得します。
 * ジョブの日付範囲、ページ番号、遡り処理情報などの詳細を含みます。
 *
 * @param userId - ユーザーID
 * @param propertyId - 施設ID
 * @returns 施設の予約同期ジョブの配列（優先度・日付順）
 * @example
 * // 施設の予約同期ジョブを取得
 * const jobs = await ctx.runQuery(
 *   internal.beds24Queue.getPropertySyncJobs,
 *   {
 *     userId: "user123",
 *     propertyId: 12345
 *   }
 * );
 * console.log(`${jobs.length}件の予約同期ジョブ`);
 */
export const getPropertySyncJobs = internalQuery({
	args: {
		userId: v.string(),
		propertyId: v.number(),
	},
	returns: v.array(
		v.object({
			_id: v.id("syncQueue"),
			status: v.string(),
			priority: v.number(),
			scheduledFor: v.number(),
			dateFrom: v.string(),
			dateTo: v.string(),
			pageNumber: v.number(),
			isRecursiveBacktrack: v.boolean(),
			recursionDepth: v.optional(v.number()),
			attempts: v.number(),
			lastError: v.optional(v.string()),
			createdAt: v.number(),
		}),
	),
	handler: async (ctx, args) => {
		const logger = createLogger("getPropertySyncJobs", ctx);
		logger.debug("施設の予約同期ジョブを検索", {
			userId: args.userId,
			propertyId: args.propertyId,
		});

		const jobs = await ctx.db
			.query("syncQueue")
			.filter((q) =>
				q.and(
					q.eq(q.field("userId"), args.userId),
					q.eq(q.field("jobType"), QueueJobType.SYNC_BOOKINGS),
				),
			)
			.collect();

		// 指定された施設のジョブのみフィルタ
		const propertyJobs = jobs
			.filter((job) => {
				const metadata = job.metadata as any;
				return metadata && metadata.propertyId === args.propertyId;
			})
			.map((job) => {
				const metadata = job.metadata as any;
				return {
					_id: job._id,
					status: job.status,
					priority: job.priority,
					scheduledFor: job.scheduledFor,
					dateFrom: metadata.dateFrom || "",
					dateTo: metadata.dateTo || "",
					pageNumber: metadata.pageNumber || 1,
					isRecursiveBacktrack: metadata.isRecursiveBacktrack || false,
					recursionDepth: metadata.recursionDepth,
					attempts: job.attempts,
					lastError: job.lastError,
					createdAt: job.createdAt,
				};
			})
			.sort((a, b) => {
				// 優先度でソート（低い値が高優先）
				if (a.priority !== b.priority) {
					return a.priority - b.priority;
				}
				// 日付でソート（新しい日付が先）
				if (a.dateFrom !== b.dateFrom) {
					return b.dateFrom.localeCompare(a.dateFrom);
				}
				// ページ番号でソート
				return a.pageNumber - b.pageNumber;
			});

		logger.info("施設の予約同期ジョブを取得", {
			propertyId: args.propertyId,
			jobCount: propertyJobs.length,
		});

		return propertyJobs;
	},
});

/**
 * 日付範囲の重複をチェックする
 *
 * 指定された施設の予約同期ジョブで、指定された日付範囲と重複する
 * PENDINGまたはPROCESSINGステータスのジョブがあるかをチェックします。
 *
 * @param userId - ユーザーID
 * @param propertyId - 施設ID
 * @param dateFrom - 開始日（YYYY-MM-DD形式）
 * @param dateTo - 終了日（YYYY-MM-DD形式）
 * @param excludeJobId - 除外するジョブID（更新時に自分自身を除外するため）
 * @returns 重複の有無と重複するジョブの情報
 * @example
 * // 日付範囲の重複をチェック
 * const result = await ctx.runQuery(
 *   internal.beds24Queue.hasOverlappingDateRange,
 *   {
 *     userId: "user123",
 *     propertyId: 12345,
 *     dateFrom: "2024-01-01",
 *     dateTo: "2024-01-31"
 *   }
 * );
 * if (result.hasOverlap) {
 *   console.log(`${result.overlappingJobs.length}件の重複ジョブがあります`);
 * }
 */
export const hasOverlappingDateRange = internalQuery({
	args: {
		userId: v.string(),
		propertyId: v.number(),
		dateFrom: v.string(),
		dateTo: v.string(),
		excludeJobId: v.optional(v.id("syncQueue")),
	},
	returns: v.object({
		hasOverlap: v.boolean(),
		overlappingJobs: v.array(
			v.object({
				_id: v.id("syncQueue"),
				dateFrom: v.string(),
				dateTo: v.string(),
				status: v.string(),
			}),
		),
	}),
	handler: async (ctx, args) => {
		const logger = createLogger("hasOverlappingDateRange", ctx);
		logger.debug("日付範囲の重複をチェック", {
			propertyId: args.propertyId,
			dateFrom: args.dateFrom,
			dateTo: args.dateTo,
		});

		const jobs = await ctx.db
			.query("syncQueue")
			.filter((q) =>
				q.and(
					q.eq(q.field("userId"), args.userId),
					q.eq(q.field("jobType"), QueueJobType.SYNC_BOOKINGS),
					q.or(
						q.eq(q.field("status"), SyncStatus.PENDING),
						q.eq(q.field("status"), SyncStatus.PROCESSING),
					),
				),
			)
			.collect();

		const overlappingJobs: Array<{
			_id: any;
			dateFrom: string;
			dateTo: string;
			status: string;
		}> = [];

		for (const job of jobs) {
			// 除外指定されたジョブはスキップ
			if (args.excludeJobId && job._id === args.excludeJobId) {
				continue;
			}

			const metadata = job.metadata as any;
			if (metadata && metadata.propertyId === args.propertyId) {
				const jobDateFrom = metadata.dateFrom;
				const jobDateTo = metadata.dateTo;

				// 日付範囲の重複をチェック
				if (jobDateFrom && jobDateTo) {
					const isOverlapping =
						(args.dateFrom >= jobDateFrom && args.dateFrom <= jobDateTo) ||
						(args.dateTo >= jobDateFrom && args.dateTo <= jobDateTo) ||
						(args.dateFrom <= jobDateFrom && args.dateTo >= jobDateTo);

					if (isOverlapping) {
						overlappingJobs.push({
							_id: job._id,
							dateFrom: jobDateFrom,
							dateTo: jobDateTo,
							status: job.status,
						});
					}
				}
			}
		}

		logger.info("日付範囲の重複チェック完了", {
			propertyId: args.propertyId,
			hasOverlap: overlappingJobs.length > 0,
			overlappingCount: overlappingJobs.length,
		});

		return {
			hasOverlap: overlappingJobs.length > 0,
			overlappingJobs,
		};
	},
});
