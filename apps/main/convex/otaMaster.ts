import { v } from "convex/values";
import { internalQuery, query } from "./_generated/server";
import { createLogger } from "./lib/logging";

// 共通のOTAオブジェクト型定義
const OtaObjectType = v.object({
	_id: v.id("otaMaster"),
	_creationTime: v.number(),
	fullName: v.string(),
	shortName: v.string(),
	createdAt: v.number(),
	updatedAt: v.number(),
});

/**
 * 全てのOTAを取得する
 *
 * OTA（Online Travel Agent）マスターデータから全てのOTA情報を取得します。
 * 結果は作成日時の降順でソートされます。
 *
 * @returns OTAオブジェクトの配列（作成日時の降順）
 * @returns._id - OTAマスターのID
 * @returns._creationTime - システム作成時刻
 * @returns.fullName - OTAの正式名称
 * @returns.shortName - OTAの短縮名（一意）
 * @returns.createdAt - 作成日時（Unix timestamp）
 * @returns.updatedAt - 更新日時（Unix timestamp）
 * @example
 * // クライアントサイドでの使用例
 * const otaList = useQuery(api.otaMaster.list);
 * otaList?.forEach(ota => {
 *   console.log(`${ota.shortName}: ${ota.fullName}`);
 * });
 */
export const list = query({
	args: {},
	returns: v.array(OtaObjectType),
	handler: async (ctx) => {
		const logger = createLogger("otaMaster.list", ctx);
		logger.info("OTAリストの取得を開始");

		const timer = logger.startTimer("データベースクエリ");
		const otaList = await ctx.db.query("otaMaster").order("desc").collect();
		timer();

		logger.info("OTAリストを取得しました", { count: otaList.length });
		return otaList;
	},
});

/**
 * IDでOTAを取得する
 *
 * 指定されたIDに一致するOTA情報を取得します。
 * 主にOTA詳細画面や関連データの参照時に使用されます。
 *
 * @param id - OTAマスターのID（Convex ID）
 * @returns OTA情報オブジェクト、見つからない場合はnull
 * @example
 * // クライアントサイドでの使用例
 * const otaId = 'j975...'; // Convex ID
 * const ota = useQuery(api.otaMaster.getById, { id: otaId });
 *
 * if (ota) {
 *   console.log(`OTA: ${ota.fullName} (${ota.shortName})`);
 * } else {
 *   console.log('OTAが見つかりません');
 * }
 */
export const getById = query({
	args: {
		id: v.id("otaMaster"),
	},
	returns: v.union(OtaObjectType, v.null()),
	handler: async (ctx, args) => {
		const logger = createLogger("otaMaster.getById", ctx);
		logger.setArgs(args);
		logger.info("OTAの取得を開始");

		const timer = logger.startTimer("データベースクエリ");
		const ota = await ctx.db.get(args.id);
		timer();

		if (!ota) {
			logger.debug("OTAが見つかりませんでした", { id: args.id });
			return null;
		}

		logger.info("OTAを取得しました", { shortName: ota.shortName });
		return ota;
	},
});

/**
 * 短縮名でOTAを取得する
 *
 * OTAの短縮名（例: 'booking_com', 'agoda'）を使用してOTA情報を取得します。
 * 内部関数のため、他のConvex関数からのみ呼び出し可能です。
 * 主にレビューデータの処理時にOTA情報を参照する際に使用されます。
 *
 * @param shortName - OTAの短縮名（一意の識別子）
 * @returns OTA情報オブジェクト、見つからない場合はnull
 * @example
 * // 内部関数からの使用例
 * const ota = await ctx.runQuery(
 *   internal.otaMaster.getByShortName,
 *   { shortName: 'booking_com' }
 * );
 *
 * if (ota) {
 *   console.log(`Found OTA: ${ota.fullName}`);
 * }
 */
export const getByShortName = internalQuery({
	args: {
		shortName: v.string(),
	},
	returns: v.union(OtaObjectType, v.null()),
	handler: async (ctx, args) => {
		const logger = createLogger("otaMaster.getByShortName", ctx);
		logger.setArgs(args);
		logger.info("短縮名でOTAの取得を開始");

		const timer = logger.startTimer("データベースクエリ");
		const ota = await ctx.db
			.query("otaMaster")
			.withIndex("by_shortName", (q) => q.eq("shortName", args.shortName))
			.unique();
		timer();

		if (!ota) {
			logger.debug("OTAが見つかりませんでした", { shortName: args.shortName });
			return null;
		}

		logger.info("OTAを取得しました", { fullName: ota.fullName });
		return ota;
	},
});
