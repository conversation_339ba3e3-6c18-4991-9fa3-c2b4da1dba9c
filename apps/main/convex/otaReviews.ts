/**
 * OTA Reviews Module
 *
 * このファイルは、分割されたotaReviews関連のモジュールから
 * すべての公開APIを再エクスポートするエントリーポイントです。
 *
 * モジュール構成:
 * - otaReviewsQueries.ts: 公開クエリ関数
 * - otaReviewsMutations.ts: 公開ミューテーション関数
 * - otaReviewsInternal.ts: 内部関数（このファイルからはエクスポートしない）
 */

// Mutations - すべての公開ミューテーション関数をエクスポート
export {
	batchUpsertReviews,
	deleteReviewsByProperty,
	upsertReview,
} from "./otaReviewsMutations";

// Queries - すべての公開クエリ関数をエクスポート
export {
	getMonthlyReviewStatsByProperties,
	getRecentReviews,
	getRecentReviewsByProperties,
	getReview,
	getReviewStats,
	getReviewsByProperty,
	getReviewsByUser,
} from "./otaReviewsQueries";
