import { v } from "convex/values";
import { internalQuery } from "./_generated/server";

export const checkReviewDuplicates = internalQuery({
	args: {
		limit: v.optional(v.number()),
	},
	returns: v.object({
		totalReviews: v.number(),
		duplicateGroups: v.array(
			v.object({
				key: v.string(),
				count: v.number(),
				reviews: v.array(
					v.object({
						id: v.id("otaReviews"),
						title: v.string(),
						contentPreview: v.string(),
						reviewerName: v.string(),
						reviewDate: v.string(),
						propertyId: v.id("beds24Properties"),
						otaId: v.id("otaMaster"),
						uniqueHash: v.string(),
					}),
				),
			}),
		),
		hashAnalysis: v.object({
			totalUniqueHashes: v.number(),
			duplicateHashes: v.array(
				v.object({
					hash: v.string(),
					count: v.number(),
				}),
			),
		}),
	}),
	handler: async (ctx, args) => {
		const limit = args.limit || 100;

		// レビューを取得
		const reviews = await ctx.db.query("otaReviews").order("desc").take(limit);

		// タイトルと内容で重複をチェック
		const reviewMap = new Map<string, any[]>();
		const duplicateGroups = [];

		for (const review of reviews) {
			const key = review.title
				? `${review.title}|${review.reviewContent}`
				: `NOTITLE_${review.uniqueHash}|${review.reviewContent}`;
			if (reviewMap.has(key)) {
				reviewMap.get(key)!.push(review);
			} else {
				reviewMap.set(key, [review]);
			}
		}

		// 重複グループを作成
		for (const [key, reviewGroup] of reviewMap) {
			if (reviewGroup.length > 1) {
				duplicateGroups.push({
					key,
					count: reviewGroup.length,
					reviews: reviewGroup.map((r) => ({
						id: r._id,
						title: r.title || "タイトルなし",
						contentPreview:
							r.reviewContent.substring(0, 100) +
							(r.reviewContent.length > 100 ? "..." : ""),
						reviewerName: r.reviewerName,
						reviewDate: new Date(r.reviewDate).toLocaleDateString("ja-JP"),
						propertyId: r.beds24PropertyId,
						otaId: r.otaId,
						uniqueHash: r.uniqueHash,
					})),
				});
			}
		}

		// uniqueHashの分析
		const hashMap = new Map<string, number>();
		for (const review of reviews) {
			hashMap.set(review.uniqueHash, (hashMap.get(review.uniqueHash) || 0) + 1);
		}

		const duplicateHashes = Array.from(hashMap.entries())
			.filter(([_, count]) => count > 1)
			.map(([hash, count]) => ({ hash, count }));

		return {
			totalReviews: reviews.length,
			duplicateGroups,
			hashAnalysis: {
				totalUniqueHashes: hashMap.size,
				duplicateHashes,
			},
		};
	},
});
