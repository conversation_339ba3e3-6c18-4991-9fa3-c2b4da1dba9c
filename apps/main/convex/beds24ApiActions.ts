"use node";

/**
 * Beds24 API Actions
 *
 * Node.js環境で実行されるBeds24 API関連のアクション
 * 外部API呼び出しを含むため、"use node"ディレクティブが必要です。
 */

import { v } from "convex/values";
import { internalAction } from "./_generated/server";
import { formatDateForBeds24 } from "./lib/dateUtils";
import { createConvexError, ERROR_CODES } from "./lib/errors";
import { createLogger } from "./lib/logging";
import type { Beds24APIProperty } from "./model/beds24Api";
import type { Beds24BookingApiResponse } from "./types/beds24Bookings";

// ========== 型定義 ==========

/**
 * APIエラーレスポンスの型
 */
interface Beds24APIError {
	error: string;
	message: string;
	code?: string;
}

/**
 * 予約一覧APIレスポンスの型
 */
interface BookingsApiResponse {
	data: Beds24BookingApiResponse[];
	count?: number;
	page?: number;
	pageCount?: number;
	pages?: number; // Beds24 APIから返される可能性のあるフィールド
	// 次の日付範囲のヒント（Beds24 APIからは直接提供されないが、クライアント側で計算可能）
	dateRangeHints?: {
		oldestBookingDate?: string; // 取得した予約の中で最も古い日付
		newestBookingDate?: string; // 取得した予約の中で最も新しい日付
		hasMoreInCurrentRange: boolean; // 現在の日付範囲内にさらにデータがあるか
		suggestedNextRange?: {
			// 次に取得すべき日付範囲の提案
			from: string;
			to: string;
		};
	};
}

/**
 * APIオプション
 */
interface ApiOptions {
	maxRetries?: number;
	retryDelay?: number;
	timeout?: number;
}

/**
 * ページネーションオプション
 */
interface PaginationOptions {
	page?: number;
	limit?: number;
}

/**
 * 予約フィルタオプション（Beds24 API用）
 */
interface Beds24BookingFilters {
	propId?: string;
	roomId?: string;
	arrival?: string; // YYYY-MM-DD
	arrivalFrom?: string; // YYYY-MM-DD
	arrivalTo?: string; // YYYY-MM-DD
	departure?: string; // YYYY-MM-DD
	departureFrom?: string; // YYYY-MM-DD
	departureTo?: string; // YYYY-MM-DD
	modifiedSince?: string; // YYYY-MM-DD HH:MM:SS
	bookingTime?: string; // YYYY-MM-DD
	status?: string; // 0=cancelled, 1=confirmed, 2=new/request, 3=black
	// 日付範囲のエイリアス（より直感的なパラメータ名）
	dateFrom?: string; // YYYY-MM-DD (maps to arrivalFrom)
	dateTo?: string; // YYYY-MM-DD (maps to departureTo)
}

// ========== ヘルパー関数 ==========

/**
 * 日付文字列のバリデーション (YYYY-MM-DD形式)
 */
function isValidDateFormat(dateStr: string | undefined): boolean {
	if (!dateStr) return true; // undefinedは許可
	const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
	if (!dateRegex.test(dateStr)) return false;

	// 実際の日付として有効かチェック
	const date = new Date(dateStr);
	return !Number.isNaN(date.getTime());
}

/**
 * 日付範囲の検証と正規化
 */
function normalizeDateFilters(
	filters: Beds24BookingFilters,
): Beds24BookingFilters {
	const normalized = { ...filters };

	// dateFrom/dateToがある場合、arrivalFrom/departureToに変換
	if (normalized.dateFrom) {
		if (!isValidDateFormat(normalized.dateFrom)) {
			throw new Error(`無効な日付形式: dateFrom=${normalized.dateFrom}`);
		}
		normalized.arrivalFrom = normalized.dateFrom;
		delete normalized.dateFrom;
	}

	if (normalized.dateTo) {
		if (!isValidDateFormat(normalized.dateTo)) {
			throw new Error(`無効な日付形式: dateTo=${normalized.dateTo}`);
		}
		normalized.departureTo = normalized.dateTo;
		delete normalized.dateTo;
	}

	// 日付範囲が指定されていない場合、デフォルトで現在日時以降を設定
	if (
		!normalized.arrivalFrom &&
		!normalized.arrival &&
		!normalized.bookingTime &&
		!normalized.modifiedSince
	) {
		// デフォルト動作：現在日時以降の予約を取得
		normalized.arrivalFrom = formatDateForBeds24(new Date());
	}

	// arrivalFromとdepartureToの関係性チェック
	if (normalized.arrivalFrom && normalized.departureTo) {
		const fromDate = new Date(normalized.arrivalFrom);
		const toDate = new Date(normalized.departureTo);
		if (fromDate > toDate) {
			throw new Error(
				`日付範囲が無効です: arrivalFrom(${normalized.arrivalFrom}) > departureTo(${normalized.departureTo})`,
			);
		}
	}

	// その他の日付フィールドのバリデーション
	const dateFields = [
		"arrival",
		"arrivalFrom",
		"arrivalTo",
		"departure",
		"departureFrom",
		"departureTo",
		"bookingTime",
	] as const;
	for (const field of dateFields) {
		if (normalized[field] && !isValidDateFormat(normalized[field])) {
			throw new Error(`無効な日付形式: ${field}=${normalized[field]}`);
		}
	}

	return normalized;
}

/**
 * APIエラーレスポンスの解析
 */
async function parseAPIError(response: Response): Promise<{
	message: string;
	code?: string;
	details?: any;
}> {
	try {
		const errorData = (await response.json()) as Beds24APIError;
		return {
			message:
				errorData.message || errorData.error || `APIエラー: ${response.status}`,
			code: errorData.code,
			details: errorData,
		};
	} catch {
		const errorText = await response.text();
		return {
			message: errorText || `APIエラー: ${response.status}`,
		};
	}
}

/**
 * レート制限エラーかどうかを判定
 */
function isRateLimitError(status: number): boolean {
	return status === 429;
}

/**
 * リトライ可能なエラーかどうかを判定
 */
function isRetryableError(status: number): boolean {
	return status >= 500 || status === 429 || status === 408;
}

/**
 * 指数バックオフの遅延時間を計算
 */
function calculateBackoffDelay(attempt: number, baseDelay: number): number {
	return Math.min(baseDelay * 2 ** attempt, 30000); // 最大30秒
}

/**
 * 予約データから日付範囲のヒントを計算
 */
function calculateDateRangeHints(
	bookings: Beds24BookingApiResponse[],
	currentFilters: Beds24BookingFilters,
	response: Omit<BookingsApiResponse, "data" | "dateRangeHints">,
): BookingsApiResponse["dateRangeHints"] {
	if (bookings.length === 0) {
		return {
			hasMoreInCurrentRange: false,
		};
	}

	// 予約の日付を抽出（arrival日付を使用）
	const bookingDates = bookings
		.map((booking) => {
			// arrivalを使用
			const dateStr = booking.arrival;
			return dateStr ? new Date(dateStr) : null;
		})
		.filter(
			(date): date is Date => date !== null && !Number.isNaN(date.getTime()),
		);

	if (bookingDates.length === 0) {
		return {
			hasMoreInCurrentRange: false,
		};
	}

	// 最古と最新の日付を取得
	const sortedDates = bookingDates.sort((a, b) => a.getTime() - b.getTime());
	const oldestDate = sortedDates[0];
	const newestDate = sortedDates[sortedDates.length - 1];

	// 現在の日付範囲内にさらにデータがあるか判定
	const hasMoreInCurrentRange = response.page
		? response.page < (response.pageCount || 1)
		: false;

	// 次の日付範囲の提案を計算（再帰的遡り用）
	let suggestedNextRange: { from: string; to: string } | undefined;

	// 現在の範囲で全てのデータを取得し終えた場合、2ヶ月前の期間を提案
	if (!hasMoreInCurrentRange && currentFilters.arrivalFrom) {
		const currentFrom = currentFilters.arrivalFrom;
		const currentTo =
			currentFilters.arrivalTo || formatDateForBeds24(new Date());

		// dateUtilsのcalculatePreviousDateRangeと同様の計算
		const fromDate = new Date(currentFrom);
		const toDate = new Date(currentTo);

		// 2ヶ月前の期間を計算
		const previousFrom = new Date(fromDate);
		previousFrom.setMonth(previousFrom.getMonth() - 2);

		const previousTo = new Date(toDate);
		previousTo.setMonth(previousTo.getMonth() - 2);

		suggestedNextRange = {
			from: formatDateForBeds24(previousFrom),
			to: formatDateForBeds24(previousTo),
		};
	}

	return {
		oldestBookingDate: formatDateForBeds24(oldestDate),
		newestBookingDate: formatDateForBeds24(newestDate),
		hasMoreInCurrentRange,
		suggestedNextRange,
	};
}

// ========== Beds24ApiWrapper クラス ==========

export class Beds24ApiWrapper {
	private readonly baseUrl = "https://api.beds24.com/v2";
	private readonly accessToken: string;
	private readonly logger?: ReturnType<typeof createLogger>;
	private readonly options: Required<ApiOptions>;

	constructor(
		accessToken: string,
		logger?: ReturnType<typeof createLogger>,
		options?: ApiOptions,
	) {
		this.accessToken = accessToken;
		this.logger = logger;
		this.options = {
			maxRetries: options?.maxRetries ?? 3,
			retryDelay: options?.retryDelay ?? 1000,
			timeout: options?.timeout ?? 30000,
		};
	}

	/**
	 * リトライ機能付きfetch
	 */
	private async fetchWithRetry(
		url: string,
		options: RequestInit,
	): Promise<Response> {
		let lastError: Error | null = null;

		for (let attempt = 0; attempt < this.options.maxRetries; attempt++) {
			try {
				if (attempt > 0) {
					const delay = calculateBackoffDelay(
						attempt - 1,
						this.options.retryDelay,
					);
					this.logger?.debug(
						`リトライ ${attempt + 1}/${this.options.maxRetries}（${delay}ms待機）`,
					);
					await new Promise((resolve) => setTimeout(resolve, delay));
				}

				// タイムアウト設定
				const controller = new AbortController();
				const timeoutId = setTimeout(
					() => controller.abort(),
					this.options.timeout,
				);

				try {
					const response = await fetch(url, {
						...options,
						signal: controller.signal,
					});

					clearTimeout(timeoutId);

					// レート制限エラーの場合は特別な処理
					if (isRateLimitError(response.status)) {
						// Beds24 API固有のヘッダーを優先的に確認
						const retryAfter = response.headers.get("Retry-After");
						const resetsIn = response.headers.get("x-five-min-limit-resets-in");

						// ヘッダーから待機時間を計算（ミリ秒）
						let delay: number;
						if (retryAfter) {
							delay = parseInt(retryAfter) * 1000;
						} else if (resetsIn) {
							delay = parseInt(resetsIn) * 1000;
						} else {
							// デフォルト: 5秒
							delay = 5000;
						}

						// ランダムジッター（0-3秒）を追加してサーバー負荷を分散
						const jitter = Math.floor(Math.random() * 3000);
						const totalDelay = delay + jitter;

						this.logger?.warn(
							`レート制限に達しました。${totalDelay}ms後にリトライします (base: ${delay}ms, jitter: ${jitter}ms)`,
						);

						if (attempt < this.options.maxRetries - 1) {
							await new Promise((resolve) => setTimeout(resolve, totalDelay));
							continue;
						}
					}

					// リトライ可能なエラーでなければ、またはリトライ回数を超えた場合は返す
					if (
						!isRetryableError(response.status) ||
						attempt === this.options.maxRetries - 1
					) {
						return response;
					}
				} catch (error) {
					clearTimeout(timeoutId);

					if (error instanceof Error && error.name === "AbortError") {
						throw new Error(
							`リクエストタイムアウト（${this.options.timeout}ms）`,
						);
					}
					throw error;
				}
			} catch (error) {
				lastError = error as Error;
				this.logger?.warn(
					`API呼び出し失敗（試行 ${attempt + 1}/${this.options.maxRetries}）`,
					{
						error: lastError.message,
					},
				);

				if (attempt === this.options.maxRetries - 1) {
					throw lastError;
				}
			}
		}

		throw lastError || new Error("予期しないエラーが発生しました");
	}

	/**
	 * APIリクエストの実行
	 */
	private async request<T>(
		endpoint: string,
		options?: RequestInit & { params?: Record<string, any> },
	): Promise<T> {
		const url = new URL(`${this.baseUrl}${endpoint}`);

		// クエリパラメータの追加
		if (options?.params) {
			Object.entries(options.params).forEach(([key, value]) => {
				if (value !== undefined && value !== null) {
					url.searchParams.append(key, String(value));
				}
			});
		}

		const timer = this.logger?.startTimer(`Beds24 API: ${endpoint}`);

		try {
			const response = await this.fetchWithRetry(url.toString(), {
				...options,
				headers: {
					accept: "application/json",
					token: this.accessToken,
					...options?.headers,
				},
			});

			if (!response.ok) {
				const errorInfo = await parseAPIError(response);
				this.logger?.error("Beds24 APIエラー", {
					endpoint,
					status: response.status,
					...errorInfo,
				});

				// 認証エラー
				if (response.status === 401) {
					throw createConvexError(
						ERROR_CODES.INVALID_TOKEN,
						"アクセストークンが無効です",
						{ status: response.status, ...errorInfo },
					);
				}

				// その他のAPIエラー
				throw createConvexError(ERROR_CODES.API_ERROR, errorInfo.message, {
					status: response.status,
					...errorInfo,
				});
			}

			const data = await response.json();
			timer?.();

			return data as T;
		} catch (error) {
			timer?.();

			// ConvexErrorの場合はそのまま再スロー
			if (error instanceof Error && error.name === "ConvexError") {
				throw error;
			}

			// ネットワークエラー
			this.logger?.error("API呼び出し中にエラーが発生しました", {
				endpoint,
				error: error instanceof Error ? error.message : String(error),
			});

			throw createConvexError(
				ERROR_CODES.NETWORK_ERROR,
				"ネットワークエラーが発生しました",
				{
					originalError: error instanceof Error ? error.message : String(error),
				},
			);
		}
	}

	/**
	 * 施設一覧の取得（互換性のため既存の関数から移植）
	 */
	async fetchProperties(): Promise<Beds24APIProperty[]> {
		const response = await this.request<{ data: Beds24APIProperty[] }>(
			"/properties",
		);

		if (!Array.isArray(response.data)) {
			throw createConvexError(
				ERROR_CODES.API_ERROR,
				"施設データが配列ではありません",
				{ dataType: typeof response.data },
			);
		}

		this.logger?.info(`施設データを取得しました`, {
			count: response.data.length,
		});

		return response.data;
	}

	/**
	 * 予約一覧の取得（ページネーション対応）
	 * 日付範囲の検証とデフォルト動作を含む
	 */
	async fetchBookings(
		filters?: Beds24BookingFilters,
		pagination?: PaginationOptions,
	): Promise<BookingsApiResponse> {
		// 日付フィルタの検証と正規化
		let normalizedFilters: Beds24BookingFilters = {};
		if (filters) {
			try {
				normalizedFilters = normalizeDateFilters(filters);
			} catch (error) {
				throw createConvexError(
					ERROR_CODES.VALIDATION_ERROR,
					error instanceof Error
						? error.message
						: "日付パラメータの検証に失敗しました",
					{ originalFilters: filters },
				);
			}
		} else {
			// フィルタが指定されていない場合、現在日時以降をデフォルトとする
			normalizedFilters = {
				arrivalFrom: formatDateForBeds24(new Date()),
			};
		}

		const params: Record<string, any> = {
			...normalizedFilters,
			page: pagination?.page ?? 1,
			limit: pagination?.limit ?? 100,
		};

		this.logger?.debug("予約一覧を取得します", {
			originalFilters: filters,
			normalizedFilters,
			pagination,
		});

		const response = await this.request<BookingsApiResponse>("/bookings", {
			method: "GET",
			params,
		});

		// レスポンスの検証
		if (!response.data || !Array.isArray(response.data)) {
			throw createConvexError(
				ERROR_CODES.API_ERROR,
				"予約データが正しい形式ではありません",
				{ responseType: typeof response },
			);
		}

		// 日付範囲のヒントを計算
		const dateRangeHints = calculateDateRangeHints(
			response.data,
			normalizedFilters,
			{
				count: response.count,
				page: response.page,
				pageCount: response.pageCount,
			},
		);

		this.logger?.info("予約データを取得しました", {
			count: response.data.length,
			page: response.page,
			pageCount: response.pageCount,
			totalCount: response.count,
			dateRange: {
				from: normalizedFilters.arrivalFrom || normalizedFilters.arrival,
				to: normalizedFilters.departureTo || normalizedFilters.departure,
			},
			dateRangeHints,
		});

		return {
			...response,
			dateRangeHints,
		};
	}

	/**
	 * 予約詳細の取得
	 */
	async fetchBookingDetails(
		bookingId: string,
	): Promise<Beds24BookingApiResponse> {
		this.logger?.debug("予約詳細を取得します", { bookingId });

		const response = await this.request<{ data: Beds24BookingApiResponse }>(
			`/bookings/${bookingId}`,
			{ method: "GET" },
		);

		if (!response.data) {
			throw createConvexError(
				ERROR_CODES.API_ERROR,
				"予約詳細が取得できませんでした",
				{ bookingId },
			);
		}

		this.logger?.info("予約詳細を取得しました", { bookingId });

		return response.data;
	}

	/**
	 * 全ページの予約を取得（自動ページネーション）
	 * 日付範囲の検証を含む
	 */
	async fetchAllBookings(
		filters?: Beds24BookingFilters,
		onProgress?: (current: number, total: number) => void,
	): Promise<Beds24BookingApiResponse[]> {
		const allBookings: Beds24BookingApiResponse[] = [];
		let currentPage = 1;
		let hasMore = true;

		// 日付フィルタの検証と正規化（fetchBookingsで行われるが、ログ用に事前チェック）
		let normalizedFilters: Beds24BookingFilters | undefined;
		if (filters) {
			try {
				normalizedFilters = normalizeDateFilters(filters);
			} catch (error) {
				throw createConvexError(
					ERROR_CODES.VALIDATION_ERROR,
					error instanceof Error
						? error.message
						: "日付パラメータの検証に失敗しました",
					{ originalFilters: filters },
				);
			}
		}

		this.logger?.info("全予約データの取得を開始します", {
			originalFilters: filters,
			normalizedFilters,
		});

		while (hasMore) {
			const response = await this.fetchBookings(filters, {
				page: currentPage,
				limit: 100,
			});

			allBookings.push(...response.data);

			// 進捗コールバック
			if (onProgress && response.pageCount) {
				onProgress(currentPage, response.pageCount);
			}

			// 次のページがあるか判定
			hasMore = response.pageCount ? currentPage < response.pageCount : false;
			currentPage++;

			// レート制限対策のための待機
			if (hasMore) {
				await new Promise((resolve) => setTimeout(resolve, 1000));
			}
		}

		this.logger?.info("全予約データの取得が完了しました", {
			totalCount: allBookings.length,
			pagesFetched: currentPage - 1,
		});

		return allBookings;
	}

	/**
	 * 更新された予約のみを取得（差分同期用）
	 */
	async fetchModifiedBookings(
		modifiedSince: Date,
		filters?: Omit<Beds24BookingFilters, "modifiedSince">,
	): Promise<Beds24BookingApiResponse[]> {
		const modifiedSinceStr = modifiedSince
			.toISOString()
			.replace("T", " ")
			.slice(0, 19);

		this.logger?.info("更新された予約を取得します", {
			modifiedSince: modifiedSinceStr,
		});

		return this.fetchAllBookings({
			...filters,
			modifiedSince: modifiedSinceStr,
		});
	}
}

// ========== Convex Actions ==========

/**
 * 施設一覧を取得
 */
export const fetchProperties = internalAction({
	args: {
		accessToken: v.string(),
	},
	returns: v.array(v.any()), // Beds24APIPropertyの型定義が複雑なため、anyを使用
	handler: async (_ctx, args) => {
		const logger = createLogger("fetchProperties");
		const apiWrapper = new Beds24ApiWrapper(args.accessToken, logger);
		return await apiWrapper.fetchProperties();
	},
});

/**
 * 予約一覧を取得（ページネーション対応）
 */
export const fetchBookings = internalAction({
	args: {
		accessToken: v.string(),
		filters: v.optional(
			v.object({
				propId: v.optional(v.string()),
				roomId: v.optional(v.string()),
				arrival: v.optional(v.string()),
				arrivalFrom: v.optional(v.string()),
				arrivalTo: v.optional(v.string()),
				departure: v.optional(v.string()),
				departureFrom: v.optional(v.string()),
				departureTo: v.optional(v.string()),
				modifiedSince: v.optional(v.string()),
				bookingTime: v.optional(v.string()),
				status: v.optional(v.string()),
				dateFrom: v.optional(v.string()),
				dateTo: v.optional(v.string()),
			}),
		),
		pagination: v.optional(
			v.object({
				page: v.optional(v.number()),
				limit: v.optional(v.number()),
			}),
		),
	},
	returns: v.object({
		data: v.array(v.any()),
		count: v.optional(v.number()),
		page: v.optional(v.number()),
		pageCount: v.optional(v.number()),
		pages: v.optional(v.number()), // Beds24 APIから返される可能性のあるフィールド
		dateRangeHints: v.optional(
			v.object({
				oldestBookingDate: v.optional(v.string()),
				newestBookingDate: v.optional(v.string()),
				hasMoreInCurrentRange: v.boolean(),
				suggestedNextRange: v.optional(
					v.object({
						from: v.string(),
						to: v.string(),
					}),
				),
			}),
		),
	}),
	handler: async (_ctx, args) => {
		const logger = createLogger("fetchBookings");
		const apiWrapper = new Beds24ApiWrapper(args.accessToken, logger);
		return await apiWrapper.fetchBookings(args.filters, args.pagination);
	},
});

/**
 * 予約詳細を取得
 */
export const fetchBookingDetails = internalAction({
	args: {
		accessToken: v.string(),
		bookingId: v.string(),
	},
	returns: v.any(),
	handler: async (_ctx, args) => {
		const logger = createLogger("fetchBookingDetails");
		const apiWrapper = new Beds24ApiWrapper(args.accessToken, logger);
		return await apiWrapper.fetchBookingDetails(args.bookingId);
	},
});

/**
 * 全ページの予約を取得
 */
export const fetchAllBookings = internalAction({
	args: {
		accessToken: v.string(),
		filters: v.optional(
			v.object({
				propId: v.optional(v.string()),
				roomId: v.optional(v.string()),
				arrival: v.optional(v.string()),
				arrivalFrom: v.optional(v.string()),
				arrivalTo: v.optional(v.string()),
				departure: v.optional(v.string()),
				departureFrom: v.optional(v.string()),
				departureTo: v.optional(v.string()),
				modifiedSince: v.optional(v.string()),
				bookingTime: v.optional(v.string()),
				status: v.optional(v.string()),
				dateFrom: v.optional(v.string()),
				dateTo: v.optional(v.string()),
			}),
		),
	},
	returns: v.array(v.any()),
	handler: async (_ctx, args) => {
		const logger = createLogger("fetchAllBookings");
		const apiWrapper = new Beds24ApiWrapper(args.accessToken, logger);
		return await apiWrapper.fetchAllBookings(args.filters);
	},
});

/**
 * 更新された予約を取得
 */
export const fetchModifiedBookings = internalAction({
	args: {
		accessToken: v.string(),
		modifiedSince: v.number(), // Unix timestamp
		filters: v.optional(
			v.object({
				propId: v.optional(v.string()),
				roomId: v.optional(v.string()),
				arrival: v.optional(v.string()),
				arrivalFrom: v.optional(v.string()),
				arrivalTo: v.optional(v.string()),
				departure: v.optional(v.string()),
				departureFrom: v.optional(v.string()),
				departureTo: v.optional(v.string()),
				bookingTime: v.optional(v.string()),
				status: v.optional(v.string()),
				dateFrom: v.optional(v.string()),
				dateTo: v.optional(v.string()),
			}),
		),
	},
	returns: v.array(v.any()),
	handler: async (_ctx, args) => {
		const logger = createLogger("fetchModifiedBookings");
		const apiWrapper = new Beds24ApiWrapper(args.accessToken, logger);
		return await apiWrapper.fetchModifiedBookings(
			new Date(args.modifiedSince),
			args.filters,
		);
	},
});
