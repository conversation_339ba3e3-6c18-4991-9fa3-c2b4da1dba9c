import { v } from "convex/values";
import { internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import {
	internalAction,
	internalMutation,
	internalQuery,
} from "./_generated/server";
import { createConvexError, ERROR_CODES } from "./lib/errors";
import { createLogger } from "./lib/logging";

// 型エイリアスの定義
type UserSettingsFromDB = {
	_id: Id<"userSettings">;
	_creationTime: number;
	userId: string;
	theme: string;
	beds24?: {
		refreshToken?: string;
	};
	createdAt: number;
	updatedAt: number;
} | null;

/**
 * アクセストークンをデータベースに保存または更新する
 *
 * Beds24 APIから取得したアクセストークンを保存します。
 * 既存のトークンがある場合は更新し、ない場合は新規作成します。
 * 内部関数のため、refreshAccessTokenからのみ呼び出されます。
 *
 * @param userId - ユーザーID（Clerk ID）
 * @param accessToken - Beds24 APIアクセストークン
 * @param expiresAt - トークンの有効期限（Unix timestamp）
 * @returns null
 * @example
 * // 内部アクションからの使用例
 * await ctx.runMutation(internal.beds24Tokens.saveAccessToken, {
 *   userId: 'user_123',
 *   accessToken: 'token_abc',
 *   expiresAt: Date.now() + 3600000 // 1時間後
 * });
 */
export const saveAccessToken = internalMutation({
	args: {
		userId: v.string(),
		accessToken: v.string(),
		expiresAt: v.number(),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		const logger = createLogger("saveAccessToken", ctx);
		logger.setArgs({ userId: args.userId, expiresAt: args.expiresAt });
		logger.debug("アクセストークンの保存を開始");
		// 既存のトークンを検索
		const existing = await ctx.db
			.query("beds24AccessTokens")
			.withIndex("by_userId", (q) => q.eq("userId", args.userId))
			.unique();

		const now = Date.now();

		if (existing) {
			// 既存のトークンを更新
			await ctx.db.patch(existing._id, {
				accessToken: args.accessToken,
				expiresAt: args.expiresAt,
				updatedAt: now,
				lastRefreshedAt: now,
			});
			logger.info("アクセストークンを更新しました", {
				tokenId: existing._id,
				expiresAt: args.expiresAt,
			});
		} else {
			// 新規作成
			const tokenId = await ctx.db.insert("beds24AccessTokens", {
				userId: args.userId,
				accessToken: args.accessToken,
				expiresAt: args.expiresAt,
				createdAt: now,
				updatedAt: now,
				lastRefreshedAt: now,
			});
			logger.info("アクセストークンを保存しました", {
				tokenId,
				expiresAt: args.expiresAt,
			});
		}
		return null;
	},
});

/**
 * 保存されたアクセストークンを取得する
 *
 * 指定したユーザーのBeds24 APIアクセストークンをデータベースから取得します。
 * トークンの有効期限も含めて返すため、呼び出し側で有効性を確認できます。
 * 内部関数のため、他のConvex関数からのみ呼び出し可能です。
 *
 * @param userId - ユーザーID（Clerk ID）
 * @returns アクセストークン情報、存在しない場合はnull
 * @returns.userId - ユーザーID
 * @returns.accessToken - Beds24 APIアクセストークン
 * @returns.expiresAt - トークンの有効期限（Unix timestamp）
 * @returns.createdAt - 作成日時（Unix timestamp）
 * @returns.updatedAt - 更新日時（Unix timestamp）
 * @returns.lastRefreshedAt - 最終更新日時（Unix timestamp）
 * @example
 * // 内部関数からの使用例
 * const tokenInfo = await ctx.runQuery(
 *   internal.beds24Tokens.getAccessToken,
 *   { userId: 'user_123' }
 * );
 *
 * if (tokenInfo && tokenInfo.expiresAt > Date.now()) {
 *   // トークンは有効
 *   console.log('Token is valid');
 * }
 */
export const getAccessToken = internalQuery({
	args: { userId: v.string() },
	returns: v.union(
		v.object({
			userId: v.string(),
			accessToken: v.string(),
			expiresAt: v.number(),
			createdAt: v.number(),
			updatedAt: v.number(),
			lastRefreshedAt: v.number(),
		}),
		v.null(),
	),
	handler: async (ctx, args) => {
		const logger = createLogger("getAccessToken", ctx);
		logger.setArgs(args);
		logger.debug("アクセストークンの取得を開始");
		const token = await ctx.db
			.query("beds24AccessTokens")
			.withIndex("by_userId", (q) => q.eq("userId", args.userId))
			.unique();

		if (!token) {
			logger.debug("トークンが見つかりませんでした");
			return null;
		}

		const isExpired = token.expiresAt < Date.now();
		logger.debug("トークンが見つかりました", {
			isExpired,
			expiresAt: token.expiresAt,
			lastRefreshedAt: token.lastRefreshedAt,
		});

		return {
			userId: token.userId,
			accessToken: token.accessToken,
			expiresAt: token.expiresAt,
			createdAt: token.createdAt,
			updatedAt: token.updatedAt,
			lastRefreshedAt: token.lastRefreshedAt,
		};
	},
});

/**
 * リトライロジック付きのfetch関数
 *
 * ネットワークエラーや一時的な障害に対応するため、
 * 指数バックオフによるリトライ機能を持つfetch関数です。
 *
 * @param url - リクエストURL
 * @param options - fetch関数のオプション
 * @param maxRetries - 最大リトライ回数（デフォルト: 3）
 * @param logger - ロガーインスタンス（オプション）
 * @returns Responseオブジェクト
 * @throws 最大リトライ回数を超えた場合、最後のエラーをスロー
 * @example
 * // 3回までリトライ（デフォルト）
 * const response = await fetchWithRetry(
 *   'https://api.example.com/data',
 *   { method: 'GET', headers: { 'Authorization': 'Bearer token' } }
 * );
 *
 * // カスタムリトライ回数とロガー付き
 * const response = await fetchWithRetry(
 *   'https://api.example.com/data',
 *   { method: 'POST', body: JSON.stringify(data) },
 *   5,
 *   logger
 * );
 */
async function fetchWithRetry(
	url: string,
	options: RequestInit,
	maxRetries = 3,
	logger?: ReturnType<typeof createLogger>,
): Promise<Response> {
	let lastError: Error;

	for (let i = 0; i < maxRetries; i++) {
		try {
			if (logger && i > 0) {
				logger.debug(`リトライ ${i + 1}/${maxRetries}`);
			}
			return await fetch(url, options);
		} catch (error) {
			lastError = error as Error;
			if (logger) {
				logger.warn(`API呼び出し失敗 (試行 ${i + 1}/${maxRetries})`, {
					error: lastError.message,
				});
			}
			if (i < maxRetries - 1) {
				// 指数バックオフ
				const delay = 2 ** i * 1000;
				if (logger) {
					logger.debug(`${delay}ms待機してからリトライします`);
				}
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}
	}

	throw lastError!;
}

/**
 * Beds24 APIからアクセストークンを更新する
 *
 * ユーザー設定に保存されたリフレッシュトークンを使用して、
 * Beds24 APIから新しいアクセストークンを取得し、データベースに保存します。
 * 内部アクションのため、Convex関数からのみ呼び出し可能です。
 *
 * @param userId - ユーザーID（Clerk ID）
 * @returns null
 * @throws ユーザー設定が見つからない場合（NOT_FOUND）
 * @throws リフレッシュトークンが未設定の場合（VALIDATION_ERROR）
 * @throws リフレッシュトークンが無効な場合（INVALID_TOKEN）
 * @throws その他のAPIエラー（API_ERROR）
 * @example
 * // Cron jobやバックグラウンドタスクから呼び出し
 * await ctx.runAction(
 *   internal.beds24Tokens.refreshAccessToken,
 *   { userId: 'user_123' }
 * );
 *
 * // エラーハンドリング例
 * try {
 *   await ctx.runAction(internal.beds24Tokens.refreshAccessToken, { userId });
 * } catch (error) {
 *   if (error.code === 'INVALID_TOKEN') {
 *     // リフレッシュトークンが無効 - ユーザーに再設定を促す
 *   }
 * }
 */
export const refreshAccessToken = internalAction({
	args: { userId: v.string() },
	returns: v.null(),
	handler: async (ctx, args) => {
		const logger = createLogger("refreshAccessToken");
		logger.setArgs(args);
		logger.info("Beds24アクセストークンの更新を開始");
		// userSettingsからリフレッシュトークンを取得
		const userSettings: UserSettingsFromDB = await ctx.runQuery(
			internal.userSettings.getByUserId,
			{
				userId: args.userId,
			},
		);

		if (!userSettings) {
			logger.error("ユーザー設定が見つかりません");
			throw createConvexError(
				ERROR_CODES.NOT_FOUND,
				"ユーザー設定が見つかりません",
				undefined,
				{ functionName: "refreshAccessToken", userId: args.userId },
			);
		}

		if (!userSettings.beds24?.refreshToken) {
			logger.error("Beds24リフレッシュトークンが未設定");
			throw createConvexError(
				ERROR_CODES.VALIDATION_ERROR,
				"Beds24のリフレッシュトークンが設定されていません",
				undefined,
				{ functionName: "refreshAccessToken", userId: args.userId },
			);
		}

		// Beds24 APIを呼び出す（リトライ付き）
		const timer = logger.startTimer("Beds24 API呼び出し");
		const response = await fetchWithRetry(
			"https://api.beds24.com/v2/authentication/token",
			{
				method: "GET",
				headers: {
					accept: "application/json",
					refreshToken: userSettings.beds24.refreshToken,
				},
			},
			3,
			logger,
		);
		timer();

		if (!response.ok) {
			const errorText = await response.text();
			logger.error("Beds24 APIエラー", {
				status: response.status,
				errorText,
			});

			if (response.status === 401) {
				throw createConvexError(
					ERROR_CODES.INVALID_TOKEN,
					"リフレッシュトークンが無効です",
					{ status: response.status, errorText },
					{ functionName: "refreshAccessToken", userId: args.userId },
				);
			}
			throw createConvexError(
				ERROR_CODES.API_ERROR,
				`Beds24 APIエラー: ${response.status}`,
				{ status: response.status, errorText },
				{ functionName: "refreshAccessToken", userId: args.userId },
			);
		}

		const data = await response.json();
		logger.debug("Beds24 APIレスポンス", {
			hasToken: !!data.token,
			expiresIn: data.expiresIn,
		});

		// トークンを保存
		const expiresAt = Date.now() + data.expiresIn * 1000;
		await ctx.runMutation(internal.beds24Tokens.saveAccessToken, {
			userId: args.userId,
			accessToken: data.token,
			expiresAt,
		});

		logger.info("Beds24アクセストークンを更新しました", {
			expiresAt,
			expiresIn: data.expiresIn,
		});

		return null;
	},
});
