{
	/* This TypeScript project config describes the environment that
	 * Convex functions run in and is used to typecheck them.
	 * You can modify it, but some settings required to use Convex.
	 */
	"extends": "../../../tsconfig.base.json",
	"compilerOptions": {
		/* These compiler options are required by Convex */
		"lib": ["ES2023", "dom"],
		"jsx": "react-jsx"
	},
	"include": ["./**/*", "./types/ambient.d.ts"],
	"exclude": ["./_generated"]
}
