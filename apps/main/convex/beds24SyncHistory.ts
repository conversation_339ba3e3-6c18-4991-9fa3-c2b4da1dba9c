import { v } from "convex/values";
import { internalMutation, internalQuery, query } from "./_generated/server";
import { requireAuth } from "./lib/auth";
import { createLogger } from "./lib/logging";
import { SyncHistoryStatus } from "./types/beds24";

/**
 * 同期開始を記録する
 *
 * 同期処理の開始時に履歴レコードを作成します。
 * 初期ステータスは"PROCESSING"に設定されます。
 *
 * @param userId - ユーザーID
 * @param jobType - ジョブタイプ（sync_properties, sync_reviews, scrape_booking_com_slug, sync_bookings）
 * @param metadata - 同期処理に関する追加メタデータ（オプション）
 * @returns 作成された同期履歴のID
 * @example
 * // 施設同期の開始を記録
 * const historyId = await ctx.runMutation(
 *   internal.beds24SyncHistory.recordSyncStart,
 *   {
 *     userId: "user123",
 *     jobType: "sync_properties",
 *     metadata: { propertyCount: 10 }
 *   }
 * );
 */
export const recordSyncStart = internalMutation({
	args: {
		userId: v.string(),
		jobType: v.union(
			v.literal("sync_properties"),
			v.literal("sync_reviews"),
			v.literal("scrape_booking_com_slug"),
			v.literal("sync_bookings"),
		),
		metadata: v.optional(v.any()),
	},
	returns: v.id("beds24SyncHistory"),
	handler: async (ctx, args) => {
		const timestamp = Date.now();

		const historyId = await ctx.db.insert("beds24SyncHistory", {
			userId: args.userId,
			jobType: args.jobType,
			status: SyncHistoryStatus.PROCESSING, // 初期状態は処理中
			startedAt: timestamp,
			duration: 0, // 完了時に計算
			totalItems: 0,
			successCount: 0,
			failedCount: 0,
			errors: [],
			metadata: args.metadata,
			createdAt: timestamp,
			updatedAt: timestamp,
		});

		const logger = createLogger("recordSyncStart", ctx);
		logger.info(`同期履歴を作成しました: ${historyId}`, {
			userId: args.userId,
			jobType: args.jobType,
		});

		return historyId;
	},
});

/**
 * 同期完了を記録する
 *
 * 同期処理の完了時に履歴レコードを更新します。
 * 処理時間は自動的に計算されます。
 *
 * @param historyId - 履歴ID
 * @param status - 完了ステータス（SUCCESS, PARTIAL_SUCCESS, FAILED）
 * @param totalItems - 処理対象アイテム数
 * @param successCount - 成功したアイテム数
 * @param failedCount - 失敗したアイテム数
 * @param metadata - その他の情報（オプション）
 * @returns null
 * @throws 同期履歴が見つからない場合エラー
 * @example
 * // 同期成功の記録
 * await ctx.runMutation(
 *   internal.beds24SyncHistory.recordSyncComplete,
 *   {
 *     historyId: "history456",
 *     status: SyncHistoryStatus.SUCCESS,
 *     totalItems: 10,
 *     successCount: 10,
 *     failedCount: 0
 *   }
 * );
 */
export const recordSyncComplete = internalMutation({
	args: {
		historyId: v.id("beds24SyncHistory"),
		status: v.union(
			v.literal(SyncHistoryStatus.SUCCESS),
			v.literal(SyncHistoryStatus.PARTIAL_SUCCESS),
			v.literal(SyncHistoryStatus.FAILED),
		),
		totalItems: v.number(),
		successCount: v.number(),
		failedCount: v.number(),
		metadata: v.optional(v.any()),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		const timestamp = Date.now();
		const history = await ctx.db.get(args.historyId);

		if (!history) {
			throw new Error(`同期履歴が見つかりません: ${args.historyId}`);
		}

		const duration = timestamp - history.startedAt;

		await ctx.db.patch(args.historyId, {
			status: args.status,
			completedAt: timestamp,
			duration,
			totalItems: args.totalItems,
			successCount: args.successCount,
			failedCount: args.failedCount,
			metadata: args.metadata || history.metadata,
			updatedAt: timestamp,
		});

		const logger = createLogger("recordSyncComplete", ctx);
		logger.info(`同期が完了しました: ${args.historyId}`, {
			status: args.status,
			duration,
			totalItems: args.totalItems,
			successCount: args.successCount,
			failedCount: args.failedCount,
		});

		return null;
	},
});

/**
 * エラーを記録する
 *
 * 同期処理中に発生したエラーを履歴に追加します。
 * エラーがある場合、ステータスは自動的にFAILEDまたはPARTIAL_SUCCESSに更新されます。
 *
 * @param historyId - 履歴ID
 * @param errors - エラー情報の配列
 * @returns null
 * @throws 同期履歴が見つからない場合エラー
 * @example
 * // エラーの記録
 * await ctx.runMutation(
 *   internal.beds24SyncHistory.recordSyncError,
 *   {
 *     historyId: "history456",
 *     errors: [{
 *       code: "API_ERROR",
 *       message: "APIリクエストが失敗しました",
 *       detail: { statusCode: 500 }
 *     }]
 *   }
 * );
 */
export const recordSyncError = internalMutation({
	args: {
		historyId: v.id("beds24SyncHistory"),
		errors: v.array(
			v.object({
				code: v.string(),
				message: v.string(),
				detail: v.optional(v.any()),
			}),
		),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		const timestamp = Date.now();
		const history = await ctx.db.get(args.historyId);

		if (!history) {
			throw new Error(`同期履歴が見つかりません: ${args.historyId}`);
		}

		// エラー情報にタイムスタンプを追加
		const errorsWithTimestamp = args.errors.map((error) => ({
			...error,
			occurredAt: timestamp,
		}));

		// 既存のエラーに追加
		const updatedErrors = [...history.errors, ...errorsWithTimestamp];

		// ステータスを更新（エラーがある場合はFAILEDまたはPARTIAL_SUCCESS）
		let status = history.status;
		if (
			status === SyncHistoryStatus.PROCESSING ||
			status === SyncHistoryStatus.SUCCESS
		) {
			// 成功数が0より大きい場合は部分成功、そうでなければ失敗
			status =
				history.successCount > 0
					? SyncHistoryStatus.PARTIAL_SUCCESS
					: SyncHistoryStatus.FAILED;
		}

		await ctx.db.patch(args.historyId, {
			status,
			errors: updatedErrors,
			updatedAt: timestamp,
		});

		const logger = createLogger("recordSyncError", ctx);
		logger.info(`同期エラーを記録しました: ${args.historyId}`, {
			errorCount: args.errors.length,
			totalErrors: updatedErrors.length,
		});

		return null;
	},
});

/**
 * 同期履歴を取得する（内部用）
 *
 * 指定したユーザーの同期履歴を最新順で取得します。
 * ジョブタイプでフィルタリングすることも可能です。
 *
 * @param userId - ユーザーID
 * @param limit - 取得件数（デフォルト: 10）
 * @param jobType - ジョブタイプでフィルタリング（オプション）
 * @returns 同期履歴の配列（エラー詳細を含む）
 * @example
 * // 最新10件の施設同期履歴を取得
 * const histories = await ctx.runQuery(
 *   internal.beds24SyncHistory.getSyncHistoryInternal,
 *   {
 *     userId: "user123",
 *     jobType: "sync_properties",
 *     limit: 10
 *   }
 * );
 */
export const getSyncHistoryInternal = internalQuery({
	args: {
		userId: v.string(),
		limit: v.optional(v.number()),
		jobType: v.optional(
			v.union(
				v.literal("sync_properties"),
				v.literal("sync_reviews"),
				v.literal("scrape_booking_com_slug"),
				v.literal("sync_bookings"),
			),
		),
	},
	returns: v.array(
		v.object({
			_id: v.id("beds24SyncHistory"),
			_creationTime: v.number(),
			userId: v.string(),
			jobType: v.string(),
			status: v.string(),
			startedAt: v.number(),
			completedAt: v.optional(v.number()),
			duration: v.number(),
			totalItems: v.number(),
			successCount: v.number(),
			failedCount: v.number(),
			errors: v.array(
				v.object({
					code: v.string(),
					message: v.string(),
					detail: v.optional(v.any()),
					occurredAt: v.number(),
				}),
			),
			metadata: v.optional(v.any()),
			createdAt: v.number(),
			updatedAt: v.number(),
		}),
	),
	handler: async (ctx, args) => {
		const limit = args.limit || 10;

		let query: any;
		if (args.jobType) {
			// ジョブタイプが指定されている場合は効率的なインデックスを使用
			query = ctx.db
				.query("beds24SyncHistory")
				.withIndex("by_userId_and_jobType_and_startedAt", (q) =>
					q.eq("userId", args.userId).eq("jobType", args.jobType!),
				)
				.order("desc");
		} else {
			// ジョブタイプが指定されていない場合は従来のインデックスを使用
			query = ctx.db
				.query("beds24SyncHistory")
				.withIndex("by_userId_and_startedAt", (q) =>
					q.eq("userId", args.userId),
				)
				.order("desc");
		}

		const histories = await query.take(limit);

		const logger = createLogger("getSyncHistoryInternal", ctx);
		logger.debug(`同期履歴を取得しました: ${histories.length}件`, {
			userId: args.userId,
			limit,
			jobType: args.jobType,
		});

		return histories;
	},
});

/**
 * 同期履歴を取得する（公開API）
 *
 * 認証されたユーザーの同期履歴を最新順で取得します。
 * セキュリティのため、エラーの詳細情報は除外され、エラー数のみが返されます。
 *
 * @param limit - 取得件数（デフォルト: 10）
 * @param jobType - ジョブタイプでフィルタリング（オプション）
 * @returns 同期履歴の配列（エラー詳細を除く）
 * @throws 認証されていない場合エラー
 * @example
 * // 最新の同期履歴を取得
 * const histories = await convex.query(
 *   api.beds24SyncHistory.getSyncHistory,
 *   { limit: 20 }
 * );
 */
export const getSyncHistory = query({
	args: {
		limit: v.optional(v.number()),
		jobType: v.optional(
			v.union(
				v.literal("sync_properties"),
				v.literal("sync_reviews"),
				v.literal("scrape_booking_com_slug"),
				v.literal("sync_bookings"),
			),
		),
	},
	returns: v.array(
		v.object({
			_id: v.id("beds24SyncHistory"),
			_creationTime: v.number(),
			jobType: v.string(),
			status: v.string(),
			startedAt: v.number(),
			completedAt: v.optional(v.number()),
			duration: v.number(),
			totalItems: v.number(),
			successCount: v.number(),
			failedCount: v.number(),
			errorCount: v.number(), // エラー詳細は公開APIでは返さない
			metadata: v.optional(v.any()),
			createdAt: v.number(),
			updatedAt: v.number(),
		}),
	),
	handler: async (ctx, args) => {
		// 認証チェック
		const userId = await requireAuth(ctx);
		const limit = args.limit || 10;

		let query: any;
		if (args.jobType) {
			// ジョブタイプが指定されている場合は効率的なインデックスを使用
			query = ctx.db
				.query("beds24SyncHistory")
				.withIndex("by_userId_and_jobType_and_startedAt", (q) =>
					q.eq("userId", userId).eq("jobType", args.jobType!),
				)
				.order("desc");
		} else {
			// ジョブタイプが指定されていない場合は従来のインデックスを使用
			query = ctx.db
				.query("beds24SyncHistory")
				.withIndex("by_userId_and_startedAt", (q) => q.eq("userId", userId))
				.order("desc");
		}

		const histories = await query.take(limit);

		// 公開APIではエラー詳細を除外
		return histories.map((history: any) => ({
			_id: history._id,
			_creationTime: history._creationTime,
			jobType: history.jobType,
			status: history.status,
			startedAt: history.startedAt,
			completedAt: history.completedAt,
			duration: history.duration,
			totalItems: history.totalItems,
			successCount: history.successCount,
			failedCount: history.failedCount,
			errorCount: history.errors.length,
			metadata: history.metadata,
			createdAt: history.createdAt,
			updatedAt: history.updatedAt,
		}));
	},
});

/**
 * 最新の同期状態を取得する
 *
 * 認証されたユーザーの最新の同期状態を取得します。
 * ジョブタイプが指定された場合は、そのタイプの最新の同期状態を返します。
 *
 * @param jobType - ジョブタイプ（オプション）
 * @returns 最新の同期状態、または同期履歴がない場合はnull
 * @throws 認証されていない場合エラー
 * @example
 * // 最新の施設同期状態を取得
 * const status = await convex.query(
 *   api.beds24SyncHistory.getLatestSyncStatus,
 *   { jobType: "sync_properties" }
 * );
 * if (status?.isProcessing) {
 *   console.log("同期処理中...");
 * }
 */
export const getLatestSyncStatus = query({
	args: {
		jobType: v.optional(
			v.union(
				v.literal("sync_properties"),
				v.literal("sync_reviews"),
				v.literal("scrape_booking_com_slug"),
				v.literal("sync_bookings"),
			),
		),
	},
	returns: v.union(
		v.object({
			_id: v.id("beds24SyncHistory"),
			jobType: v.string(),
			status: v.string(),
			startedAt: v.number(),
			completedAt: v.optional(v.number()),
			duration: v.number(),
			totalItems: v.number(),
			successCount: v.number(),
			failedCount: v.number(),
			isProcessing: v.boolean(),
		}),
		v.null(),
	),
	handler: async (ctx, args) => {
		// 認証チェック
		const userId = await requireAuth(ctx);

		let query: any;
		if (args.jobType) {
			// ジョブタイプが指定されている場合は効率的なインデックスを使用
			query = ctx.db
				.query("beds24SyncHistory")
				.withIndex("by_userId_and_jobType_and_startedAt", (q) =>
					q.eq("userId", userId).eq("jobType", args.jobType!),
				)
				.order("desc");
		} else {
			// ジョブタイプが指定されていない場合は従来のインデックスを使用
			query = ctx.db
				.query("beds24SyncHistory")
				.withIndex("by_userId_and_startedAt", (q) => q.eq("userId", userId))
				.order("desc");
		}

		const latestHistory = await query.first();

		if (!latestHistory) {
			return null;
		}

		return {
			_id: latestHistory._id,
			jobType: latestHistory.jobType,
			status: latestHistory.status,
			startedAt: latestHistory.startedAt,
			completedAt: latestHistory.completedAt,
			duration: latestHistory.duration,
			totalItems: latestHistory.totalItems,
			successCount: latestHistory.successCount,
			failedCount: latestHistory.failedCount,
			isProcessing: latestHistory.status === SyncHistoryStatus.PROCESSING,
		};
	},
});

/**
 * 同期プロセス全体を管理する統合mutation
 *
 * syncUserPropertiesの最適化のため、複数のミューテーションを統合して管理します。
 * start、complete、errorの各アクションに対応した処理を実行します。
 *
 * @param userId - ユーザーID
 * @param jobType - ジョブタイプ
 * @param action - 実行するアクション（start, complete, error）
 * @param historyId - 履歴ID（completeとerrorアクションで必須）
 * @param result - 処理結果（completeとerrorアクションで使用）
 * @returns startアクションの場合は履歴ID、それ以外はnull
 * @throws historyIdが必要な場合に提供されていない、または履歴が見つからない場合エラー
 * @example
 * // 同期の開始
 * const historyId = await ctx.runMutation(
 *   internal.beds24SyncHistory.manageSyncLifecycle,
 *   {
 *     userId: "user123",
 *     jobType: "sync_properties",
 *     action: "start"
 *   }
 * );
 *
 * // 同期の完了
 * await ctx.runMutation(
 *   internal.beds24SyncHistory.manageSyncLifecycle,
 *   {
 *     userId: "user123",
 *     jobType: "sync_properties",
 *     action: "complete",
 *     historyId: historyId,
 *     result: {
 *       status: SyncHistoryStatus.SUCCESS,
 *       totalItems: 10,
 *       successCount: 10,
 *       failedCount: 0
 *     }
 *   }
 * );
 */
export const manageSyncLifecycle = internalMutation({
	args: {
		userId: v.string(),
		jobType: v.union(
			v.literal("sync_properties"),
			v.literal("sync_reviews"),
			v.literal("scrape_booking_com_slug"),
			v.literal("sync_bookings"),
		),
		action: v.union(
			v.literal("start"),
			v.literal("complete"),
			v.literal("error"),
		),
		historyId: v.optional(v.id("beds24SyncHistory")),
		result: v.optional(
			v.object({
				status: v.optional(
					v.union(
						v.literal(SyncHistoryStatus.SUCCESS),
						v.literal(SyncHistoryStatus.PARTIAL_SUCCESS),
						v.literal(SyncHistoryStatus.FAILED),
					),
				),
				totalItems: v.optional(v.number()),
				successCount: v.optional(v.number()),
				failedCount: v.optional(v.number()),
				metadata: v.optional(v.any()),
				error: v.optional(
					v.object({
						code: v.string(),
						message: v.string(),
					}),
				),
			}),
		),
	},
	returns: v.union(v.id("beds24SyncHistory"), v.null()),
	handler: async (ctx, args) => {
		const timestamp = Date.now();
		const logger = createLogger("manageSyncLifecycle", ctx);

		switch (args.action) {
			case "start": {
				// 同期開始の記録
				const historyId = await ctx.db.insert("beds24SyncHistory", {
					userId: args.userId,
					jobType: args.jobType,
					status: SyncHistoryStatus.PROCESSING,
					startedAt: timestamp,
					duration: 0,
					totalItems: 0,
					successCount: 0,
					failedCount: 0,
					errors: [],
					metadata: args.result?.metadata,
					createdAt: timestamp,
					updatedAt: timestamp,
				});
				logger.info(`同期履歴を作成しました: ${historyId}`, {
					userId: args.userId,
					jobType: args.jobType,
				});
				return historyId;
			}

			case "complete":
			case "error": {
				if (!args.historyId) {
					throw new Error("historyIdが必要です");
				}

				const history = await ctx.db.get(args.historyId);
				if (!history) {
					throw new Error(`同期履歴が見つかりません: ${args.historyId}`);
				}

				const duration = timestamp - history.startedAt;
				const updateData: any = {
					completedAt: timestamp,
					duration,
					updatedAt: timestamp,
				};

				if (args.action === "complete" && args.result) {
					updateData.status = args.result.status || SyncHistoryStatus.SUCCESS;
					updateData.totalItems = args.result.totalItems || 0;
					updateData.successCount = args.result.successCount || 0;
					updateData.failedCount = args.result.failedCount || 0;
					if (args.result.metadata) {
						updateData.metadata = args.result.metadata;
					}
				} else if (args.action === "error" && args.result?.error) {
					// エラーの場合
					updateData.status = SyncHistoryStatus.FAILED;
					updateData.errors = [
						...history.errors,
						{
							code: args.result.error.code,
							message: args.result.error.message,
							detail: undefined,
							occurredAt: timestamp,
						},
					];
					updateData.failedCount = 1;
				}

				await ctx.db.patch(args.historyId, updateData);

				logger.info(
					args.action === "complete"
						? "同期が完了しました"
						: "同期がエラーで終了しました",
					{
						historyId: args.historyId,
						status: updateData.status,
						duration,
					},
				);

				return null;
			}

			default:
				throw new Error(`不明なアクション: ${args.action}`);
		}
	},
});
