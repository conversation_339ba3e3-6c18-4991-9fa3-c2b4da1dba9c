/**
 * Beds24 Queue Processing Mutations
 *
 * このファイルは予約同期用のキュー処理関数を提供します。
 * sync_bookingsジョブタイプの処理を担当します。
 *
 * 注意: "use node"を使用しないV8環境で動作します
 */

import { v } from "convex/values";
import { internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import { internalMutation } from "./_generated/server";
import { createLogger } from "./lib/logging";
import { QueueJobType, SyncStatus } from "./types/beds24";

// dequeueJobの戻り値の型定義
type DequeueJobResult = {
	job: {
		_id: Id<"syncQueue">;
		_creationTime: number;
		userId: string;
		jobType: string;
		priority: number;
		status: string;
		attempts: number;
		maxAttempts: number;
		scheduledFor: number;
		startedAt?: number;
		completedAt?: number;
		lastError?: string;
		nextRetryAt?: number;
		metadata?: any;
		result?: any;
		createdAt: number;
		updatedAt: number;
	};
} | null;

/**
 * 未処理の予約同期タスクを取得して処理を開始する
 *
 * このミューテーションは以下の処理を行います：
 * 1. 未処理のsync_bookingsタスクを取得
 * 2. タスクのステータスをPROCESSINGに更新
 * 3. 予約同期アクション（syncBookingsFromBeds24）をスケジュール
 * 4. エラー時はタスクをFAILEDに更新し、リトライを設定
 *
 * @returns 処理を開始したタスクの情報、またはnull（処理可能なタスクがない場合）
 * @example
 * // キュー処理を実行
 * const result = await ctx.runMutation(
 *   internal.beds24QueueMutations.processQueue,
 *   {}
 * );
 * if (result) {
 *   console.log(`タスク${result.taskId}の処理を開始しました`);
 * }
 *
 * @note sync_bookings以外のジョブタイプはスキップされます
 */
export const processQueue = internalMutation({
	args: {},
	returns: v.union(
		v.object({
			taskId: v.id("syncQueue"),
			jobType: v.string(),
			userId: v.string(),
			scheduled: v.boolean(),
		}),
		v.null(),
	),
	handler: async (
		ctx,
	): Promise<{
		taskId: Id<"syncQueue">;
		jobType: string;
		userId: string;
		scheduled: boolean;
	} | null> => {
		const logger = createLogger("processQueue", ctx);
		logger.debug("キュー処理を開始");

		// 未処理のジョブを取得
		const jobResult: DequeueJobResult = await ctx.runQuery(
			internal.beds24Queue.dequeueJob,
			{},
		);

		if (!jobResult) {
			logger.debug("処理可能なジョブがありません");
			return null;
		}

		const { job } = jobResult;

		// sync_bookingsジョブのみを処理
		if (job.jobType !== QueueJobType.SYNC_BOOKINGS) {
			logger.debug("sync_bookings以外のジョブはスキップ", {
				jobId: job._id,
				jobType: job.jobType,
			});
			return null;
		}

		logger.info("予約同期ジョブを取得", {
			jobId: job._id,
			userId: job.userId,
			attempts: job.attempts,
		});

		try {
			// ジョブステータスをPROCESSINGに更新
			await ctx.runMutation(internal.beds24Queue.updateJobStatus, {
				jobId: job._id,
				status: SyncStatus.PROCESSING,
			});

			// 予約同期アクションをスケジュール
			await ctx.scheduler.runAfter(
				0,
				internal.beds24BookingSync.syncBookingsFromBeds24,
				{
					taskId: job._id,
				},
			);

			logger.info("予約同期アクションをスケジュールしました", {
				jobId: job._id,
				userId: job.userId,
			});

			return {
				taskId: job._id,
				jobType: job.jobType,
				userId: job.userId,
				scheduled: true,
			};
		} catch (error) {
			// エラーが発生した場合はジョブをFAILEDに更新
			logger.error("ジョブの処理中にエラーが発生", {
				jobId: job._id,
				error: error instanceof Error ? error.message : String(error),
			});

			// リトライまでの遅延を計算（指数バックオフ）
			const delayMs = Math.min(
				60000 * 2 ** job.attempts, // 1分, 2分, 4分, ...
				300000, // 最大5分
			);
			const nextRetryAt = Date.now() + delayMs;

			await ctx.runMutation(internal.beds24Queue.updateJobStatus, {
				jobId: job._id,
				status: SyncStatus.FAILED,
				error: error instanceof Error ? error.message : String(error),
				nextRetryAt,
			});

			// 最大試行回数に達していない場合は再キューを検討
			if (job.attempts < job.maxAttempts) {
				logger.info("ジョブを再キューします", {
					jobId: job._id,
					attempts: job.attempts,
					nextRetryAt: new Date(nextRetryAt).toISOString(),
				});
			} else {
				logger.error("ジョブが最大試行回数に達しました", {
					jobId: job._id,
					attempts: job.attempts,
					maxAttempts: job.maxAttempts,
				});
			}

			return null;
		}
	},
});

/**
 * 完了した予約同期タスクの後処理を行う
 *
 * syncBookingsFromBeds24アクションから呼び出され、
 * タスクのステータスをCOMPLETEDに更新し、同期結果を記録します。
 *
 * @param taskId - 同期タスクのID
 * @param result - 同期結果（施設数、予約数、エラー情報を含む）
 * @returns null
 * @example
 * // 予約同期タスクの完了を記録
 * await ctx.runMutation(
 *   internal.beds24QueueMutations.completeBookingSyncTask,
 *   {
 *     taskId: "task123",
 *     result: {
 *       totalProperties: 5,
 *       successfulProperties: 4,
 *       failedProperties: 1,
 *       totalBookings: 100,
 *       newBookings: 20,
 *       updatedBookings: 30,
 *       deletedBookings: 5,
 *       errors: [{ propertyId: "12345", error: "API error" }]
 *     }
 *   }
 * );
 */
export const completeBookingSyncTask = internalMutation({
	args: {
		taskId: v.id("syncQueue"),
		result: v.object({
			totalProperties: v.number(),
			successfulProperties: v.number(),
			failedProperties: v.number(),
			totalBookings: v.number(),
			newBookings: v.number(),
			updatedBookings: v.number(),
			deletedBookings: v.number(),
			errors: v.array(
				v.object({
					propertyId: v.string(),
					error: v.string(),
				}),
			),
		}),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		const logger = createLogger("completeBookingSyncTask", ctx);
		logger.info("予約同期タスクを完了", {
			taskId: args.taskId,
			result: args.result,
		});

		await ctx.runMutation(internal.beds24Queue.updateJobStatus, {
			jobId: args.taskId,
			status: SyncStatus.COMPLETED,
			result: args.result,
		});

		return null;
	},
});

/**
 * 失敗した予約同期タスクの後処理を行う
 *
 * syncBookingsFromBeds24アクションから呼び出され、
 * タスクのステータスをFAILEDに更新し、リトライ可能な場合は次回実行時刻を設定します。
 * リトライ間隔は指数バックオフで計算されます（最大5分）。
 *
 * @param taskId - 同期タスクのID
 * @param error - エラーメッセージ
 * @param attempts - 現在の試行回数
 * @param maxAttempts - 最大試行回数
 * @returns null
 * @example
 * // 予約同期タスクの失敗を記録
 * await ctx.runMutation(
 *   internal.beds24QueueMutations.failBookingSyncTask,
 *   {
 *     taskId: "task123",
 *     error: "Beds24 API rate limit exceeded",
 *     attempts: 2,
 *     maxAttempts: 3
 *   }
 * );
 * // → 次回リトライは4分後（2^2 = 4分）
 */
export const failBookingSyncTask = internalMutation({
	args: {
		taskId: v.id("syncQueue"),
		error: v.string(),
		attempts: v.number(),
		maxAttempts: v.number(),
	},
	returns: v.null(),
	handler: async (ctx, args) => {
		const logger = createLogger("failBookingSyncTask", ctx);
		logger.error("予約同期タスクが失敗", {
			taskId: args.taskId,
			error: args.error,
			attempts: args.attempts,
		});

		// リトライまでの遅延を計算
		const delayMs = Math.min(
			60000 * 2 ** args.attempts,
			300000, // 最大5分
		);
		const nextRetryAt = Date.now() + delayMs;

		await ctx.runMutation(internal.beds24Queue.updateJobStatus, {
			jobId: args.taskId,
			status: SyncStatus.FAILED,
			error: args.error,
			nextRetryAt: args.attempts < args.maxAttempts ? nextRetryAt : undefined,
		});

		// 最大試行回数に達していない場合は再キューの情報をログ
		if (args.attempts < args.maxAttempts) {
			logger.info("タスクは再試行されます", {
				taskId: args.taskId,
				nextRetryAt: new Date(nextRetryAt).toISOString(),
				remainingAttempts: args.maxAttempts - args.attempts,
			});
		} else {
			logger.error("タスクが最大試行回数に達しました", {
				taskId: args.taskId,
				attempts: args.attempts,
				maxAttempts: args.maxAttempts,
			});
		}

		return null;
	},
});
