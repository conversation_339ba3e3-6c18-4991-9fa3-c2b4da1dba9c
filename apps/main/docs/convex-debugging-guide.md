# Convex デバッグガイド

このガイドでは、Convex関数のエラーハンドリングとデバッグ方法について説明します。

## エラーハンドリングの仕組み

### 1. 構造化されたエラー

すべてのConvex関数で `ConvexError` を使用して構造化されたエラーを返します：

```typescript
import { createConvexError, ERROR_CODES } from "@/convex/lib/errors";

throw createConvexError(
  ERROR_CODES.UNAUTHORIZED,
  "認証が必要です",
  { additionalInfo: "詳細情報" },  // オプション
  { functionName: "myFunction" }    // コンテキスト情報
);
```

### 2. ログ出力

すべてのConvex関数でロガーを使用してデバッグ情報を記録：

```typescript
import { createLogger } from "@/convex/lib/logging";

const logger = createLogger("functionName", ctx);
logger.setArgs(args);  // センシティブな情報は自動マスク
logger.info("処理開始");
logger.debug("詳細情報", { data });
logger.error("エラー発生", error);
```

## 開発環境でのデバッグ

### 1. リアルタイムログの確認

開発サーバー起動時に自動的にログが表示されます：

```bash
npm run dev
```

別ターミナルでログのみを確認する場合：

```bash
npx convex dev --tail-logs always
```

### 2. ログレベルの制御

- **開発環境**: すべてのログ（DEBUG以上）が出力されます
- **本番環境**: INFO以上のログのみ出力されます

### 3. ログの検索

特定の関数のログを検索：

```bash
npx convex logs | grep "functionName"
```

エラーログのみを表示：

```bash
npx convex logs | grep "ERROR"
```

### 4. Convexダッシュボード

ブラウザでConvexダッシュボードを開く：

```bash
npx convex dashboard
```

ダッシュボードでは以下が確認できます：
- 関数の実行履歴
- エラーログ
- データベースの状態
- パフォーマンスメトリクス

## クライアント側でのエラー処理

### 1. エラーハンドラーの使用

```typescript
import { handleConvexError } from "@/lib/convex-errors";

try {
  await mutation({ /* args */ });
} catch (error) {
  handleConvexError(error, "デフォルトメッセージ");
}
```

### 2. withErrorHandlingヘルパー

```typescript
import { withErrorHandling } from "@/lib/convex-errors";

const safeMutation = withErrorHandling(mutation, {
  defaultMessage: "更新に失敗しました",
  onSuccess: () => console.log("成功"),
  onError: (error) => console.error("失敗", error)
});
```

## 本番環境でのデバッグ

### 1. 本番ログの確認

```bash
npx convex logs --prod
```

### 2. Sentryでのエラー追跡

エラーは自動的にSentryに送信されます。Sentryダッシュボードで以下を確認：
- エラーの詳細情報
- Convexエラーコード
- 関数名とコンテキスト
- ユーザー情報

### 3. エラーのフィルタリング

特定のエラーコードでフィルタ：

```
convex_error.code:UNAUTHORIZED
```

## トラブルシューティング

### ログが表示されない

1. 開発サーバーが起動しているか確認
2. `--tail-logs always` オプションを使用
3. ログレベルを確認（開発環境ではDEBUGレベルも表示）

### エラーの詳細が不明

1. 開発環境でブラウザのコンソールを確認
2. Convexダッシュボードで関数の実行履歴を確認
3. Sentryでエラーの詳細を確認

### パフォーマンスの問題

1. `logger.startTimer()` を使用して処理時間を計測
2. Convexダッシュボードでクエリのパフォーマンスを確認
3. インデックスが適切に設定されているか確認

## ベストプラクティス

1. **常にロガーを使用**: すべての関数でロガーを作成し、重要な処理をログに記録
2. **構造化エラーを使用**: `ConvexError` を使用してエラーコードと詳細情報を含める
3. **センシティブ情報に注意**: パスワードやトークンはログに含めない（自動マスク機能を活用）
4. **適切なログレベル**: DEBUG（詳細）、INFO（通常）、WARN（警告）、ERROR（エラー）を使い分ける
5. **コンテキスト情報を追加**: userId、functionName などのコンテキスト情報をログに含める