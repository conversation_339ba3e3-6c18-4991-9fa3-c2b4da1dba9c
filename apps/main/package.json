{"name": "@kadou-delta-next/main", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"npm:dev:frontend\" \"npm:dev:backend\"", "dev:frontend": "next dev --turbopack --hostname 0.0.0.0 --port 3232", "dev:backend": "NODE_NO_HTTP2=1 convex dev", "predev": "NODE_NO_HTTP2=1 convex dev --until-success --run init && convex dashboard", "build": "next build", "build:debug": "EXPERIMENTAL_DEBUG_MEMORY_USAGE=1 next build --experimental-debug-memory-usage", "start": "next start", "deploy": "convex deploy && npm run postdeploy", "postdeploy": "convex run --prod init", "test": "vitest", "test:run": "vitest run --project=unit --silent --coverage.enabled=false", "test:run:all": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "lint": "biome check --write .", "format": "biome format --write .", "check": "biome check .", "ls-lint": "ls-lint", "typecheck": "tsc --noEmit", "verify": "concurrently \"npm:check\" \"npm:ls-lint\" \"npm:typecheck\" \"npm:test:run\"", "envcheck": "./dotenv-linter compare .env.local .env.example", "postinstall": "./scripts/install-dotenv-linter.sh", "analyze": "ANALYZE=true npm run build", "storybook": "storybook dev -p 6006 -h 0.0.0.0 --no-open", "build-storybook": "storybook build", "test:storybook:ci": "bash ./scripts/test-storybook.sh"}, "dependencies": {"@chakra-ui/charts": "^3.22.0", "@chakra-ui/react": "^3.21.0", "@clerk/clerk-react": "^5.25.0", "@clerk/localizations": "^3.17.0", "@clerk/nextjs": "^6.24.0", "@convex-dev/migrations": "^0.2.9", "@emotion/react": "^11.14.0", "@radix-ui/react-icons": "^1.3.2", "@scrapingant/scrapingant-client": "^0.2.1", "@sentry/nextjs": "^9.38.0", "cheerio": "^1.1.2", "clsx": "^2.1.1", "convex": "^1.23.0", "jsdom": "^26.1.0", "lucide-react": "^0.525.0", "luxon": "^3.7.1", "next": "15.3.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-is": "^19.1.0", "recharts": "^3.1.0"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@clerk/types": "^4.64.0", "@ls-lint/ls-lint": "^2.3.1", "@next/bundle-analyzer": "^15.3.4", "@stagewise-plugins/react": "^0.5.2", "@stagewise/toolbar-next": "^0.5.2", "@storybook/addon-a11y": "^9.0.15", "@storybook/addon-docs": "^9.0.15", "@storybook/addon-onboarding": "^9.0.15", "@storybook/addon-vitest": "^9.0.15", "@storybook/nextjs-vite": "^9.0.15", "@storybook/react": "^9.0.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/cheerio": "^1.0.0", "@types/jsdom": "^21.1.7", "@types/luxon": "^3.6.2", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/seedrandom": "^3.0.8", "@vitejs/plugin-react": "^4.6.0", "@vitest/browser": "3.2.4", "@vitest/coverage-v8": "3.2.4", "concurrently": "^9.1.2", "convex-test": "^0.0.38", "playwright": "^1.54.1", "seedrandom": "^3.0.5", "storybook": "^9.0.15", "typescript": "^5", "typescript-language-server": "^4.3.4", "vite": "^6.3.5", "vitest": "^3.2.4"}}