#!/bin/bash
set -euo pipefail

# Install dotenv-linter to the apps/main directory
echo "Installing dotenv-linter..."

# Get the directory of this script
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PARENT_DIR="$(dirname "$DIR")"

# Create temporary directory for downloads
TEMP_DIR=$(mktemp -d)
trap 'rm -rf "$TEMP_DIR"' EXIT

# Version and platform detection
VERSION="v3.3.0"
OS=$(uname -s | tr '[:upper:]' '[:lower:]')
ARCH=$(uname -m)

# Map architecture names
case "$ARCH" in
    x86_64) ARCH="x86_64" ;;
    aarch64) ARCH="aarch64" ;;
    arm64) 
        # Darwin uses arm64, Linux uses aarch64
        if [ "$OS" = "darwin" ]; then
            ARCH="arm64"
        else
            ARCH="aarch64"
        fi
        ;;
    *) echo "❌ Unsupported architecture: $ARCH"; exit 1 ;;
esac

# Map OS names and set file format
case "$OS" in
    linux) 
        FILE_NAME="dotenv-linter-${OS}-${ARCH}.tar.gz"
        FILE_FORMAT="tar.gz"
        ;;
    darwin) 
        FILE_NAME="dotenv-linter-${OS}-${ARCH}.tar.gz"
        FILE_FORMAT="tar.gz"
        ;;
    *) echo "❌ Unsupported OS: $OS"; exit 1 ;;
esac

# Download URL
DOWNLOAD_URL="https://github.com/dotenv-linter/dotenv-linter/releases/download/${VERSION}/${FILE_NAME}"

echo "Downloading dotenv-linter ${VERSION} for ${OS}-${ARCH}..."

# Download compressed file
curl -sSfL "$DOWNLOAD_URL" -o "$TEMP_DIR/${FILE_NAME}"

# Extract the binary
echo "Extracting binary..."
cd "$TEMP_DIR"
tar -xzf "${FILE_NAME}"

# The extracted binary is named "dotenv-linter"
if [ ! -f "dotenv-linter" ]; then
    echo "❌ Failed to extract dotenv-linter binary"
    exit 1
fi

# Install to target directory
chmod +x dotenv-linter
mv dotenv-linter "$PARENT_DIR/dotenv-linter"

# Check if installation was successful
if [ -f "$PARENT_DIR/dotenv-linter" ]; then
    echo "✅ dotenv-linter installed successfully to $PARENT_DIR/dotenv-linter"
    echo "Version: $("$PARENT_DIR/dotenv-linter" --version)"
    exit 0
else
    echo "❌ Failed to install dotenv-linter"
    exit 1
fi