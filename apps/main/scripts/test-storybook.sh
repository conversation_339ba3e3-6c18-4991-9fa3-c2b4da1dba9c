#!/bin/bash

# CI環境でStorybookテストを実行するためのヘルパースクリプト
# このスクリプトはStorybookサーバーを起動し、テストを実行してからサーバーを停止します

set -e

# Storybookサーバーのプロセス情報を保存
STORYBOOK_PID=""

# クリーンアップ関数
cleanup() {
    if [ ! -z "$STORYBOOK_PID" ]; then
        echo "Stopping Storybook server (PID: $STORYBOOK_PID)..."
        kill $STORYBOOK_PID 2>/dev/null || true
        wait $STORYBOOK_PID 2>/dev/null || true
    fi
}

# 終了時にクリーンアップ
trap cleanup EXIT INT TERM

# Storybookサーバーを起動
echo "Starting Storybook server..."
npm run storybook -- --ci --quiet &
STORYBOOK_PID=$!

# Storybookサーバーが起動するまで待機
echo "Waiting for Storybook server to start..."
MAX_RETRIES=30
RETRY_COUNT=0

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if curl -s http://localhost:6006 > /dev/null; then
        echo "Storybook server is ready!"
        break
    fi
    echo "Waiting for Storybook server... (attempt $((RETRY_COUNT + 1))/$MAX_RETRIES)"
    sleep 2
    RETRY_COUNT=$((RETRY_COUNT + 1))
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    echo "ERROR: Storybook server failed to start within 60 seconds"
    exit 1
fi

# テストを実行
echo "Running Vitest tests..."
npx vitest run --project=storybook

# クリーンアップはtrapで実行される