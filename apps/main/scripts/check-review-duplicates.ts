// このスクリプトはサンプルデータを使用してレビュー重複チェックロジックをテストします

// 型定義
interface Review {
	_id: string;
	title?: string;
	reviewContent: string;
	reviewerName: string;
	reviewerCountry?: string;
	reviewDate: number;
	beds24PropertyId: string;
	otaId: string;
	uniqueHash: string;
}

interface DuplicateGroup {
	key: string;
	count: number;
	reviews: Array<{
		id: string;
		title: string;
		content: string;
		reviewerName: string;
		reviewDate: string;
		propertyId: string;
		otaId: string;
		uniqueHash: string;
	}>;
}

// ヘルパー関数: レビューの重複チェック
function checkDuplicates(reviews: Review[]): DuplicateGroup[] {
	const reviewMap = new Map<string, Review[]>();
	const duplicates: DuplicateGroup[] = [];

	for (const review of reviews) {
		const key = `${review.title || "NO_TITLE"}|${review.reviewContent}`;
		if (reviewMap.has(key)) {
			reviewMap.get(key)!.push(review);
		} else {
			reviewMap.set(key, [review]);
		}
	}

	// 重複を検出
	for (const [key, reviewGroup] of reviewMap) {
		if (reviewGroup.length > 1) {
			duplicates.push({
				key,
				count: reviewGroup.length,
				reviews: reviewGroup.map((r) => ({
					id: r._id,
					title: r.title || "タイトルなし",
					content: `${r.reviewContent.substring(0, 100)}...`,
					reviewerName: r.reviewerName,
					reviewDate: new Date(r.reviewDate).toLocaleDateString("ja-JP"),
					propertyId: r.beds24PropertyId,
					otaId: r.otaId,
					uniqueHash: r.uniqueHash,
				})),
			});
		}
	}

	return duplicates;
}

async function main() {
	try {
		console.log("=== OTAレビュー重複チェック ===\n");

		// Convexからレビューデータを取得（ページネーションなしの簡易版）
		// 注: 実際の環境では認証が必要な場合があります
		console.log("レビューデータを取得中...");

		// サンプルデータで確認（実際にはクエリを実行）
		const sampleReviews: Review[] = [
			{
				_id: "review1",
				title: "素晴らしい滞在",
				reviewContent:
					"スタッフの対応が素晴らしく、部屋も清潔でした。朝食も美味しかったです。",
				reviewerName: "山田太郎",
				reviewDate: Date.now() - 86400000,
				beds24PropertyId: "prop1",
				otaId: "ota1",
				uniqueHash: "hash1",
			},
			{
				_id: "review2",
				title: "素晴らしい滞在",
				reviewContent:
					"スタッフの対応が素晴らしく、部屋も清潔でした。朝食も美味しかったです。",
				reviewerName: "鈴木一郎",
				reviewDate: Date.now() - 172800000,
				beds24PropertyId: "prop1",
				otaId: "ota1",
				uniqueHash: "hash2",
			},
			{
				_id: "review3",
				title: "快適な宿泊",
				reviewContent:
					"立地が良く、観光に便利でした。部屋は少し狭いですが、清潔で快適でした。",
				reviewerName: "佐藤花子",
				reviewDate: Date.now() - 259200000,
				beds24PropertyId: "prop2",
				otaId: "ota2",
				uniqueHash: "hash3",
			},
			{
				_id: "review4",
				title: "Great experience",
				reviewContent:
					"The hotel exceeded my expectations. Staff was friendly and helpful.",
				reviewerName: "John Smith",
				reviewDate: Date.now() - 345600000,
				beds24PropertyId: "prop1",
				otaId: "ota3",
				uniqueHash: "hash4",
			},
			{
				_id: "review5",
				title: "Great experience",
				reviewContent:
					"The hotel exceeded my expectations. Staff was friendly and helpful.",
				reviewerName: "Jane Doe",
				reviewDate: Date.now() - 432000000,
				beds24PropertyId: "prop1",
				otaId: "ota3",
				uniqueHash: "hash5",
			},
		];

		console.log(`\n取得したレビュー数: ${sampleReviews.length}`);

		// 重複チェック
		const duplicates = checkDuplicates(sampleReviews);

		if (duplicates.length === 0) {
			console.log("\n✅ 重複は見つかりませんでした。");
		} else {
			console.log(
				`\n⚠️  ${duplicates.length}件の重複グループが見つかりました:\n`,
			);

			for (const [index, dupGroup] of duplicates.entries()) {
				console.log(`=== 重複グループ ${index + 1} (${dupGroup.count}件) ===`);
				console.log(`タイトル: "${dupGroup.reviews[0].title}"`);
				console.log(`内容: "${dupGroup.reviews[0].content}"`);
				console.log("\n詳細:");

				for (const review of dupGroup.reviews) {
					console.log(`  - ID: ${review.id}`);
					console.log(`    レビュアー: ${review.reviewerName}`);
					console.log(`    投稿日: ${review.reviewDate}`);
					console.log(`    施設ID: ${review.propertyId}`);
					console.log(`    OTA ID: ${review.otaId}`);
					console.log(`    ハッシュ: ${review.uniqueHash}`);
					console.log("");
				}
			}

			// uniqueHashの分析
			console.log("\n=== UniqueHash分析 ===");
			const hashMap = new Map<string, number>();
			for (const review of sampleReviews) {
				hashMap.set(
					review.uniqueHash,
					(hashMap.get(review.uniqueHash) || 0) + 1,
				);
			}

			const duplicateHashes = Array.from(hashMap.entries()).filter(
				([_, count]) => count > 1,
			);
			if (duplicateHashes.length > 0) {
				console.log("⚠️  同じuniqueHashを持つレビューが存在します:");
				for (const [hash, count] of duplicateHashes) {
					console.log(`  - ハッシュ: ${hash} (${count}件)`);
				}
			} else {
				console.log("✅ すべてのレビューが異なるuniqueHashを持っています。");
			}
		}
	} catch (error) {
		console.error("エラーが発生しました:", error);
	}
}

main();
