import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { jaJP } from "@clerk/localizations";
import { ClerkProvider } from "@clerk/nextjs";
import { PerformanceMonitor } from "@/shared/components/PerformanceMonitor";
import ConvexClientProvider from "@/shared/components/providers/ConvexClientProvider";
import { StagewiseWrapper } from "@/src/components/StagewiseWrapper";
import { Provider } from "@/src/components/ui/provider";
import { Toaster } from "@/src/components/ui/toaster";

const geistSans = Geist({
	variable: "--font-geist-sans",
	subsets: ["latin"],
	display: "swap",
	preload: true,
});

const geistMono = Geist_Mono({
	variable: "--font-geist-mono",
	subsets: ["latin"],
	display: "swap",
	preload: true,
});

export const metadata: Metadata = {
	title: "KADOU-DELTA",
	description: "KADOU-DELTA",
	icons: {
		icon: "/convex.svg",
	},
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="ja" suppressHydrationWarning>
			<body
				className={`${geistSans.variable} ${geistMono.variable} antialiased`}
			>
				{/* Stagewise Toolbar - 開発環境でのみ表示 */}
				<StagewiseWrapper />

				<Provider>
					<ClerkProvider
						dynamic
						localization={jaJP}
						afterSignInUrl="/app"
						afterSignUpUrl="/app"
					>
						<ConvexClientProvider>
							<PerformanceMonitor />
							{children}
						</ConvexClientProvider>
					</ClerkProvider>
					<Toaster />
				</Provider>
			</body>
		</html>
	);
}
