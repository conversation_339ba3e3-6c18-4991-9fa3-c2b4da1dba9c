"use client";

import {
	<PERSON>,
	<PERSON><PERSON>,
	Container,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Spinner,
	Text,
	VStack,
} from "@chakra-ui/react";
import { SignInButton, SignUpButton } from "@clerk/nextjs";
import { Authenticated, Unauthenticated } from "convex/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { ErrorBoundary } from "@/shared/components/ErrorBoundary";

export default function Home() {
	return (
		<ErrorBoundary errorType="page">
			<Box minH="100vh" bgGradient="linear(to-b, gray.50, gray.100)">
				<Container maxW="container.xl" py={16}>
					<VStack gap={12} maxW="4xl" mx="auto" textAlign="center">
						<Heading
							as="h1"
							size="2xl"
							bgGradient="linear(to-r, blue.500, blue.300)"
							bgClip="text"
							mb={6}
						>
							KADOU-DELTA
						</Heading>
						<Text fontSize="xl" color="fg.muted">
							民泊管理システム
						</Text>
						<Authenticated>
							<AuthenticatedContent />
						</Authenticated>
						<Unauthenticated>
							<SignInForm />
						</Unauthenticated>
					</VStack>
				</Container>
			</Box>
		</ErrorBoundary>
	);
}

function SignInForm() {
	return (
		<VStack gap={8}>
			<Text fontSize="lg" color="fg.muted">
				アプリケーションを利用するにはログインしてください
			</Text>
			<HStack gap={4}>
				<SignInButton mode="modal">
					<Button colorScheme="blue" size="lg" px={6}>
						ログイン
					</Button>
				</SignInButton>
				<SignUpButton mode="modal">
					<Button colorScheme="gray" variant="solid" size="lg" px={6}>
						新規登録
					</Button>
				</SignUpButton>
			</HStack>
		</VStack>
	);
}

function AuthenticatedContent() {
	const router = useRouter();

	useEffect(() => {
		// ログイン済みユーザーを即座に/appへリダイレクト
		router.push("/app");
	}, [router]);

	return (
		<VStack gap={4}>
			<Spinner size="xl" color="blue.500" />
			<Text fontSize="lg" color="fg.muted">
				アプリケーションへ移動中...
			</Text>
		</VStack>
	);
}
