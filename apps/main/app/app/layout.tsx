"use client";

import { AppLayout } from "@/shared/components/layout/AppLayout";
import { useNavigationItems } from "@/shared/hooks/useNavigationItems";

export default function ApplicationLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	const { topItems, bottomItems } = useNavigationItems();

	return (
		<AppLayout topNavItems={topItems} bottomNavItems={bottomItems}>
			{children}
		</AppLayout>
	);
}
