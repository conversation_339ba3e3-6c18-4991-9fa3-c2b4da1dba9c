"use client";

import { Box, Container, Heading, Text } from "@chakra-ui/react";
import dynamic from "next/dynamic";

// SettingsTabsを動的インポート
// SSRを無効にしてクライアントサイドでのみロード
const SettingsTabs = dynamic(
	() => import("@/features/settings").then((mod) => mod.SettingsTabs),
	{
		ssr: false,
		loading: () => (
			<Box py={10} textAlign="center">
				<Text color="fg.subtle">読み込み中...</Text>
			</Box>
		),
	},
);

export default function SettingsPage() {
	return (
		<Container maxW="container.md" py={8}>
			<Box mb={8}>
				<Heading as="h1" mb={2} color="fg">
					設定
				</Heading>
				<Text color="fg.muted">アカウントの設定を管理します</Text>
			</Box>

			<SettingsTabs />
		</Container>
	);
}
