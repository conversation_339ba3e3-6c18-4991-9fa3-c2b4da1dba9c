"use client";

import { Box } from "@chakra-ui/react";
import { useEffect } from "react";
import { ReviewAnalysisTabs } from "@/features/review-analysis";
import { handleError } from "@/shared/lib/error-handling";

export default function ReviewAnalysisPage() {
	useEffect(() => {
		try {
			document.title = "レビュー分析 | 鹿童DELTA";
		} catch (error) {
			handleError(error, {
				feature: "ReviewAnalysisPage",
				context: "Setting page title",
			});
		}
	}, []);

	return (
		<Box width="full" minHeight="100vh" bg="bg.canvas">
			<ReviewAnalysisTabs />
		</Box>
	);
}
