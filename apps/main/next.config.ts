import { withSentryConfig } from "@sentry/nextjs";
import type { NextConfig } from "next";

// Bundle Analyzerの設定
const withBundleAnalyzer = require("@next/bundle-analyzer")({
	enabled: process.env.ANALYZE === "true",
});

const nextConfig: NextConfig = {
	/* 設定オプションをここに記述 */
	// 開発サーバーへのクロスオリジンリクエストを許可
	allowedDevOrigins: ["i9"],
	experimental: {
		optimizePackageImports: ["@chakra-ui/react"],
		// webpackのメモリ使用量を削減（ビルド時間が若干増加する可能性あり）
		webpackMemoryOptimizations: true,
		// webpackビルドワーカーを明示的に有効化
		webpackBuildWorker: true,
	},
};

// Bundle Analyzerを適用してからSentryConfigを適用
const configWithAnalyzer = withBundleAnalyzer(nextConfig);

export default withSentryConfig(configWithAnalyzer, {
	// 利用可能なすべてのオプションについては以下を参照:
	// https://www.npmjs.com/package/@sentry/webpack-plugin#options

	org: "wh3at",
	project: "kadou-delta-next",

	// CIでのソースマップアップロード時のみログを出力
	silent: !process.env.CI,

	// 利用可能なすべてのオプションについては以下を参照:
	// https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

	// メモリ使用量を削減するため、依存関係のソースマップアップロードを無効化
	// 本番環境では主要なアプリケーションコードのソースマップで十分
	widenClientFileUpload: false,

	// 広告ブロッカーを回避するため、Next.jsのリライト機能を使用してSentryへのブラウザリクエストをルーティング
	// これによりサーバー負荷およびホスティング料金が増加する可能性があります
	// 注意: 設定されたルートがNext.jsミドルウェアと競合しないよう確認してください。競合するとクライアント
	// サイドエラーの報告が失敗します
	tunnelRoute: "/monitoring",

	// バンドルサイズを削減するため、Sentryロガーステートメントを自動的にtree-shake
	disableLogger: true,

	// Vercel Cron Monitorsの自動インストルメンテーションを有効化（App Routerのルートハンドラーではまだ動作しません）
	// 詳細については以下を参照:
	// https://docs.sentry.io/product/crons/
	// https://vercel.com/docs/cron-jobs
	automaticVercelMonitors: true,

	// CI環境以外ではソースマップ生成を無効化（開発環境でのメモリ使用を削減）
	sourcemaps: {
		disable: !process.env.CI,
	},
});
