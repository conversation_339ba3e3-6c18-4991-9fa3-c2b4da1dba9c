/// <reference types="vitest/config" />

import path from "node:path";
import { fileURLToPath } from "node:url";
import { storybookTest } from "@storybook/addon-vitest/vitest-plugin";
import react from "@vitejs/plugin-react";
import { defineConfig } from "vitest/config";

const dirname =
	typeof __dirname !== "undefined"
		? __dirname
		: path.dirname(fileURLToPath(import.meta.url));

// More info at: https://storybook.js.org/docs/next/writing-tests/integrations/vitest-addon
export default defineConfig({
	plugins: [react()],
	optimizeDeps: {
		// markdown-to-jsxを事前最適化から除外
		exclude: ["markdown-to-jsx"],
		// Storybookに必要な依存関係を明示的に含める
		include: ["@storybook/addon-docs", "@mdx-js/react"],
	},
	test: {
		coverage: {
			// CI環境でカバレッジを無効化（一時ファイルエラー対策）
			enabled: !process.env.CI,
			provider: "v8",
			reporter: ["text", "json", "html"],
			reportsDirectory: "./coverage",
		},
		environment: "jsdom",
		globals: true,
		setupFiles: ["./vitest.setup.ts"],
		css: false,
		passWithNoTests: true,
		// テストが見つからない場合でも成功とする
		// 注意: test.includeはStorybook 8.5+では非推奨。代わりにprojectsを使用
		exclude: [
			"**/node_modules/**",
			"**/dist/**",
			"**/.{idea,git,cache,output,temp}/**",
			"**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build}.config.*",
			"**/app/**",
		],
		projects: [
			{
				plugins: [react()],
				test: {
					name: "unit",
					include: [
						"__tests__/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}",
					],
					environment: "jsdom",
					globals: true,
					setupFiles: ["./vitest.setup.ts"],
					pool: process.env.CI ? "forks" : "threads",
					server: {
						deps: {
							inline: ["convex-test"],
						},
					},
				},
				resolve: {
					alias: [
						{
							find: "@/features",
							replacement: path.resolve(__dirname, "./src/features"),
						},
						{
							find: "@/shared",
							replacement: path.resolve(__dirname, "./src/shared"),
						},
						{
							find: "@",
							replacement: path.resolve(__dirname, "."),
						},
					],
				},
			},
			{
				extends: true,
				plugins: [
					// The plugin will run tests for the stories defined in your Storybook config
					// See options at: https://storybook.js.org/docs/next/writing-tests/integrations/vitest-addon#storybooktest
					storybookTest({
						configDir: path.join(dirname, ".storybook"),
						// CI環境用の設定
						// CIではStorybookサーバーが別途起動されることを期待
						storybookUrl: process.env.CI ? "http://localhost:6006" : undefined,
						// ローカルでは自動起動
						storybookScript: process.env.CI
							? undefined
							: "npm run storybook -- --ci",
					}),
				],
				test: {
					name: "storybook",
					browser: {
						enabled: true,
						headless: true,
						provider: "playwright",
						instances: [
							{
								browser: "chromium",
							},
						],
					},
					setupFiles: [".storybook/vitest.setup.ts"],
					// テストのタイムアウト
					testTimeout: process.env.CI ? 30000 : 10000,
					pool: "threads",
					poolOptions: {
						threads: {
							singleThread: true,
						},
					},
				},
			},
		],
	},
	resolve: {
		alias: [
			{
				find: "@/features",
				replacement: path.resolve(__dirname, "./src/features"),
			},
			{
				find: "@/shared",
				replacement: path.resolve(__dirname, "./src/shared"),
			},
			{
				find: "@",
				replacement: path.resolve(__dirname, "."),
			},
		],
	},
});
