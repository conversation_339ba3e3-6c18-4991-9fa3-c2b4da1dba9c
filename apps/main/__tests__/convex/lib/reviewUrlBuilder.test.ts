import { describe, expect, it, vi } from "vitest";
import { ConvexError } from "@/convex/lib/errors";
import {
	createBookingReviewUrl,
	type ReviewUrlBuilderInput,
} from "@/convex/lib/reviewUrlBuilder";

// コンソールのモック
const mockConsoleInfo = vi.spyOn(console, "info").mockImplementation(() => {});
const mockConsoleWarn = vi.spyOn(console, "warn").mockImplementation(() => {});

describe("createBookingReviewUrl", () => {
	describe("新規URL作成", () => {
		it("booking_com_facility_nameが存在する場合、正しいURLを生成する", () => {
			const input: ReviewUrlBuilderInput = {
				property: {
					id: "prop_123",
					name: "Test Hotel",
					booking_com_facility_name: "test-hotel-tokyo",
				},
			};

			const result = createBookingReviewUrl(input);

			expect(result).toBe(
				"https://www.booking.com/reviews/jp/hotel/test-hotel-tokyo.ja.html?r_lang=all&customer_type=total&order=completed_desc&rows=25",
			);
		});

		it("booking_com_facility_nameが存在しない場合、エラーをスローする", () => {
			const input: ReviewUrlBuilderInput = {
				property: {
					id: "prop_123",
					name: "Test Hotel",
					booking_com_facility_name: undefined,
				},
			};

			expect(() => createBookingReviewUrl(input)).toThrow(ConvexError);
			expect(() => createBookingReviewUrl(input)).toThrow(
				"Booking.com facility name is not set for this property",
			);
		});

		it("booking_com_facility_nameが空文字の場合、エラーをスローする", () => {
			const input: ReviewUrlBuilderInput = {
				property: {
					id: "prop_123",
					name: "Test Hotel",
					booking_com_facility_name: "",
				},
			};

			expect(() => createBookingReviewUrl(input)).toThrow(ConvexError);
			expect(() => createBookingReviewUrl(input)).toThrow(
				"Booking.com facility name is not set for this property",
			);
		});

		it("特殊文字を含むfacility_nameが正しくエンコードされる", () => {
			const input: ReviewUrlBuilderInput = {
				property: {
					id: "prop_123",
					name: "テストホテル",
					booking_com_facility_name: "test-hotel-東京",
				},
			};

			const result = createBookingReviewUrl(input);

			expect(result).toBe(
				"https://www.booking.com/reviews/jp/hotel/test-hotel-%E6%9D%B1%E4%BA%AC.ja.html?r_lang=all&customer_type=total&order=completed_desc&rows=25",
			);
		});
	});

	describe("既存URL更新", () => {
		it("pageパラメータが存在する場合、old_pageを設定する", () => {
			const input: ReviewUrlBuilderInput = {
				property: {
					id: "prop_123",
					name: "Test Hotel",
					booking_com_facility_name: "test-hotel-tokyo",
				},
				existingUrl:
					"https://www.booking.com/reviews/jp/hotel/test-hotel-tokyo.ja.html?page=3&rows=10",
			};

			const result = createBookingReviewUrl(input);

			// URLSearchParamsはパラメータの順序を保証しないため、個別にチェック
			const resultUrl = new URL(result);
			expect(resultUrl.pathname).toBe(
				"/reviews/jp/hotel/test-hotel-tokyo.ja.html",
			);
			expect(resultUrl.searchParams.get("page")).toBe("3");
			expect(resultUrl.searchParams.get("rows")).toBe("25");
			expect(resultUrl.searchParams.get("old_page")).toBe("2");
			expect(resultUrl.searchParams.get("order")).toBe("completed_desc");
		});

		it("pageパラメータが存在しない場合、old_pageを設定しない", () => {
			const input: ReviewUrlBuilderInput = {
				property: {
					id: "prop_123",
					name: "Test Hotel",
					booking_com_facility_name: "test-hotel-tokyo",
				},
				existingUrl:
					"https://www.booking.com/reviews/jp/hotel/test-hotel-tokyo.ja.html?rows=10",
			};

			const result = createBookingReviewUrl(input);

			// URLSearchParamsはパラメータの順序を保証しないため、個別にチェック
			const resultUrl = new URL(result);
			expect(resultUrl.pathname).toBe(
				"/reviews/jp/hotel/test-hotel-tokyo.ja.html",
			);
			expect(resultUrl.searchParams.get("rows")).toBe("25");
			expect(resultUrl.searchParams.get("order")).toBe("completed_desc");
			expect(resultUrl.searchParams.get("old_page")).toBeNull();
		});

		it("rowsパラメータを25に統一する", () => {
			const input: ReviewUrlBuilderInput = {
				property: {
					id: "prop_123",
					name: "Test Hotel",
					booking_com_facility_name: "test-hotel-tokyo",
				},
				existingUrl:
					"https://www.booking.com/reviews/jp/hotel/test-hotel-tokyo.ja.html?rows=50",
			};

			const result = createBookingReviewUrl(input);

			// URLSearchParamsはパラメータの順序を保証しないため、個別にチェック
			const resultUrl = new URL(result);
			expect(resultUrl.pathname).toBe(
				"/reviews/jp/hotel/test-hotel-tokyo.ja.html",
			);
			expect(resultUrl.searchParams.get("rows")).toBe("25");
			expect(resultUrl.searchParams.get("order")).toBe("completed_desc");
		});

		it("その他のパラメータは保持されるが、orderはcompleted_descに上書きされる", () => {
			const input: ReviewUrlBuilderInput = {
				property: {
					id: "prop_123",
					name: "Test Hotel",
					booking_com_facility_name: "test-hotel-tokyo",
				},
				existingUrl:
					"https://www.booking.com/reviews/jp/hotel/test-hotel-tokyo.ja.html?r_lang=ja&customer_type=leisure&order=score_desc&rows=10",
			};

			const result = createBookingReviewUrl(input);

			expect(result).toBe(
				"https://www.booking.com/reviews/jp/hotel/test-hotel-tokyo.ja.html?r_lang=ja&customer_type=leisure&order=completed_desc&rows=25",
			);
		});

		it("orderパラメータが存在しない場合、completed_descを追加する", () => {
			const input: ReviewUrlBuilderInput = {
				property: {
					id: "prop_123",
					name: "Test Hotel",
					booking_com_facility_name: "test-hotel-tokyo",
				},
				existingUrl:
					"https://www.booking.com/reviews/jp/hotel/test-hotel-tokyo.ja.html?r_lang=ja&customer_type=leisure",
			};

			const result = createBookingReviewUrl(input);

			const resultUrl = new URL(result);
			expect(resultUrl.searchParams.get("order")).toBe("completed_desc");
			expect(resultUrl.searchParams.get("r_lang")).toBe("ja");
			expect(resultUrl.searchParams.get("customer_type")).toBe("leisure");
			expect(resultUrl.searchParams.get("rows")).toBe("25");
		});

		it("URLが変更された場合、ログを出力する", () => {
			const input: ReviewUrlBuilderInput = {
				property: {
					id: "prop_123",
					name: "Test Hotel",
					booking_com_facility_name: "test-hotel-tokyo",
				},
				existingUrl:
					"https://www.booking.com/reviews/jp/hotel/test-hotel-tokyo.ja.html?page=2&rows=10",
			};

			createBookingReviewUrl(input);

			expect(mockConsoleInfo).toHaveBeenCalledWith(
				"Modified URL parameters",
				expect.objectContaining({
					original_url: input.existingUrl,
					modified_url: expect.stringContaining("rows=25"),
					modifications: expect.objectContaining({
						rows: "25",
						order: "completed_desc",
						page: "2",
						old_page: "1",
					}),
				}),
			);
		});

		it("URLが変更されない場合、ログを出力しない", () => {
			mockConsoleInfo.mockClear();

			const input: ReviewUrlBuilderInput = {
				property: {
					id: "prop_123",
					name: "Test Hotel",
					booking_com_facility_name: "test-hotel-tokyo",
				},
				existingUrl:
					"https://www.booking.com/reviews/jp/hotel/test-hotel-tokyo.ja.html?rows=25&order=completed_desc",
			};

			createBookingReviewUrl(input);

			expect(mockConsoleInfo).not.toHaveBeenCalled();
		});

		it("facility_nameが存在しない場合、警告ログを出力する", () => {
			const input: ReviewUrlBuilderInput = {
				property: {
					id: "prop_123",
					name: "Test Hotel",
					booking_com_facility_name: undefined,
				},
			};

			expect(() => createBookingReviewUrl(input)).toThrow();

			expect(mockConsoleWarn).toHaveBeenCalledWith(
				"booking_com_facility_name is missing",
				{
					property_id: "prop_123",
					name: "Test Hotel",
				},
			);
		});
	});

	describe("エッジケース", () => {
		it("page=1の場合、old_page=0を設定する", () => {
			const input: ReviewUrlBuilderInput = {
				property: {
					id: "prop_123",
					name: "Test Hotel",
					booking_com_facility_name: "test-hotel-tokyo",
				},
				existingUrl:
					"https://www.booking.com/reviews/jp/hotel/test-hotel-tokyo.ja.html?page=1",
			};

			const result = createBookingReviewUrl(input);

			// URLSearchParamsはパラメータの順序を保証しないため、個別にチェック
			const resultUrl = new URL(result);
			expect(resultUrl.pathname).toBe(
				"/reviews/jp/hotel/test-hotel-tokyo.ja.html",
			);
			expect(resultUrl.searchParams.get("page")).toBe("1");
			expect(resultUrl.searchParams.get("old_page")).toBe("0");
			expect(resultUrl.searchParams.get("rows")).toBe("25");
		});

		it("無効なURLの場合、エラーをスローする", () => {
			const input: ReviewUrlBuilderInput = {
				property: {
					id: "prop_123",
					name: "Test Hotel",
					booking_com_facility_name: "test-hotel-tokyo",
				},
				existingUrl: "not-a-valid-url",
			};

			expect(() => createBookingReviewUrl(input)).toThrow();
		});
	});
});
