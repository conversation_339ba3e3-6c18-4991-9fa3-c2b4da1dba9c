import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
// テスト対象のモジュール
import {
	extractPropertyUrl,
	extractSlugFromUrl,
	generateSearchUrl,
	scrapeBookingComSlug,
} from "@/convex/lib/bookingComSlugScraper";
import type { ConvexLogger } from "@/convex/lib/logging";
import type { ScrapingAntWrapper } from "@/convex/lib/scrapingAnt";

describe("bookingComSlugScraper", () => {
	let mockLogger: ConvexLogger;

	beforeEach(() => {
		mockLogger = {
			info: vi.fn(),
			warn: vi.fn(),
			error: vi.fn(),
			debug: vi.fn(),
		} as unknown as ConvexLogger;
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	describe("generateSearchUrl", () => {
		it("should generate search URL with encoded property name", () => {
			const propertyName =
				"⭐️加美屋リゾート伊勢おかげテラス KamiyaResOkageTerrass";
			const url = generateSearchUrl(propertyName);

			expect(url).toBe(
				"https://www.booking.com/searchresults.ja.html?ss=%22%E2%AD%90%EF%B8%8F%E5%8A%A0%E7%BE%8E%E5%B1%8B%E3%83%AA%E3%82%BE%E3%83%BC%E3%83%88%E4%BC%8A%E5%8B%A2%E3%81%8A%E3%81%8B%E3%81%92%E3%83%86%E3%83%A9%E3%82%B9+KamiyaResOkageTerrass%22&group_adults=2&no_rooms=1&group_children=0",
			);
		});

		it("should handle empty property name", () => {
			const url = generateSearchUrl("");
			expect(url).toBe(
				"https://www.booking.com/searchresults.ja.html?ss=%22%22&group_adults=2&no_rooms=1&group_children=0",
			);
		});

		it("should properly encode special characters", () => {
			const propertyName = "Hotel & Resort #1";
			const url = generateSearchUrl(propertyName);
			expect(url).toContain("ss=%22Hotel+%26+Resort+%231%22");
		});
	});

	describe("extractPropertyUrl", () => {
		it("should extract property URL from search results HTML", async () => {
			// 実際のBooking.com検索結果の簡略化したHTML
			const html = `
				<div>
					<a class="js-sr-hotel-link hotel_name_link url"
					   href="https://www.booking.com/hotel/jp/jia-mei-wu-rizotoyi-shi-shen-gong-okageterasu.ja.html?aid=304142">
						<span class="sr-hotel__name">
							⭐️加美屋リゾート伊勢おかげテラス KamiyaResOkageTerrass
						</span>
					</a>
				</div>
			`;

			const url = await extractPropertyUrl(html);
			expect(url).toBe(
				"https://www.booking.com/hotel/jp/jia-mei-wu-rizotoyi-shi-shen-gong-okageterasu.ja.html",
			);
		});

		it("should return null when no property URL found", async () => {
			const html = `<div>No hotel links here</div>`;
			const url = await extractPropertyUrl(html);
			expect(url).toBeNull();
		});

		it("should extract the first URL when multiple properties exist", async () => {
			const html = `
				<div>
					<a href="https://www.booking.com/hotel/jp/first-hotel.ja.html" 
					   class="js-sr-hotel-link hotel_name_link url">First Hotel</a>
					<a href="https://www.booking.com/hotel/jp/second-hotel.ja.html" 
					   class="js-sr-hotel-link hotel_name_link url">Second Hotel</a>
				</div>
			`;

			const url = await extractPropertyUrl(html);
			expect(url).toBe("https://www.booking.com/hotel/jp/first-hotel.ja.html");
		});
	});

	describe("extractSlugFromUrl", () => {
		it("should extract slug from property URL", () => {
			const url =
				"https://www.booking.com/hotel/jp/jia-mei-wu-rizotoyi-shi-shen-gong-okageterasu.ja.html";
			const slug = extractSlugFromUrl(url);
			expect(slug).toBe("jia-mei-wu-rizotoyi-shi-shen-gong-okageterasu");
		});

		it("should return null for invalid URL", () => {
			const url = "https://example.com/not-a-hotel";
			const slug = extractSlugFromUrl(url);
			expect(slug).toBeNull();
		});

		it("should extract slug from URL with query parameters", () => {
			const url =
				"https://www.booking.com/hotel/jp/test-hotel.ja.html?aid=304142&ucfs=1";
			const slug = extractSlugFromUrl(url);
			expect(slug).toBe("test-hotel");
		});

		it("should handle different country codes", () => {
			const urls = [
				{
					url: "https://www.booking.com/hotel/us/american-hotel.en-us.html",
					expected: "american-hotel",
				},
				{
					url: "https://www.booking.com/hotel/fr/french-hotel.fr.html",
					expected: "french-hotel",
				},
				{
					url: "https://www.booking.com/hotel/cn/chinese-hotel.zh-cn.html",
					expected: "chinese-hotel",
				},
			];

			urls.forEach(({ url, expected }) => {
				const slug = extractSlugFromUrl(url);
				expect(slug).toBe(expected);
			});
		});
	});

	describe("scrapeBookingComSlug", () => {
		let mockScrapingAnt: ScrapingAntWrapper;

		beforeEach(() => {
			mockScrapingAnt = {
				scrapeUrl: vi.fn(),
			} as unknown as ScrapingAntWrapper;
		});

		it("should successfully scrape property slug", async () => {
			const propertyName =
				"⭐️加美屋リゾート伊勢おかげテラス KamiyaResOkageTerrass";
			const mockHtml = `
				<div>
					<a href="https://www.booking.com/hotel/jp/jia-mei-wu-rizotoyi-shi-shen-gong-okageterasu.ja.html?aid=304142" 
					   class="js-sr-hotel-link hotel_name_link url">
						${propertyName}
					</a>
				</div>
			`;

			(mockScrapingAnt.scrapeUrl as any).mockResolvedValue({
				content: mockHtml,
				statusCode: 200,
			});

			const result = await scrapeBookingComSlug(
				propertyName,
				mockScrapingAnt,
				mockLogger,
			);

			expect(result).toEqual({
				success: true,
				slug: "jia-mei-wu-rizotoyi-shi-shen-gong-okageterasu",
				propertyUrl:
					"https://www.booking.com/hotel/jp/jia-mei-wu-rizotoyi-shi-shen-gong-okageterasu.ja.html",
			});

			expect(mockScrapingAnt.scrapeUrl).toHaveBeenCalledWith(
				expect.stringContaining("searchresults.ja.html"),
				expect.objectContaining({
					browser: true,
					wait_for_selector: ".js-sr-hotel-link",
				}),
			);

			expect(mockLogger.info).toHaveBeenCalledWith(
				"Booking.com施設スラッグ取得開始",
				expect.objectContaining({ propertyName }),
			);

			expect(mockLogger.info).toHaveBeenCalledWith(
				"施設スラッグ取得成功",
				expect.objectContaining({
					slug: "jia-mei-wu-rizotoyi-shi-shen-gong-okageterasu",
				}),
			);
		});

		it("should return error when no property found", async () => {
			const propertyName = "存在しない施設";
			const mockHtml = `<div>No results found</div>`;

			(mockScrapingAnt.scrapeUrl as any).mockResolvedValue({
				content: mockHtml,
				statusCode: 200,
			});

			const result = await scrapeBookingComSlug(
				propertyName,
				mockScrapingAnt,
				mockLogger,
			);

			expect(result).toEqual({
				success: false,
				error: "施設が見つかりませんでした",
			});

			expect(mockLogger.warn).toHaveBeenCalledWith(
				"施設URLが見つかりませんでした",
				expect.objectContaining({ propertyName }),
			);
		});

		it("should handle scraping errors", async () => {
			const propertyName = "テスト施設";
			const error = new Error("Network error");

			(mockScrapingAnt.scrapeUrl as any).mockRejectedValue(error);

			const result = await scrapeBookingComSlug(
				propertyName,
				mockScrapingAnt,
				mockLogger,
			);

			expect(result).toEqual({
				success: false,
				error: "Network error",
			});

			expect(mockLogger.error).toHaveBeenCalledWith(
				"施設スラッグ取得エラー",
				expect.objectContaining({
					propertyName,
					error: "Network error",
				}),
			);
		});

		it("should handle non-booking.com URLs in search results", async () => {
			const propertyName = "テスト施設";
			const mockHtml = `
				<div>
					<a href="https://example.com/invalid-url" class="js-sr-hotel-link">
						${propertyName}
					</a>
				</div>
			`;

			(mockScrapingAnt.scrapeUrl as any).mockResolvedValue({
				content: mockHtml,
				statusCode: 200,
			});

			const result = await scrapeBookingComSlug(
				propertyName,
				mockScrapingAnt,
				mockLogger,
			);

			expect(result).toEqual({
				success: false,
				error: "施設が見つかりませんでした",
			});

			expect(mockLogger.warn).toHaveBeenCalledWith(
				"施設URLが見つかりませんでした",
				expect.objectContaining({ propertyName }),
			);
		});

		it("should handle invalid booking.com URL format", async () => {
			const propertyName = "テスト施設";
			// 有効なBooking.comの施設URLだが、期待される形式ではない（国コードがない）
			const mockHtml = `
				<div>
					<a href="https://www.booking.com/hotel/invalid-format.html" class="js-sr-hotel-link">
						${propertyName}
					</a>
				</div>
			`;

			(mockScrapingAnt.scrapeUrl as any).mockResolvedValue({
				content: mockHtml,
				statusCode: 200,
			});

			const result = await scrapeBookingComSlug(
				propertyName,
				mockScrapingAnt,
				mockLogger,
			);

			expect(result).toEqual({
				success: false,
				error: "施設URLからスラッグを抽出できませんでした",
			});

			expect(mockLogger.error).toHaveBeenCalledWith(
				"施設URLからスラッグを抽出できませんでした",
				expect.objectContaining({
					propertyUrl: "https://www.booking.com/hotel/invalid-format.html",
				}),
			);
		});
	});
});
