/**
 * レビューハッシュ生成の重複問題を再現するテスト
 *
 * このテストは、同じレビューが異なるタイミングで処理されると
 * 異なるuniqueHashが生成される問題を再現します。
 */

import { beforeEach, describe, expect, it, vi } from "vitest";
import type { Id } from "../../../convex/_generated/dataModel";
import type { ConvexLogger } from "../../../convex/lib/logging";
import { createReviewParser } from "../../../convex/lib/reviewParser";
import { generateReviewHash } from "../../../convex/model/otaReviews";

// モックロガー
const mockLogger: ConvexLogger = {
	info: vi.fn(),
	error: vi.fn(),
	warn: vi.fn(),
	debug: vi.fn(),
	addContext: vi.fn(),
	setArgs: vi.fn(),
	startTimer: vi.fn(() => () => {}),
} as unknown as ConvexLogger;

describe("レビューハッシュ生成の重複問題", () => {
	beforeEach(() => {
		// 各テストの前にタイマーをリセット
		vi.clearAllTimers();
		vi.useRealTimers();
	});

	describe("日付解析失敗によるレビュースキップ", () => {
		it("日付要素が見つからない場合、レビューはスキップされる", async () => {
			const parser = createReviewParser(mockLogger);

			// 日付要素のないHTML
			const htmlWithoutDate = `
				<html>
					<body>
						<ul class="review_list">
							<li class="review_item">
								<span class="review-score-badge">8.5</span>
								<div class="review_item_header_content">
									<span>素晴らしい滞在</span>
								</div>
								<p class="review_pos">
									<span itemprop="reviewBody">清潔で快適な部屋でした。</span>
								</p>
								<p class="reviewer_name">
									<span>山田太郎</span>
								</p>
								<!-- 日付要素なし -->
							</li>
						</ul>
					</body>
				</html>
			`;

			const result = parser.parseHtml(
				htmlWithoutDate,
				"booking.com",
				"https://example.com",
			);

			// 日付がないレビューはスキップされるため、レビュー数は0になる
			expect(result.reviews).toHaveLength(0);

			// エラーログが記録されていることを確認
			expect(mockLogger.error).toHaveBeenCalledWith(
				expect.stringContaining("レビュー日付要素が見つかりません"),
				expect.objectContaining({
					selector: "p.review_item_date",
					reviewerName: "山田太郎",
					reviewScore: 8.5,
				}),
			);
		});

		it("日付形式が不正な場合、レビューはスキップされる", async () => {
			const parser = createReviewParser(mockLogger);

			// 不正な日付形式のHTML
			const htmlWithInvalidDate = `
				<html>
					<body>
						<ul class="review_list">
							<li class="review_item">
								<span class="review-score-badge">9.0</span>
								<p class="review_pos">
									<span itemprop="reviewBody">素晴らしいホテルでした。</span>
								</p>
								<p class="reviewer_name">
									<span>佐藤花子</span>
								</p>
								<p class="review_item_date">不正な日付形式です</p>
							</li>
						</ul>
					</body>
				</html>
			`;

			const result = parser.parseHtml(
				htmlWithInvalidDate,
				"booking.com",
				"https://example.com",
			);

			// 日付解析に失敗したレビューはスキップされるため、レビュー数は0になる
			expect(result.reviews).toHaveLength(0);

			// エラーログが記録されていることを確認
			expect(mockLogger.error).toHaveBeenCalledWith(
				expect.stringContaining("日付解析に失敗しました"),
				expect.objectContaining({
					dateString: "不正な日付形式です",
					triedPatterns: expect.arrayContaining([
						"YYYY年MM月DD日",
						"ISO format",
						"LLLL d, yyyy",
					]),
				}),
			);

			// レビュー日付の解析失敗ログも確認
			expect(mockLogger.error).toHaveBeenCalledWith(
				expect.stringContaining("レビュー日付の解析に失敗"),
				expect.objectContaining({
					reviewDateText: "不正な日付形式です",
					reviewerName: "佐藤花子",
					reviewScore: 9.0,
				}),
			);
		});
	});

	it("reviewDateが同じなら同じハッシュが生成される", () => {
		const reviewData = {
			otaId: "ota-789" as Id<"otaMaster">,
			beds24PropertyId: "prop-012" as Id<"beds24Properties">,
			reviewDate: new Date("2024-03-01T15:30:00Z").getTime(),
			reviewContent: "良いホテルでした。",
		};

		// 同じデータで複数回ハッシュを生成
		const hash1 = generateReviewHash(reviewData);
		const hash2 = generateReviewHash(reviewData);
		const hash3 = generateReviewHash(reviewData);

		// すべて同じハッシュになることを確認
		expect(hash1).toBe(hash2);
		expect(hash2).toBe(hash3);
	});

	it("レビュー内容の一部が異なれば異なるハッシュになる", () => {
		const baseData = {
			otaId: "ota-aaa" as Id<"otaMaster">,
			beds24PropertyId: "prop-bbb" as Id<"beds24Properties">,
			reviewDate: new Date("2024-04-01T09:00:00Z").getTime(),
		};

		const hash1 = generateReviewHash({
			...baseData,
			reviewContent: "素晴らしいホテルでした。",
		});

		const hash2 = generateReviewHash({
			...baseData,
			reviewContent: "素晴らしいホテルでした！", // 感嘆符を追加
		});

		// 内容が少し違うだけで異なるハッシュになることを確認
		expect(hash1).not.toBe(hash2);
	});
});

describe("正常な日付解析のテスト", () => {
	it("日本語形式の日付が正しく解析される", () => {
		const parser = createReviewParser(mockLogger);

		const htmlWithJapaneseDate = `
				<html>
					<body>
						<ul class="review_list">
							<li class="review_item">
								<span class="review-score-badge">9.5</span>
								<p class="review_pos">
									<span itemprop="reviewBody">とても良かったです。</span>
								</p>
								<p class="reviewer_name">
									<span>鈴木一郎</span>
								</p>
								<p class="review_item_date">2024年12月25日</p>
							</li>
						</ul>
					</body>
				</html>
			`;

		const result = parser.parseHtml(
			htmlWithJapaneseDate,
			"booking.com",
			"https://example.com",
		);

		// レビューが正しく解析されることを確認
		expect(result.reviews).toHaveLength(1);
		const review = result.reviews[0];

		// 日付が正しく解析されていることを確認
		// 注意: DateTime.localは日本時間で作成されるが、toMillis()でUTCに変換される
		// 2024-12-25 00:00:00 JST = 2024-12-24 15:00:00 UTC
		const reviewDateUTC = new Date(review.reviewDate);
		expect(reviewDateUTC.getUTCFullYear()).toBe(2024);
		expect(reviewDateUTC.getUTCMonth() + 1).toBe(12);
		expect(reviewDateUTC.getUTCDate()).toBe(24); // UTC日付は1日前

		// 成功ログが記録されていることを確認
		expect(mockLogger.info).toHaveBeenCalledWith(
			expect.stringContaining("日本語形式の日付解析に成功"),
			expect.objectContaining({
				dateString: "2024年12月25日",
				year: 2024,
				month: 12,
				day: 25,
			}),
		);
	});

	it.each([
		{
			format: "ISO",
			date: "2024-11-15",
			expectedLog: "ISO形式の日付解析に成功",
		},
		{
			format: "英語長形式",
			date: "November 15, 2024",
			expectedLog: "英語形式の日付解析に成功",
		},
		{
			format: "英語短形式",
			date: "Nov 15, 2024",
			expectedLog: "代替形式での日付解析に成功",
		},
		{
			format: "日付前置",
			date: "15 November 2024",
			expectedLog: "代替形式での日付解析に成功",
		},
	])("$format（$date）が正しく解析される", ({ date, expectedLog }) => {
		// 各テストケースごとにモックをリセット
		vi.clearAllMocks();
		const parser = createReviewParser(mockLogger);

		const html = `
				<html>
					<body>
						<ul class="review_list">
							<li class="review_item">
								<span class="review-score-badge">8.0</span>
								<p class="review_pos">
									<span itemprop="reviewBody">Good</span>
								</p>
								<p class="reviewer_name">
									<span>Test User</span>
								</p>
								<p class="review_item_date">${date}</p>
							</li>
						</ul>
					</body>
				</html>
			`;

		const result = parser.parseHtml(html, "booking.com", "https://example.com");
		expect(result.reviews).toHaveLength(1);

		// 解析成功ログが記録されていることを確認
		expect(mockLogger.info).toHaveBeenCalledWith(
			expect.stringContaining(expectedLog),
			expect.objectContaining({
				dateString: date,
			}),
		);

		// レビュー日付の解析成功ログも確認
		expect(mockLogger.info).toHaveBeenCalledWith(
			expect.stringContaining("レビュー日付の解析に成功"),
			expect.objectContaining({
				originalText: date,
				reviewerName: "Test User",
			}),
		);
	});
});
