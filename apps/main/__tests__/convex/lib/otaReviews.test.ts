import { convexTest } from "convex-test";
import { beforeEach, describe, expect, it } from "vitest";
import { api, internal } from "@/convex/_generated/api";
import type { Id } from "@/convex/_generated/dataModel";
import schema from "@/convex/schema";
import { modules } from "../test.setup";
import { createUserIdForDatabase } from "./test-helpers";

describe("otaReviews", () => {
	let t: ReturnType<typeof convexTest>;

	beforeEach(() => {
		t = convexTest(schema, modules);
	});

	describe("getReviewSyncTask", () => {
		// テスト用の固定propertyId
		const testPropertyId = "jh7property123" as Id<"beds24Properties">;

		it("存在しないタスクIDでnullを返す", async () => {
			// syncQueueテーブル用のIDを作成（実際には存在しない）
			const firstTaskId = await t.run(async (ctx) => {
				return await ctx.db.insert("syncQueue", {
					userId: "test-user",
					jobType: "sync_reviews",
					priority: 1,
					status: "pending",
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// IDを削除してから、そのIDで検索
			await t.run(async (ctx) => {
				await ctx.db.delete(firstTaskId);
			});

			const result = await t.query(
				internal.otaReviewsInternal.getReviewSyncTask,
				{
					taskId: firstTaskId,
				},
			);

			expect(result).toBeNull();
		});

		it("jobTypeがsync_reviews以外のタスクでエラーを返す", async () => {
			// sync_propertiesタイプのタスクを作成
			const taskId = await t.run(async (ctx) => {
				return await ctx.db.insert("syncQueue", {
					userId: "test-user",
					jobType: "sync_properties",
					priority: 1,
					status: "pending",
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// タスクを取得
			const result = await t.query(
				internal.otaReviewsInternal.getReviewSyncTask,
				{
					taskId,
				},
			);

			expect(result).toBeNull();
		});

		it("ステータスがprocessingのタスクも正常に取得できる", async () => {
			// processingステータスのタスクを作成
			const taskId = await t.run(async (ctx) => {
				return await ctx.db.insert("syncQueue", {
					userId: "test-user",
					jobType: "sync_reviews",
					priority: 1,
					status: "processing",
					attempts: 1,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					startedAt: Date.now(),
					metadata: {
						url: "https://booking.com/hotel/test/reviews",
						otaId: "jh7abc123xyz456def789ghi01" as Id<"otaMaster">,
						otaType: "booking.com",
						pageNumber: 1,
						propertyId: testPropertyId,
					},
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// タスクを取得
			const result = await t.query(
				internal.otaReviewsInternal.getReviewSyncTask,
				{
					taskId,
				},
			);

			// processingステータスでも正常に取得できることを確認
			expect(result).not.toBeNull();
			expect(result?.task.jobType).toBe("sync_reviews");
			expect(result?.task.status).toBe("processing");
			expect(result?.metadata.url).toBe(
				"https://booking.com/hotel/test/reviews",
			);
		});

		it("メタデータが不正な場合にエラーを返す", async () => {
			// メタデータが不正なタスクを作成
			const taskId = await t.run(async (ctx) => {
				return await ctx.db.insert("syncQueue", {
					userId: "test-user",
					jobType: "sync_reviews",
					priority: 1,
					status: "pending",
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					metadata: {
						// urlが欠けている
						otaId: "jh7abc123xyz456def789ghi01" as Id<"otaMaster">,
						otaType: "booking.com",
						pageNumber: 1,
						propertyId: testPropertyId,
					},
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// タスクを取得
			const result = await t.query(
				internal.otaReviewsInternal.getReviewSyncTask,
				{
					taskId,
				},
			);

			expect(result).toBeNull();
		});

		it("正常なレビュー同期タスクを取得できる", async () => {
			const otaId = "jh7abc123xyz456def789ghi01" as Id<"otaMaster">;

			// 正常なタスクを作成
			const taskId = await t.run(async (ctx) => {
				return await ctx.db.insert("syncQueue", {
					userId: "test-user",
					jobType: "sync_reviews",
					priority: 1,
					status: "pending",
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					metadata: {
						url: "https://booking.com/hotel/test/reviews",
						otaId,
						otaType: "booking.com",
						pageNumber: 1,
						maxPages: 10,
						propertyId: testPropertyId,
					},
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// タスクを取得
			const result = await t.query(
				internal.otaReviewsInternal.getReviewSyncTask,
				{
					taskId,
				},
			);

			expect(result).not.toBeNull();
			expect(result?.task.jobType).toBe("sync_reviews");
			expect(result?.task.status).toBe("pending");
			expect(result?.metadata.url).toBe(
				"https://booking.com/hotel/test/reviews",
			);
			expect(result?.metadata.otaType).toBe("booking.com");
			expect(result?.metadata.pageNumber).toBe(1);
			expect(result?.metadata.maxPages).toBe(10);
		});
	});

	describe("processReviewBatch", () => {
		let propertyId: Id<"beds24Properties">;
		let otaId: Id<"otaMaster">;
		let taskId: Id<"syncQueue">;
		let userId: string;

		beforeEach(async () => {
			userId = "user_test123456789";

			// OTAマスタを作成
			otaId = await t.run(async (ctx) => {
				return await ctx.db.insert("otaMaster", {
					fullName: "Booking.com",
					shortName: "Booking",
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// 施設を作成
			propertyId = await t.run(async (ctx) => {
				return await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "123456",
					beds24PropertyKey: "test-key",
					name: "Test Hotel",
					propertyType: "hotel",
					currency: "JPY",
					country: "Japan",
					city: "Tokyo",
					data: {},
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
					isDeleted: false,
				});
			});

			// ユーザーと施設の関連を作成
			await t.run(async (ctx) => {
				await ctx.db.insert("userProperties", {
					userId,
					propertyId,
					createdAt: Date.now(),
				});
			});

			// 同期タスクを作成
			taskId = await t.run(async (ctx) => {
				return await ctx.db.insert("syncQueue", {
					userId,
					jobType: "sync_reviews",
					priority: 1,
					status: "processing",
					attempts: 1,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					startedAt: Date.now(),
					metadata: {
						url: "https://booking.com/hotel/test/reviews",
						otaId,
						otaType: "booking.com",
						pageNumber: 1,
						propertyId,
					},
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});
		});

		it("空のレビュー配列で処理できる", async () => {
			const result = await t.mutation(
				internal.otaReviewsInternal.processReviewBatch,
				{
					taskId,
					reviews: [],
					hasNextPage: false,
				},
			);

			expect(result.processedCount).toBe(0);
			expect(result.createdCount).toBe(0);
			expect(result.updatedCount).toBe(0);
			expect(result.nextTaskId).toBeUndefined();

			// タスクのステータスが更新されているか確認
			const task = await t.run(async (ctx) => {
				return await ctx.db.get(taskId);
			});
			expect(task?.status).toBe("completed");
			expect(task?.completedAt).toBeDefined();
		});

		it("新規レビューを正常に保存できる", async () => {
			const result = await t.mutation(
				internal.otaReviewsInternal.processReviewBatch,
				{
					taskId,
					reviews: [
						{
							reviewId: "review-1",
							score: 8.5,
							title: "Great hotel",
							content: "Really enjoyed my stay",
							reviewerName: "John Doe",
							reviewerCountry: "USA",
							reviewDate: Date.now() - 86400000, // 1日前
						},
						{
							reviewId: "review-2",
							score: 9.0,
							content: "Excellent service",
							contentStructured: {
								positive: "Staff was friendly",
								negative: "Room was a bit small",
							},
							reviewerName: "Jane Smith",
							reviewerCountry: "UK",
							reviewDate: Date.now() - 172800000, // 2日前
						},
					],
					hasNextPage: false,
				},
			);

			expect(result.processedCount).toBe(2);
			expect(result.createdCount).toBe(2);
			expect(result.updatedCount).toBe(0);
			expect(result.nextTaskId).toBeUndefined();

			// 保存されたレビューを確認
			const reviews = await t.run(async (ctx) => {
				// テスト環境ではインデックスの型推論に問題があるため、
				// フィルタリングで代替
				const allReviews = await ctx.db.query("otaReviews").collect();
				return allReviews.filter(
					(review) => review.beds24PropertyId === propertyId,
				);
			});

			expect(reviews).toHaveLength(2);
			expect(reviews[0].score).toBe(8.5);
			expect(reviews[0].title).toBe("Great hotel");
			expect(reviews[1].score).toBe(9.0);
		});

		it("次ページがある場合に新しいタスクを作成する", async () => {
			const result = await t.mutation(
				internal.otaReviewsInternal.processReviewBatch,
				{
					taskId,
					reviews: [
						{
							reviewId: "review-1",
							score: 8.5,
							content: "Good hotel",
							reviewerName: "Test User",
							reviewDate: Date.now(),
						},
					],
					hasNextPage: true,
				},
			);

			expect(result.processedCount).toBe(1);
			expect(result.nextTaskId).toBeDefined();

			// 新しいタスクが作成されているか確認
			const nextTask = await t.run(async (ctx) => {
				return await ctx.db.get(result.nextTaskId!);
			});
			expect(nextTask).toBeDefined();
			expect(nextTask?.status).toBe("pending");
			expect((nextTask?.metadata as any)?.pageNumber).toBe(2);
		});

		it("maxPagesに達した場合は次ページタスクを作成しない", async () => {
			// maxPagesを1に設定したタスクを作成
			await t.run(async (ctx) => {
				await ctx.db.patch(taskId, {
					metadata: {
						url: "https://booking.com/hotel/test/reviews",
						otaId,
						otaType: "booking.com",
						pageNumber: 1,
						maxPages: 1,
					},
				});
			});

			const result = await t.mutation(
				internal.otaReviewsInternal.processReviewBatch,
				{
					taskId,
					reviews: [
						{
							reviewId: "review-1",
							score: 8.5,
							content: "Good hotel",
							reviewerName: "Test User",
							reviewDate: Date.now(),
						},
					],
					hasNextPage: true,
				},
			);

			expect(result.processedCount).toBe(1);
			expect(result.nextTaskId).toBeUndefined();
		});

		it("エラーが発生した場合にタスクステータスをfailedに更新する", async () => {
			// タスクIDを無効にして、エラーを発生させる
			const invalidTaskId = await t.run(async (ctx) => {
				// 存在しないタスクIDを作成して削除
				const tempId = await ctx.db.insert("syncQueue", {
					userId: "test-user",
					jobType: "sync_reviews",
					priority: 1,
					status: "pending",
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
				await ctx.db.delete(tempId);
				return tempId;
			});

			// 存在しないタスクIDでミューテーションを実行
			try {
				await t.mutation(internal.otaReviewsInternal.processReviewBatch, {
					taskId: invalidTaskId,
					reviews: [
						{
							reviewId: "review-1",
							score: 8.5,
							content: "Test",
							reviewerName: "Test User",
							reviewDate: Date.now(),
						},
					],
					hasNextPage: false,
				});
				// エラーが期待される
				expect.fail("Should throw an error");
			} catch (error: any) {
				// エラーが投げられることを確認
				// 存在しないドキュメントへのPatchでエラーが発生
				expect(error.message || error.toString()).toContain(
					"Patch on non-existent document",
				);
			}
		});
	});

	describe("getMonthlyReviewStatsByProperties", () => {
		let userId: string;
		let subject: string; // withIdentityで使用するためのsubject
		let propertyId1: Id<"beds24Properties">;
		let propertyId2: Id<"beds24Properties">;
		let propertyId3: Id<"beds24Properties">; // アクセス権限のない施設
		let otaId: Id<"otaMaster">;

		beforeEach(async () => {
			// テスト用のsubjectを定義
			subject = "user_test123456789";
			// 本番環境と同じtokenIdentifier形式をデータベースに保存
			userId = createUserIdForDatabase(subject);

			// OTAマスタを作成
			otaId = await t.run(async (ctx) => {
				return await ctx.db.insert("otaMaster", {
					fullName: "Booking.com",
					shortName: "Booking",
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// 施設を作成
			propertyId1 = await t.run(async (ctx) => {
				return await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "123456",
					beds24PropertyKey: "test-key-1",
					name: "Test Hotel 1",
					propertyType: "hotel",
					currency: "JPY",
					country: "Japan",
					city: "Tokyo",
					data: {},
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
					isDeleted: false,
				});
			});

			propertyId2 = await t.run(async (ctx) => {
				return await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "789012",
					beds24PropertyKey: "test-key-2",
					name: "Test Hotel 2",
					propertyType: "hotel",
					currency: "JPY",
					country: "Japan",
					city: "Osaka",
					data: {},
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
					isDeleted: false,
				});
			});

			propertyId3 = await t.run(async (ctx) => {
				return await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "345678",
					beds24PropertyKey: "test-key-3",
					name: "Test Hotel 3",
					propertyType: "hotel",
					currency: "JPY",
					country: "Japan",
					city: "Kyoto",
					data: {},
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
					isDeleted: false,
				});
			});

			// ユーザーと施設の関連を作成（propertyId1とpropertyId2のみ）
			// userIdにはtokenIdentifier形式を使用
			await t.run(async (ctx) => {
				await ctx.db.insert("userProperties", {
					userId, // tokenIdentifier形式
					propertyId: propertyId1,
					createdAt: Date.now(),
				});
				await ctx.db.insert("userProperties", {
					userId, // tokenIdentifier形式
					propertyId: propertyId2,
					createdAt: Date.now(),
				});
			});
		});

		it("認証がない場合はエラーになる", async () => {
			await expect(
				t.query(api.otaReviews.getMonthlyReviewStatsByProperties, {
					propertyIds: [propertyId1],
				}),
			).rejects.toThrow("Unauthenticated");
		});

		it("空の配列を渡した場合は空の配列を返す", async () => {
			const result = await t
				.withIdentity({ subject })
				.query(api.otaReviews.getMonthlyReviewStatsByProperties, {
					propertyIds: [],
				});
			expect(result).toEqual([]);
		});

		it("アクセス権限のない施設は結果に含まれない", async () => {
			const result = await t
				.withIdentity({ subject })
				.query(api.otaReviews.getMonthlyReviewStatsByProperties, {
					propertyIds: [propertyId1, propertyId3], // propertyId3はアクセス権限なし
				});
			expect(result).toHaveLength(1);
			expect(result[0].propertyId).toBe(propertyId1);
		});

		it("レビューがない施設は空の統計データを返す", async () => {
			const result = await t
				.withIdentity({ subject })
				.query(api.otaReviews.getMonthlyReviewStatsByProperties, {
					propertyIds: [propertyId1],
				});
			expect(result).toHaveLength(1);
			expect(result[0].propertyId).toBe(propertyId1);
			expect(result[0].stats).toEqual([]);
		});

		it("月別に累積統計を正しく計算する", async () => {
			// 2024年1月のレビューを2件作成
			await t.run(async (ctx) => {
				await ctx.db.insert("otaReviews", {
					beds24PropertyId: propertyId1,
					otaId,
					uniqueHash: "hash1",
					score: 8.0,
					reviewContent: "Good hotel",
					reviewerName: "User 1",
					reviewDate: new Date("2024-01-15").getTime(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
				await ctx.db.insert("otaReviews", {
					beds24PropertyId: propertyId1,
					otaId,
					uniqueHash: "hash2",
					score: 9.0,
					reviewContent: "Great hotel",
					reviewerName: "User 2",
					reviewDate: new Date("2024-01-20").getTime(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// 2024年2月のレビューを1件作成
			await t.run(async (ctx) => {
				await ctx.db.insert("otaReviews", {
					beds24PropertyId: propertyId1,
					otaId,
					uniqueHash: "hash3",
					score: 7.0,
					reviewContent: "Nice stay",
					reviewerName: "User 3",
					reviewDate: new Date("2024-02-10").getTime(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// 統計を取得
			const result = await t
				.withIdentity({ subject })
				.query(api.otaReviews.getMonthlyReviewStatsByProperties, {
					propertyIds: [propertyId1],
				});

			expect(result).toHaveLength(1);
			expect(result[0].propertyId).toBe(propertyId1);
			expect(result[0].stats).toHaveLength(2);

			// 2024年1月の累積統計
			expect(result[0].stats[0].date).toBe("2024-01");
			expect(result[0].stats[0].reviewCount).toBe(2); // 累積2件
			expect(result[0].stats[0].averageScore).toBeCloseTo(8.5, 1); // (8+9)/2 = 8.5

			// 2024年2月の累積統計
			expect(result[0].stats[1].date).toBe("2024-02");
			expect(result[0].stats[1].reviewCount).toBe(3); // 累積3件
			expect(result[0].stats[1].averageScore).toBeCloseTo(8.0, 1); // (8+9+7)/3 = 8.0
		});

		it("複数施設の統計を並列で取得できる", async () => {
			// property1のレビュー
			await t.run(async (ctx) => {
				await ctx.db.insert("otaReviews", {
					beds24PropertyId: propertyId1,
					otaId,
					uniqueHash: "prop1-hash1",
					score: 9.0,
					reviewContent: "Excellent",
					reviewerName: "User A",
					reviewDate: new Date("2024-03-01").getTime(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// property2のレビュー
			await t.run(async (ctx) => {
				await ctx.db.insert("otaReviews", {
					beds24PropertyId: propertyId2,
					otaId,
					uniqueHash: "prop2-hash1",
					score: 7.5,
					reviewContent: "Good value",
					reviewerName: "User B",
					reviewDate: new Date("2024-03-15").getTime(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
				await ctx.db.insert("otaReviews", {
					beds24PropertyId: propertyId2,
					otaId,
					uniqueHash: "prop2-hash2",
					score: 8.5,
					reviewContent: "Very nice",
					reviewerName: "User C",
					reviewDate: new Date("2024-04-01").getTime(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// 統計を取得
			const result = await t
				.withIdentity({ subject })
				.query(api.otaReviews.getMonthlyReviewStatsByProperties, {
					propertyIds: [propertyId1, propertyId2],
				});

			expect(result).toHaveLength(2);

			// property1の統計
			const prop1Stats = result.find((r) => r.propertyId === propertyId1);
			expect(prop1Stats).toBeDefined();
			expect(prop1Stats!.stats).toHaveLength(1);
			expect(prop1Stats!.stats[0].date).toBe("2024-03");
			expect(prop1Stats!.stats[0].reviewCount).toBe(1);
			expect(prop1Stats!.stats[0].averageScore).toBe(9.0);

			// property2の統計
			const prop2Stats = result.find((r) => r.propertyId === propertyId2);
			expect(prop2Stats).toBeDefined();
			expect(prop2Stats!.stats).toHaveLength(2);
			expect(prop2Stats!.stats[0].date).toBe("2024-03");
			expect(prop2Stats!.stats[0].reviewCount).toBe(1);
			expect(prop2Stats!.stats[0].averageScore).toBe(7.5);
			expect(prop2Stats!.stats[1].date).toBe("2024-04");
			expect(prop2Stats!.stats[1].reviewCount).toBe(2);
			expect(prop2Stats!.stats[1].averageScore).toBeCloseTo(8.0, 1); // (7.5+8.5)/2 = 8.0
		});

		it("日付順（昇順）でソートされて返される", async () => {
			// 異なる順序でレビューを作成
			await t.run(async (ctx) => {
				// 2024年3月
				await ctx.db.insert("otaReviews", {
					beds24PropertyId: propertyId1,
					otaId,
					uniqueHash: "march",
					score: 8.0,
					reviewContent: "March review",
					reviewerName: "User",
					reviewDate: new Date("2024-03-15").getTime(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
				// 2024年1月
				await ctx.db.insert("otaReviews", {
					beds24PropertyId: propertyId1,
					otaId,
					uniqueHash: "jan",
					score: 9.0,
					reviewContent: "January review",
					reviewerName: "User",
					reviewDate: new Date("2024-01-10").getTime(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
				// 2024年2月
				await ctx.db.insert("otaReviews", {
					beds24PropertyId: propertyId1,
					otaId,
					uniqueHash: "feb",
					score: 7.0,
					reviewContent: "February review",
					reviewerName: "User",
					reviewDate: new Date("2024-02-20").getTime(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// 統計を取得
			const result = await t
				.withIdentity({ subject })
				.query(api.otaReviews.getMonthlyReviewStatsByProperties, {
					propertyIds: [propertyId1],
				});

			expect(result[0].stats).toHaveLength(3);
			expect(result[0].stats[0].date).toBe("2024-01");
			expect(result[0].stats[1].date).toBe("2024-02");
			expect(result[0].stats[2].date).toBe("2024-03");
		});
	});
});
