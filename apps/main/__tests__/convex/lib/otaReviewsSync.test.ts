import { convexTest } from "convex-test";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { internal } from "@/convex/_generated/api";
import { createReviewParser } from "@/convex/lib/reviewParser";
import {
	createScrapingAntClient,
	type ScrapingAntWrapper,
} from "@/convex/lib/scrapingAnt";
import schema from "@/convex/schema";
import { QueueJobType } from "@/convex/types/beds24";
import type { ScrapingAntApiError } from "@/convex/types/reviews";
import { modules } from "../test.setup";

// ScrapingAntクライアントのモック
vi.mock("@/convex/lib/scrapingAnt", () => ({
	createScrapingAntClient: vi.fn(() => ({
		scrapeUrl: vi.fn(),
	})),
}));

// レビューパーサーのモック
vi.mock("@/convex/lib/reviewParser", () => ({
	createReviewParser: vi.fn(() => ({
		parseHtml: vi.fn(),
		generateHash: vi.fn(),
	})),
}));

describe("otaReviewsSync", () => {
	const t = convexTest(schema, modules);

	beforeEach(() => {
		vi.clearAllMocks();
		// 環境変数のモック
		vi.stubEnv("SCRAPING_ANT_API_KEY", "test-api-key");
	});

	afterEach(() => {
		vi.unstubAllEnvs();
	});

	describe("getReviewSyncTask", () => {
		it("should return null for non-existent task", async () => {
			// まずタスクを作成してIDを取得
			const taskId = await t.run(async (ctx) => {
				const id = await ctx.db.insert("syncQueue", {
					userId: "test-user",
					jobType: QueueJobType.SYNC_REVIEWS,
					priority: 10,
					status: "pending",
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
				// タスクを削除
				await ctx.db.delete(id);
				return id;
			});

			const result = await t.query(
				internal.otaReviewsInternal.getReviewSyncTask,
				{
					taskId,
				},
			);
			expect(result).toBeNull();
		});

		it("should return task with proper metadata", async () => {
			// Arrange
			const { taskId } = await t.run(async (ctx) => {
				// OTA masterデータを作成
				const otaId = await ctx.db.insert("otaMaster", {
					fullName: "Booking.com",
					shortName: "Booking",
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});

				// Beds24 propertyを作成
				const propertyId = await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "test-hotel-123",
					beds24PropertyKey: "test-hotel-123",
					name: "Test Hotel",
					propertyType: "hotel",
					currency: "JPY",
					country: "JP",
					city: "Tokyo",
					data: {
						roomCount: 10,
						otaMasters: [otaId],
					},
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
					isDeleted: false,
				});

				// syncQueueタスクを作成
				const taskId = await ctx.db.insert("syncQueue", {
					userId: "test-user",
					jobType: QueueJobType.SYNC_REVIEWS,
					priority: 10,
					status: "pending",
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
					metadata: {
						url: "https://booking.com/hotel/test-hotel-123/reviews",
						otaId,
						otaType: "booking.com",
						pageNumber: 1,
						maxPages: 10,
						propertyId,
					},
				});

				// ユーザーアクセス権限の設定
				await ctx.db.insert("userProperties", {
					userId: "test-user",
					propertyId,
					createdAt: Date.now(),
				});

				return { otaId, propertyId, taskId };
			});

			// Act
			const result = await t.query(
				internal.otaReviewsInternal.getReviewSyncTask,
				{
					taskId,
				},
			);

			// Assert
			expect(result).not.toBeNull();
			expect(result?.task.jobType).toBe(QueueJobType.SYNC_REVIEWS);
			expect(result?.metadata).toMatchObject({
				url: "https://booking.com/hotel/test-hotel-123/reviews",
				otaType: "booking.com",
				pageNumber: 1,
				maxPages: 10,
			});
		});
	});

	describe("processReviewBatch", () => {
		it("should successfully process review batch", async () => {
			// Arrange
			const { taskId } = await t.run(async (ctx) => {
				// OTA masterデータを作成
				const otaId = await ctx.db.insert("otaMaster", {
					fullName: "Booking.com",
					shortName: "Booking",
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});

				// Beds24 propertyを作成
				const propertyId = await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "test-hotel-123",
					beds24PropertyKey: "test-hotel-123",
					name: "Test Hotel",
					propertyType: "hotel",
					currency: "JPY",
					country: "JP",
					city: "Tokyo",
					data: {
						roomCount: 10,
						otaMasters: [otaId],
					},
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
					isDeleted: false,
				});

				// syncQueueタスクを作成
				const taskId = await ctx.db.insert("syncQueue", {
					userId: "test-user",
					jobType: QueueJobType.SYNC_REVIEWS,
					priority: 10,
					status: "pending",
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
					metadata: {
						url: "https://booking.com/hotel/test-hotel-123/reviews",
						otaId,
						otaType: "booking.com",
						pageNumber: 1,
						maxPages: 10,
						propertyId,
					},
				});

				// ユーザーアクセス権限の設定
				await ctx.db.insert("userProperties", {
					userId: "test-user",
					propertyId,
					createdAt: Date.now(),
				});

				return { otaId, propertyId, taskId };
			});

			// Act
			const results = await t.mutation(
				internal.otaReviewsInternal.processReviewBatch,
				{
					taskId,
					reviews: [
						{
							reviewId: "review-123",
							score: 8.5,
							content: "Great hotel!",
							reviewerName: "John Doe",
							reviewDate: new Date("2024-01-20").getTime(),
						},
					],
					hasNextPage: false,
					nextPageUrl: undefined,
				},
			);

			// Assert
			expect(results.processedCount).toBe(1);
			expect(results.createdCount).toBe(1);
			expect(results.updatedCount).toBe(0);

			// レビューが保存されていることを確認
			const reviews = await t.run(async (ctx) => {
				return await ctx.db
					.query("otaReviews")
					.filter((q) => q.eq(q.field("reviewerName"), "John Doe"))
					.collect();
			});
			expect(reviews).toHaveLength(1);
			expect(reviews[0]).toMatchObject({
				score: 8.5,
				reviewContent: "Great hotel!",
				reviewerName: "John Doe",
			});

			// タスクのステータスが更新されていることを確認
			const updatedTask = await t.run(async (ctx) => {
				return await ctx.db.get(taskId);
			});
			expect(updatedTask?.status).toBe("completed");
		});

		it("should handle multiple pages correctly", async () => {
			// Arrange
			const { taskId } = await t.run(async (ctx) => {
				const otaId = await ctx.db.insert("otaMaster", {
					fullName: "Booking.com",
					shortName: "Booking",
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});

				const propertyId = await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "test-hotel-123",
					beds24PropertyKey: "test-hotel-123",
					name: "Test Hotel",
					propertyType: "hotel",
					currency: "JPY",
					country: "JP",
					city: "Tokyo",
					data: {
						roomCount: 10,
						otaMasters: [otaId],
					},
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
					isDeleted: false,
				});

				const taskId = await ctx.db.insert("syncQueue", {
					userId: "test-user",
					jobType: QueueJobType.SYNC_REVIEWS,
					priority: 10,
					status: "pending",
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
					metadata: {
						url: "https://booking.com/hotel/test-hotel-123/reviews",
						otaId,
						otaType: "booking.com",
						pageNumber: 1,
						maxPages: 10,
						propertyId,
					},
				});

				// ユーザーアクセス権限の設定
				await ctx.db.insert("userProperties", {
					userId: "test-user",
					propertyId,
					createdAt: Date.now(),
				});

				return { otaId, propertyId, taskId };
			});

			// Act
			const results = await t.mutation(
				internal.otaReviewsInternal.processReviewBatch,
				{
					taskId,
					reviews: [
						{
							reviewId: "review-456",
							score: 9.0,
							content: "Excellent!",
							reviewerName: "Jane Smith",
							reviewDate: new Date("2024-01-21").getTime(),
						},
					],
					hasNextPage: true,
					nextPageUrl: undefined,
				},
			);

			// Assert
			expect(results.processedCount).toBe(1);

			// 新しいタスクが作成されていることを確認（次ページ用）
			const nextPageTasks = await t.run(async (ctx) => {
				return await ctx.db
					.query("syncQueue")
					.filter((q) =>
						q.and(
							q.eq(q.field("jobType"), QueueJobType.SYNC_REVIEWS),
							q.eq(q.field("status"), "pending"),
						),
					)
					.collect();
			});

			const nextPageTask = nextPageTasks.find((task) => {
				const metadata = task.metadata as any;
				return metadata.pageNumber === 2;
			});

			expect(nextPageTask).toBeDefined();
			expect(nextPageTask?.metadata).toMatchObject({
				url: "https://booking.com/hotel/test-hotel-123/reviews?page=2",
				pageNumber: 2,
			});
		});
	});

	describe("reviewParser integration", () => {
		it("should correctly parse booking.com HTML", () => {
			// モックされたcreateReviewParserの動作を確認
			const mockParser = {
				parseHtml: vi.fn().mockReturnValue({
					reviews: [
						{
							reviewId: "test-review",
							score: 8.0,
							content: "Nice hotel",
							reviewerName: "Test User",
							reviewDate: new Date("2024-01-01").getTime(),
						},
					],
					nextPageUrl: null,
				}),
				generateHash: vi.fn().mockReturnValue("test-hash"),
			};

			vi.mocked(createReviewParser).mockReturnValue(mockParser);

			const parser = createReviewParser();
			const result = parser.parseHtml(
				"<html>test</html>",
				"booking.com",
				"https://test.com",
			);
			expect(result.reviews).toHaveLength(1);
			expect(result.reviews[0].reviewId).toBe("test-review");
		});
	});

	describe("ScrapingAnt client integration", () => {
		it("should handle rate limit errors", async () => {
			const mockClient = {
				scrapeUrl: vi.fn().mockRejectedValue({
					name: "ScrapingAntApiError",
					message: "Rate limit exceeded",
					statusCode: 429,
					httpMethod: "POST",
				} as ScrapingAntApiError),
			} as unknown as ScrapingAntWrapper;

			vi.mocked(createScrapingAntClient).mockReturnValue(mockClient);

			const client = createScrapingAntClient();
			await expect(client.scrapeUrl("https://test.com")).rejects.toMatchObject({
				statusCode: 429,
			});
		});

		it("should successfully scrape content", async () => {
			const mockClient = {
				scrapeUrl: vi.fn().mockResolvedValue({
					content: "<html>test content</html>",
					statusCode: 200,
				}),
			} as unknown as ScrapingAntWrapper;

			vi.mocked(createScrapingAntClient).mockReturnValue(mockClient);

			const client = createScrapingAntClient();
			const result = await client.scrapeUrl("https://test.com");
			expect(result.content).toBe("<html>test content</html>");
			expect(result.statusCode).toBe(200);
		});
	});
});
