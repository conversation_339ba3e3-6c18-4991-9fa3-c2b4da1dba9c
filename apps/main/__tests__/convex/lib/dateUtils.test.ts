import { describe, expect, it } from "vitest";
import {
	addMonths,
	calculatePreviousDateRange,
	formatDateForBeds24,
	parseISODate,
} from "@/convex/lib/dateUtils";

describe("dateUtils", () => {
	describe("parseISODate", () => {
		it("ISO日付文字列をDateオブジェクトに変換する", () => {
			const result = parseISODate("2024-01-15");
			expect(result).toBeInstanceOf(Date);
			expect(result.getFullYear()).toBe(2024);
			expect(result.getMonth()).toBe(0); // 0-indexed
			expect(result.getDate()).toBe(15);
		});

		it("時刻付きISO日付文字列も処理できる", () => {
			const result = parseISODate("2024-01-15T10:30:00Z");
			expect(result).toBeInstanceOf(Date);
			expect(result.getUTCFullYear()).toBe(2024);
			expect(result.getUTCMonth()).toBe(0);
			expect(result.getUTCDate()).toBe(15);
			expect(result.getUTCHours()).toBe(10);
			expect(result.getUTCMinutes()).toBe(30);
		});

		it("無効な日付文字列の場合はエラーをスローする", () => {
			expect(() => parseISODate("invalid-date")).toThrow("Invalid date string");
		});
	});

	describe("formatDateForBeds24", () => {
		it("DateオブジェクトをYYYY-MM-DD形式に変換する", () => {
			const date = new Date(2024, 0, 15); // 2024-01-15
			expect(formatDateForBeds24(date)).toBe("2024-01-15");
		});

		it("月と日が1桁の場合もゼロパディングする", () => {
			const date = new Date(2024, 8, 5); // 2024-09-05
			expect(formatDateForBeds24(date)).toBe("2024-09-05");
		});

		it("タイムゾーンに関わらず正しい日付を返す", () => {
			// UTC時間で日付を設定
			const date = new Date(Date.UTC(2024, 0, 1, 0, 0, 0));
			const formatted = formatDateForBeds24(date);
			// 日付部分のみを確認（タイムゾーンにより異なる可能性）
			expect(formatted).toMatch(/^\d{4}-\d{2}-\d{2}$/);
		});
	});

	describe("addMonths", () => {
		it("正の月数を加算できる", () => {
			const date = new Date(2024, 0, 15); // 2024-01-15
			const result = addMonths(date, 2);
			expect(result.getFullYear()).toBe(2024);
			expect(result.getMonth()).toBe(2); // March
			expect(result.getDate()).toBe(15);
		});

		it("負の月数を減算できる", () => {
			const date = new Date(2024, 2, 15); // 2024-03-15
			const result = addMonths(date, -2);
			expect(result.getFullYear()).toBe(2024);
			expect(result.getMonth()).toBe(0); // January
			expect(result.getDate()).toBe(15);
		});

		it("年を跨ぐ加算ができる", () => {
			const date = new Date(2023, 11, 15); // 2023-12-15
			const result = addMonths(date, 2);
			expect(result.getFullYear()).toBe(2024);
			expect(result.getMonth()).toBe(1); // February
			expect(result.getDate()).toBe(15);
		});

		it("年を跨ぐ減算ができる", () => {
			const date = new Date(2024, 0, 15); // 2024-01-15
			const result = addMonths(date, -2);
			expect(result.getFullYear()).toBe(2023);
			expect(result.getMonth()).toBe(10); // November
			expect(result.getDate()).toBe(15);
		});

		it("月末日の調整を正しく行う", () => {
			const date = new Date(2024, 0, 31); // 2024-01-31
			const result = addMonths(date, 1); // February has 29 days in 2024
			expect(result.getFullYear()).toBe(2024);
			expect(result.getMonth()).toBe(1); // February
			expect(result.getDate()).toBe(29); // Last day of February
		});

		it("元のDateオブジェクトを変更しない", () => {
			const date = new Date(2024, 0, 15);
			const originalTime = date.getTime();
			addMonths(date, 2);
			expect(date.getTime()).toBe(originalTime);
		});
	});

	describe("calculatePreviousDateRange", () => {
		it("現在の日付範囲から2ヶ月前の期間を計算する", () => {
			const result = calculatePreviousDateRange("2024-03-01", "2024-03-31");
			expect(result).toEqual({
				from: "2024-01-01",
				to: "2024-01-31",
			});
		});

		it("日付範囲が月を跨ぐ場合も正しく計算する", () => {
			const result = calculatePreviousDateRange("2024-03-15", "2024-04-15");
			expect(result).toEqual({
				from: "2024-01-15",
				to: "2024-02-15",
			});
		});

		it("年を跨ぐ場合も正しく計算する", () => {
			const result = calculatePreviousDateRange("2024-02-01", "2024-02-29");
			expect(result).toEqual({
				from: "2023-12-01",
				to: "2023-12-29", // 2024-02-29の2ヶ月前は2023-12-29
			});
		});

		it("currentToがnullの場合は現在日時から2ヶ月前の期間を計算する", () => {
			const result = calculatePreviousDateRange("2024-03-01", null);
			expect(result.from).toBe("2024-01-01");
			// toは現在の日時から2ヶ月前なので、テストでは正規表現で検証
			expect(result.to).toMatch(/^\d{4}-\d{2}-\d{2}$/);
		});

		it("currentFromが時刻付きISO文字列でも正しく処理する", () => {
			const result = calculatePreviousDateRange(
				"2024-03-15T10:30:00Z",
				"2024-04-15T10:30:00Z",
			);
			expect(result).toEqual({
				from: "2024-01-15",
				to: "2024-02-15",
			});
		});
	});
});
