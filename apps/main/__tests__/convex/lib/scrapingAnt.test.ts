/**
 * ScrapingAntラッパーのテスト
 */

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import type { ConvexLogger } from "../../../convex/lib/logging";
import {
	createScrapingAntClient,
	ScrapingAntWrapper,
} from "../../../convex/lib/scrapingAnt";
import type { ScrapingAntResponse } from "../../../convex/types/reviews";

describe("ScrapingAntWrapper", () => {
	let originalEnv: NodeJS.ProcessEnv;
	let mockLogger: ConvexLogger;
	let mockClient: any;
	let mockScrape: any;

	beforeEach(() => {
		// 環境変数のバックアップ
		originalEnv = { ...process.env };
		process.env.SCRAPING_ANT_API_KEY = "test-api-key";

		// ロガーのモック
		mockLogger = {
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
			addContext: vi.fn(),
			setArgs: vi.fn(),
		} as unknown as ConvexLogger;

		// ScrapingAntクライアントのモック
		mockScrape = vi.fn();
		mockClient = {
			scrape: mockScrape,
		};

		// モックのリセット
		vi.clearAllMocks();
	});

	afterEach(() => {
		// 環境変数の復元
		process.env = originalEnv;
		vi.clearAllMocks();
	});

	describe("コンストラクタ", () => {
		it("APIキーが設定されていない場合はエラーを投げる", () => {
			delete process.env.SCRAPING_ANT_API_KEY;

			expect(() => new ScrapingAntWrapper()).toThrow();
			expect(() => new ScrapingAntWrapper()).toThrow(
				"SCRAPING_ANT_API_KEY is not set",
			);
		});

		it("正常にクライアントを初期化できる（テストクライアント使用）", () => {
			const wrapper = new ScrapingAntWrapper(mockLogger, mockClient);
			expect(wrapper).toBeInstanceOf(ScrapingAntWrapper);
			expect(mockLogger.info).toHaveBeenCalledWith("Using test client");
		});
	});

	describe("scrapeUrl", () => {
		let wrapper: ScrapingAntWrapper;

		beforeEach(() => {
			wrapper = new ScrapingAntWrapper(mockLogger, mockClient);
		});

		it("無効なURLの場合はエラーを投げる", async () => {
			await expect(wrapper.scrapeUrl("")).rejects.toThrow(
				"Invalid URL provided",
			);
			await expect(wrapper.scrapeUrl("not-a-url")).rejects.toThrow(
				"Invalid URL provided",
			);
			await expect(wrapper.scrapeUrl("ftp://example.com")).rejects.toThrow(
				"Invalid URL provided",
			);
		});

		it("許可されていないドメインの場合はエラーを投げる", async () => {
			await expect(wrapper.scrapeUrl("https://example.com")).rejects.toThrow(
				"URL domain is not allowed for scraping",
			);
			await expect(
				wrapper.scrapeUrl("https://malicious-site.com"),
			).rejects.toThrow("URL domain is not allowed for scraping");
		});

		it("正常にスクレイピングできる", async () => {
			const mockResponse: ScrapingAntResponse = {
				content: "<html>Test content</html>",
				cookies: "test-cookie",
				statusCode: 200,
			};
			mockScrape.mockResolvedValueOnce(mockResponse);

			const result = await wrapper.scrapeUrl("https://booking.com/test");

			expect(mockScrape).toHaveBeenCalledWith(
				"https://booking.com/test",
				undefined,
			);
			expect(result).toEqual(mockResponse);
			expect(mockLogger.info).toHaveBeenCalledWith(
				"Starting scraping",
				expect.objectContaining({
					url: "https://booking.com/test",
				}),
			);
			expect(mockLogger.info).toHaveBeenCalledWith(
				"Scraping completed successfully",
				expect.any(Object),
			);
		});

		it("オプションを正しく渡す", async () => {
			const mockResponse: ScrapingAntResponse = {
				content: "<html>Test content</html>",
			};
			mockScrape.mockResolvedValueOnce(mockResponse);

			const options = {
				browser: true,
				wait_for_selector: ".review",
				proxy_type: "residential" as const,
			};

			await wrapper.scrapeUrl("https://booking.com/test", options);

			expect(mockScrape).toHaveBeenCalledWith(
				"https://booking.com/test",
				options,
			);
		});

		it("空のコンテンツの場合はエラーを投げる", async () => {
			mockScrape.mockResolvedValueOnce({ content: "" });

			await expect(
				wrapper.scrapeUrl("https://booking.com/test"),
			).rejects.toThrow("Empty response content from ScrapingAnt");
		});

		it("レート制限エラーを適切に処理する", async () => {
			const error = new Error("Rate limit exceeded");
			(error as any).statusCode = 429;
			(error as any).message = "Too many requests";
			mockScrape.mockRejectedValueOnce(error);

			await expect(
				wrapper.scrapeUrl("https://booking.com/test"),
			).rejects.toThrow("ScrapingAnt rate limit exceeded");
			expect(mockLogger.error).toHaveBeenCalledWith(
				"ScrapingAnt API error",
				expect.any(Object),
			);
		});

		it("4xxエラーを適切に処理する", async () => {
			const error = new Error("Bad request");
			(error as any).statusCode = 400;
			mockScrape.mockRejectedValueOnce(error);

			await expect(
				wrapper.scrapeUrl("https://booking.com/test"),
			).rejects.toThrow("ScrapingAnt request failed");
		});

		it("5xxエラーを適切に処理する", async () => {
			const error = new Error("Internal server error");
			(error as any).statusCode = 500;
			mockScrape.mockRejectedValueOnce(error);

			await expect(
				wrapper.scrapeUrl("https://booking.com/test"),
			).rejects.toThrow("ScrapingAnt service is temporarily unavailable");
		});

		it("ネットワークエラーを適切に処理する", async () => {
			const error = new Error("Network error");
			mockScrape.mockRejectedValueOnce(error);

			await expect(
				wrapper.scrapeUrl("https://booking.com/test"),
			).rejects.toThrow("Failed to scrape URL");
			expect(mockLogger.error).toHaveBeenCalledWith(
				"Unexpected scraping error",
				expect.any(Object),
			);
		});
	});

	describe("scrapeUrlsBatch", () => {
		let wrapper: ScrapingAntWrapper;

		beforeEach(() => {
			wrapper = new ScrapingAntWrapper(mockLogger, mockClient);
		});

		it("複数のURLを並行してスクレイピングできる", async () => {
			const urls = [
				"https://booking.com/hotel1",
				"https://booking.com/hotel2",
				"https://booking.com/hotel3",
			];

			mockScrape
				.mockResolvedValueOnce({ content: "Content 1" })
				.mockResolvedValueOnce({ content: "Content 2" })
				.mockResolvedValueOnce({ content: "Content 3" });

			const results = await wrapper.scrapeUrlsBatch(urls, undefined, 2);

			expect(results).toHaveLength(3);
			expect(results[0]).toEqual({
				url: "https://booking.com/hotel1",
				result: { content: "Content 1" },
			});
			expect(results[1]).toEqual({
				url: "https://booking.com/hotel2",
				result: { content: "Content 2" },
			});
			expect(results[2]).toEqual({
				url: "https://booking.com/hotel3",
				result: { content: "Content 3" },
			});
		});

		it("エラーが発生したURLも結果に含める", async () => {
			const urls = ["https://booking.com/hotel1", "https://booking.com/hotel2"];

			mockScrape
				.mockResolvedValueOnce({ content: "Content 1" })
				.mockRejectedValueOnce(new Error("Scraping failed"));

			const results = await wrapper.scrapeUrlsBatch(urls);

			expect(results).toHaveLength(2);
			expect(results[0].result).toBeDefined();
			expect(results[0].error).toBeUndefined();
			expect(results[1].result).toBeUndefined();
			expect(results[1].error).toBeDefined();
			expect(results[1].error?.message).toContain("Scraping failed");
		});
	});

	describe("createScrapingAntClient", () => {
		it("新しいインスタンスを作成できる", () => {
			const client = createScrapingAntClient(mockLogger, mockClient);
			expect(client).toBeInstanceOf(ScrapingAntWrapper);
		});
	});

	describe("プライベートメソッド", () => {
		it("許可されたドメインが正しく設定されている", async () => {
			const wrapper = new ScrapingAntWrapper(mockLogger, mockClient);
			const allowedUrls = [
				"https://booking.com/test",
				"https://www.booking.com/test",
			];
			const notAllowedUrls = [
				"https://expedia.com/test",
				"https://hotels.com/test",
				"https://agoda.com/test",
				"https://tripadvisor.com/test",
				"https://airbnb.com/test",
				"https://vrbo.com/test",
			];

			// モック設定
			mockScrape.mockResolvedValue({ content: "test" });

			// booking.comドメインでエラーが発生しないことを確認
			for (const url of allowedUrls) {
				await expect(wrapper.scrapeUrl(url)).resolves.toEqual({
					content: "test",
				});
			}

			// 他のドメインでエラーが発生することを確認
			for (const url of notAllowedUrls) {
				await expect(wrapper.scrapeUrl(url)).rejects.toThrow(
					"URL domain is not allowed for scraping",
				);
			}

			// booking.comのURLのみスクレイピングが実行されたことを確認
			expect(mockScrape).toHaveBeenCalledTimes(allowedUrls.length);
		});
	});
});
