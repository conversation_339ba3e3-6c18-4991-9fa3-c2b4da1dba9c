/**
 * レビューパーサーのテスト
 */

import { existsSync, readFileSync } from "node:fs";
import { join } from "node:path";
import { beforeEach, describe, expect, it, vi } from "vitest";
import type { ConvexLogger } from "../../../convex/lib/logging";
import { createReviewParser } from "../../../convex/lib/reviewParser";
import type { ParsedReview, ReviewParser } from "../../../convex/types/reviews";

// ヘルパー関数：テストフィクスチャを読み込む
const loadFixture = (filename: string): string => {
	const fixturePath = join(__dirname, "../../fixtures/booking-com", filename);
	if (!existsSync(fixturePath)) {
		throw new Error(`Test fixture '${filename}' not found at: ${fixturePath}`);
	}
	return readFileSync(fixturePath, "utf-8");
};

describe("ReviewParser", () => {
	let mockLogger: ConvexLogger;
	let parser: ReviewParser;

	beforeEach(() => {
		// ロガーのモック
		mockLogger = {
			info: vi.fn(),
			error: vi.fn(),
			warn: vi.fn(),
			debug: vi.fn(),
			addContext: vi.fn(),
			setArgs: vi.fn(),
			startTimer: vi.fn(() => vi.fn()),
		} as unknown as ConvexLogger;

		parser = createReviewParser(mockLogger);
	});

	describe("parseHtml", () => {
		it("空のHTMLでエラーをスロー", () => {
			expect(() =>
				parser.parseHtml("", "booking.com", "https://example.com"),
			).toThrow("Empty HTML content provided");
		});

		it("未実装のOTAタイプでエラーをスロー", () => {
			const html = "<html><body>Test</body></html>";
			expect(() =>
				parser.parseHtml(html, "expedia", "https://example.com"),
			).toThrow("Parser for expedia is not implemented yet");
		});

		it("不正なOTAタイプでエラーをスロー", () => {
			const html = "<html><body>Test</body></html>";
			expect(() =>
				parser.parseHtml(html, "invalid" as any, "https://example.com"),
			).toThrow("Unknown OTA type: invalid");
		});

		it("Booking.comのレビューを正しくパース", () => {
			const html = `
				<html>
					<body>
						<ul class="review_list">
							<li class="review_item">
								<span class="review-score-badge">8.5</span>
								<div class="review_item_header_content">
									<span>素晴らしい滞在</span>
								</div>
								<p class="review_pos">
									<span itemprop="reviewBody">清潔で快適な部屋でした。</span>
								</p>
								<p class="review_neg">
									<span itemprop="reviewBody">朝食の種類が少なかった。</span>
								</p>
								<p class="reviewer_name">
									<span>山田太郎</span>
								</p>
								<p class="review_item_date">2024年11月15日</p>
								<div class="review_item_reviewer">
									<span class="reviewer_country">
										<span itemprop="name">日本</span>
									</span>
								</div>
							</li>
						</ul>
					</body>
				</html>
			`;

			const result = parser.parseHtml(
				html,
				"booking.com",
				"https://example.com",
			);

			expect(result.reviews).toHaveLength(1);
			expect(result.reviews[0]).toMatchObject({
				score: 8.5,
				title: "素晴らしい滞在",
				content: expect.stringContaining("清潔で快適な部屋でした"),
				contentStructured: {
					positive: "清潔で快適な部屋でした。",
					negative: "朝食の種類が少なかった。",
				},
				reviewerName: "山田太郎",
				reviewerCountry: "日本",
			});
		});

		it("次ページリンクを正しく抽出", () => {
			const html = `
				<html>
					<body>
						<ul class="review_list">
							<li class="review_item">
								<span class="review-score-badge">8.5</span>
								<p class="reviewer_name"><span>Test User</span></p>
								<p class="review_pos">
									<span itemprop="reviewBody">Good</span>
								</p>
							</li>
						</ul>
						<a id="review_next_page_link" href="/reviews?page=2">Next</a>
					</body>
				</html>
			`;

			const result = parser.parseHtml(
				html,
				"booking.com",
				"https://booking.com/reviews?page=1",
			);

			expect(result.nextPageUrl).toBe(
				"https://booking.com/reviews?page=2&rows=25",
			);
		});

		it("レビューが見つからない場合は空の配列を返す", () => {
			const html = `
				<html>
					<body>
						<div>No reviews</div>
					</body>
				</html>
			`;

			const result = parser.parseHtml(
				html,
				"booking.com",
				"https://example.com",
			);

			expect(result.reviews).toEqual([]);
			expect(result.nextPageUrl).toBeUndefined();
		});
	});

	describe("generateHash", () => {
		it("同じレビューデータで同じハッシュを生成", async () => {
			const review: ParsedReview = {
				reviewId: "test-1",
				score: 8.5,
				content: "Test review",
				reviewerName: "Test User",
				reviewDate: 1700000000000,
			};

			const hash1 = await parser.generateHash(review, "prop-1", "ota-1");
			const hash2 = await parser.generateHash(review, "prop-1", "ota-1");

			expect(hash1).toBe(hash2);
		});

		it("異なるレビューデータで異なるハッシュを生成", async () => {
			const review1: ParsedReview = {
				reviewId: "test-1",
				score: 8.5,
				content: "Test review 1",
				reviewerName: "Test User",
				reviewDate: 1700000000000,
			};

			const review2: ParsedReview = {
				reviewId: "test-2",
				score: 9.0,
				content: "Test review 2",
				reviewerName: "Test User",
				reviewDate: 1700000001000,
			};

			const hash1 = await parser.generateHash(review1, "prop-1", "ota-1");
			const hash2 = await parser.generateHash(review2, "prop-1", "ota-1");

			expect(hash1).not.toBe(hash2);
		});
	});

	describe("エッジケース", () => {
		it("スコアが見つからないレビューをスキップ", () => {
			const html = `
				<html>
					<body>
						<ul class="review_list">
							<li class="review_item">
								<!-- スコアなし -->
								<p class="reviewer_name"><span>Test User</span></p>
								<p class="review_pos">
									<span itemprop="reviewBody">Good</span>
								</p>
							</li>
						</ul>
					</body>
				</html>
			`;

			const result = parser.parseHtml(
				html,
				"booking.com",
				"https://example.com",
			);
			expect(result.reviews).toHaveLength(0);
		});

		it("レビュー内容が無い場合でも処理する", () => {
			const html = `
				<html>
					<body>
						<ul class="review_list">
							<li class="review_item">
								<span class="review-score-badge">8.5</span>
								<p class="reviewer_name"><span>Test User</span></p>
								<p class="review_item_date">2024年11月15日</p>
								<!-- レビュー内容なし -->
							</li>
						</ul>
					</body>
				</html>
			`;

			const result = parser.parseHtml(
				html,
				"booking.com",
				"https://example.com",
			);
			expect(result.reviews).toHaveLength(1);
			expect(result.reviews[0]).toMatchObject({
				score: 8.5,
				content: "",
				reviewerName: "Test User",
			});
		});

		it("匿名のレビュアー名を処理", () => {
			const html = `
				<html>
					<body>
						<ul class="review_list">
							<li class="review_item">
								<span class="review-score-badge">8.5</span>
								<!-- レビュアー名なし -->
								<p class="review_pos">
									<span itemprop="reviewBody">Good</span>
								</p>
								<p class="review_item_date">2024年1月15日にレビュー</p>
							</li>
						</ul>
					</body>
				</html>
			`;

			const result = parser.parseHtml(
				html,
				"booking.com",
				"https://example.com",
			);
			expect(result.reviews).toHaveLength(1);
			expect(result.reviews[0].reviewerName).toBe("Anonymous");
		});
	});

	describe("実際のBooking.com HTMLを使用したテスト", () => {
		it("実際のレビューページHTMLを正しくパース", () => {
			const html = loadFixture("review-page.html");
			const result = parser.parseHtml(
				html,
				"booking.com",
				"https://www.booking.com/hotel/jp/hotel-monterey-okinawa.ja.html",
			);

			// レビューが複数件取得できることを確認
			expect(result.reviews.length).toBeGreaterThan(0);
			expect(result.reviews.length).toBeLessThanOrEqual(25); // Booking.comは1ページあたり最大25件

			// 最初のレビューの詳細を検証
			const firstReview = result.reviews[0];
			expect(firstReview).toBeDefined();
			expect(firstReview.score).toBeGreaterThanOrEqual(0);
			expect(firstReview.score).toBeLessThanOrEqual(10);
			expect(firstReview.reviewerName).toBeTruthy();
			expect(firstReview.reviewDate).toBeGreaterThan(0);

			// 構造化されたコンテンツ（良い点・悪い点）の確認
			const reviewWithStructuredContent = result.reviews.find(
				(r) => r.contentStructured?.positive || r.contentStructured?.negative,
			);
			if (reviewWithStructuredContent) {
				expect(reviewWithStructuredContent.contentStructured).toBeDefined();
				if (reviewWithStructuredContent.contentStructured?.positive) {
					expect(
						reviewWithStructuredContent.contentStructured.positive,
					).toBeTruthy();
				}
				if (reviewWithStructuredContent.contentStructured?.negative) {
					expect(
						reviewWithStructuredContent.contentStructured.negative,
					).toBeTruthy();
				}
			}
		});

		it("日本語の日付形式を正しくパース", () => {
			const html = loadFixture("review-page.html");
			const result = parser.parseHtml(
				html,
				"booking.com",
				"https://example.com",
			);

			// すべてのレビューが有効な日付を持つことを確認
			for (const review of result.reviews) {
				expect(review.reviewDate).toBeGreaterThan(0);
				// 日付が妥当な範囲内であることを確認（2020年以降）
				const reviewDateObj = new Date(review.reviewDate);
				expect(reviewDateObj.getFullYear()).toBeGreaterThanOrEqual(2020);
			}
		});

		it("総レビュー数を正しく取得", () => {
			const html = loadFixture("review-page.html");
			const result = parser.parseHtml(
				html,
				"booking.com",
				"https://example.com",
			);

			// 総レビュー数が取得できている場合の検証
			if (result.totalReviews !== undefined) {
				expect(result.totalReviews).toBeGreaterThan(0);
				// 総レビュー数は現在のページのレビュー数以上であるべき
				expect(result.totalReviews).toBeGreaterThanOrEqual(
					result.reviews.length,
				);
			}
		});
	});
});
