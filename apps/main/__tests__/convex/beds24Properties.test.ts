import { convexTest } from "convex-test";
import { describe, expect, it } from "vitest";
import { internal } from "@/convex/_generated/api";
import { ConvexError } from "@/convex/lib/errors";
import schema from "@/convex/schema";
import { modules } from "./test.setup";

describe("beds24Properties", () => {
	describe("updatePropertySlug", () => {
		it("should update property slug and timestamp", async () => {
			const t = convexTest(schema, modules);

			// テスト用の施設を作成
			const propertyId = await t.run(async (ctx) => {
				return await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "test-property-123",
					beds24PropertyKey: "test-key",
					name: "Test Property",
					propertyType: "hotel",
					currency: "JPY",
					data: {},
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
					isDeleted: false,
				});
			});

			// updatePropertySlugを実行
			const result = await t.mutation(
				internal.beds24Properties.updatePropertySlug,
				{
					propertyId,
					bookingComFacilitySlug: "test-hotel-tokyo",
				},
			);

			// 結果を検証
			expect(result.success).toBe(true);
			expect(result.propertyId).toBe(propertyId);
			expect(result.updatedFields.bookingComFacilitySlug).toBe(
				"test-hotel-tokyo",
			);
			expect(result.updatedFields.bookingComLastScrapedAt).toBeTypeOf("number");

			// データベースの更新を確認
			const updatedProperty = await t.run(async (ctx) => {
				return await ctx.db.get(propertyId);
			});

			expect(updatedProperty?.bookingComFacilitySlug).toBe("test-hotel-tokyo");
			expect(updatedProperty?.bookingComLastScrapedAt).toBe(
				result.updatedFields.bookingComLastScrapedAt,
			);
		});

		it("should throw error when property not found", async () => {
			const t = convexTest(schema, modules);

			// 存在しない施設IDで実行（有効な形式のIDを使用）
			await expect(
				t.mutation(internal.beds24Properties.updatePropertySlug, {
					propertyId: "99999;beds24Properties" as any,
					bookingComFacilitySlug: "test-slug",
				}),
			).rejects.toThrow(ConvexError);
		});

		it("should update existing slug", async () => {
			const t = convexTest(schema, modules);

			// 既にスラッグを持つ施設を作成
			const propertyId = await t.run(async (ctx) => {
				return await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "test-property-456",
					beds24PropertyKey: "test-key-2",
					name: "Test Property 2",
					propertyType: "hotel",
					currency: "JPY",
					data: {},
					bookingComFacilitySlug: "old-slug",
					bookingComLastScrapedAt: Date.now() - 86400000, // 1日前
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
					isDeleted: false,
				});
			});

			// 新しいスラッグで更新
			const result = await t.mutation(
				internal.beds24Properties.updatePropertySlug,
				{
					propertyId,
					bookingComFacilitySlug: "new-slug",
				},
			);

			// 結果を検証
			expect(result.success).toBe(true);
			expect(result.updatedFields.bookingComFacilitySlug).toBe("new-slug");

			// データベースの更新を確認
			const updatedProperty = await t.run(async (ctx) => {
				return await ctx.db.get(propertyId);
			});

			expect(updatedProperty?.bookingComFacilitySlug).toBe("new-slug");
			expect(updatedProperty?.bookingComLastScrapedAt).toBeGreaterThan(
				Date.now() - 86400000,
			);
		});
	});
});
