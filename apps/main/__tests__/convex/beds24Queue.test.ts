import { convexTest } from "convex-test";
import { beforeEach, describe, expect, test } from "vitest";
import { internal } from "@/convex/_generated/api";
import schema from "@/convex/schema";
import { JobPriority, QueueJobType, SyncStatus } from "@/convex/types/beds24";
import { modules } from "./test.setup";

const t = convexTest(schema, modules);

describe("beds24Queue - 並列処理機能", () => {
	beforeEach(async () => {
		await t.run(async (ctx) => {
			// syncQueueテーブルをクリア
			const allJobs = await ctx.db.query("syncQueue").collect();
			for (const job of allJobs) {
				await ctx.db.delete(job._id);
			}
		});
	});

	test("dequeueJobByType - 特定のジョブタイプから1つのジョブを取得", async () => {
		// 異なるジョブタイプの複数のジョブを作成
		const userId = "test-user-123";
		const now = Date.now();

		await t.run(async (ctx) => {
			// 各ジョブタイプに2つずつジョブを作成
			await ctx.db.insert("syncQueue", {
				userId,
				jobType: QueueJobType.SYNC_PROPERTIES,
				priority: JobPriority.NORMAL,
				status: SyncStatus.PENDING,
				attempts: 0,
				maxAttempts: 3,
				scheduledFor: now,
				createdAt: now,
				updatedAt: now,
			});

			await ctx.db.insert("syncQueue", {
				userId,
				jobType: QueueJobType.SYNC_PROPERTIES,
				priority: JobPriority.LOW,
				status: SyncStatus.PENDING,
				attempts: 0,
				maxAttempts: 3,
				scheduledFor: now,
				createdAt: now,
				updatedAt: now,
			});

			await ctx.db.insert("syncQueue", {
				userId,
				jobType: QueueJobType.SYNC_REVIEWS,
				priority: JobPriority.NORMAL,
				status: SyncStatus.PENDING,
				attempts: 0,
				maxAttempts: 3,
				scheduledFor: now,
				createdAt: now,
				updatedAt: now,
			});

			await ctx.db.insert("syncQueue", {
				userId,
				jobType: QueueJobType.SYNC_BOOKINGS,
				priority: JobPriority.HIGH,
				status: SyncStatus.PENDING,
				attempts: 0,
				maxAttempts: 3,
				scheduledFor: now,
				createdAt: now,
				updatedAt: now,
			});
		});

		// SYNC_PROPERTIESタイプのジョブを取得
		const result = await t.query(internal.beds24Queue.dequeueJobByType, {
			jobType: QueueJobType.SYNC_PROPERTIES,
		});

		expect(result).not.toBeNull();
		expect(result?.job.jobType).toBe(QueueJobType.SYNC_PROPERTIES);
		expect(result?.job.priority).toBe(JobPriority.NORMAL); // 優先度が高い方が選ばれる

		// SYNC_REVIEWSタイプのジョブを取得
		const result2 = await t.query(internal.beds24Queue.dequeueJobByType, {
			jobType: QueueJobType.SYNC_REVIEWS,
		});

		expect(result2).not.toBeNull();
		expect(result2?.job.jobType).toBe(QueueJobType.SYNC_REVIEWS);

		// 存在しないジョブタイプ
		const result3 = await t.query(internal.beds24Queue.dequeueJobByType, {
			jobType: QueueJobType.SCRAPE_BOOKING_COM_SLUG,
		});

		expect(result3).toBeNull();
	});

	test("dequeueJobByType - 優先度順にジョブを取得", async () => {
		const userId = "test-user-123";
		const now = Date.now();

		await t.run(async (ctx) => {
			// 同じジョブタイプで異なる優先度のジョブを作成
			await ctx.db.insert("syncQueue", {
				userId,
				jobType: QueueJobType.SYNC_BOOKINGS,
				priority: JobPriority.LOW, // 10
				status: SyncStatus.PENDING,
				attempts: 0,
				maxAttempts: 3,
				scheduledFor: now,
				createdAt: now,
				updatedAt: now,
			});

			await ctx.db.insert("syncQueue", {
				userId,
				jobType: QueueJobType.SYNC_BOOKINGS,
				priority: JobPriority.HIGH, // 1
				status: SyncStatus.PENDING,
				attempts: 0,
				maxAttempts: 3,
				scheduledFor: now,
				createdAt: now,
				updatedAt: now,
			});

			await ctx.db.insert("syncQueue", {
				userId,
				jobType: QueueJobType.SYNC_BOOKINGS,
				priority: JobPriority.NORMAL, // 5
				status: SyncStatus.PENDING,
				attempts: 0,
				maxAttempts: 3,
				scheduledFor: now,
				createdAt: now,
				updatedAt: now,
			});
		});

		// 優先度が最も高いジョブが選ばれることを確認
		const result = await t.query(internal.beds24Queue.dequeueJobByType, {
			jobType: QueueJobType.SYNC_BOOKINGS,
		});

		expect(result).not.toBeNull();
		expect(result?.job.priority).toBe(JobPriority.HIGH); // 最も優先度が高い
	});

	test("dequeueJobByType - FAILEDでリトライ可能なジョブも取得対象", async () => {
		const userId = "test-user-123";
		const now = Date.now();

		await t.run(async (ctx) => {
			// FAILEDだがリトライ可能なジョブ
			await ctx.db.insert("syncQueue", {
				userId,
				jobType: QueueJobType.SYNC_PROPERTIES,
				priority: JobPriority.NORMAL,
				status: SyncStatus.FAILED,
				attempts: 1,
				maxAttempts: 3,
				scheduledFor: now - 10000,
				nextRetryAt: now - 1000, // リトライ時刻を過ぎている
				createdAt: now - 20000,
				updatedAt: now - 10000,
			});

			// FAILEDでリトライ不可能なジョブ（最大試行回数に到達）
			await ctx.db.insert("syncQueue", {
				userId,
				jobType: QueueJobType.SYNC_PROPERTIES,
				priority: JobPriority.HIGH,
				status: SyncStatus.FAILED,
				attempts: 3,
				maxAttempts: 3,
				scheduledFor: now - 10000,
				nextRetryAt: now - 1000,
				createdAt: now - 30000,
				updatedAt: now - 10000,
			});
		});

		const result = await t.query(internal.beds24Queue.dequeueJobByType, {
			jobType: QueueJobType.SYNC_PROPERTIES,
		});

		expect(result).not.toBeNull();
		expect(result?.job.status).toBe(SyncStatus.FAILED);
		expect(result?.job.attempts).toBe(1); // リトライ可能なジョブが選ばれる
	});
});
