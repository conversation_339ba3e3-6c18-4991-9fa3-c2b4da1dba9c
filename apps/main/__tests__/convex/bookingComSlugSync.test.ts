import { convexTest } from "convex-test";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import type { Id } from "@/convex/_generated/dataModel";
import schema from "@/convex/schema";
import {
	type BookingComSlugSyncMetadata,
	JobPriority,
	QueueJobType,
} from "@/convex/types/beds24";
import { modules } from "./test.setup";

describe("bookingComSlugSync", () => {
	const t = convexTest(schema, modules);

	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe("scrapeBookingComSlug", () => {
		// 注意: 以下のテストは実装が完了したら有効にできます。
		// 現在は実装がないため、テストケースの期待される動作を示しています。
		it("should fetch task and property information", async () => {
			// Arrange
			const userId = "user123";
			const propertyName = "Test Hotel Tokyo";
			const beds24PropertyKey = "test-hotel-tokyo";
			let taskId: Id<"syncQueue">;
			let propertyId: Id<"beds24Properties">;

			await t.run(async (ctx) => {
				// プロパティを作成
				propertyId = await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "beds24-123",
					beds24PropertyKey,
					name: propertyName,
					propertyType: "hotel",
					currency: "JPY",
					data: {},
					isDeleted: false,
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});

				// タスクを作成
				taskId = await ctx.db.insert("syncQueue", {
					userId,
					jobType: QueueJobType.SCRAPE_BOOKING_COM_SLUG,
					status: "processing",
					priority: JobPriority.MEDIUM,
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					startedAt: Date.now(),
					metadata: JSON.stringify({
						propertyId,
						propertyName,
						beds24PropertyKey,
						userId,
					} satisfies BookingComSlugSyncMetadata),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// ScrapingAnt APIのモック
			const mockFetch = vi.fn().mockResolvedValue({
				ok: true,
				status: 200,
				text: async () => `
					<html>
						<body>
							<div data-testid="property-card">
								<a href="/hotel/jp/test-hotel-tokyo-12345.ja.html">Test Hotel Tokyo</a>
							</div>
						</body>
					</html>
				`,
			} as Response);
			vi.stubGlobal("fetch", mockFetch);

			// Act - 実装が追加されたら以下のコメントを外す
			// await t.run(async (ctx) => {
			// 	await ctx.runAction(internal.bookingComSlugSync.scrapeBookingComSlug, { taskId });
			// });

			// 現在は実装がないため、テストデータが作成されたことのみ確認
			await t.run(async (ctx) => {
				const task = await ctx.db.get(taskId);
				expect(task).toBeDefined();
				expect(task?.jobType).toBe(QueueJobType.SCRAPE_BOOKING_COM_SLUG);

				const property = await ctx.db.get(propertyId);
				expect(property).toBeDefined();
				expect(property?.name).toBe(propertyName);
			});
		});

		// 実装が完了したらスキップを解除
		it.skip("should complete slug scraping and trigger review sync", async () => {
			// このテストは実装完了後に有効化
			// 注意: 実際の実装時は、上記のテストと統合するか、
			// 別途taskIdとpropertyIdをセットアップする必要があります
			// 期待される動作:
			// 1. タスクが完了ステータスになる
			// 2. プロパティにbookingComFacilitySlugが保存される
			// 3. レビュー同期タスクが作成される
		});

		it("should scrape Booking.com and extract slug", async () => {
			// Arrange
			const userId = "user456";
			const propertyName = "ホテル日本橋";
			let taskId: Id<"syncQueue">;
			let propertyId: Id<"beds24Properties">;

			await t.run(async (ctx) => {
				// プロパティを作成
				propertyId = await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "beds24-456",
					beds24PropertyKey: "hotel-nihonbashi",
					name: propertyName,
					propertyType: "hotel",
					currency: "JPY",
					data: {},
					isDeleted: false,
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});

				// タスクを作成
				taskId = await ctx.db.insert("syncQueue", {
					userId,
					jobType: QueueJobType.SCRAPE_BOOKING_COM_SLUG,
					status: "processing",
					priority: JobPriority.MEDIUM,
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					startedAt: Date.now(),
					metadata: JSON.stringify({
						propertyId,
						propertyName,
						beds24PropertyKey: "hotel-nihonbashi",
						userId,
					} satisfies BookingComSlugSyncMetadata),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// 複数の施設が検索結果に含まれるケース
			const mockFetch = vi.fn().mockResolvedValue({
				ok: true,
				status: 200,
				text: async () => `
					<html>
						<body>
							<div data-testid="property-card">
								<a href="/hotel/jp/other-hotel.ja.html">Other Hotel</a>
							</div>
							<div data-testid="property-card">
								<a href="/hotel/jp/hotel-nihonbashi-tokyo.ja.html">
									<span>ホテル日本橋</span>
								</a>
							</div>
						</body>
					</html>
				`,
			} as Response);
			vi.stubGlobal("fetch", mockFetch);

			// 実装が追加されたら以下のアサーションを有効にする
			// Act & Assert
			// await t.run(async (ctx) => {
			// 	await ctx.runAction(internal.bookingComSlugSync.scrapeBookingComSlug, { taskId });
			// });

			// await t.run(async (ctx) => {
			// 	const property = await ctx.db.get(propertyId);
			// 	expect(property?.bookingComFacilitySlug).toBe("hotel-nihonbashi-tokyo");

			// 	// ScrapingAnt APIが正しく呼ばれたか確認
			// 	expect(mockFetch).toHaveBeenCalledWith(
			// 		expect.stringContaining("https://api.scrapingant.com/v2/general"),
			// 		expect.objectContaining({
			// 			method: "GET",
			// 			headers: expect.objectContaining({
			// 				"x-api-key": expect.any(String),
			// 			}),
			// 		}),
			// 	);
			// });

			// 現在はテストデータのみ確認
			await t.run(async (ctx) => {
				const task = await ctx.db.get(taskId);
				expect(task).toBeDefined();
				expect(mockFetch).toBeDefined();
			});
		});

		it("should update property with scraped slug", async () => {
			// Arrange
			const userId = "user789";
			const propertyName = "京都ガーデンホテル";
			let _taskId: Id<"syncQueue">;
			let propertyId: Id<"beds24Properties">;

			await t.run(async (ctx) => {
				// bookingComFacilitySlugがnullのプロパティを作成
				propertyId = await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "beds24-789",
					beds24PropertyKey: "kyoto-garden",
					name: propertyName,
					propertyType: "hotel",
					currency: "JPY",
					data: {},
					bookingComFacilitySlug: undefined,
					isDeleted: false,
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});

				// タスクを作成
				_taskId = await ctx.db.insert("syncQueue", {
					userId,
					jobType: QueueJobType.SCRAPE_BOOKING_COM_SLUG,
					status: "processing",
					priority: JobPriority.HIGH,
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					startedAt: Date.now(),
					metadata: JSON.stringify({
						propertyId,
						propertyName,
						beds24PropertyKey: "kyoto-garden",
						userId,
					} satisfies BookingComSlugSyncMetadata),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// モックレスポンス
			const mockFetch = vi.fn().mockResolvedValue({
				ok: true,
				status: 200,
				text: async () => `
					<html>
						<body>
							<div data-testid="property-card">
								<a href="/hotel/jp/kyoto-garden-hotel-abc123.ja.html">京都ガーデンホテル</a>
							</div>
						</body>
					</html>
				`,
			} as Response);
			vi.stubGlobal("fetch", mockFetch);

			// 実装が追加されたら有効化
			// Act & Assert
			// await t.run(async (ctx) => {
			// 	await ctx.runAction(internal.bookingComSlugSync.scrapeBookingComSlug, { taskId });
			// });

			// await t.run(async (ctx) => {
			// 	const property = await ctx.db.get(propertyId);
			// 	expect(property?.bookingComFacilitySlug).toBe("kyoto-garden-hotel-abc123");
			// 	expect(property?.updatedAt).toBeGreaterThan(property?.createdAt ?? 0);
			// });

			// 現在はテストデータのみ確認
			await t.run(async (ctx) => {
				const property = await ctx.db.get(propertyId);
				expect(property?.bookingComFacilitySlug).toBeUndefined();
			});
		});

		it("should create review sync task after successful scraping", async () => {
			// このテストは、スクレイピング成功後に自動的にレビュー同期タスクが作成されることを確認
			const userId = "user999";
			const propertyName = "大阪ベイホテル";
			let _taskId: Id<"syncQueue">;
			let propertyId: Id<"beds24Properties">;

			await t.run(async (ctx) => {
				// プロパティを作成
				propertyId = await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "beds24-999",
					beds24PropertyKey: "osaka-bay",
					name: propertyName,
					propertyType: "hotel",
					currency: "JPY",
					data: {},
					isDeleted: false,
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});

				// タスクを作成
				_taskId = await ctx.db.insert("syncQueue", {
					userId,
					jobType: QueueJobType.SCRAPE_BOOKING_COM_SLUG,
					status: "processing",
					priority: JobPriority.NORMAL,
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					startedAt: Date.now(),
					metadata: JSON.stringify({
						propertyId,
						propertyName,
						beds24PropertyKey: "osaka-bay",
						userId,
					} satisfies BookingComSlugSyncMetadata),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});

				// レビュー同期タスクが存在しないことを確認
				const existingReviewTask = await ctx.db
					.query("syncQueue")
					.filter((q) => q.eq(q.field("jobType"), QueueJobType.SYNC_REVIEWS))
					.filter((q) => q.eq(q.field("userId"), userId))
					.first();
				expect(existingReviewTask).toBeNull();
			});

			// モックレスポンス
			const mockFetch = vi.fn().mockResolvedValue({
				ok: true,
				status: 200,
				text: async () => `
					<html>
						<body>
							<div data-testid="property-card">
								<a href="/hotel/jp/osaka-bay-hotel.ja.html">大阪ベイホテル</a>
							</div>
						</body>
					</html>
				`,
			} as Response);
			vi.stubGlobal("fetch", mockFetch);

			// 実装が追加されたら以下を有効化
			// await t.run(async (ctx) => {
			// 	await ctx.runAction(internal.bookingComSlugSync.scrapeBookingComSlug, { taskId });
			// });

			// await t.run(async (ctx) => {
			// 	// レビュー同期タスクが作成されているか確認
			// 	const reviewSyncTask = await ctx.db
			// 		.query("syncQueue")
			// 		.filter((q) => q.eq(q.field("jobType"), QueueJobType.SYNC_REVIEWS))
			// 		.filter((q) => q.eq(q.field("userId"), userId))
			// 		.first();

			// 	expect(reviewSyncTask).toBeDefined();
			// 	expect(reviewSyncTask?.status).toBe("pending");
			// 	expect(reviewSyncTask?.priority).toBe(JobPriority.NORMAL);

			// 	// メタデータを確認
			// 	const metadata = reviewSyncTask?.metadata
			// 		? JSON.parse(reviewSyncTask.metadata as string)
			// 		: null;
			// 	expect(metadata?.propertyId).toBe(propertyId);
			// 	expect(metadata?.propertyName).toBe(propertyName);
			// 	expect(metadata?.bookingComFacilitySlug).toBe("osaka-bay-hotel");
			// });
		});

		it("should handle scraping errors gracefully", async () => {
			// ネットワークエラーや予期しないエラーが発生した場合の処理を確認
			const userId = "user-error";
			const propertyName = "Error Test Hotel";
			let _taskId: Id<"syncQueue">;
			let propertyId: Id<"beds24Properties">;

			await t.run(async (ctx) => {
				// プロパティを作成
				propertyId = await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "beds24-error",
					beds24PropertyKey: "error-hotel",
					name: propertyName,
					propertyType: "hotel",
					currency: "JPY",
					data: {},
					isDeleted: false,
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});

				// タスクを作成
				_taskId = await ctx.db.insert("syncQueue", {
					userId,
					jobType: QueueJobType.SCRAPE_BOOKING_COM_SLUG,
					status: "processing",
					priority: JobPriority.MEDIUM,
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					startedAt: Date.now(),
					metadata: JSON.stringify({
						propertyId,
						propertyName,
						beds24PropertyKey: "error-hotel",
						userId,
					} satisfies BookingComSlugSyncMetadata),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// ネットワークエラーをシミュレート
			const mockFetch = vi.fn().mockRejectedValue(new Error("Network error"));
			vi.stubGlobal("fetch", mockFetch);

			// 実装が追加されたら有効化
			// await t.run(async (ctx) => {
			// 	await ctx.runAction(internal.bookingComSlugSync.scrapeBookingComSlug, { taskId });
			// });

			// await t.run(async (ctx) => {
			// 	// タスクが失敗状態になっているか確認
			// 	const task = await ctx.db.get(taskId);
			// 	expect(task?.status).toBe("failed");
			// 	expect(task?.lastError).toContain("Network error");
			// 	expect(task?.attempts).toBe(1);

			// 	// プロパティのスラッグが更新されていないことを確認
			// 	const property = await ctx.db.get(propertyId);
			// 	expect(property?.bookingComFacilitySlug).toBeUndefined();

			// 	// レビュー同期タスクが作成されていないことを確認
			// 	const reviewSyncTask = await ctx.db
			// 		.query("syncQueue")
			// 		.filter((q) => q.eq(q.field("jobType"), QueueJobType.SYNC_REVIEWS))
			// 		.filter((q) => q.eq(q.field("userId"), userId))
			// 		.first();
			// 	expect(reviewSyncTask).toBeNull();
			// });
		});

		it("should handle rate limit errors", async () => {
			// レート制限エラーの場合、リトライ対象としてマークされることを確認
			const userId = "user-rate-limit";
			const propertyName = "Rate Limit Hotel";
			let _taskId: Id<"syncQueue">;
			let propertyId: Id<"beds24Properties">;

			await t.run(async (ctx) => {
				// プロパティを作成
				propertyId = await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "beds24-rate",
					beds24PropertyKey: "rate-limit-hotel",
					name: propertyName,
					propertyType: "hotel",
					currency: "JPY",
					data: {},
					isDeleted: false,
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});

				// タスクを作成
				_taskId = await ctx.db.insert("syncQueue", {
					userId,
					jobType: QueueJobType.SCRAPE_BOOKING_COM_SLUG,
					status: "processing",
					priority: JobPriority.MEDIUM,
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					startedAt: Date.now(),
					metadata: JSON.stringify({
						propertyId,
						propertyName,
						beds24PropertyKey: "rate-limit-hotel",
						userId,
					} satisfies BookingComSlugSyncMetadata),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// レート制限エラーをシミュレート
			const mockFetch = vi.fn().mockResolvedValue({
				ok: false,
				status: 429,
				statusText: "Too Many Requests",
				text: async () => "Rate limit exceeded",
			} as Response);
			vi.stubGlobal("fetch", mockFetch);

			// 実装が追加されたら有効化
			// await t.run(async (ctx) => {
			// 	await ctx.runAction(internal.bookingComSlugSync.scrapeBookingComSlug, { taskId });
			// });

			// await t.run(async (ctx) => {
			// 	// タスクがリトライ対象になっているか確認
			// 	const task = await ctx.db.get(taskId);
			// 	expect(task?.status).toBe("pending"); // リトライのためpendingに戻る
			// 	expect(task?.lastError).toContain("Rate limit");
			// 	expect(task?.attempts).toBe(1);
			// 	expect(task?.nextRetryAt).toBeDefined();
			// 	// 次回実行時刻が将来の時刻になっているか
			// 	expect(task?.nextRetryAt).toBeGreaterThan(Date.now());

			// 	// プロパティのスラッグが更新されていないことを確認
			// 	const property = await ctx.db.get(propertyId);
			// 	expect(property?.bookingComFacilitySlug).toBeUndefined();
			// });
		});

		it("should handle property not found error", async () => {
			// 検索結果に施設が見つからない場合の処理を確認
			const userId = "user-not-found";
			const propertyName = "Non Existent Hotel 特殊名";
			let taskId: Id<"syncQueue">;
			let propertyId: Id<"beds24Properties">;

			await t.run(async (ctx) => {
				// プロパティを作成
				propertyId = await ctx.db.insert("beds24Properties", {
					beds24PropertyId: "beds24-notfound",
					beds24PropertyKey: "nonexistent-hotel",
					name: propertyName,
					propertyType: "hotel",
					currency: "JPY",
					data: {},
					isDeleted: false,
					lastSyncedAt: Date.now(),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});

				// タスクを作成
				taskId = await ctx.db.insert("syncQueue", {
					userId,
					jobType: QueueJobType.SCRAPE_BOOKING_COM_SLUG,
					status: "processing",
					priority: JobPriority.MEDIUM,
					attempts: 0,
					maxAttempts: 3,
					scheduledFor: Date.now(),
					startedAt: Date.now(),
					metadata: JSON.stringify({
						propertyId,
						propertyName,
						beds24PropertyKey: "nonexistent-hotel",
						userId,
					} satisfies BookingComSlugSyncMetadata),
					createdAt: Date.now(),
					updatedAt: Date.now(),
				});
			});

			// 検索結果が0件のレスポンス
			const mockFetch = vi.fn().mockResolvedValue({
				ok: true,
				status: 200,
				text: async () => `
					<html>
						<body>
							<div class="no-results">
								<p>検索結果が見つかりませんでした</p>
							</div>
						</body>
					</html>
				`,
			} as Response);
			vi.stubGlobal("fetch", mockFetch);

			// 実装が追加されたら有効化
			// await t.run(async (ctx) => {
			// 	await ctx.runAction(internal.bookingComSlugSync.scrapeBookingComSlug, { taskId });
			// });

			// await t.run(async (ctx) => {
			// 	// タスクが失敗状態になっているか確認
			// 	const task = await ctx.db.get(taskId);
			// 	expect(task?.status).toBe("failed");
			// 	expect(task?.lastError).toContain("Property not found");

			// 	// プロパティのスラッグが更新されていないことを確認
			// 	const property = await ctx.db.get(propertyId);
			// 	expect(property?.bookingComFacilitySlug).toBeUndefined();

			// 	// レビュー同期タスクが作成されていないことを確認
			// 	const reviewSyncTask = await ctx.db
			// 		.query("syncQueue")
			// 		.filter((q) => q.eq(q.field("jobType"), QueueJobType.SYNC_REVIEWS))
			// 		.filter((q) => q.eq(q.field("userId"), userId))
			// 		.first();
			// 	expect(reviewSyncTask).toBeNull();
			// });

			// 現在はテストデータのみ確認
			await t.run(async (ctx) => {
				const task = await ctx.db.get(taskId);
				expect(task).toBeDefined();
				expect(mockFetch).toBeDefined();
			});
		});
	});
});
