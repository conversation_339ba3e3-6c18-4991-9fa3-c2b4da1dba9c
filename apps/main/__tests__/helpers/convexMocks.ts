import { vi } from "vitest";

/**
 * Convex ReactMutationモックを作成するヘルパー関数
 */
export function createMockMutation<T extends (...args: any[]) => any>() {
	const mockFn = vi.fn<T>();
	return Object.assign(mockFn, {
		withOptimisticUpdate: vi.fn().mockReturnThis(),
	});
}

/**
 * Convex ReactActionモックを作成するヘルパー関数
 */
export function createMockAction<T extends (...args: any[]) => any>() {
	return vi.fn<T>();
}
