import { screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import type { PropertyMetric } from "@/features/dashboard/components/PropertyMetrics";
import { PropertyMetrics } from "@/features/dashboard/components/PropertyMetrics";
import { renderWithChakra } from "@/vitest.setup";

describe("PropertyMetrics Integration Tests", () => {
	it("should handle consistent object shapes in useMemo correctly", () => {
		// 実際のデータ構造をシミュレート
		const properties: PropertyMetric[] = [
			{
				propertyId: 1,
				propertyName: "東京ホテル",
				totalRoomCount: 50,
				totalCapacity: 100,
				accommodationVariableCost: 1000000,
				operationFixedCost: 2000000,
			},
			{
				propertyId: 2,
				propertyName: "大阪ホテル",
				totalRoomCount: 30,
				totalCapacity: 60,
				// コスト情報なし
			},
		];

		// 選択された宿がある場合
		const { rerender } = renderWithChakra(
			<PropertyMetrics propertyMetrics={properties} selectedPropertyId={1} />,
		);

		// コスト情報が表示される
		expect(screen.getByText("宿泊変動費")).toBeInTheDocument();
		expect(screen.getByText("¥1M")).toBeInTheDocument();
		expect(screen.getByText("運営固定費")).toBeInTheDocument();
		expect(screen.getByText("¥2M")).toBeInTheDocument();

		// 選択を解除（全ての宿の集計表示）
		rerender(
			<PropertyMetrics
				propertyMetrics={properties}
				selectedPropertyId={null}
			/>,
		);

		// コスト情報は表示されない
		expect(screen.queryByText("宿泊変動費")).not.toBeInTheDocument();
		expect(screen.queryByText("運営固定費")).not.toBeInTheDocument();
		// 合計値は表示される
		expect(screen.getByText("80")).toBeInTheDocument(); // 合計部屋数
		expect(screen.getByText("160")).toBeInTheDocument(); // 合計定員

		// コスト情報のない宿を選択
		rerender(
			<PropertyMetrics propertyMetrics={properties} selectedPropertyId={2} />,
		);

		// コスト情報は表示されない（undefinedのため）
		expect(screen.queryByText("宿泊変動費")).not.toBeInTheDocument();
		expect(screen.queryByText("運営固定費")).not.toBeInTheDocument();
		// 部屋数と定員は表示される
		expect(screen.getByText("30")).toBeInTheDocument();
		expect(screen.getByText("60")).toBeInTheDocument();
	});

	it("should format large numbers correctly", () => {
		const properties: PropertyMetric[] = [
			{
				propertyId: 1,
				propertyName: "大規模リゾート",
				totalRoomCount: 500,
				totalCapacity: 2000,
				accommodationVariableCost: 15500000, // 15.5M
				operationFixedCost: 999999, // 999k
			},
		];

		renderWithChakra(
			<PropertyMetrics propertyMetrics={properties} selectedPropertyId={1} />,
		);

		// 大きな数値のフォーマットを確認
		expect(screen.getByText("¥15.5M")).toBeInTheDocument();
		expect(screen.getByText("¥999k")).toBeInTheDocument();
	});
});
