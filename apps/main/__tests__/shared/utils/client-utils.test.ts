import { beforeEach, describe, expect, it, vi } from "vitest";
import { handleError } from "@/shared/lib/error-handling";
import {
	CleanupManager,
	debounce,
	isBrowser,
} from "@/shared/utils/client-utils";

vi.mock("@/shared/lib/error-handling", () => ({
	handleError: vi.fn(),
}));

describe("client-utils", () => {
	describe("isBrowser", () => {
		it("should return true in browser environment", () => {
			expect(isBrowser()).toBe(true);
		});
	});

	describe("debounce", () => {
		beforeEach(() => {
			vi.useFakeTimers();
		});

		it("should delay function execution", () => {
			const mockFn = vi.fn();
			const debouncedFn = debounce(mockFn, 300);

			debouncedFn("test");
			expect(mockFn).not.toHaveBeenCalled();

			vi.advanceTimersByTime(300);
			expect(mockFn).toHaveBeenCalledWith("test");
			expect(mockFn).toHaveBeenCalledTimes(1);
		});

		it("should cancel previous calls", () => {
			const mockFn = vi.fn();
			const debouncedFn = debounce(mockFn, 300);

			debouncedFn("first");
			vi.advanceTimersByTime(100);
			debouncedFn("second");
			vi.advanceTimersByTime(100);
			debouncedFn("third");

			vi.advanceTimersByTime(300);
			expect(mockFn).toHaveBeenCalledWith("third");
			expect(mockFn).toHaveBeenCalledTimes(1);
		});
	});

	describe("CleanupManager", () => {
		beforeEach(() => {
			vi.clearAllMocks();
			vi.spyOn(console, "error").mockImplementation(() => {});
		});

		it("should add and execute cleanup functions", () => {
			const manager = new CleanupManager();
			const fn1 = vi.fn();
			const fn2 = vi.fn();

			manager.add(fn1);
			manager.add(fn2);
			manager.cleanup();

			expect(fn1).toHaveBeenCalledTimes(1);
			expect(fn2).toHaveBeenCalledTimes(1);
		});

		it("should prevent duplicate functions from being added", () => {
			const manager = new CleanupManager();
			const fn = vi.fn();

			manager.add(fn);
			manager.add(fn); // 同じ関数を再度追加
			manager.cleanup();

			expect(fn).toHaveBeenCalledTimes(1); // 1回だけ実行される
		});

		it("should continue executing other functions when one fails", () => {
			const manager = new CleanupManager();
			const fn1 = vi.fn();
			const fn2 = vi.fn(() => {
				throw new Error("Test error");
			});
			const fn3 = vi.fn();

			manager.add(fn1);
			manager.add(fn2);
			manager.add(fn3);
			manager.cleanup();

			expect(fn1).toHaveBeenCalledTimes(1);
			expect(fn2).toHaveBeenCalledTimes(1);
			expect(fn3).toHaveBeenCalledTimes(1);
			expect(console.error).toHaveBeenCalledWith(
				"Cleanup function 1 failed:",
				expect.any(Error),
			);
		});

		it("should send errors to Sentry when cleanup functions fail", () => {
			const manager = new CleanupManager();
			const error1 = new Error("Error 1");
			const error2 = new Error("Error 2");

			manager.add(() => {
				throw error1;
			});
			manager.add(() => {}); // 正常な関数
			manager.add(() => {
				throw error2;
			});

			manager.cleanup();

			expect(handleError).toHaveBeenCalledWith(
				expect.objectContaining({
					message: "2 cleanup functions failed",
				}),
				expect.objectContaining({
					failedCount: 2,
					totalCount: 3,
					errors: expect.arrayContaining([
						expect.objectContaining({
							index: 0,
							message: "Error 1",
							stack: expect.any(String),
						}),
						expect.objectContaining({
							index: 2,
							message: "Error 2",
							stack: expect.any(String),
						}),
					]),
					feature: "CleanupManager",
				}),
			);
		});

		it("should clear all functions after cleanup", () => {
			const manager = new CleanupManager();
			const fn = vi.fn();

			manager.add(fn);
			manager.cleanup();
			expect(fn).toHaveBeenCalledTimes(1);

			// 再度cleanupを呼んでも関数は実行されない
			manager.cleanup();
			expect(fn).toHaveBeenCalledTimes(1);
		});

		it("should handle non-Error objects thrown in cleanup functions", () => {
			const manager = new CleanupManager();

			manager.add(() => {
				throw "string error";
			});
			manager.add(() => {
				throw { message: "object error" };
			});

			manager.cleanup();

			expect(handleError).toHaveBeenCalledWith(
				expect.any(Error),
				expect.objectContaining({
					errors: expect.arrayContaining([
						expect.objectContaining({
							index: 0,
							message: "string error",
							stack: undefined,
						}),
						expect.objectContaining({
							index: 1,
							message: "[object Object]",
							stack: undefined,
						}),
					]),
				}),
			);
		});
	});
});
