import { renderHook } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import { useNavigationItems } from "@/shared/hooks/useNavigationItems";

describe("useNavigationItems", () => {
	it("should return top and bottom navigation items", () => {
		const { result } = renderHook(() => useNavigationItems());

		expect(result.current).toHaveProperty("topItems");
		expect(result.current).toHaveProperty("bottomItems");
		expect(Array.isArray(result.current.topItems)).toBe(true);
		expect(Array.isArray(result.current.bottomItems)).toBe(true);
	});

	it("should have dashboard in top items", () => {
		const { result } = renderHook(() => useNavigationItems());

		const dashboardItem = result.current.topItems.find(
			(item) => item.id === "dashboard",
		);
		expect(dashboardItem).toBeDefined();
		expect(dashboardItem?.label).toBe("ダッシュボード");
		expect(dashboardItem?.href).toBe("/app");
	});

	it("should have manual and settings in bottom items", () => {
		const { result } = renderHook(() => useNavigationItems());

		const manualItem = result.current.bottomItems.find(
			(item) => item.id === "manual",
		);
		const settingsItem = result.current.bottomItems.find(
			(item) => item.id === "settings",
		);

		expect(manualItem).toBeDefined();
		expect(manualItem?.label).toBe("マニュアル");
		expect(manualItem?.isExternal).toBe(true);

		expect(settingsItem).toBeDefined();
		expect(settingsItem?.label).toBe("設定");
		expect(settingsItem?.href).toBe("/app/settings");
	});

	it("should return the same items on each render", () => {
		const { result, rerender } = renderHook(() => useNavigationItems());

		const firstTopItems = result.current.topItems;
		const firstBottomItems = result.current.bottomItems;

		rerender();

		expect(result.current.topItems).toBe(firstTopItems);
		expect(result.current.bottomItems).toBe(firstBottomItems);
	});
});
