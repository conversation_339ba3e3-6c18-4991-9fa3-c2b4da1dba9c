import { screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";
import type { PropertyMetric } from "@/features/dashboard/components/PropertyMetrics";
import { PropertyMetrics } from "@/features/dashboard/components/PropertyMetrics";
import { renderWithChakra } from "@/vitest.setup";

describe("PropertyMetrics", () => {
	const mockProperties: PropertyMetric[] = [
		{
			propertyId: 1,
			propertyName: "テストホテル",
			totalRoomCount: 10,
			totalCapacity: 20,
			accommodationVariableCost: 100000,
			operationFixedCost: 200000,
		},
		{
			propertyId: 2,
			propertyName: "ゼロコストホテル",
			totalRoomCount: 5,
			totalCapacity: 10,
			accommodationVariableCost: 0, // ゼロ値のテスト
			operationFixedCost: 0, // ゼロ値のテスト
		},
	];

	it("should display property metrics with zero values correctly", () => {
		renderWithChakra(
			<PropertyMetrics
				propertyMetrics={mockProperties}
				selectedPropertyId={2}
			/>,
		);

		// ゼロ値が正しく表示されることを確認
		const zeroValues = screen.getAllByText("¥0");
		expect(zeroValues).toHaveLength(2); // 宿泊変動費と運営固定費の両方
		expect(screen.getByText("宿泊変動費")).toBeInTheDocument();
		expect(screen.getByText("運営固定費")).toBeInTheDocument();
	});

	it("should not display cost fields when showing aggregated data", () => {
		renderWithChakra(
			<PropertyMetrics
				propertyMetrics={mockProperties}
				selectedPropertyId={null}
			/>,
		);

		// 集計データの場合、コストフィールドが表示されないことを確認
		expect(screen.queryByText("宿泊変動費")).not.toBeInTheDocument();
		expect(screen.queryByText("運営固定費")).not.toBeInTheDocument();

		// 部屋数と定員は表示される
		expect(screen.getByText("15")).toBeInTheDocument(); // 合計部屋数
		expect(screen.getByText("30")).toBeInTheDocument(); // 合計定員
	});

	it("should display property metrics with undefined values correctly", () => {
		const propertiesWithUndefined: PropertyMetric[] = [
			{
				propertyId: 3,
				propertyName: "部分データホテル",
				totalRoomCount: 8,
				totalCapacity: 16,
				// accommodationVariableCost と operationFixedCost は undefined
			},
		];

		renderWithChakra(
			<PropertyMetrics
				propertyMetrics={propertiesWithUndefined}
				selectedPropertyId={3}
			/>,
		);

		// undefinedの場合、コストフィールドが表示されないことを確認
		expect(screen.queryByText("宿泊変動費")).not.toBeInTheDocument();
		expect(screen.queryByText("運営固定費")).not.toBeInTheDocument();
	});
});
