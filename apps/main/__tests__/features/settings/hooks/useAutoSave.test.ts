import { act, renderHook } from "@testing-library/react";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { useAutoSave } from "@/features/settings/hooks/useAutoSave";

describe("useAutoSave", () => {
	beforeEach(() => {
		vi.clearAllMocks();
		vi.useFakeTimers();
	});

	afterEach(() => {
		vi.useRealTimers();
	});

	describe("初期状態", () => {
		it("初期状態が正しい", () => {
			const onSave = vi.fn();
			const value = "test value";
			const { result } = renderHook(() => useAutoSave(value, { onSave }));

			expect(result.current.isSaving).toBe(false);
			expect(result.current.isSuccess).toBe(false);
			expect(result.current.error).toBeNull();
		});
	});

	describe("デバウンス機能", () => {
		it("指定時間後に保存関数を呼び出す", async () => {
			const onSave = vi.fn().mockResolvedValue(undefined);
			const value = { name: "test" };

			const { rerender } = renderHook(
				({ value }) => useAutoSave(value, { onSave, debounceMs: 1000 }),
				{ initialProps: { value } },
			);

			// 値を変更してデバウンスを開始
			rerender({ value: { name: "updated" } });

			expect(onSave).not.toHaveBeenCalled();

			// デバウンス時間を進める
			await act(async () => {
				vi.advanceTimersByTime(1000);
			});

			expect(onSave).toHaveBeenCalledWith({ name: "updated" });
		});

		it("連続した変更で最後の値のみ保存する", async () => {
			const onSave = vi.fn().mockResolvedValue(undefined);

			const { rerender } = renderHook(
				({ value }) => useAutoSave(value, { onSave, debounceMs: 500 }),
				{ initialProps: { value: { name: "initial" } } },
			);

			// 複数回データを変更
			rerender({ value: { name: "second" } });
			act(() => {
				vi.advanceTimersByTime(200);
			});

			rerender({ value: { name: "third" } });
			act(() => {
				vi.advanceTimersByTime(200);
			});

			rerender({ value: { name: "final" } });

			// デバウンス時間を完全に進める
			await act(async () => {
				vi.advanceTimersByTime(500);
			});

			expect(onSave).toHaveBeenCalledTimes(1);
			expect(onSave).toHaveBeenCalledWith({ name: "final" });
		});
	});

	describe("バリデーション統合", () => {
		it("バリデーション成功時のみ保存を実行する", async () => {
			const onSave = vi.fn().mockResolvedValue(undefined);
			const validate = vi.fn().mockReturnValue(true);
			const value = { email: "<EMAIL>" };

			const { rerender } = renderHook(
				({ value }) =>
					useAutoSave(value, { onSave, validate, debounceMs: 100 }),
				{ initialProps: { value } },
			);

			// 値を変更してデバウンスを開始
			rerender({ value: { email: "<EMAIL>" } });

			await act(async () => {
				vi.advanceTimersByTime(100);
			});

			expect(validate).toHaveBeenCalledWith({ email: "<EMAIL>" });
			expect(onSave).toHaveBeenCalledWith({ email: "<EMAIL>" });
		});

		it("バリデーション失敗時は保存をスキップする", async () => {
			const onSave = vi.fn();
			const validate = vi.fn().mockReturnValue(false);
			const value = { email: "invalid-email" };

			const { rerender } = renderHook(
				({ value }) =>
					useAutoSave(value, { onSave, validate, debounceMs: 100 }),
				{ initialProps: { value } },
			);

			// 値を変更してデバウンスを開始
			rerender({ value: { email: "still-invalid" } });

			await act(async () => {
				vi.advanceTimersByTime(100);
			});

			expect(validate).toHaveBeenCalled();
			expect(onSave).not.toHaveBeenCalled();
		});
	});

	describe("保存状態の管理", () => {
		it("保存中の状態を正しく管理する", async () => {
			const onSave = vi
				.fn()
				.mockImplementation(
					() => new Promise((resolve) => setTimeout(resolve, 100)),
				);
			const value = { name: "test" };

			const { result, rerender } = renderHook(
				({ value }) => useAutoSave(value, { onSave, debounceMs: 50 }),
				{ initialProps: { value } },
			);

			expect(result.current.isSaving).toBe(false);

			// 値を変更してデバウンスを開始
			rerender({ value: { name: "updated" } });

			await act(async () => {
				vi.advanceTimersByTime(50);
			});

			expect(result.current.isSaving).toBe(true);

			await act(async () => {
				vi.advanceTimersByTime(100);
			});

			expect(result.current.isSaving).toBe(false);
		});

		it("保存成功状態を更新する", async () => {
			const onSave = vi.fn().mockResolvedValue(undefined);
			const value = { name: "test" };

			const { result, rerender } = renderHook(
				({ value }) => useAutoSave(value, { onSave, debounceMs: 50 }),
				{ initialProps: { value } },
			);

			expect(result.current.isSuccess).toBe(false);

			// 値を変更してデバウンスを開始
			rerender({ value: { name: "updated" } });

			await act(async () => {
				vi.advanceTimersByTime(50);
			});

			expect(result.current.isSuccess).toBe(true);
		});
	});

	describe("エラーハンドリング", () => {
		it("保存エラーを適切に処理する", async () => {
			const saveError = new Error("保存に失敗しました");
			const onSave = vi.fn().mockRejectedValue(saveError);
			const value = { name: "test" };

			const { result, rerender } = renderHook(
				({ value }) => useAutoSave(value, { onSave, debounceMs: 50 }),
				{ initialProps: { value } },
			);

			// 値を変更してデバウンスを開始
			rerender({ value: { name: "updated" } });

			await act(async () => {
				vi.advanceTimersByTime(50);
			});

			expect(result.current.error).toBe(saveError);
			expect(result.current.isSaving).toBe(false);
		});

		it("新しい保存でエラーをクリアする", async () => {
			const saveError = new Error("保存に失敗しました");
			const onSave = vi
				.fn()
				.mockRejectedValueOnce(saveError)
				.mockResolvedValue(undefined);

			const { result, rerender } = renderHook(
				({ value }) => useAutoSave(value, { onSave, debounceMs: 50 }),
				{ initialProps: { value: { name: "test" } } },
			);

			// 最初の保存でエラーを発生させる
			rerender({ value: { name: "error" } });
			await act(async () => {
				vi.advanceTimersByTime(50);
			});

			expect(result.current.error).toBe(saveError);

			// 新しいデータで再実行
			rerender({ value: { name: "success" } });
			await act(async () => {
				vi.advanceTimersByTime(50);
			});

			expect(result.current.error).toBeNull();
			expect(result.current.isSuccess).toBe(true);
		});
	});

	describe("手動保存とリセット", () => {
		it("save関数で即座に保存を実行する", async () => {
			const onSave = vi.fn().mockResolvedValue(undefined);
			const value = { name: "test" };

			const { result } = renderHook(() =>
				useAutoSave(value, { onSave, debounceMs: 1000 }),
			);

			// 手動で保存を実行
			await act(async () => {
				await result.current.save();
			});

			expect(onSave).toHaveBeenCalledWith({ name: "test" });
		});

		it("reset関数で状態をリセットする", async () => {
			const saveError = new Error("保存に失敗しました");
			const onSave = vi.fn().mockRejectedValue(saveError);
			const value = { name: "test" };

			const { result, rerender } = renderHook(
				({ value }) => useAutoSave(value, { onSave, debounceMs: 50 }),
				{ initialProps: { value } },
			);

			// エラーを発生させる
			rerender({ value: { name: "error" } });
			await act(async () => {
				vi.advanceTimersByTime(50);
			});

			expect(result.current.error).toBe(saveError);

			// リセット
			act(() => {
				result.current.reset();
			});

			expect(result.current.error).toBeNull();
			expect(result.current.isSuccess).toBe(false);
			expect(result.current.isSaving).toBe(false);
		});
	});

	describe("コールバック", () => {
		it("保存成功時にonSuccessコールバックが呼ばれる", async () => {
			const onSave = vi.fn().mockResolvedValue(undefined);
			const onSuccess = vi.fn();
			const value = { name: "test" };

			const { rerender } = renderHook(
				({ value }) =>
					useAutoSave(value, { onSave, onSuccess, debounceMs: 50 }),
				{ initialProps: { value } },
			);

			// 値を変更してデバウンスを開始
			rerender({ value: { name: "updated" } });

			await act(async () => {
				vi.advanceTimersByTime(50);
			});

			expect(onSuccess).toHaveBeenCalled();
		});

		it("保存失敗時にonErrorコールバックが呼ばれる", async () => {
			const saveError = new Error("保存エラー");
			const onSave = vi.fn().mockRejectedValue(saveError);
			const onError = vi.fn();
			const value = { name: "test" };

			const { rerender } = renderHook(
				({ value }) => useAutoSave(value, { onSave, onError, debounceMs: 50 }),
				{ initialProps: { value } },
			);

			// 値を変更してデバウンスを開始
			rerender({ value: { name: "error" } });

			await act(async () => {
				vi.advanceTimersByTime(50);
			});

			expect(onError).toHaveBeenCalledWith(saveError);
		});
	});

	describe("クリーンアップ", () => {
		it("アンマウント時にペンディングの保存をクリーンアップする", () => {
			const onSave = vi.fn();
			const value = { name: "test" };

			const { unmount, rerender } = renderHook(
				({ value }) => useAutoSave(value, { onSave, debounceMs: 1000 }),
				{ initialProps: { value } },
			);

			// 値を変更してデバウンスを開始
			rerender({ value: { name: "updated" } });

			unmount();

			act(() => {
				vi.advanceTimersByTime(1000);
			});

			// アンマウント後は保存が実行されない
			expect(onSave).not.toHaveBeenCalled();
		});
	});
});
