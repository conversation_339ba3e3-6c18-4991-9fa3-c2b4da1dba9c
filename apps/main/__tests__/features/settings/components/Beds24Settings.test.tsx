import { ChakraProvider, defaultSystem } from "@chakra-ui/react";
import {
	type RenderOptions,
	render,
	screen,
	waitFor,
} from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import type React from "react";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import {
	createMockAction,
	createMockMutation,
} from "@/__tests__/helpers/convexMocks";
import type { Id } from "@/convex/_generated/dataModel";
import { Beds24Settings } from "@/features/settings/components/Beds24Settings";
import { useAutoSave } from "@/features/settings/hooks/useAutoSave";
import { useUserSettings } from "@/features/userSettings/hooks/useUserSettings";

// モックの設定
vi.mock("@/features/userSettings/hooks/useUserSettings");
vi.mock("@/features/settings/hooks/useAutoSave");

// カスタムレンダラー
const customRender = (
	ui: React.ReactElement,
	options?: Omit<RenderOptions, "wrapper">,
) =>
	render(ui, {
		wrapper: ({ children }) => (
			<ChakraProvider value={defaultSystem}>{children}</ChakraProvider>
		),
		...options,
	});

const mockUpdateBeds24RefreshToken = Object.assign(
	vi.fn().mockResolvedValue({ wasUpdated: true }),
	{ withOptimisticUpdate: vi.fn().mockReturnThis() },
);
const mockUpdateBeds24Settings = Object.assign(
	vi.fn().mockResolvedValue({
		id: "test-id",
		wasUpdated: true,
		wasCreated: false,
	}),
	{ withOptimisticUpdate: vi.fn().mockReturnThis() },
);
const mockSave = vi.fn();
const mockReset = vi.fn();

describe("Beds24Settings", () => {
	const user = userEvent.setup();

	beforeEach(() => {
		// useUserSettingsのモック設定
		vi.mocked(useUserSettings).mockReturnValue({
			userSettings: {
				_id: "test-id" as Id<"userSettings">,
				_creationTime: Date.now(),
				userId: "test-user-id",
				theme: "light",
				beds24: {
					refreshToken: "",
				},
				has_beds24_token: false,
				createdAt: Date.now(),
				updatedAt: Date.now(),
			},
			updateBeds24RefreshToken: mockUpdateBeds24RefreshToken,
			isLoading: false,
			isAuthenticated: true,
			updateUserSettings: createMockMutation(),
			updateUserSettingsField: createMockMutation(),
			deleteUserSettings: createMockMutation(),
			updateBeds24Settings: mockUpdateBeds24Settings,
			initializeUserSettings: createMockAction(),
			updateTheme: vi.fn(),
		});

		// useAutoSaveのモック設定
		vi.mocked(useAutoSave).mockReturnValue({
			isSaving: false,
			isSuccess: false,
			error: null,
			save: mockSave,
			reset: mockReset,
		});
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	describe("バリデーション", () => {
		it("空文字を許可する", async () => {
			customRender(<Beds24Settings />);
			const input = screen.getByPlaceholderText("リフレッシュトークンを入力");

			// 空文字のままでエラーが表示されないことを確認
			expect(input).toHaveValue("");
			expect(
				screen.queryByText(/152〜172文字である必要があります/),
			).not.toBeInTheDocument();
		});

		it("152文字以上172文字以下の文字列を許可する", async () => {
			customRender(<Beds24Settings />);
			const input = screen.getByPlaceholderText("リフレッシュトークンを入力");

			// 152文字の文字列を入力
			const validToken152 = "a".repeat(152);
			// pre-commit環境でのタイムアウトを防ぐため、pasteを使用
			await user.click(input);
			await user.paste(validToken152);

			expect(
				screen.queryByText(/152〜172文字である必要があります/),
			).not.toBeInTheDocument();

			// 172文字の文字列も確認
			await user.clear(input);
			const validToken172 = "b".repeat(172);
			await user.paste(validToken172);

			expect(
				screen.queryByText(/152〜172文字である必要があります/),
			).not.toBeInTheDocument();
		});

		it("152文字未満の文字列でエラーを表示する", async () => {
			customRender(<Beds24Settings />);
			const input = screen.getByPlaceholderText("リフレッシュトークンを入力");

			// 151文字の文字列を入力
			const invalidToken = "a".repeat(151);
			await user.click(input);
			await user.paste(invalidToken);

			expect(
				screen.getByText(/152〜172文字である必要があります/),
			).toBeInTheDocument();
		});

		it("172文字を超える文字列でエラーを表示する", async () => {
			customRender(<Beds24Settings />);
			const input = screen.getByPlaceholderText("リフレッシュトークンを入力");

			// 173文字の文字列を入力
			const invalidToken = "a".repeat(173);
			await user.click(input);
			await user.paste(invalidToken);

			expect(
				screen.getByText(/152〜172文字である必要があります/),
			).toBeInTheDocument();
		});
	});

	describe("トークン表示/非表示", () => {
		it("デフォルトではトークンが非表示", () => {
			customRender(<Beds24Settings />);
			const input = screen.getByPlaceholderText("リフレッシュトークンを入力");

			expect(input).toHaveAttribute("type", "password");
		});

		it("表示ボタンをクリックするとトークンが表示される", async () => {
			customRender(<Beds24Settings />);
			const input = screen.getByPlaceholderText("リフレッシュトークンを入力");
			const toggleButton = screen.getByLabelText("トークンを表示");

			await user.click(toggleButton);

			expect(input).toHaveAttribute("type", "text");
			expect(screen.getByLabelText("トークンを隠す")).toBeInTheDocument();
		});
	});

	describe("自動保存との統合", () => {
		it("バリデーションエラーがある場合は保存されない", async () => {
			const mockOnSave = vi.fn();
			let callCount = 0;
			vi.mocked(useAutoSave).mockImplementation(
				(value: unknown, options?: Parameters<typeof useAutoSave>[1]) => {
					callCount++;
					// 初回のマウント時は除外（空文字列のバリデーションは通る）
					if (callCount > 1) {
						// バリデーション関数を呼び出して確認
						if (
							options &&
							typeof options.validate === "function" &&
							typeof options.onSave === "function"
						) {
							const isValid = options.validate(value);
							if (isValid) {
								mockOnSave(value);
							}
						}
					}
					return {
						isSaving: false,
						isSuccess: false,
						error: null,
						save: mockSave,
						reset: mockReset,
					};
				},
			);

			customRender(<Beds24Settings />);
			const input = screen.getByPlaceholderText("リフレッシュトークンを入力");

			// 無効な文字列を入力
			const invalidToken = "short";
			await user.click(input);
			await user.paste(invalidToken);

			// onSaveが呼ばれないことを確認
			expect(mockOnSave).not.toHaveBeenCalled();
		});

		it("有効な値の場合は自動保存される", async () => {
			let _savedValue = "";
			vi.mocked(useAutoSave).mockImplementation((value, options) => {
				// onSaveコールバックを実行
				if (
					options &&
					typeof options.validate === "function" &&
					typeof options.onSave === "function"
				) {
					if (options.validate(value)) {
						_savedValue = value as string;
						options.onSave(value);
					}
				}
				return {
					isSaving: false,
					isSuccess: true,
					error: null,
					save: mockSave,
					reset: mockReset,
				};
			});

			customRender(<Beds24Settings />);
			const input = screen.getByPlaceholderText("リフレッシュトークンを入力");

			// 有効な文字列を入力
			const validToken = "a".repeat(160);
			await user.click(input);
			await user.paste(validToken);

			// updateBeds24Settingsが呼ばれることを確認
			await waitFor(() => {
				expect(mockUpdateBeds24Settings).toHaveBeenCalledWith({
					refreshToken: validToken,
				});
			});

			// 戻り値が正しいことを確認
			const result = await mockUpdateBeds24Settings({
				refreshToken: validToken,
			});
			expect(result).toEqual({
				id: "test-id",
				wasUpdated: true,
				wasCreated: false,
			});
		});

		it("ステータス表示が存在しないことを確認", () => {
			vi.mocked(useAutoSave).mockReturnValue({
				isSaving: true,
				isSuccess: true,
				error: new Error("test error"),
				save: mockSave,
				reset: mockReset,
			});

			customRender(<Beds24Settings />);

			// ステータス表示が存在しないことを確認
			expect(screen.queryByText("保存中...")).not.toBeInTheDocument();
			expect(screen.queryByText("保存済み")).not.toBeInTheDocument();
			expect(screen.queryByText("保存エラー")).not.toBeInTheDocument();
			expect(
				screen.queryByText("変更は自動的に保存されます"),
			).not.toBeInTheDocument();
		});

		it("更新が成功しなかった場合のテスト", async () => {
			// 更新が成功しなかった場合のモック設定を新しく設定
			const mockUpdateBeds24SettingsFalse = Object.assign(
				vi.fn().mockResolvedValue({
					id: "test-id",
					wasUpdated: false,
					wasCreated: false,
				}),
				{ withOptimisticUpdate: vi.fn().mockReturnThis() },
			);

			vi.mocked(useUserSettings).mockReturnValue({
				userSettings: {
					_id: "test-id" as Id<"userSettings">,
					_creationTime: Date.now(),
					userId: "test-user-id",
					theme: "light",
					beds24: {
						refreshToken: "",
					},
					has_beds24_token: false,
					createdAt: Date.now(),
					updatedAt: Date.now(),
				},
				updateBeds24RefreshToken: mockUpdateBeds24RefreshToken,
				isLoading: false,
				isAuthenticated: true,
				updateUserSettings: createMockMutation(),
				updateUserSettingsField: createMockMutation(),
				deleteUserSettings: createMockMutation(),
				updateBeds24Settings: mockUpdateBeds24SettingsFalse,
				initializeUserSettings: createMockAction(),
				updateTheme: vi.fn(),
			});

			let _savedValue = "";
			vi.mocked(useAutoSave).mockImplementation((value, options) => {
				if (
					options &&
					typeof options.validate === "function" &&
					typeof options.onSave === "function"
				) {
					if (options.validate(value)) {
						_savedValue = value as string;
						options.onSave(value);
					}
				}
				return {
					isSaving: false,
					isSuccess: false,
					error: null,
					save: mockSave,
					reset: mockReset,
				};
			});

			customRender(<Beds24Settings />);
			const input = screen.getByPlaceholderText("リフレッシュトークンを入力");

			// 有効な文字列を入力
			const validToken = "a".repeat(160);
			await user.click(input);
			await user.paste(validToken);

			// updateBeds24Settingsが呼ばれることを確認
			await waitFor(() => {
				expect(mockUpdateBeds24SettingsFalse).toHaveBeenCalledWith({
					refreshToken: validToken,
				});
			});

			// 戻り値が false の場合の確認
			const result = await mockUpdateBeds24SettingsFalse({
				refreshToken: validToken,
			});
			expect(result).toEqual({
				id: "test-id",
				wasUpdated: false,
				wasCreated: false,
			});
		});
	});

	describe("ローディング状態", () => {
		it("ローディング中は入力とボタンが無効になる", () => {
			vi.mocked(useUserSettings).mockReturnValue({
				userSettings: null,
				updateBeds24RefreshToken: mockUpdateBeds24RefreshToken,
				isLoading: true,
				isAuthenticated: true,
				updateUserSettings: createMockMutation(),
				updateUserSettingsField: createMockMutation(),
				deleteUserSettings: createMockMutation(),
				updateBeds24Settings: mockUpdateBeds24Settings,
				initializeUserSettings: createMockAction(),
				updateTheme: vi.fn(),
			});

			customRender(<Beds24Settings />);

			const input = screen.getByPlaceholderText("リフレッシュトークンを入力");
			const toggleButton = screen.getByLabelText("トークンを表示");

			expect(input).toBeDisabled();
			expect(toggleButton).toBeDisabled();
		});
	});

	describe("初期値の処理", () => {
		it("既存のトークンがある場合は初期値として表示される", () => {
			const existingToken = "a".repeat(160);
			vi.mocked(useUserSettings).mockReturnValue({
				userSettings: {
					_id: "test-id" as Id<"userSettings">,
					_creationTime: Date.now(),
					userId: "test-user-id",
					theme: "light",
					beds24: {
						refreshToken: existingToken,
					},
					has_beds24_token: true,
					createdAt: Date.now(),
					updatedAt: Date.now(),
				},
				updateBeds24RefreshToken: mockUpdateBeds24RefreshToken,
				isLoading: false,
				isAuthenticated: true,
				updateUserSettings: createMockMutation(),
				updateUserSettingsField: createMockMutation(),
				deleteUserSettings: createMockMutation(),
				updateBeds24Settings: mockUpdateBeds24Settings,
				initializeUserSettings: createMockAction(),
				updateTheme: vi.fn(),
			});

			customRender(<Beds24Settings />);

			const input = screen.getByPlaceholderText("リフレッシュトークンを入力");
			expect(input).toHaveValue(existingToken);
		});

		it("変更がない場合は自動保存メッセージを表示しない", () => {
			vi.mocked(useAutoSave).mockReturnValue({
				isSaving: false,
				isSuccess: false,
				error: null,
				save: mockSave,
				reset: mockReset,
			});

			customRender(<Beds24Settings />);

			// ステータス表示が存在しないことを確認
			expect(screen.queryByText("保存中...")).not.toBeInTheDocument();
			expect(screen.queryByText("保存済み")).not.toBeInTheDocument();
			expect(screen.queryByText("保存エラー")).not.toBeInTheDocument();
			expect(
				screen.queryByText("変更は自動的に保存されます"),
			).not.toBeInTheDocument();
		});
	});
});
