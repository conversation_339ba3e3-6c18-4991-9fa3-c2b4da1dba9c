import { ChakraProvider, defaultSystem } from "@chakra-ui/react";
import { render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import { DisplaySettingsTab } from "@/src/features/settings/components/DisplaySettingsTab";

// ThemeSelectorコンポーネントをモック
vi.mock("@/src/features/settings/components/ThemeSelector", () => ({
	ThemeSelector: () => (
		<div data-testid="theme-selector">ThemeSelector Mock</div>
	),
}));

// カスタムrender関数
const customRender = (component: React.ReactElement) => {
	return render(component, {
		wrapper: ({ children }) => (
			<ChakraProvider value={defaultSystem}>{children}</ChakraProvider>
		),
	});
};

describe("DisplaySettingsTab", () => {
	it("コンポーネントが正しくレンダリングされる", () => {
		customRender(<DisplaySettingsTab />);

		// カードのルート要素が存在することを確認（chakra-card__rootクラスで確認）
		const cardRoot = document.querySelector(".chakra-card__root");
		expect(cardRoot).toBeInTheDocument();
	});

	it("タイトルが正しく表示される", () => {
		customRender(<DisplaySettingsTab />);

		const title = screen.getByText("テーマ設定");
		expect(title).toBeInTheDocument();
	});

	it("説明文が正しく表示される", () => {
		customRender(<DisplaySettingsTab />);

		const description = screen.getByText(
			"アプリケーションの表示テーマを選択します",
		);
		expect(description).toBeInTheDocument();
	});

	it("ThemeSelectorコンポーネントが含まれている", () => {
		customRender(<DisplaySettingsTab />);

		const themeSelector = screen.getByTestId("theme-selector");
		expect(themeSelector).toBeInTheDocument();
		expect(themeSelector).toHaveTextContent("ThemeSelector Mock");
	});

	it("VStackが適切なスタイルで配置されている", () => {
		const { container } = customRender(<DisplaySettingsTab />);

		// VStackの存在を確認（Chakra UIのVStackはdivとしてレンダリングされる）
		const vstack = container.firstChild as HTMLElement;
		expect(vstack).toBeInTheDocument();
		expect(vstack.tagName).toBe("DIV");
	});
});
