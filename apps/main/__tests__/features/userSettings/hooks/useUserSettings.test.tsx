import { renderHook, waitFor } from "@testing-library/react";
import { useAction, useConvexAuth, useMutation, useQuery } from "convex/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import type { Doc, Id } from "@/convex/_generated/dataModel";
import { useUserSettings } from "@/features/userSettings/hooks/useUserSettings";

// Mock the dependencies
vi.mock("convex/react");

describe("useUserSettings", () => {
	const mockUseConvexAuth = vi.mocked(useConvexAuth);
	const mockUseQuery = vi.mocked(useQuery);
	const mockUseMutation = vi.mocked(useMutation);
	const mockUseAction = vi.mocked(useAction);

	const mockUpdateUserSettings = Object.assign(vi.fn(), {
		withOptimisticUpdate: vi.fn().mockReturnThis(),
	});
	const mockUpdateUserSettingsField = Object.assign(vi.fn(), {
		withOptimisticUpdate: vi.fn().mockReturnThis(),
	});
	const mockDeleteUserSettings = Object.assign(vi.fn(), {
		withOptimisticUpdate: vi.fn().mockReturnThis(),
	});
	const mockInitializeUserSettings = vi.fn();

	beforeEach(() => {
		vi.clearAllMocks();

		// Default mock implementations
		mockUseConvexAuth.mockReturnValue({
			isAuthenticated: true,
			isLoading: false,
		});

		let mutationCallCount = 0;
		mockUseMutation.mockImplementation(() => {
			const mutations = [
				mockUpdateUserSettings,
				mockUpdateUserSettingsField,
				mockDeleteUserSettings,
			];
			return mutations[mutationCallCount++ % mutations.length];
		});

		mockUseAction.mockImplementation(() => {
			return mockInitializeUserSettings;
		});
	});

	it("未認証時はユーザー設定をスキップする", () => {
		mockUseConvexAuth.mockReturnValue({
			isAuthenticated: false,
			isLoading: false,
		});

		mockUseQuery.mockReturnValue(undefined);

		renderHook(() => useUserSettings());

		expect(mockUseQuery).toHaveBeenCalledWith(expect.any(Object), "skip");
	});

	it("認証済みでユーザー設定が存在する場合は設定を返す", () => {
		const mockSettings: Doc<"userSettings"> = {
			_id: "settings-id" as Id<"userSettings">,
			_creationTime: Date.now(),
			userId: "test-user-id",
			theme: "system",
			has_beds24_token: false,
			createdAt: Date.now(),
			updatedAt: Date.now(),
		};

		mockUseQuery.mockReturnValue(mockSettings);

		const { result } = renderHook(() => useUserSettings());

		expect(result.current.userSettings).toEqual(mockSettings);
		expect(result.current.isLoading).toBe(false);
		expect(result.current.isAuthenticated).toBe(true);
	});

	it("認証済みで設定が存在しない場合は初期化を実行する", async () => {
		// Navigator APIのモック
		Object.defineProperty(window.navigator, "language", {
			value: "en-US",
			configurable: true,
		});

		// Intl APIのモック
		global.Intl.DateTimeFormat = vi.fn().mockImplementation(() => ({
			resolvedOptions: () => ({ timeZone: "America/New_York" }),
		})) as unknown as typeof Intl.DateTimeFormat;

		mockUseQuery.mockReturnValue(null);
		mockInitializeUserSettings.mockResolvedValue("new-settings-id");

		renderHook(() => useUserSettings());

		await waitFor(() => {
			expect(mockInitializeUserSettings).toHaveBeenCalledWith({});
		});
	});

	it("テーマ更新のヘルパー関数が正しく動作する", () => {
		const mockSettings: Doc<"userSettings"> = {
			_id: "settings-id" as Id<"userSettings">,
			_creationTime: Date.now(),
			userId: "test-user-id",
			theme: "light",
			has_beds24_token: false,
			createdAt: Date.now(),
			updatedAt: Date.now(),
		};
		mockUseQuery.mockReturnValue(mockSettings);

		const { result } = renderHook(() => useUserSettings());

		result.current.updateTheme("dark");

		expect(mockUpdateUserSettingsField).toHaveBeenCalledWith({
			update: {
				field: "theme",
				value: "dark",
			},
		});
	});
});
