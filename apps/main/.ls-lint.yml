ls:
  app:
    .dir: kebab-case
    .tsx: kebab-case | PascalCase
    .ts: kebab-case | PascalCase
    .css: kebab-case
  
  components:
    .dir: kebab-case | PascalCase
    .tsx: PascalCase
    .ts: PascalCase
  
  convex:
    .dir: camelCase 
    .ts: camelCase | kebab-case
    .js: camelCase | kebab-case
  
  lib:
    .dir: kebab-case
    .ts: camelCase | kebab-case
    .tsx: camelCase | kebab-case
  
  public:
    .dir: kebab-case
    .svg: kebab-case
    .png: kebab-case
    .jpg: kebab-case
    .jpeg: kebab-case
    .ico: kebab-case

ignore:
  - node_modules
  - .next
  - dist
  - build
  - convex/_generated
  - .git
  - .cursor
  - __tests__
