import path from "node:path";
import type { StorybookConfig } from "@storybook/nextjs-vite";

const getAbsolutePath = (packageName: string): string =>
	path
		.dirname(require.resolve(path.join(packageName, "package.json")))
		.replace(/^file:\/\//, "");

const config: StorybookConfig = {
	stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
	addons: [
		getAbsolutePath("@storybook/addon-docs"),
		getAbsolutePath("@storybook/addon-onboarding"),
		getAbsolutePath("@storybook/addon-a11y"),
		getAbsolutePath("@storybook/addon-vitest"),
	],
	framework: {
		name: getAbsolutePath("@storybook/nextjs-vite"),
		options: {},
	},
	staticDirs: ["../public"],
};
export default config;
