import { <PERSON><PERSON><PERSON>rovider } from "@chakra-ui/react";
import type { Preview } from "@storybook/nextjs-vite";
import type { Decorator } from "@storybook/react";
import { ColorModeProvider } from "../src/components/ui/color-mode";
import { system } from "../src/theme/system";

const withProviders: Decorator = (Story, context) => {
	// Storybookのglobalsからカラーモードを取得
	const colorMode = context.globals?.theme || "light";

	return (
		<ChakraProvider value={system}>
			<ColorModeProvider defaultTheme={colorMode} forcedTheme={colorMode}>
				<Story />
			</ColorModeProvider>
		</ChakraProvider>
	);
};

const preview: Preview = {
	tags: ["autodocs"], // 自動ドキュメント生成を有効化
	decorators: [withProviders],
	parameters: {
		controls: {
			matchers: {
				color: /(background|color)$/i,
				date: /Date$/i,
			},
		},

		a11y: {
			// 'todo' - show a11y violations in the test UI only
			// 'error' - fail CI on a11y violations
			// 'off' - skip a11y checks entirely
			test: "todo",
		},
	},
	globalTypes: {
		theme: {
			name: "Theme",
			description: "Global theme for components",
			defaultValue: "light",
			toolbar: {
				icon: "moon",
				items: [
					{ value: "light", icon: "sun", title: "Light" },
					{ value: "dark", icon: "moon", title: "Dark" },
				],
				showName: true,
				dynamicTitle: true,
			},
		},
	},
};

export default preview;
