{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": true, "includes": ["**", "!**/node_modules/**", "!**/.next/**", "!**/build/**", "!**/out/**", "!**/coverage/**", "!**/storybook-static/**", "!**/.vercel/**", "!**/convex/_generated/**", "!**/*.tsbuildinfo"]}, "formatter": {"enabled": true, "indentStyle": "tab"}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noForEach": "off"}, "style": {"noNonNullAssertion": "off"}, "correctness": {"useExhaustiveDependencies": "off"}, "suspicious": {"noExplicitAny": "off"}, "a11y": {"useButtonType": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}