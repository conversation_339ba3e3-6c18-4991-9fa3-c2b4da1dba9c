name: CI

on:
  push:
    branches: [main, develop, "feature/**"]
  pull_request:
    branches: [main, develop]
  workflow_dispatch:

jobs:
  lint:
    name: Lint & Format Check
    runs-on: [self-hosted, Linux, ARM64, lint]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Enable pnpm via Corepack
        run: |
          corepack enable
          corepack prepare pnpm@10.13.1 --activate

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Setup TypeScript cache for type checking
        uses: actions/cache@v4
        with:
          path: |
            apps/*/tsconfig.tsbuildinfo
          key: ${{ runner.os }}-tsbuildinfo-lint-${{ hashFiles('apps/fumadocs/tsconfig.json', 'apps/main/tsconfig.json', 'apps/main/convex/tsconfig.json', 'tsconfig.base.json', 'tsconfig.build.json') }}
          restore-keys: |
            ${{ runner.os }}-tsbuildinfo-lint-

      - name: Run linter
        run: pnpm lint:all

      - name: Check formatting
        run: pnpm check:all

      - name: Run type check
        run: pnpm typecheck:all

      - name: Check protected files
        run: |
          if [ -f "./scripts/check-protected-files.sh" ]; then
            ./scripts/check-protected-files.sh --ci
          fi

  test:
    name: Test
    runs-on: [self-hosted, Linux, ARM64, test]
    
    strategy:
      matrix:
        workspace: [main]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Enable pnpm via Corepack
        run: |
          corepack enable
          corepack prepare pnpm@10.13.1 --activate

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Copy environment variables
        if: matrix.workspace == 'main'
        run: |
          cp apps/main/.env.example apps/main/.env.local

      - name: Run tests for ${{ matrix.workspace }}
        run: pnpm --filter @kadou-delta-next/${{ matrix.workspace }} test:run

      - name: Upload coverage
        if: matrix.workspace == 'main'
        uses: actions/upload-artifact@v4
        with:
          name: coverage-${{ matrix.workspace }}
          path: apps/${{ matrix.workspace }}/coverage/
          retention-days: 7

  build:
    name: Build ${{ matrix.workspace }}
    runs-on: ${{ matrix.workspace == 'main' && fromJSON('["self-hosted", "Linux", "ARM64", "build-main"]') || fromJSON('["self-hosted", "Linux", "ARM64", "build-fumadocs"]') }}
    
    strategy:
      matrix:
        workspace: [main, fumadocs]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Enable pnpm via Corepack
        run: |
          corepack enable
          corepack prepare pnpm@10.13.1 --activate

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Setup TypeScript incremental build cache
        uses: actions/cache@v4
        with:
          path: |
            apps/${{ matrix.workspace }}/tsconfig.tsbuildinfo
          key: ${{ runner.os }}-tsbuildinfo-${{ matrix.workspace }}-${{ hashFiles(format('apps/{0}/tsconfig.json', matrix.workspace), 'tsconfig.base.json', 'tsconfig.build.json') }}
          restore-keys: |
            ${{ runner.os }}-tsbuildinfo-${{ matrix.workspace }}-

      - name: Setup Next.js build cache
        uses: actions/cache@v4
        with:
          path: |
            apps/${{ matrix.workspace }}/.next/cache/
            apps/${{ matrix.workspace }}/node_modules/.cache/
          key: ${{ runner.os }}-nextjs-${{ matrix.workspace }}-${{ hashFiles('pnpm-lock.yaml', format('apps/{0}/next.config.*', matrix.workspace), format('apps/{0}/tsconfig.json', matrix.workspace)) }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ matrix.workspace }}-

      - name: Copy environment variables
        if: matrix.workspace == 'main'
        run: |
          cp apps/main/.env.example apps/main/.env.local

      - name: Build ${{ matrix.workspace }}
        run: pnpm --filter @kadou-delta-next/${{ matrix.workspace }} build
        env:
          NEXT_TELEMETRY_DISABLED: 1
          NODE_ENV: production

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-${{ matrix.workspace }}
          path: |
            apps/${{ matrix.workspace }}/.next/
            apps/${{ matrix.workspace }}/out/
          retention-days: 7

  verify:
    name: Verify All Checks
    runs-on: [self-hosted, Linux, ARM64, verify]
    needs: [lint, test, build]
    if: always()
    
    steps:
      - name: Check all jobs status
        run: |
          if [[ "${{ needs.lint.result }}" != "success" || \
              "${{ needs.test.result }}" != "success" || \
              "${{ needs.build.result }}" != "success" ]]; then
            echo "One or more jobs failed"
            exit 1
          fi
          echo "All checks passed!"
