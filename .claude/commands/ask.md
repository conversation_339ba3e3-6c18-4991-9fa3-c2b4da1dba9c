---
allowed-tools: Read, Grep, Glob, L<PERSON>, WebSearch, mcp__deepwiki, mcp__linear__search_documentation, mcp__sentry__search_docs, mcp__sentry__get_doc, mcp__o3__o3-search, mcp__convex__status, mcp__convex__tables, mcp__convex__functionSpec, mcp__beds24-api, mcp__lsmcp, ListMcpResourcesTool, ReadMcpResourceTool
description: コードベースやライブラリのドキュメントについて質問し、ファイルを変更せずに情報を提供します
---

ultrathink.

## Context

- User's question: $ARGUMENTS

## Your task

ユーザーからの質問に対して、コードベースを読み取り専用で調査し、ライブラリのドキュメントを検索して回答します。

## 使用可能なツール

### 基本ファイル操作ツール
- **Read**: ファイルの内容を読む
- **Grep**: ファイル内のパターンを検索
- **Glob**: ファイルパターンにマッチするファイルを検索
- **LS**: ディレクトリ内のファイルをリスト

### Web検索ツール
- **WebSearch**: 一般的なWeb検索
- **mcp__o3__o3-search**: AI強化されたWeb検索（エラー解決やトラブルシューティングに最適）

### ライブラリドキュメント検索
- **mcp__deepwiki__***: GitHub リポジトリのドキュメント（React, Next.js, Convex, Tailwind CSS, Clerk, Vitest等）
  - `read_wiki_structure`: リポジトリのドキュメント構造を表示
  - `read_wiki_contents`: ドキュメントの内容を読む
  - `ask_question`: リポジトリについて質問
- **mcp__sentry__search_docs**: Sentry SDK設定とインテグレーションガイド
- **mcp__linear__search_documentation**: Linear API ドキュメント

### コードベース分析ツール
- **mcp__lsmcp__***: Language Server Protocol ベースのコード分析
  - `list_tools`: 利用可能なLSPツールを表示
  - `get_hover`: シンボルの型情報とドキュメントを取得
  - `find_references`: シンボルのすべての参照を検索
  - `get_definitions`: シンボルの定義を取得
  - `get_document_symbols`: ファイル内のすべてのシンボルを取得

### プロジェクト固有のツール
- **mcp__convex__***: Convexバックエンドの情報
  - `status`: デプロイメント状態を確認
  - `tables`: データベーススキーマを表示
  - `functionSpec`: 関数のメタデータを取得
- **mcp__beds24-api__***: Beds24 API 情報
  - `searchEndpoints`: APIエンドポイントを検索
  - `getEndpointDetails`: エンドポイントの詳細仕様
  - `getApiSummary`: API概要を取得

### MCPリソースツール
- **ListMcpResourcesTool**: 利用可能なMCPリソースをリスト
- **ReadMcpResourceTool**: 特定のMCPリソースを読む

## ガイドライン
1. **ファイルの変更は行わない** - 読み取り専用での調査
2. **優先順位に従った検索**:
   - まずDeepWikiでライブラリドキュメントを検索
   - 複雑なエラーにはO3 Searchを使用
   - 一般的な情報はWebSearchを使用
3. **コード参照は具体的に** - 実際のファイルパスと行番号を示す（例: `app/routes/dashboard.tsx:45`）
4. **包括的な調査** - 複数のツールを組み合わせて完全な回答を提供

## 実行手順
1. 質問の内容を理解し、必要な情報源を特定
2. 適切なツールを選択して情報を収集
3. 複数の情報源から得た知識を統合
4. 整理された形式で回答を提供
