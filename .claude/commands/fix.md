ultrathink.

以下のエラーについて詳細な分析をお願いします。
エラー内容：
```$ARGUMENTS```

分析の指示：
表面的な問題の修正だけでなく、以下の点を深掘りし、根本的な原因を特定してください。その上で、将来同様の問題が発生することを防ぐための包括的な解決策を提案してください。
- 根本原因の調査・特定：
  - アーキテクチャ上の潜在的な問題点： システムの設計や構造に、このエラーを引き起こす可能性のある根本的な問題がないか調査し、具体的に指摘してください。
  - 考慮漏れのエッジケース： 開発時に想定されていなかった、あるいは見落とされていた特殊な状況や入力パターン（エッジケース）が、このエラーの引き金となっていないか検討してください。
  - 外部依存の仕様や利用方法の問題点: ライブラリ等の外部依存の仕様を `context7` を使って、このエラーを引き起こす可能性のある根本的な問題がないか調査し、具体的に指摘してください。
  - ToDo リストを使用してください。
- 包括的な解決策の提案：
  - 特定された根本原因を解消するために、どのような具体的な修正が必要か提示してください。
  - 将来的に類似の問題の再発を防ぐために、どのような予防策や改善策が考えられるか、具体的かつ網羅的に提案してください。
