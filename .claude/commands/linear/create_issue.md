---
allowed-tools: Task, TodoRead, TodoWrite, mcp__linear
description: 会話履歴を踏まえて引数のタイトルで Linear Issue を作成
---

ultrathink.

## Context

このタスクは、現在のセッションにおける会話履歴全体をインプットとします。
会話履歴を分析し、Issue作成に必要な以下の要素を整理してください。

- issue_title: `$ARGUMENTS`
- background: 議論の背景や文脈
- problem: 議論の中心となった問題や課題
- solution: 決定された、または議論された技術的な詳細や実装方針
- references: 会話で言及された関連コード、ファイル、URLなど
- acceptance_criteria: Issueを完了するための明確な基準（もし会話にあれば）

## Your tasks

1. **チーム情報の取得**
   - `mcp__linear__list_teams`を実行してチーム一覧を取得（取得できない場合は後続の処理を全て中止し、ユーザーに報告）
   - 適切なチームを選択

2. **ラベルの選択**
   - `mcp__linear__list_issue_labels`を実行して利用可能なラベル一覧を取得
   - 会話内容とタイトル「$ARGUMENTS」に基づいて最も適切なラベルを選択

3. **Issue作成**
   - `mcp__linear__create_issue`を実行
   - 必須パラメータ:
     - title: "$ARGUMENTS"
     - teamId: 選択したチームID
     - description: 以下を含む詳細な説明文
       - **背景**: 会話履歴から抽出した文脈
       - **問題/課題**: 議論された具体的な内容
       - **解決方針**: 会話で決まった実装方針（あれば）
       - **関連情報**: 言及されたファイル、コード、リンクなど
       - **完了条件**: 明確な完了基準
     - labelIds: 選択したラベルのID配列

4. **作成完了の報告**
   - 作成されたIssueのIDとURLを表示
   - 会話履歴から反映した主要なポイントをサマリー
