---
allowed-tools: Bash(git:*), Read
description: gitにステージング済みのファイルの変更内容を分析し、適切にステージング・コミット
---

## Target

ステージング済みのファイル

## Context

- Current git status: !`git status`
- Current git diff (staged and unstaged changes): !`git diff HEAD`
- Current branch: !`git branch --show-current`
- Recent commits: !`git log --oneline -10`

## Rules

- もしステージングされたファイルがない場合は、その旨を報告してコミットしない

## Your task

適切な粒度で `git commit -m "{コミットメッセージ}"`

## Commit message format

- 形式: [type]: [subject]
- 言語: 日本語
- ステージング済みのファイルの主要な変更を要約
