---
allowed-tools: Bash(git:*), Read
description: 現在のセッションの変更内容を分析し、適切にステージング・コミット
---

## Target

このセッションで変更・作成したファイル

## Context

- Current git status: !`git status`
- Current git diff (staged and unstaged changes): !`git diff HEAD`
- Current branch: !`git branch --show-current`
- Recent commits: !`git log --oneline -10`

## Commit message format

- 形式: [type]: [subject]
- 言語: 日本語
- このセッションで行った主要な作業を要約

## Workflow

1. 現在の変更状況を確認（git status）
2. 変更内容を詳細に確認（git diff）
3. コミットに含めるべきファイルを選別
4. 適切なファイルをステージング（git add）
5. ステージング内容を最終確認
6. コミットメッセージを作成してコミット
