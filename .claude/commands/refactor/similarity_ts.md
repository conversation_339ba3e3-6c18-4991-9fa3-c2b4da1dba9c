---
allowed-tools: <PERSON><PERSON>(similarity-ts:*), <PERSON>, <PERSON><PERSON>(rg:*), <PERSON><PERSON>(git:*), Glob, LS, Task, TodoRead, TodoWrite, mcp__lsmcp__find_references, mcp__deepwiki
description: ローカルファイルのコード重複を確認し、リファクタの計画を立てる
---

ultrathink.

## Context

- Similar code: !`similarity-ts .`
- Timestamp: !`date '+%Y%m%d-%H%M%S'`

## Your tasks

ローカルファイルのコード重複を確認し、リファクタの計画を立てて、".scratch/"ディレクトリに保存してください。

## Report file naming

similarity-report-{Timestamp}.md
