---
allowed-tools: Ba<PERSON>(knip:*), Read, Ba<PERSON>(rg:*), <PERSON><PERSON>(git:*), Glob, LS, Task, TodoRead, TodoWrite, mcp__lsmcp__find_references, mcp__deepwiki
description: 未使用のコンポーネントを特定し、リファクタの計画を立てる
---

ultrathink.

## Context

- Unused components: !`$(if [ -f "package-lock.json" ]; then echo "npm run"; elif [ -f "yarn.lock" ]; then echo "yarn"; elif [ -f "pnpm-lock.yaml" ]; then echo "pnpm"; elif [ -f "bun.lockb" ]; then echo "bun"; fi) knip`
- Timestamp: !`date '+%Y%m%d-%H%M%S'`

## Your tasks

未使用のコンポーネントを特定し、リファクタの計画を立てて、".scratch/"ディレクトリに保存してください。

## Report file naming

knip-report-{Timestamp}.md

## Report contents

1. **未使用であると判断された理由**  
   例：どのファイルからもインポート／参照されていない等

2. **定義されているファイルパス**

3. **安全に削除できるかどうかについての考察**  
   削除による影響範囲や、追加で確認すべき点があれば記述してください
