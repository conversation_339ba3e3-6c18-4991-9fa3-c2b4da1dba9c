---
allowed-tools: Bash(git:*)
description: PRマージ後にメインブランチに切り替えて現在のブランチを削除
---

ultrathink. 

## Context

- Main branch: !`git remote show origin | grep 'HEAD branch' | cut -d' ' -f5`
- Current branch: !`git branch --show-current`
- Uncommited changes: !`git status --porcelain`

## Your tasks

1. 未コミットの変更がないことを確認（ある場合は中断）
2. メインブランチを自動検出（main/master/developの順で確認）
3. 現在のブランチ名を記録
4. メインブランチに切り替え
5. `git pull`で最新の変更を取得
6. 元の作業ブランチを削除
7. 完了メッセージを表示

## Rules

- メインブランチ上で実行した場合はエラーをユーザーに通知する
- ブランチ削除前にユーザーに確認する
