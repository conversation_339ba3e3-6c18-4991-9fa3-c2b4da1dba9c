# Claude Code カスタムコマンド集

このリポジトリは、[<PERSON> Code](https://claude.ai/code) で利用可能なカスタムコマンドを集めたものです。
Git submoduleとしてプロジェクトに追加することで、これらのコマンドを簡単に利用できるようになります。

## セットアップ方法

1. **submoduleとして追加:**

    あなたのプロジェクトのルートディレクトリで以下のコマンドを実行し、このリポジトリをsubmoduleとして追加します。

    ```bash
    git submodule add https://github.com/wh3at/claude-code-commands.git .claude/commands
    ```

2. **submoduleの初期化と更新:**

    ```bash
    git submodule update --init --recursive
    ```

3. **Claude Codeへの登録:**
    Claude Codeが `.claude/commands` ディレクトリ内のコマンドを自動的に認識するはずです。もし認識されない場合は、Claude Codeのドキュメントを参照してカスタムコマンドの登録方法を確認してください。

## 利用可能なコマンド

このリポジトリには、以下のカスタムコマンドが含まれています。各コマンドの詳細は、対応するドキュメントファイルを参照してください。

- `/can-delete`: [./commands/can-delete.md](./commands/can-delete.md)
    - 不要なファイルやコードを安全に削除できるかどうかを判断するためのコマンドです。
- `/commit-staged`: [./commands/commit-staged.md](./commands/commit-staged.md)
    - ステージングされている変更をコミットするためのコマンドです。
- `/commit`: [./commands/commit.md](./commands/commit.md)
    - 現在の変更をコミットメッセージと共にコミットするためのコマンドです。
- `/create-guide`: [./commands/create-guide.md](./commands/create-guide.md)
    - 特定のタスクや機能に関するガイドドキュメントを生成するコマンドです。
- `/fix`: [./commands/fix.md](./commands/fix.md)
    - 提供されたエラーを修正するコマンドです。
- `/knip`: [./commands/knip.md](./commands/knip.md)
    - [Knip](https://knip.dev/) を利用してプロジェクト内のデッドコードを検出するコマンドです。
- `/task`: [./commands/task.md](./commands/task.md)
    - 一般的な実装タスクに使用する汎用コマンドです。
- `/update-guide-if-needed`: [./commands/update-guide-if-needed.md](./commands/update-guide-if-needed.md)
    - 既存のガイドドキュメントが最新の情報に基づいているかどうかを判断し、必要であれば更新を促すコマンドです。
