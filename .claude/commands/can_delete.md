---
allowed-tools: Read, Bash(rg:*), Bash(git:*), Glob, LS, Task, TodoRead, TodoWrite, mcp__lsmcp__find_references, mcp__deepwiki
description: 指定されたファイル・ディレクトリ・クラスなどが安全に削除できるか評価
---

ultrathink.

## Context

- target: $ARGUMENTS

## Your tasks

- 削除の安全性を「安全 / 要注意 / 危険」の3段階で判定し、その根拠を示します
- 「要注意」「危険」の場合は、削除を可能にするために踏むべき具体的ステップを提案します

実際の評価では以下のステップを実行します：

1. **参照調査**  
   - リポジトリ全体を検索し、ターゲットへの参照（import, include, new, extends, テンプレート、リソースパスなど）を洗い出してください。
   - 外部依存（パッケージ、バイナリ、CI/CD設定、インフラIaCなど）への影響も確認してください。

2. **実行パス確認**  
   - 参照がなくとも動的読み込み・リフレクション・コンフィグ依存がないかチェックしてください。

3. **テスト網羅率チェック**  
   - 既存テストでカバーされている／されていない機能を列挙してください。
   - カバーが薄い場合は追加テストが必要か判断してください。

4. **ビルド & デプロイ影響**  
   - ビルドスクリプト、Dockerfile、パッケージング設定への影響を確認してください。

5. **ロールバック戦略**  
   - 万一不具合が起きた場合のロールバック手段（git revert, feature flagなど）を用意できるか評価してください。

削除対象のファイルを分析した上で、安全性評価と必要なアクションをユーザーに報告してください。
