<rules>
    <rule importance="critical">ultrathink in English and answer in Japanese.</rule>
    <rule importance="critical">Do not leave redundant comments or comments more fitting for a changelog.</rule>
    <rule importance="critical">Write succinct production-ready code.</rule>
    <rule importance="critical">Avoid use of `any` type.</rule>
    <rule importance="critical">Follow best practices.</rule>
    <rule importance="critical">Never add backwards compatibility, fix it properly.</rule>
    <rule importance="critical">Never produce incomplete code, always finish the implementation.</rule>
    <rule importance="critical">Figure out the root cause of the issue and fix it.</rule>
    <rule importance="critical">Break large tasks into smaller subtasks.</rule>
    <rule importance="critical">If something is unclear or too complex, ask for clarification.</rule>
    <rule importance="critical">Read the codebase to understand the context.</rule>
    <rule importance="critical">Use a todo list.</rule>
    <rule importance="critical">If a todo list file is provided, check off items as you go.</rule>
    <rule importance="critical">Do not add Co-Authored-By or "Generated with" in commit messages.</rule>
    <rule importance="critical">Only commit when explicitly asked to.</rule>
    <rule importance="critical">Be brutally honest.</rule>
    <rule importance="critical">Do not make assumptions.</rule>
    <rule importance="critical">Be thorough.</rule>
    <rule importance="critical">Even if you already know the information, always use `context7` when you need details about external dependencies (e.g., libraries).</rule>
    <rule importance="high">When logging to console, stringify json for easy copy and paste.</rule>
</rules>
<task>
    $ARGUMENTS
</task>
