{"env": {"MAX_THINKING_TOKENS": "31999"}, "hooks": {"Notification": [{"matcher": "", "hooks": [{"type": "command", "command": "curl -s --form-string \"token=a5twqu3nrnnu61axqh1z3ezr673eb9\" --form-string \"user=uhsobvv2dtxv98mvhk853y2cssmman\" --form-string \"message=$(jq -r '.message')\" --form-string \"title=Claude Code (kadou)\" https://api.pushover.net/1/messages.json"}]}], "Stop": [{"matcher": "", "hooks": [{"type": "command", "command": "curl -s --form-string \"token=a5twqu3nrnnu61axqh1z3ezr673eb9\" --form-string \"user=uhsobvv2dtxv98mvhk853y2cssmman\" --form-string \"message=✅️ タスクが完了しました\" --form-string \"title=Claude Code (kadou)\" https://api.pushover.net/1/messages.json"}]}], "PostToolUse": [{"matcher": "Write|Edit|MultiEdit", "hooks": [{"type": "command", "command": "jq -r '.tool_input.file_path | select(endswith(\\\".js\\\") or endswith(\\\".ts\\\") or endswith(\\\".jsx\\\") or endswith(\\\".tsx\\\"))' | xargs -r npm biome format --write"}]}]}, "permissions": {"allow": ["mcp__deep<PERSON><PERSON>__ask_question", "mcp__linear__get_issue", "Bash(similarity-ts . --experimental-types)", "Bash(npm run typecheck:*)", "Bash(npm run verify:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(rg:*)", "mcp__linear__list_comments", "mcp__convex__status", "mcp__o3__o3-search", "<PERSON><PERSON>(cat:*)", "Bash(gemini:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["<PERSON><PERSON><PERSON>", "linear", "convex", "sentry", "lsmcp", "o3", "beds24-api"]}