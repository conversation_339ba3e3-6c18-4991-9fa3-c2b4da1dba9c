# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
**/node_modules/
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# Build output, caches, and test reports
**/build/
**/out/
**/.next/
**/coverage/
**/storybook-static/
**/*.tsbuildinfo
**/.vercel/
**/.clerk/
**/.scratch/
playwright-browsers/

# Generated type definitions
**/next-env.d.ts

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
*storybook.log

# Temp files
**/debug-*.js
**/*.patch

# Misc
.DS_Store
*.pem
.mcp.json
.env.sentry-build-plugin
.claude/settings.local.json

# Binaries
apps/main/dotenv-linter

# Environment variables
# Ignore all .env files but keep .env.example files in any directory.
**/.env*
!**/.env.example
.env.docker
!.env.docker.example

# GitHub Actions Self-Hosted Runner
runner-data/
playwright-cache/

# pnpm isolation directories
