{
	// Build configuration for the monorepo
	// Configures TypeScript project references to enable building the entire monorepo with a single command
	// Usage: tsc --build tsconfig.build.json

	// Empty files array - this config doesn't compile any files directly
	"files": [],

	// Project references - all projects that should be built
	"references": [
		{
			"path": "./apps/main" // Main Next.js application with Convex backend
		},
		{
			"path": "./apps/fumadocs" // Documentation site
		}
	]
}
