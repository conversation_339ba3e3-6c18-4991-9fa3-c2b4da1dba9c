# Lefthook configuration
# https://github.com/evilmartians/lefthook

# Prevent skipping hooks with --no-verify
no_tty: false
colors: true

pre-commit:
  parallel: false
  commands:
    check-protected-files:
      run: ./scripts/check-protected-files.sh
      stage_fixed: false
      fail_text: |
        ❌ Protected files modification detected!
        
        The following protected files were modified:
        {files}
        
        To proceed:
        1. Revert changes to protected files
        2. Or get approval from the code owner
        
        Protected file patterns are defined in .protected-files
    verify:
      run: pnpm verify:all
      env:
        # miseでインストールしたpnpmへのパスを追加
        PATH: "$HOME/.local/share/mise/shims:$PATH"
      fail_text: |
        ❌ Pre-commit verification failed!
        
        Please fix all errors before committing:
        - Biome linting/formatting issues
        - File/directory naming violations
        - TypeScript type errors
        - Failing tests
        
        Run 'pnpm verify:all' to see all issues.