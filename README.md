# KADOU DELTA

**前提条件**:
- Node.js (LTS版推奨)
- pnpm 10.13.1以上
- mise (バージョン管理ツール) - 推奨

**miseを使用している場合**:
```bash
mise install  # Node.jsとpnpmを自動的にインストール
```

GitHubでこのREADMEを読んでいて、このテンプレートを使用したい場合は、以下を実行してください：

```bash
pnpm dlx create-convex@latest -t nextjs-clerk
```

## Convex環境変数の設定

Convex関数内で使用する環境変数は、Convexダッシュボードまたは CLI で設定します。

### 必要な環境変数

| 変数名 | 説明 | 設定場所 |
|--------|------|----------|
| `CLERK_JWT_ISSUER_DOMAIN` | Clerk認証用のIssuer URL | Convex Dashboard |
| `SCRAPING_ANT_API_KEY` | ScrapingAnt Web Scraping API用のAPIキー | Convex Dashboard |

### 設定方法

**方法1: Convex Dashboard**
1. [Convex Dashboard](https://dashboard.convex.dev) にアクセス
2. Settings > Environment Variables
3. 変数名と値を入力して保存

**方法2: CLI**
```bash
cd apps/main
npx convex env set VARIABLE_NAME "value"

# 例：ScrapingAnt APIキーの設定
npx convex env set SCRAPING_ANT_API_KEY "your-api-key"
```

### 環境変数の確認
```bash
npx convex env list
```

## Clerk認証のセットアップ

1. アプリを開きます。アプリの右下にClerkからの「Claim your application」ボタンがあるはずです。
2. 手順に従ってアプリケーションを取得し、このアプリにリンクします。
3. [Convex Clerk オンボーディングガイド](https://docs.convex.dev/auth/clerk#get-started)のステップ3に従って、Convex JWTテンプレートを作成します。
4. `convex/auth.config.ts`でClerkプロバイダーのコメントアウトを解除します
5. Convexダッシュボードの開発デプロイメント環境変数設定にIssuer URLを`CLERK_JWT_ISSUER_DOMAIN`として貼り付けます（[ドキュメント](https://docs.convex.dev/auth/clerk#configuring-dev-and-prod-instances)を参照）

WebhookでClerkユーザーデータを同期したい場合は、この[サンプルリポジトリ](https://github.com/thomasballinger/convex-clerk-users-table/)をチェックしてください。

## デプロイ

[Webサービス公開前のチェックリスト](https://zenn.dev/catnose99/articles/547cbf57e5ad28)
