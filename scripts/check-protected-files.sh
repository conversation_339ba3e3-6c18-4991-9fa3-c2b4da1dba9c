#!/bin/bash

# check-protected-files.sh
# This script checks if any protected files are being modified in a commit
# Protected files are defined in .protected-files at the repository root

set -e
set -o pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get the repository root
REPO_ROOT=$(git rev-parse --show-toplevel)
PROTECTED_FILES_LIST="$REPO_ROOT/.protected-files"

# Check if protected files list exists
if [ ! -f "$PROTECTED_FILES_LIST" ]; then
    echo -e "${YELLOW}Warning: .protected-files not found at repository root${NC}"
    exit 0
fi

# Parse command line arguments
CI_MODE=false
if [ "$1" = "--ci" ]; then
    CI_MODE=true
fi

# Function to check if a file matches any protected pattern
is_protected_file() {
    local file="$1"
    
    while IFS= read -r pattern; do
        # Skip empty lines and comments
        if [[ -z "$pattern" || "$pattern" =~ ^# ]]; then
            continue
        fi
        
        # Remove leading/trailing whitespace
        pattern=$(echo "$pattern" | xargs)
        
        # Add leading slash if not present in pattern
        if [[ ! "$pattern" =~ ^/ ]]; then
            pattern="/$pattern"
        fi
        
        # Convert glob pattern to regex for matching
        # Handle ** for recursive matching
        pattern_regex=$(echo "$pattern" \
          | sed -e 's/\*\*/__GLOBSTAR__/g' \
          | sed -e 's/\*/[^\/]*/g' \
          | sed -e 's/__GLOBSTAR__/.*/g')
        
        # Check if file matches the pattern
        if [[ "$file" =~ ^${pattern_regex}$ ]]; then
            return 0
        fi
    done < "$PROTECTED_FILES_LIST"
    
    return 1
}

# Get list of changed files
if [ "$CI_MODE" = true ]; then
    # In CI mode, check all changed files in the PR
    if [ -n "$GITHUB_BASE_REF" ]; then
        # GitHub Actions PR context - use merge-base for accurate comparison
        base=$(git merge-base HEAD "origin/${GITHUB_BASE_REF}")
        CHANGED_FILES=$(git diff --name-only "$base"...HEAD)
    else
        # Fallback: check against previous commit
        CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)
    fi
else
    # In local mode, check staged files
    CHANGED_FILES=$(git diff --cached --name-only)
fi

# Check each changed file
PROTECTED_FOUND=false
PROTECTED_LIST=""

for file in $CHANGED_FILES; do
    # Add leading slash if not present
    if [[ ! "$file" =~ ^/ ]]; then
        file="/$file"
    fi
    
    if is_protected_file "$file"; then
        PROTECTED_FOUND=true
        PROTECTED_LIST="${PROTECTED_LIST}  - ${file}\n"
    fi
done

# Report results
if [ "$PROTECTED_FOUND" = true ]; then
    echo -e "${RED}❌ Protected files modification detected!${NC}"
    echo ""
    echo "The following protected files were modified:"
    echo -e "$PROTECTED_LIST"
    echo "To proceed:"
    echo "1. Revert changes to protected files"
    echo "2. Or get approval from the code owner"
    echo ""
    echo "Protected file patterns are defined in .protected-files"
    exit 1
else
    if [ "$CI_MODE" = false ]; then
        echo -e "${GREEN}✓ No protected files modified${NC}"
    fi
    exit 0
fi