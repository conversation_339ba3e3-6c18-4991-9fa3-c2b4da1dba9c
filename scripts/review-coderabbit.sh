#!/bin/bash

set -e

# 現在のブランチとPR番号を取得
BRANCH=$(git rev-parse --abbrev-ref HEAD)
PR_NUMBER=$(gh pr view --json number -q .number 2>/dev/null)

if [ -z "$PR_NUMBER" ]; then
    echo "Error: No PR found for branch $BRANCH"
    exit 1
fi

# リポジトリのオーナーと名前を取得
REPO_INFO=$(gh repo view --json nameWithOwner -q .nameWithOwner)
OWNER=$(echo "$REPO_INFO" | cut -d'/' -f1)
REPO=$(echo "$REPO_INFO" | cut -d'/' -f2)

echo "Processing CodeRabbit comments from PR #$PR_NUMBER ($REPO_INFO)"

# GraphQL queryでレビュースレッドを取得
GRAPHQL_QUERY='
query($owner: String!, $repo: String!, $pr: Int!) {
  repository(owner: $owner, name: $repo) {
    pullRequest(number: $pr) {
      reviewThreads(first: 100) {
        nodes {
          id
          isResolved
          comments(first: 20) {
            nodes {
              id
              body
              path
              line
              author {
                login
              }
            }
          }
        }
      }
    }
  }
}'

# GraphQL APIでレビュースレッドを取得
echo "Fetching review threads via GraphQL..."
THREADS_JSON=$(gh api graphql -f query="$GRAPHQL_QUERY" -f owner="$OWNER" -f repo="$REPO" -F pr="$PR_NUMBER")

# 未解決のCodeRabbitコメントを抽出（配列として処理）
UNRESOLVED_THREADS=$(echo "$THREADS_JSON" | jq -c '
  [.data.repository.pullRequest.reviewThreads.nodes[] |
  select(.isResolved == false) |
  select(.comments.nodes[0].author.login == "coderabbitai") |
  {
    threadId: .id,
    comment: .comments.nodes[0]
  }]
')

# 未解決スレッドの数を取得
THREAD_COUNT=$(echo "$UNRESOLVED_THREADS" | jq 'length')
echo "Found $THREAD_COUNT unresolved CodeRabbit review threads"

if [ "$THREAD_COUNT" -eq 0 ]; then
    echo "No unresolved CodeRabbit comments found."
    exit 0
fi

# 各未解決スレッドを処理
THREAD_INDEX=0
echo "$UNRESOLVED_THREADS" | jq -c '.[]' | while read -r THREAD; do
    THREAD_INDEX=$((THREAD_INDEX + 1))
    THREAD_ID=$(echo "$THREAD" | jq -r '.threadId')
    COMMENT_ID=$(echo "$THREAD" | jq -r '.comment.id')
    COMMENT_BODY=$(echo "$THREAD" | jq -r '.comment.body')
    FILE_PATH=$(echo "$THREAD" | jq -r '.comment.path // "N/A"')
    LINE=$(echo "$THREAD" | jq -r '.comment.line // "N/A"')
    
    # コメントの1文目を抽出
    FIRST_SENTENCE=$(echo "$COMMENT_BODY" | sed -E 's/^#+\s*//g' | sed -E 's/^[[:space:]]*//;s/[[:space:]]*$//' | sed -E 's/([.!?])[[:space:]].*/\1/' | head -c 80)
    
    echo "[$THREAD_INDEX/$THREAD_COUNT] Processing unresolved comment: $FIRST_SENTENCE"
    
    # Claude Codeで評価、修正、コミット
    claude --mcp-config .mcp.json --allowedTools Read,Edit,MultiEdit,Write,Bash,Grep,Glob,LS,TodoWrite,mcp__lsmcp,mcp__deepwiki,mcp__o3,mcp__beds24-api -p "
以下のCodeRabbitレビューコメントを評価してください。

コメントID: $COMMENT_ID
ファイル: $FILE_PATH
行番号: $LINE
コメント内容:
$COMMENT_BODY

以下の基準で評価してください：
- 既存コードベースの慣習と整合する
- 実際の改善価値がある
- 破壊的変更のリスクがない

妥当でない場合:
何もせず終了

妥当と判断した場合:
1. 指定されたファイルで修正を実施
2. 修正をコミットする

"
    
    # スレッドを解決済みにマーク
    echo "Marking thread as resolved..."
    RESOLVE_MUTATION='
    mutation($threadId: ID!) {
      resolveReviewThread(input: {threadId: $threadId}) {
        thread {
          id
          isResolved
        }
      }
    }'
    
    gh api graphql -f query="$RESOLVE_MUTATION" -f threadId="$THREAD_ID" > /dev/null
    echo "Thread marked as resolved"
    
    echo "----------------------------------------"
    sleep 1
done

echo "Done processing all unresolved CodeRabbit comments"

# Pushover通知（環境変数が設定されている場合のみ）
if [[ -n "${PUSHOVER_TOKEN}" && -n "${PUSHOVER_USER}" ]]; then
    curl -s \
        --form-string "token=${PUSHOVER_TOKEN}" \
        --form-string "user=${PUSHOVER_USER}" \
        --form-string 'message=✅️ PRレビュー対応が完了しました' \
        --form-string 'title=Claude Code (kadou)' \
        https://api.pushover.net/1/messages.json
fi
