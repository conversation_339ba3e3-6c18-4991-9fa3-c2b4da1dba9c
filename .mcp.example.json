{"mcpServers": {"deepwiki": {"command": "npx", "args": ["-y", "mcp-remote", "https://mcp.deepwiki.com/sse"]}, "linear": {"command": "npx", "args": ["-y", "mcp-remote", "https://mcp.linear.app/sse"]}, "convex": {"command": "npx", "args": ["-y", "convex@latest", "mcp", "start"]}, "sentry": {"command": "npx", "args": ["-y", "mcp-remote", "https://mcp.sentry.dev/sse"]}, "lsmcp": {"command": "npx", "args": ["-y", "@mizchi/lsmcp", "--language", "typescript"]}, "o3": {"command": "npx", "args": ["-y", "o3-search-mcp"], "env": {"OPENAI_API_KEY": "your-api-key", "SEARCH_CONTEXT_SIZE": "medium", "REASONING_EFFORT": "medium", "OPENAI_API_TIMEOUT": "90000"}}, "beds24-api": {"command": "node", "args": ["/path/to/beds24-api-v2-mcp/dist/server.js"]}, "context7": {"type": "http", "url": "https://mcp.context7.com/mcp"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "@21st-dev/magic": {"command": "npx", "args": ["-y", "@21st-dev/magic@latest", "API_KEY=\"your-api-key\""]}, "playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"]}}}